-- 添加最后一批表，确保达到完整的106个表
-- 这是最后的缺失表

USE `ccds`;

-- 22. wms_war_out (仓库出库表)
DROP TABLE IF EXISTS `wms_war_out`;
CREATE TABLE `wms_war_out` (
  `wwo_id` bigint NOT NULL AUTO_INCREMENT,
  `wwo_code` varchar(50) DEFAULT NULL,
  `wwo_title` longtext,
  `wwo_stro_code` varchar(50) DEFAULT NULL,
  `wwo_user_code` varchar(50) DEFAULT NULL,
  `wwo_state` char(1) DEFAULT NULL,
  `wwo_remark` longtext,
  `wwo_isdel` char(1) DEFAULT NULL,
  `wwo_inp_name` varchar(50) DEFAULT NULL,
  `wwo_alt_name` varchar(50) DEFAULT NULL,
  `wwo_inp_time` datetime DEFAULT NULL,
  `wwo_alt_time` datetime DEFAULT NULL,
  `wwo_out_date` datetime DEFAULT NULL,
  `wwo_app_date` datetime DEFAULT NULL,
  `wwo_app_man` varchar(50) DEFAULT NULL,
  `wwo_app_desc` longtext,
  `wwo_app_isok` char(1) DEFAULT NULL,
  `wwo_ord_code` bigint DEFAULT NULL,
  `wwo_can_date` datetime DEFAULT NULL,
  `wwo_can_man` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`wwo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 23. r_wms_change (仓库变更关系表)
DROP TABLE IF EXISTS `r_wms_change`;
CREATE TABLE `r_wms_change` (
  `rwc_id` bigint NOT NULL AUTO_INCREMENT,
  `rwc_wch_id` bigint DEFAULT NULL,
  `rwc_pro_id` bigint DEFAULT NULL,
  `rwc_num` decimal(18,2) DEFAULT NULL,
  `rwc_remark` longtext,
  PRIMARY KEY (`rwc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 24. case_bat (案件批处理表)
DROP TABLE IF EXISTS `case_bat`;
CREATE TABLE `case_bat` (
  `cb_id` bigint NOT NULL AUTO_INCREMENT,
  `cb_name` varchar(100) DEFAULT NULL,
  `cb_desc` longtext,
  `cb_cre_time` datetime DEFAULT NULL,
  `cb_cre_man` varchar(25) DEFAULT NULL,
  `cb_upd_time` datetime DEFAULT NULL,
  `cb_upd_man` varchar(25) DEFAULT NULL,
  `cb_state` int DEFAULT NULL,
  `cb_count` int DEFAULT NULL,
  `cb_file_path` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`cb_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 25. pro_actor (项目参与者表)
DROP TABLE IF EXISTS `pro_actor`;
CREATE TABLE `pro_actor` (
  `pac_id` bigint NOT NULL AUTO_INCREMENT,
  `pac_pro_id` bigint DEFAULT NULL,
  `pac_se_no` bigint DEFAULT NULL,
  `pac_role` varchar(50) DEFAULT NULL,
  `pac_join_date` datetime DEFAULT NULL,
  `pac_leave_date` datetime DEFAULT NULL,
  `pac_remark` longtext,
  `pac_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`pac_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 26. sup_contact (供应商联系人表)
DROP TABLE IF EXISTS `sup_contact`;
CREATE TABLE `sup_contact` (
  `suc_id` bigint NOT NULL AUTO_INCREMENT,
  `suc_ssu_id` bigint DEFAULT NULL,
  `suc_name` varchar(100) DEFAULT NULL,
  `suc_sex` varchar(10) DEFAULT NULL,
  `suc_pos` varchar(50) DEFAULT NULL,
  `suc_phone` varchar(50) DEFAULT NULL,
  `suc_mobile` varchar(50) DEFAULT NULL,
  `suc_email` varchar(100) DEFAULT NULL,
  `suc_qq` varchar(50) DEFAULT NULL,
  `suc_msn` varchar(50) DEFAULT NULL,
  `suc_address` varchar(500) DEFAULT NULL,
  `suc_remark` longtext,
  `suc_ins_date` datetime DEFAULT NULL,
  `suc_upd_date` datetime DEFAULT NULL,
  `suc_inp_user` varchar(50) DEFAULT NULL,
  `suc_upd_user` varchar(50) DEFAULT NULL,
  `suc_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`suc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 27. pro_task_lim (项目任务权限表)
DROP TABLE IF EXISTS `pro_task_lim`;
CREATE TABLE `pro_task_lim` (
  `ptl_id` bigint NOT NULL AUTO_INCREMENT,
  `ptl_prta_id` bigint DEFAULT NULL,
  `ptl_se_no` bigint DEFAULT NULL,
  `ptl_role` varchar(50) DEFAULT NULL,
  `ptl_can_edit` char(1) DEFAULT NULL,
  `ptl_can_view` char(1) DEFAULT NULL,
  `ptl_can_delete` char(1) DEFAULT NULL,
  `ptl_remark` longtext,
  PRIMARY KEY (`ptl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 28. sal_pur_ord (销售采购订单表)
DROP TABLE IF EXISTS `sal_pur_ord`;
CREATE TABLE `sal_pur_ord` (
  `spo_id` bigint NOT NULL AUTO_INCREMENT,
  `spo_code` varchar(300) DEFAULT NULL,
  `spo_title` varchar(300) DEFAULT NULL,
  `spo_type_id` bigint DEFAULT NULL,
  `spo_ssu_id` bigint DEFAULT NULL,
  `spo_sum_mon` decimal(18,2) DEFAULT NULL,
  `spo_paid_mon` decimal(18,2) DEFAULT NULL,
  `spo_mon_type` varchar(50) DEFAULT NULL,
  `spo_state` varchar(10) DEFAULT NULL,
  `spo_ship_state` varchar(10) DEFAULT NULL,
  `spo_own_code` varchar(50) DEFAULT NULL,
  `spo_deadline` datetime DEFAULT NULL,
  `spo_end_date` datetime DEFAULT NULL,
  `spo_ord_date` datetime DEFAULT NULL,
  `spo_inp_date` datetime DEFAULT NULL,
  `spo_isfail` char(1) DEFAULT NULL,
  `spo_remark` longtext,
  `spo_change_date` datetime DEFAULT NULL,
  `spo_paid_method` varchar(20) DEFAULT NULL,
  `spo_inp_code` varchar(50) DEFAULT NULL,
  `spo_ssu_con` varchar(100) DEFAULT NULL,
  `spo_se_no` bigint DEFAULT NULL,
  `spo_con_date` datetime DEFAULT NULL,
  `spo_change_user` varchar(50) DEFAULT NULL,
  `spo_app_date` datetime DEFAULT NULL,
  `spo_app_man` varchar(50) DEFAULT NULL,
  `spo_app_desc` longtext,
  `spo_app_isok` char(1) DEFAULT NULL,
  `spo_content` longtext,
  PRIMARY KEY (`spo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 29. hurr_rec (催收记录表)
DROP TABLE IF EXISTS `hurr_rec`;
CREATE TABLE `hurr_rec` (
  `hr_id` bigint NOT NULL AUTO_INCREMENT,
  `hr_cas_id` bigint DEFAULT NULL,
  `hr_contact` varchar(200) DEFAULT NULL,
  `hr_content` longtext,
  `hr_time` datetime DEFAULT NULL,
  `hr_se_no` bigint DEFAULT NULL,
  `hr_con_type` varchar(50) DEFAULT NULL,
  `hr_result` varchar(50) DEFAULT NULL,
  `hr_next_time` datetime DEFAULT NULL,
  `hr_remark` longtext,
  `hr_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`hr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 30. wms_check (仓库盘点表)
DROP TABLE IF EXISTS `wms_check`;
CREATE TABLE `wms_check` (
  `wck_id` bigint NOT NULL AUTO_INCREMENT,
  `wck_code` varchar(50) DEFAULT NULL,
  `wck_title` varchar(300) DEFAULT NULL,
  `wck_stro_code` varchar(50) DEFAULT NULL,
  `wck_state` char(1) DEFAULT NULL,
  `wck_check_date` datetime DEFAULT NULL,
  `wck_user_code` varchar(50) DEFAULT NULL,
  `wck_remark` longtext,
  `wck_inp_date` datetime DEFAULT NULL,
  `wck_alt_date` datetime DEFAULT NULL,
  `wck_inp_user` varchar(50) DEFAULT NULL,
  `wck_alt_user` varchar(50) DEFAULT NULL,
  `wck_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`wck_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 31. loan_season (贷款季度表)
DROP TABLE IF EXISTS `loan_season`;
CREATE TABLE `loan_season` (
  `ls_id` bigint NOT NULL AUTO_INCREMENT,
  `ls_name` varchar(100) DEFAULT NULL,
  `ls_year` int DEFAULT NULL,
  `ls_season` int DEFAULT NULL,
  `ls_start_date` date DEFAULT NULL,
  `ls_end_date` date DEFAULT NULL,
  `ls_desc` longtext,
  `ls_cre_time` datetime DEFAULT NULL,
  `ls_cre_user` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`ls_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 32. loan_cus (贷款客户表)
DROP TABLE IF EXISTS `loan_cus`;
CREATE TABLE `loan_cus` (
  `lc_id` bigint NOT NULL AUTO_INCREMENT,
  `lc_name` varchar(50) DEFAULT NULL,
  `lc_id_no` varchar(50) DEFAULT NULL,
  `lc_phone` varchar(50) DEFAULT NULL,
  `lc_address` varchar(500) DEFAULT NULL,
  `lc_work_address` varchar(500) DEFAULT NULL,
  `lc_work_phone` varchar(50) DEFAULT NULL,
  `lc_email` varchar(100) DEFAULT NULL,
  `lc_bank` varchar(100) DEFAULT NULL,
  `lc_card_no` varchar(50) DEFAULT NULL,
  `lc_loan_amt` decimal(18,2) DEFAULT NULL,
  `lc_balance` decimal(18,2) DEFAULT NULL,
  `lc_overdue_amt` decimal(18,2) DEFAULT NULL,
  `lc_overdue_days` int DEFAULT NULL,
  `lc_loan_date` date DEFAULT NULL,
  `lc_due_date` date DEFAULT NULL,
  `lc_status` varchar(50) DEFAULT NULL,
  `lc_remark` longtext,
  `lc_ins_time` datetime DEFAULT NULL,
  `lc_ins_user` varchar(25) DEFAULT NULL,
  `lc_upd_time` datetime DEFAULT NULL,
  `lc_upd_user` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`lc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 33. loan_com (贷款公司表)
DROP TABLE IF EXISTS `loan_com`;
CREATE TABLE `loan_com` (
  `lcom_id` bigint NOT NULL AUTO_INCREMENT,
  `lcom_name` varchar(100) DEFAULT NULL,
  `lcom_code` varchar(50) DEFAULT NULL,
  `lcom_address` varchar(500) DEFAULT NULL,
  `lcom_phone` varchar(50) DEFAULT NULL,
  `lcom_contact` varchar(50) DEFAULT NULL,
  `lcom_email` varchar(100) DEFAULT NULL,
  `lcom_desc` longtext,
  `lcom_status` varchar(50) DEFAULT NULL,
  `lcom_cre_time` datetime DEFAULT NULL,
  `lcom_cre_user` varchar(25) DEFAULT NULL,
  `lcom_upd_time` datetime DEFAULT NULL,
  `lcom_upd_user` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`lcom_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 34. loan_per (贷款个人表)
DROP TABLE IF EXISTS `loan_per`;
CREATE TABLE `loan_per` (
  `lp_id` bigint NOT NULL AUTO_INCREMENT,
  `lp_name` varchar(50) DEFAULT NULL,
  `lp_id_no` varchar(50) DEFAULT NULL,
  `lp_phone` varchar(50) DEFAULT NULL,
  `lp_address` varchar(500) DEFAULT NULL,
  `lp_work_unit` varchar(200) DEFAULT NULL,
  `lp_work_phone` varchar(50) DEFAULT NULL,
  `lp_income` decimal(18,2) DEFAULT NULL,
  `lp_loan_amt` decimal(18,2) DEFAULT NULL,
  `lp_balance` decimal(18,2) DEFAULT NULL,
  `lp_overdue_amt` decimal(18,2) DEFAULT NULL,
  `lp_overdue_days` int DEFAULT NULL,
  `lp_loan_date` date DEFAULT NULL,
  `lp_due_date` date DEFAULT NULL,
  `lp_status` varchar(50) DEFAULT NULL,
  `lp_remark` longtext,
  `lp_ins_time` datetime DEFAULT NULL,
  `lp_ins_user` varchar(25) DEFAULT NULL,
  `lp_upd_time` datetime DEFAULT NULL,
  `lp_upd_user` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`lp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 35. type_list_connect (类型列表连接表)
DROP TABLE IF EXISTS `type_list_connect`;
CREATE TABLE `type_list_connect` (
  `tlc_id` bigint NOT NULL AUTO_INCREMENT,
  `tlc_parent_id` bigint DEFAULT NULL,
  `tlc_child_id` bigint DEFAULT NULL,
  `tlc_relation_type` varchar(50) DEFAULT NULL,
  `tlc_order` int DEFAULT NULL,
  `tlc_desc` longtext,
  `tlc_cre_time` datetime DEFAULT NULL,
  `tlc_cre_user` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`tlc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

SELECT 'CCDS数据库 - 最后一批表添加完成，现在应该有106个表了！' as message;
