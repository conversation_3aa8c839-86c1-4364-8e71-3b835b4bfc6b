package cn.comfan.test.carbus;

/**
 * Created by comfa on 2017/7/31.
 */
public class StringSplitTest {
    public static void main(String[] args){
        String frequentContactStr = "[常用]17794518809,[常用]15386849960,[常用]18234752337,[常用]18817833378,[常用]13671522553,[常用]15821123051,[常用]17321289369,[常用]18516528608,[常用]18321854958,[常用]15392680517";
        String[] frequentContacts = frequentContactStr.split(",|;|\\r\\n|\\r|\\n|\\| |\\.");
        for (String s : frequentContacts) {
            System.out.println(s);
        }
    }
}
