<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>保存案人数据</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript">
		function check(){
			var errStr = "";
			if(isEmpty("idNumber")){
				errStr+="- 未填写证件号！\n";
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				waitSubmit("doSave","保存中...");
				waitSubmit("doCancel");
				return $("saveForm").submit();
			}
		}
		
		window.onload=function(){
			if("${bankCase}"!=""){
				$("isIfrm").value="1";
			}
		}
  	</script>
  </head>
  <body>
  	<div class="inputDiv">
        <form id="saveForm" action="extraInfAction.do" method="post">
            <input type="hidden" name="op" value="saveExtraInf" />
           	<input type="hidden" name="isIfrm" id="isIfrm" />
            <table class="dashTab inputForm" cellpadding="0" cellspacing="0">  
            	<tbody>    
                	<tr>
                        <th class="required">证件号：<span class='red'>*</span></th>
                        <td colspan="3"><input type="text" id="idNumber" class="inputSize2L" name="extraInf.exiIdNumber" onBlur="autoShort(this,25)" value="${extraInf.exiIdNumber}${bankCase.casNum}"/></td>
                    </tr>
                    <tr>
                        <th>姓名：</th>
                        <td><input type="text" class="inputSize2" name="extraInf.exiName" onBlur="autoShort(this,25)" value="${extraInf.exiName}${bankCase.casName}"/></td>
                        <th>信息类别：</th>
                        <td>
                        <input type="text" class="inputSize2 inputBoxAlign"style="width:80px;" id="exiType" name="extraInf.exiType" onBlur="autoShort(this,50)" value="${extraInf.exiType}"/>
                        <c:if test="${!empty exiTypeList}">
	                    <select style="width:70px;" class="inputSize2 inputBoxAlign"onChange="setValueFromSel(this,'exiType')">
	                        <option value="">请选择</option>
	                        <c:forEach items="${exiTypeList}" var="exiTypeObj">
	                        <option value="${exiTypeObj.typName}">${exiTypeObj.typName}</option>
	                        </c:forEach>
	                    </select>
	                    </c:if>
	                    <c:if test="${empty exiTypeList}">
	                        <select style="width:70px;" class="inputSize2 inputBoxAlign" disabled="disabled">
	                            <option>未添加</option>
	                        </select>
	                    </c:if>
	                    <img src="images/content/plugin.gif" alt="该字段可在类别管理中自定义" style="vertical-align:middle"/>
                        </td>
                    </tr>  
                    <tr>
                    	  <th>信息内容：</th>
                          <td colspan="3"><textarea class="inputSize2L" name="extraInf.exiContent" rows="5" onBlur="autoShort(this,4000)">${extraInf.exiContent}</textarea></td>
                    </tr>                                                  
                    <tr class="noBorderBot">
                    	  <th>备注：</th>
                          <td colspan="3"><textarea class="inputSize2L" name="extraInf.exiRemark" rows="4" onBlur="autoShort(this,4000)">${extraInf.exiRemark}</textarea></td>
                    </tr>
                    <tr class="submitTr">
                        <td colspan="4">
                        <input id="cbatSave" class="butSize1" type="button" value="保存" onClick="check()" />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                        </td>
                    </tr>                   
                </tbody>
            </table>
        </form>
    </div>
  </body>
</html>
