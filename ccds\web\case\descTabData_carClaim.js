function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE_WITH_REMARK','C_DT'],
		['ID_NO','STATE','CIP_CLAIM_DATE','CIP_ACC_DATE'],
		['AMT','PAID_LEFT',['LAST_M',3]],
		['CL_T','PTP','CP','HOM'],
		['CIP_ACC_PLACE','CIP_ACC_CAUSE','CIP_DEPT','CIP_DEST'],
		['CIP_GOODS','CIP_DRV1','CIP_OWNER','CIP_AFF'],
		['CIP_LIC1','CIP_PHONE1','CIP_POL_NO1','CIP_COM1'],
		['CIP_TYPE1','CIP_STATUS1','CIP_RSP1','CIP_DRV2'],
		['CIP_LIC2','CIP_PHONE2','CIP_STATUS2','CIP_COM2'],
		['CIP_POL_NO2','CIP_TYPE2','CIP_RSP2','CIP_CLAIM_STATUS'],
		[['TIPS_DT',3],['PR_COUNT',3]],
		['CL_AREA','EMP_NAME','LAST_CL','LAST_VIS'],
		[['TREMARK',7]]
	);
}
function getLayout2(){
	return  new Array(
		['ASS_TM',['ASS_HIS',5]],
		[['ATT',7]]
	);
}