<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>保存提示日期</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/> 
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>  
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/case.js"></script>
    <script type="text/javascript">
		function check(){
			waitSubmit("save","保存中...");
			waitSubmit("doCancel");
			return $("create").submit();
		}
		window.onload = function(){
			$("tipsDateTxt").innerHTML = getTipsDateTxt("${CUS_VER_ID}")+"：";
		}
  	</script>
  </head>
  <body>
  	<div class="inputDiv">
        <form id="create" action="caseAction.do" method="post">
            <input type="hidden" name="op" value="saveTipTime" />
            <input type="hidden" id="casId" name="casId" value="${casId}" />
            <table class="dashTab inputForm single" cellpadding="0" cellspacing="0">                       
                    <tr class="noBorderBot">
                       	<th id="tipsDateTxt" class="required"></th>
                           	<td>
                           		<input class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:140px;" readonly="readonly" ondblClick="clearInput(this)" type="text" name="startTime" id="staTim" onClick="WdatePicker({skin:'default',dateFmt:'yyyy-MM-dd'})"/>
                			</td>
                   	</tr>
                    <tr class="submitTr">
                        <td colspan="2">
                        <input id="save" class="butSize1" type="button" value="保存" onClick="check()" />
                        &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;
                        <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                        </td>
                    </tr>                   
            </table>
        </form>
    </div>
  </body>
</html>