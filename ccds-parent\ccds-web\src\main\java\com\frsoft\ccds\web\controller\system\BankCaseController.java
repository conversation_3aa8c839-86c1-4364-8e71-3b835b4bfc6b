package com.frsoft.ccds.web.controller.system;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.common.core.page.TableDataInfo;
import com.frsoft.ccds.system.domain.BankCase;
import com.frsoft.ccds.system.service.IBankCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 银行案件Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Controller
@RequestMapping("/system/bankcase")
public class BankCaseController extends BaseController {
    
    private String prefix = "system/bankcase";

    @Autowired
    private IBankCaseService bankCaseService;

    /**
     * 跳转到银行案件页面
     */
    @GetMapping()
    public String bankcase() {
        return prefix + "/bankcase";
    }

    /**
     * 查询银行案件列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BankCase bankCase) {
        startPage();
        List<BankCase> list = bankCaseService.selectBankCaseList(bankCase);
        return getDataTable(list);
    }

    /**
     * 新增银行案件
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存银行案件
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BankCase bankCase) {
        return toAjax(bankCaseService.insertBankCase(bankCase));
    }

    /**
     * 修改银行案件
     */
    @GetMapping("/edit/{casId}")
    public String edit(@PathVariable("casId") Long casId, ModelMap mmap) {
        BankCase bankCase = bankCaseService.selectBankCaseByCasId(casId);
        mmap.put("bankCase", bankCase);
        return prefix + "/edit";
    }

    /**
     * 修改保存银行案件
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BankCase bankCase) {
        return toAjax(bankCaseService.updateBankCase(bankCase));
    }

    /**
     * 删除银行案件
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bankCaseService.deleteBankCaseByCasIds(convertToLongArray(ids)));
    }

    /**
     * 查看银行案件详情
     */
    @GetMapping("/detail/{casId}")
    public String detail(@PathVariable("casId") Long casId, ModelMap mmap) {
        BankCase bankCase = bankCaseService.selectBankCaseByCasId(casId);
        mmap.put("bankCase", bankCase);
        return prefix + "/detail";
    }

    /**
     * 分配案件
     */
    @GetMapping("/assign")
    public String assign() {
        return prefix + "/assign";
    }

    /**
     * 分配案件保存
     */
    @PostMapping("/assign")
    @ResponseBody
    public AjaxResult assignSave(@RequestParam("casIds") String casIds, 
                                @RequestParam("seNo") Long seNo,
                                @RequestParam("assignUser") String assignUser) {
        Long[] casIdArray = convertToLongArray(casIds);
        return toAjax(bankCaseService.assignBankCaseToEmployee(casIdArray, seNo, assignUser));
    }

    /**
     * 根据员工编号查询案件列表
     */
    @PostMapping("/listByEmployee")
    @ResponseBody
    public TableDataInfo listByEmployee(@RequestParam("seNo") Long seNo) {
        startPage();
        List<BankCase> list = bankCaseService.selectBankCaseListBySeNo(seNo);
        return getDataTable(list);
    }

    /**
     * 根据案件状态查询案件列表
     */
    @PostMapping("/listByState")
    @ResponseBody
    public TableDataInfo listByState(@RequestParam("casState") Integer casState) {
        startPage();
        List<BankCase> list = bankCaseService.selectBankCaseListByState(casState);
        return getDataTable(list);
    }

    /**
     * 根据客户姓名查询案件列表
     */
    @PostMapping("/listByName")
    @ResponseBody
    public TableDataInfo listByName(@RequestParam("casName") String casName) {
        startPage();
        List<BankCase> list = bankCaseService.selectBankCaseListByName(casName);
        return getDataTable(list);
    }

    /**
     * 根据客户电话查询案件列表
     */
    @PostMapping("/listByPhone")
    @ResponseBody
    public TableDataInfo listByPhone(@RequestParam("casPhone") String casPhone) {
        startPage();
        List<BankCase> list = bankCaseService.selectBankCaseListByPhone(casPhone);
        return getDataTable(list);
    }

    /**
     * 统计案件总数
     */
    @PostMapping("/count")
    @ResponseBody
    public AjaxResult count(BankCase bankCase) {
        int count = bankCaseService.countBankCase(bankCase);
        return AjaxResult.success("查询成功", count);
    }

    /**
     * 字符串转Long数组
     */
    private Long[] convertToLongArray(String str) {
        String[] strArray = str.split(",");
        Long[] longArray = new Long[strArray.length];
        for (int i = 0; i < strArray.length; i++) {
            longArray[i] = Long.parseLong(strArray[i]);
        }
        return longArray;
    }
}
