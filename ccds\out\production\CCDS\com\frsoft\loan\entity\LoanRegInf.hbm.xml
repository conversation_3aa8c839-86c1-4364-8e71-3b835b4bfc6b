<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.LoanRegInf" table="loan_reg_inf" schema="dbo" >
        <id name="lregId" type="java.lang.Long">
            <column name="lreg_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="lregLoanSeason" class="com.frsoft.loan.entity.LoanSeason" fetch="select">
            <column name="lreg_lse_id" />
        </many-to-one>
        <property name="lregCompany" type="java.lang.String">
            <column name="lreg_company" length="100" />
        </property>
        <property name="lregContent" type="java.lang.String">
            <column name="lreg_content" length="1073741823" />
        </property>
        <property name="lregInsUser" type="java.lang.String">
            <column name="lreg_ins_user" length="25" />
        </property>
        <property name="lregInsTime" type="java.util.Date">
            <column name="lreg_ins_time" length="23" />
        </property>
        <property name="lregAltUser" type="java.lang.String">
            <column name="lreg_alt_user" length="25" />
        </property>
        <property name="lregAltTime" type="java.util.Date">
            <column name="lreg_alt_time" length="23" />
        </property>
        <property name="lregState" type="java.lang.String">
            <column name="lreg_state" length="50" />
        </property>
        <property name="lregLastYear" type="java.lang.String">
            <column name="lreg_last_year" length="50" />
        </property>
        <property name="lregBossName" type="java.lang.String">
            <column name="lreg_boss_name" length="500" />
        </property>
        <property name="lregOrdCode" type="java.lang.String">
            <column name="lreg_ord_code" length="50" />
        </property>
        <property name="lregLawMan" type="java.lang.String">
            <column name="lreg_law_man" length="50" />
        </property>
    </class>
</hibernate-mapping>
