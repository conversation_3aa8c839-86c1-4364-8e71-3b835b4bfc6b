<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.Schedule" table="schedule" schema="dbo" >
        <id name="schId" type="java.lang.Long">
            <column name="sch_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="schType" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="sch_type" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="sch_se_no" />
        </many-to-one>
        <property name="schTitle" type="java.lang.String">
            <column name="sch_title" length="100" />
        </property>
        <property name="schStartDate" type="java.util.Date">
            <column name="sch_start_date" length="23" />
        </property>
         <property name="schState" type="java.lang.String">
            <column name="sch_state" length="50" />
        </property>
        <property name="schInsUser" type="java.lang.String">
            <column name="sch_ins_user" length="50" />
        </property>
        <property name="schUpdUser" type="java.lang.String">
            <column name="sch_upd_user" length="50" />
        </property>
        <property name="schUpdDate" type="java.util.Date">
            <column name="sch_upd_date" length="23" />
        </property>
         <property name="schStartTime" type="java.lang.String">
            <column name="sch_start_time" length="50" />
        </property>
         <property name="schEndTime" type="java.lang.String">
            <column name="sch_end_time" length="50" />
        </property>
        <property name="schDate" type="java.util.Date">
            <column name="sch_date" length="23" />
        </property>
    </class>
</hibernate-mapping>
