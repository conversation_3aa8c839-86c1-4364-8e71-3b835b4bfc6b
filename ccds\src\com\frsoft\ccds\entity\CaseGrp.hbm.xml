<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.CaseGrp" table="case_grp" schema="dbo" >
        <id name="cgId" type="java.lang.Long">
            <column name="cg_id" />
            <generator class="identity" />
        </id>
        <property name="cgName" type="java.lang.String">
            <column name="cg_name" length="30" />
        </property>
        <property name="cgSeNo" type="java.lang.Long">
            <column name="cg_se_no" />
        </property>
    </class>
</hibernate-mapping>
