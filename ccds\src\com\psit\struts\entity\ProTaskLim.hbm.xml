<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.ProTaskLim" table="pro_task_lim" schema="dbo" >
        <id name="ptlId" type="java.lang.Long">
            <column name="ptl_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="proTask" class="com.psit.struts.entity.ProTask" fetch="select" cascade="all" not-null="false">
            <column name="ptl_prta_id" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="ptl_se_no"/>
        </many-to-one>
        <property name="ptlName" type="java.lang.String">
            <column name="ptl_name" length="50" />
        </property>
        <property name="ptlIsfin" type="java.lang.String">
            <column name="ptl_isfin" length="1" />
        </property>
        <property name="ptlFinDate" type="java.util.Date">
            <column name="ptl_fin_date" length="23" />
        </property>
        <property name="ptlIsdel" type="java.lang.String">
            <column name="ptl_isdel" length="1" />
        </property>
         <property name="ptlDesc" type="java.lang.String">
            <column name="ptl_desc" length="1073741823" />
        </property>
        <set name="attachments" inverse="true"  cascade="all" where="att_type='prta'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
    </class>
</hibernate-mapping>
