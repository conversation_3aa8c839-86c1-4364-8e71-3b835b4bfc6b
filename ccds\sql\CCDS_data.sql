use ccds;
GO


insert into sys_pref(syp_name,syp_is_def,syp_is_app,syp_pwd_len,syp_pwd_rule,syp_pwd_upd_days,syp_login_fail,syp_offline_days,syp_has_captcha) 
values(N'系统默认',1,1,6,'0',0,5,0,1);

INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case001', N'导入案件')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case002', N'删除批次')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case003', N'编辑批次 ')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case004', N'编辑案件')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case005', N'退案')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case006', N'分配案件')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case007', N'暂停案件')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case008', N'关闭案件')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case009', N'恢复案件')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case010', N'批量评语')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case011', N'导出案件')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'case012', N'导出催收记录')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'casehp001', N'确认核查')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'casehp002', N'确认登帐 ')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'casehp003', N'修改CP金额')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'casehp004', N'新建登帐')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'fun001', N'添加权限')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'fun002', N'删除权限')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'fun003', N'修改权限')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'fun004', N'查看详情权限')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'fun005', N'访问权限')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'fun006', N'查看全部')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'fun007', N'管理权限')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'fun008', N'审核权限')
insert into lim_operate values('fun009','导入权限');
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr001', N'添加评语')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr002', N'添加警告')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr003', N'修改催收小结')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr004', N'修改案件地区')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr031',N'添加电话')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr032',N'添加地址')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr017', N'查看部门案件')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr022', N'添加电催记录')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'visR001', N'外访审核、撤销、排程')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'visR003', N'修改排程时间')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'visR005', N'完成外访')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr023',N'修改电话')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr024',N'删除电话')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr025',N'修改地址')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr026',N'删除地址')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr027',N'访问操作记录')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr028',N'修改操作记录')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr029',N'删除操作记录')
INSERT [dbo].[lim_operate] ([ope_code], [ope_desc]) VALUES (N'hurr030',N'修改预计退案日')
insert into lim_operate values('hurr033','添加辅助催记');
insert into lim_operate values('hurr034','添加协催记录'); 
insert into lim_operate values('hurr035','添加案人数据');
insert into lim_operate values('fun010','添加修改权限');
insert into lim_operate values('law001','访问案件办理记录');
insert into lim_operate values('law002','添加修改办理记录');
insert into lim_operate values('law003','删除办理记录');
insert into lim_operate values('law004','访问案件收费记录');
insert into lim_operate values('law005','添加修改收费记录');
insert into lim_operate values('law006','删除收费记录');
insert into lim_operate values('law007','访问关联催收案件');
insert into lim_operate values('hurr036','删除案件附件');
insert into lim_operate values('hurr037',N'访问共债案件');
insert into lim_operate values('hurr038',N'批量标色');
insert into lim_operate values ('sys001','锁定账号');
insert into lim_operate values ('sys002','查看账号日志');
insert into lim_operate values ('sys003','删除账号日志');
insert into lim_operate values ('hurr016','作废CP');
insert into lim_operate values ('file001','上传附件');
insert into lim_operate values ('file002','查看附件');
insert into lim_operate values('hurr039','发送短信');
insert into lim_operate values('sys004','设置短信额度');




SET IDENTITY_INSERT [dbo].[cus_province] ON
INSERT [dbo].[cus_province] ([prv_id], [prv_area_id], [prv_name], [prv_isenabled]) VALUES (1, 1, N'请选择', N'1')
SET IDENTITY_INSERT [dbo].[cus_province] OFF
--SET IDENTITY_INSERT [dbo].[type_list] ON
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'日常工作', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'会议', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'出差', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'培训', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'来访接待', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'外出活动', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'商务餐饮', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'电话', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'上门', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'其他', NULL, N'21', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'电话', NULL, N'22', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'上门', NULL, N'22', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'来访接待', NULL, N'22', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'会议', NULL, N'22', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'培训', NULL, N'22', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'商务餐饮', NULL, N'22', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'外出活动', NULL, N'22', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'其他', NULL, N'22', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'工作日报', NULL, N'23', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'工作周报', NULL, N'23', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'工作月报', NULL, N'23', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'工作年报', NULL, N'23', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'工作报告', NULL, N'23', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'其他报告', NULL, N'23', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'暂停催收', N'lock_stop_cl', N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'要求退案',  N'lock_back_case', N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'PTP', NULL, N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'每日必须跟进', NULL, N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'重点跟进', NULL, N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'跳票', NULL, N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'部分还款', NULL, N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'查账', NULL, N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'有可靠线索', NULL, N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'所有信息均无效', NULL, N'caseState', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'无效电话', N'lock_invalid', N'casePhone', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'可联本人', NULL, N'casePhone', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'可联第三人', NULL, N'casePhone', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'可联家人', NULL, N'casePhone', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'可联村委', NULL, N'casePhone', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'网搜无效', NULL, N'casePhone', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'114查询无效', NULL, N'casePhone', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'长帐龄', N'houseLoan', N'caseBat', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'短账龄', NULL, N'caseBat', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'一手单', NULL, N'excLimType', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'中国银行', NULL, N'caseBank', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'工商银行', NULL, N'caseBank', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'建设银行', NULL, N'caseBank', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'农业银行', NULL, N'caseBank', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'交通银行', NULL, N'caseBank', N'1')
INSERT [dbo].[type_list] ([typ_name], [typ_desc], [typ_type], [typ_isenabled]) VALUES (N'招商银行', NULL, N'caseBank', N'1')
--SET IDENTITY_INSERT [dbo].[type_list] OFF
SET IDENTITY_INSERT [dbo].[cus_area] ON
INSERT [dbo].[cus_area] ([are_id], [are_name], [are_isenabled]) VALUES (1, N'请选择', N'1')
INSERT [dbo].[cus_area] ([are_id], [are_name], [are_isenabled]) VALUES (3, N'江苏', N'1')
INSERT [dbo].[cus_area] ([are_id], [are_name], [are_isenabled]) VALUES (4, N'安徽', N'1')
INSERT [dbo].[cus_area] ([are_id], [are_name], [are_isenabled]) VALUES (5, N'浙江', N'1')
INSERT [dbo].[cus_area] ([are_id], [are_name], [are_isenabled]) VALUES (6, N'上海', N'1')
INSERT [dbo].[cus_area] ([are_id], [are_name], [are_isenabled]) VALUES (7, N'北京', N'1')
SET IDENTITY_INSERT [dbo].[cus_area] OFF

INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'a025', N'cus001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'a026', N'cus002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'a028', N'cus004', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ac020', N'cop001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ac021', N'cop002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ac022', N'cop003', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ac023', N'cop004', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ac024', N'cop005', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'af019', N'fin001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'af020', N'fin002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'af021', N'fin003', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'af022', N'fin004', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'af023', N'fin005', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'af024', N'fin006', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ah005', N'hr001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'apu029', N'pur001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'apu030', N'pur002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'apu032', N'pur003', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'apu033', N'pur004', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'apu034', N'pur005', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'apu035', N'pur006', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'apu036', N'pur007', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'asy001', N'sys001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'asy006', N'sys002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'asy011', N'sys003', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'asy014', N'sys004', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'b025', N'sal001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'b028', N'sta000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'b037', N'sal004', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c001', N'cop001', N'fun007', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c004', N'cop001', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c005', N'cop002', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c006', N'cop002', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c007', N'cop002', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c008', N'cop002', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c009', N'cop003', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c010', N'cop003', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c011', N'cop003', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c012', N'cop003', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c013', N'cop004', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c014', N'cop004', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c015', N'cop004', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c016', N'cop004', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c017', N'cop005', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c018', N'cop005', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c019', N'cop005', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'c025', N'cop000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca001', N'c001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca002', N'c001', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca003', N'c001', N'case001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca004', N'c001', N'case002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca005', N'c001', N'case003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca006', N'c002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca007', N'c002', N'case004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca008', N'c002', N'case005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca009', N'c002', N'case006', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca010', N'c002', N'case007', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca011', N'c002', N'case008', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca012', N'c002', N'case009', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca013', N'c002', N'case010', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca016', N'c000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca017', N'c002', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca018', N'c005', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca020', N'c004', N'case011', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'ca021', N'c004', N'case012', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'cp001', N'casehelp001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'cp002', N'casehelp001', N'casehp001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'cp003', N'casehelp002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'cp004', N'casehelp002', N'casehp002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'cp005', N'casehelp002', N'casehp003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'cp006', N'casehelp000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'cp007', N'casehelp002', N'casehp004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'd013', N'pro001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'd014', N'pro003', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'd015', N'pro004', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'd024', N'pro006', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f001', N'fin002', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f002', N'fin002', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f003', N'fin002', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f004', N'fin002', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f007', N'fin003', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f010', N'fin004', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f012', N'fin005', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f013', N'fin005', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f015', N'fin006', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f016', N'fin006', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f017', N'fin006', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'f018', N'fin006', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'h001', N'hr001', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'h002', N'hr001', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'h003', N'hr001', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'h004', N'hr001', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'h008', N'hr000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu001', N'hurry001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu002', N'c005', N'hurr001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu003', N'c005', N'hurr002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu004', N'c005', N'hurr003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu005', N'c005', N'hurr004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu008', N'c005', N'hurr030', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu017', N'c003', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu018', N'c005', N'hurr027', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu026', N'hurry002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu027', N'hurry003', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu028', N'c005', N'hurr022', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu029', N'hurry000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu030', N'c005', N'hurr023', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu031', N'c005', N'hurr024', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu032', N'c005', N'hurr025', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu033', N'c005', N'hurr026', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu034', N'c005', N'hurr028', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu035', N'c005', N'hurr029', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu036', N'c003', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu037', N'c003', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'hu040', N'hurry001', N'hurr017', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p001', N'pro001', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p002', N'pro001', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p003', N'pro001', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p004', N'pro001', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p005', N'pro001', N'fun006', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p006', N'pro002', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p007', N'pro002', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p008', N'pro002', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p009', N'pro003', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p010', N'pro003', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p011', N'pro003', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p012', N'pro003', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p016', N'pro005', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p017', N'pro003', N'fun006', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p020', N'pro006', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p021', N'pro006', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p022', N'pro006', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p023', N'pro006', N'fun006', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'p025', N'pro006', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu001', N'pur002', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu002', N'pur002', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu003', N'pur002', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu004', N'pur002', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu010', N'pur003', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu011', N'pur003', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu012', N'pur003', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu013', N'pur004', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu014', N'pur004', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu015', N'pur004', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu016', N'pur005', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu017', N'pur005', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu018', N'pur005', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu019', N'pur005', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu020', N'pur006', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu021', N'pur006', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu022', N'pur006', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu023', N'pur006', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu024', N'pur007', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu025', N'pur007', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu026', N'pur007', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'pu027', N'pur007', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r001', N'cus001', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r002', N'cus001', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r003', N'cus001', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r004', N'cus001', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r005', N'cus002', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r006', N'cus002', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r007', N'cus002', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r008', N'cus002', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r013', N'cus004', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r014', N'cus004', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r015', N'cus004', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r016', N'cus004', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r030', N'cus000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'r032', N'cus001', N'fun006', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's001', N'sal001', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's002', N'sal001', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's003', N'sal001', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's004', N'sal001', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's005', N'sal001', N'fun006', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's010', N'sal004', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's011', N'sal004', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's012', N'sal004', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's029', N'sal000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's030', N'sal001', N'fun008', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N's039', N'sal001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy002', N'sys002', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy003', N'sys002', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy004', N'sys002', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy007', N'sys003', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy008', N'sys003', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy009', N'sys003', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy010', N'sys003', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy012', N'sys004', N'fun001', NULL)
GO
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy013', N'sys004', N'fun003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'sy017', N'sys000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'v001', N'serv001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'v002', N'serv001', N'fun001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'v003', N'serv001', N'fun002', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'v005', N'serv001', N'fun004', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'v006', N'serv002', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'vi001', N'vis001', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'vi002', N'vis001', N'visR001', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'vi006', N'vis001', N'visR003', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'vi007', N'vis001', N'visR005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'vi009', N'vis000', N'fun005', NULL)
INSERT [dbo].[lim_right] ([rig_code], [rig_fun_code], [rig_ope_code], [rig_wms_name]) VALUES (N'vi010', N'vis001', N'fun006', NULL)
insert into lim_right values('loan001','loan001','fun005',NULL);
insert into lim_right values('hu0291','c005','hurr031',NULL);
insert into lim_right values('hu0292','c005','hurr032',NULL);
insert into lim_right values ('asy016','sys006','fun005',NULL);
insert into lim_right values('cp008','casehelp004','fun005',null);
insert into lim_right values('cp009','casehelp004','fun009',null);
insert into lim_right values('cp010','casehelp004','fun002',null);
insert into lim_right values('hu041','c005','hurr033',null);
insert into lim_right values('hu042','c005','hurr034',NULL);
insert into lim_right values('hu043','c005','hurr035',null);
insert into lim_right values('law001','law001','fun005',NULL);
insert into lim_right values('law002','law002','fun005',null);
insert into lim_right values('law003','law002','fun010',null);
insert into lim_right values('law004','law002','fun002',null);
insert into lim_right values('law005','law002','fun004',null);
insert into lim_right values('law006','law002','law001',null);
insert into lim_right values('law007','law002','law002',null);
insert into lim_right values('law008','law002','law003',null);
insert into lim_right values('law009','law002','law004',null);
insert into lim_right values('law010','law002','law005',null);
insert into lim_right values('law011','law002','law006',null);
insert into lim_right values('law012','law002','law007',null);
insert into lim_right values('law100','law003','fun005',null);
insert into lim_right values('law101','law003','fun006',null);
insert into lim_right values('law200','law004','fun005',null);
insert into lim_right values('hu044','c005','hurr036',null);
insert into lim_right values('hu024','c005','hurr037',null);
insert into lim_right values('ca022','c002','hurr038',null);
insert into lim_right values('asy017','sys007','fun005',null);
insert into lim_right values ('sy018','sys001','sys001',null);
insert into lim_right values ('sy019','sys001','sys002',null);
insert into lim_right values ('sy020','sys001','sys003',null);
insert into lim_right values ('hu038','c005','hurr016',null);
insert into lim_right values ('mv001','m001','fun005',null);
insert into lim_right values ('mc001','m002','fun005',null);
insert into lim_right values('hu045','c005','hurr039',NULL);
insert into lim_right values('sy021','sys001','sys004',NULL);


SET IDENTITY_INSERT [dbo].[cus_city] ON
INSERT [dbo].[cus_city] ([city_id], [city_prv_id], [city_name], [city_isenabled]) VALUES (1, 1, N'请选择', N'1')
SET IDENTITY_INSERT [dbo].[cus_city] OFF

INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'c000', N'访问权限', N'case')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'c001', N'批次管理', N'case')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'c002', N'案件管理', N'case')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'c003', N'催记管理', N'case')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'c004', N'导出', N'case')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'c005', N'案件详情', N'case')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'casehelp000', N'访问权限', N'cp')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'casehelp001', N'案件协助', N'cp')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'casehelp002', N'银行登帐', N'cp')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cop000', N'访问权限', N'oa')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cop001', N'新闻公告', N'oa')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cop002', N'日程', N'oa')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cop003', N'工作任务', N'oa')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cop004', N'报告', N'oa')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cop005', N'邮件', N'oa')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cus000', N'访问权限', N'cus')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cus001', N'客户资料', N'cus')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cus002', N'客户联系人', N'cus')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cus004', N'联系记录', N'cus')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'cus005', N'客户关怀', N'cus')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'fin001', N'财务管理', N'acc')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'fin002', N'账户管理', N'acc')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'fin003', N'入账记录', N'acc')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'fin004', N'出账记录', N'acc')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'fin005', N'内部转账', N'acc')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'fin006', N'票据管理', N'acc')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'hr000', N'访问权限', N'hr')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'hr001', N'员工档案', N'hr')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'hurry000', N'访问权限', N'hurry')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'hurry001', N'我的案件', N'hurry')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'hurry002', N'来电查询', N'hurry')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'hurry003', N'主管协催', N'hurry')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pro001', N'项目', N'proj')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pro002', N'子项目', N'proj')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pro003', N'项目任务', N'proj')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pro004', N'项目统计', N'proj')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pro005', N'项目管理', N'proj')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pro006', N'项目日志', N'proj')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pro007', N'查看特定项目', N'proj')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pur001', N'采购管理', N'pur')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pur002', N'采购单', N'pur')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pur003', N'付款计划', N'pur')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pur004', N'付款记录', N'pur')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pur005', N'供应商资料', N'pur')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pur006', N'供应商联系人', N'pur')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'pur007', N'询价管理', N'pur')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sal000', N'访问权限', N'sal')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sal001', N'合同', N'sal')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sal004', N'回款记录', N'sal')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sta000', N'访问权限', N'sta')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sys000', N'访问权限', N'sys')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sys001', N'帐号设置', N'sys')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sys002', N'职位设置', N'sys')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sys003', N'部门设置', N'sys')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'sys004', N'类别管理', N'sys')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'vis000', N'访问权限', N'vis')
INSERT [dbo].[lim_function] ([fun_code], [fun_desc], [fun_type]) VALUES (N'vis001', N'外访管理', N'vis')
insert into lim_function values('loan001','访问权限','loan');
insert into lim_function values ('sys006',N'安全设置','sys');
insert into lim_function values('casehelp004','案人数据库','cp');
insert into lim_function values('law001','访问权限','law');
insert into lim_function values('law002','我的诉讼案件','law');
insert into lim_function values('law003','部门诉讼案件','law');
insert into lim_function values('law004','收费记录','law');
insert into lim_function values('sys007','权限组设置','sys');
insert into lim_function values ('m001','我的外访','mob');
insert into lim_function values ('m002','案件查询','mob');

INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'acc_trans', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'cus_cor_cus', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'sal_org', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'sal_paid_past', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'sal_supplier', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'spo_paid_past', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'wms_change', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'wms_check', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'wms_stro', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'wms_war_in', 0)
INSERT [dbo].[lock_table] ([table_name], [table_max]) VALUES (N'wms_war_out', 0)