package com.frsoft.ccds.common.utils;

import com.frsoft.ccds.common.exception.BusinessException;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据验证工具类测试
 * 
 * <AUTHOR>
 */
public class ValidationUtilsTest {

    @Test
    public void testIsValidMobile() {
        // 有效手机号
        assertTrue(ValidationUtils.isValidMobile("***********"));
        assertTrue(ValidationUtils.isValidMobile("***********"));
        assertTrue(ValidationUtils.isValidMobile("***********"));
        
        // 无效手机号
        assertFalse(ValidationUtils.isValidMobile("***********")); // 不是1开头的有效号段
        assertFalse(ValidationUtils.isValidMobile("1380013800")); // 长度不够
        assertFalse(ValidationUtils.isValidMobile("***********1")); // 长度过长
        assertFalse(ValidationUtils.isValidMobile("1380013800a")); // 包含字母
        assertFalse(ValidationUtils.isValidMobile("")); // 空字符串
        assertFalse(ValidationUtils.isValidMobile(null)); // null
    }

    @Test
    public void testIsValidIdCard() {
        // 有效身份证号
        assertTrue(ValidationUtils.isValidIdCard("320101199001011234"));
        assertTrue(ValidationUtils.isValidIdCard("32010119900101123X"));
        
        // 无效身份证号
        assertFalse(ValidationUtils.isValidIdCard("32010119900101123")); // 长度不够
        assertFalse(ValidationUtils.isValidIdCard("320101199013011234")); // 月份无效
        assertFalse(ValidationUtils.isValidIdCard("320101199001321234")); // 日期无效
        assertFalse(ValidationUtils.isValidIdCard("320101199001011234a")); // 包含无效字符
        assertFalse(ValidationUtils.isValidIdCard("")); // 空字符串
        assertFalse(ValidationUtils.isValidIdCard(null)); // null
    }

    @Test
    public void testIsValidEmail() {
        // 有效邮箱
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"));
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"));
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"));
        
        // 无效邮箱
        assertFalse(ValidationUtils.isValidEmail("test@")); // 缺少域名
        assertFalse(ValidationUtils.isValidEmail("@example.com")); // 缺少用户名
        assertFalse(ValidationUtils.isValidEmail("test.example.com")); // 缺少@
        assertFalse(ValidationUtils.isValidEmail("test@example")); // 缺少顶级域名
        assertFalse(ValidationUtils.isValidEmail("")); // 空字符串
        assertFalse(ValidationUtils.isValidEmail(null)); // null
    }

    @Test
    public void testIsValidBankCard() {
        // 有效银行卡号
        assertTrue(ValidationUtils.isValidBankCard("***************0123"));
        assertTrue(ValidationUtils.isValidBankCard("****************"));
        
        // 无效银行卡号
        assertFalse(ValidationUtils.isValidBankCard("0222021234567890123")); // 0开头
        assertFalse(ValidationUtils.isValidBankCard("***************")); // 长度不够
        assertFalse(ValidationUtils.isValidBankCard("622202****************78901")); // 长度过长
        assertFalse(ValidationUtils.isValidBankCard("***************012a")); // 包含字母
        assertFalse(ValidationUtils.isValidBankCard("")); // 空字符串
        assertFalse(ValidationUtils.isValidBankCard(null)); // null
    }

    @Test
    public void testValidateRequired() {
        // 正常情况
        ValidationUtils.validateRequired("test", "测试字段");
        ValidationUtils.validateRequired(123, "数字字段");
        
        // 异常情况
        try {
            ValidationUtils.validateRequired(null, "空值字段");
            fail("应该抛出BusinessException");
        } catch (BusinessException e) {
            assertEquals("空值字段不能为空", e.getMessage());
        }
        
        try {
            ValidationUtils.validateRequired("", "空字符串字段");
            fail("应该抛出BusinessException");
        } catch (BusinessException e) {
            assertEquals("空字符串字段不能为空", e.getMessage());
        }
    }

    @Test
    public void testValidateLength() {
        // 正常情况
        ValidationUtils.validateLength("test", "测试字段", 1, 10);
        ValidationUtils.validateLength("", "空字段", 0, 10); // 空字符串跳过验证
        ValidationUtils.validateLength(null, "null字段", 1, 10); // null跳过验证
        
        // 异常情况
        try {
            ValidationUtils.validateLength("a", "短字段", 2, 10);
            fail("应该抛出BusinessException");
        } catch (BusinessException e) {
            assertTrue(e.getMessage().contains("长度必须在2-10个字符之间"));
        }
        
        try {
            ValidationUtils.validateLength("this is a very long string", "长字段", 1, 10);
            fail("应该抛出BusinessException");
        } catch (BusinessException e) {
            assertTrue(e.getMessage().contains("长度必须在1-10个字符之间"));
        }
    }

    @Test
    public void testValidateRange() {
        // 正常情况
        ValidationUtils.validateRange(50, "数值字段", 1, 100);
        ValidationUtils.validateRange(50.5, "小数字段", 1.0, 100.0);
        ValidationUtils.validateRange(null, "null字段", 1, 100); // null跳过验证
        
        // 异常情况
        try {
            ValidationUtils.validateRange(0, "小数值字段", 1, 100);
            fail("应该抛出BusinessException");
        } catch (BusinessException e) {
            assertTrue(e.getMessage().contains("必须在1-100之间"));
        }
        
        try {
            ValidationUtils.validateRange(101, "大数值字段", 1, 100);
            fail("应该抛出BusinessException");
        } catch (BusinessException e) {
            assertTrue(e.getMessage().contains("必须在1-100之间"));
        }
    }

    @Test
    public void testValidateMobile() {
        // 正常情况
        ValidationUtils.validateMobile("***********", "手机号");
        ValidationUtils.validateMobile("", "空手机号"); // 空字符串跳过验证
        ValidationUtils.validateMobile(null, "null手机号"); // null跳过验证
        
        // 异常情况
        try {
            ValidationUtils.validateMobile("***********", "无效手机号");
            fail("应该抛出BusinessException");
        } catch (BusinessException e) {
            assertEquals("无效手机号格式不正确", e.getMessage());
        }
    }

    @Test
    public void testValidateEmail() {
        // 正常情况
        ValidationUtils.validateEmail("<EMAIL>", "邮箱");
        ValidationUtils.validateEmail("", "空邮箱"); // 空字符串跳过验证
        ValidationUtils.validateEmail(null, "null邮箱"); // null跳过验证
        
        // 异常情况
        try {
            ValidationUtils.validateEmail("invalid-email", "无效邮箱");
            fail("应该抛出BusinessException");
        } catch (BusinessException e) {
            assertEquals("无效邮箱格式不正确", e.getMessage());
        }
    }
}
