package com.frsoft.ccds.system.mapper;

import com.frsoft.ccds.system.domain.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户表 数据层
 */
@Mapper
public interface SysUserMapper {
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合
     */
    List<SysUser> selectUserList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param loginName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByLoginName(String loginName);

    /**
     * 通过用户编码查询用户
     *
     * @param userCode 用户编码
     * @return 用户对象信息
     */
    SysUser selectUserByUserCode(String userCode);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 修改用户密码
     *
     * @param userCode 用户编码
     * @param password 密码
     * @return 结果
     */
    int updateUserPassword(@Param("userCode") String userCode, @Param("password") String password);

    /**
     * 修改用户状态
     *
     * @param userCode 用户编码
     * @param enabled 帐号状态
     * @return 结果
     */
    int updateUserStatus(@Param("userCode") String userCode, @Param("enabled") String enabled);



    /**
     * 通过用户编码删除用户
     *
     * @param userCode 用户编码
     * @return 结果
     */
    int deleteUserByUserCode(String userCode);

    /**
     * 批量删除用户信息
     *
     * @param userCodes 需要删除的用户编码
     * @return 结果
     */
    int deleteUserByUserCodes(String[] userCodes);
}