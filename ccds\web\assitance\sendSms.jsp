<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>发送短信(更新关联协催)</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/sms-connect${CUS_VER_ID}.js"></script>

	<script type="text/javascript" >
	/* function getMsgByTmp(){
        if($("smsTmp").value!=""){
            var url="assitanceAction.do";
            var pars=[];
            pars.op="getMsgByTmp";
            pars.casId="${casId}";
            pars.tmpId=$("smsTmp").value;
            new Ajax.Request(url,{
                method      :   'post',
                parameters  :   pars,
                onSuccess   :   function(response){
                    var tmpData = response.responseText;
                    if(tmpData!=""){
                        $('msgContent').value=tmpData;
                    }
                },
                onfailure   :   function(response){
                    if (transport.status == 404)
                    
                        alert("您访问的url地址不存在！");
                    else
                        alert("Error: status code is " + transport.status);
                }
            });
        }
    }   */
	</script>
  </head>
  
  <body>
  <div class="inputDiv">
  	<form id="sendForm" method="post" style="padding:0px; margin:0px">
  		<input type="hidden" id="op" name="op" />
        <input type="hidden" name="chId" value="<c:out value="${caseHp.chId}"/>"/>
  		<input type="hidden" name="phone" value="<c:out value="${caseHp.chAdr}"/>"/>
  		<input type="hidden" id="sn" name="sn" />
  		<input type="hidden" id="pwd" name="pwd" />
        <input type="hidden" id="sign" name="sign" />
        
        <table class="dashTab inputForm" cellpadding="0" cellspacing="0">
        	<tr>
            	<th>接收号码：</th>
                <td colspan="3" class="longTd"><div class="scrollBarNoStyle" style="height:30px; width:440px; margin:0;padding:0;">${caseHp.chAdr}</div></td>
           	</tr>
           	<%-- <tr>
           	    <th>短信模板：</th>
                <td colspan="3"><c:if test="${!empty templateList}">
                        <select id="smsTmp" onchange="getMsgByTmp()" class="inputSize2">
                            <option value="">请选择</option>
                            <c:forEach items="${templateList}" var="t">
                            <option value="${t.tmpId}">${t.tmpName}</option>
                            </c:forEach>
                        </select>
                        </c:if>
                        <c:if test="${empty templateList}">
                            <select id="smsTmp" class="inputSize2 inputBoxAlign" disabled="disabled">
                                <option>未添加模板</option>
                            </select>
                        </c:if>
                 </td>
           	</tr> --%>
            <tr class="noBorderBot">
            	<th>短信内容：</th>
                <td colspan="3"><textarea class="inputSize2L" rows="5" id="msgContent" name="msgContent">${caseHp.chText}</textarea></td>
           	</tr>		
           	<tr class="submitTr">
            	<td colspan="4">
                	<input id="save" class="butSize1" type="button" value="发送" onClick="execSend()" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                </td>
            </tr>
        </table>
        <div id="rsLayer" style="display:none" class="lightGrayBack" style="padding:2px; width:98%"></div>
    </div>
  </body>
</html>
