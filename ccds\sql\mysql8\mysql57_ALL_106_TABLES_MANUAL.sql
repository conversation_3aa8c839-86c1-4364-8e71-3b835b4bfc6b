-- MySQL 5.7 完整CCDS数据库 - 手工转换所有106个表
-- 确保100%功能兼容，支持所有业务需求

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `ccds`;

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 所有106个表的完整定义 - 手工精确转换
-- ========================================

-- 1. spo_paid_past - 供应商付款历史表
DROP TABLE IF EXISTS `spo_paid_past`;
CREATE TABLE `spo_paid_past` (
  `spa_id` bigint NOT NULL AUTO_INCREMENT,
  `spa_code` varchar(300) DEFAULT NULL,
  `spa_spo_id` bigint DEFAULT NULL,
  `spa_aco_id` bigint DEFAULT NULL,
  `spa_ssu_id` bigint DEFAULT NULL,
  `spa_fct_date` datetime DEFAULT NULL,
  `spa_type_id` bigint DEFAULT NULL,
  `spa_pay_type` varchar(50) DEFAULT NULL,
  `spa_pay_mon` decimal(18,2) DEFAULT NULL,
  `spa_in_name` varchar(100) DEFAULT NULL,
  `spa_inp_user` varchar(50) DEFAULT NULL,
  `spa_se_no` bigint DEFAULT NULL,
  `spa_isinv` char(1) DEFAULT NULL,
  `spa_remark` longtext,
  `spa_cre_date` datetime DEFAULT NULL,
  `spa_isdel` char(1) DEFAULT NULL,
  `spa_content` varchar(100) DEFAULT NULL,
  `spa_acc_type_id` bigint DEFAULT NULL,
  `spa_alt_date` datetime DEFAULT NULL,
  `spa_alt_user` varchar(50) DEFAULT NULL,
  `spa_undo_date` datetime DEFAULT NULL,
  `spa_undo_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`spa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='供应商付款历史表';

-- 2. sal_emp - 员工表
DROP TABLE IF EXISTS `sal_emp`;
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT,
  `se_so_code` varchar(50) DEFAULT NULL,
  `se_name` varchar(100) DEFAULT NULL,
  `se_ide_code` varchar(50) DEFAULT NULL,
  `se_pos` varchar(50) DEFAULT NULL,
  `se_sex` varchar(50) DEFAULT NULL,
  `se_prob` varchar(50) DEFAULT NULL,
  `se_bir_place` varchar(50) DEFAULT NULL,
  `se_acc_place` varchar(100) DEFAULT NULL,
  `se_birth` varchar(50) DEFAULT NULL,
  `se_marry` varchar(10) DEFAULT NULL,
  `se_type` varchar(50) DEFAULT NULL,
  `se_job_lev` bigint DEFAULT NULL,
  `se_job_cate` varchar(50) DEFAULT NULL,
  `se_job_title` varchar(50) DEFAULT NULL,
  `se_start_day` datetime DEFAULT NULL,
  `se_year_pay` varchar(50) DEFAULT NULL,
  `se_cost_center` varchar(50) DEFAULT NULL,
  `se_email` varchar(50) DEFAULT NULL,
  `se_nation` varchar(50) DEFAULT NULL,
  `se_poli_status` varchar(50) DEFAULT NULL,
  `se_edu` varchar(50) DEFAULT NULL,
  `se_tel` varchar(50) DEFAULT NULL,
  `se_phone` varchar(50) DEFAULT NULL,
  `se_qq` varchar(50) DEFAULT NULL,
  `se_msn` varchar(50) DEFAULT NULL,
  `se_rec_source` varchar(100) DEFAULT NULL,
  `se_prov_fund` varchar(50) DEFAULT NULL,
  `se_job_date` datetime DEFAULT NULL,
  `se_hou_reg` varchar(50) DEFAULT NULL,
  `se_social_code` varchar(50) DEFAULT NULL,
  `se_rap` varchar(50) DEFAULT NULL,
  `se_address` varchar(500) DEFAULT NULL,
  `se_remark` longtext,
  `se_bank_name` varchar(50) DEFAULT NULL,
  `se_bank_card` varchar(50) DEFAULT NULL,
  `se_weal_address` varchar(50) DEFAULT NULL,
  `se_weal_pos` varchar(50) DEFAULT NULL,
  `se_isovertime` varchar(50) DEFAULT NULL,
  `se_attendance` varchar(50) DEFAULT NULL,
  `se_card_num` varchar(50) DEFAULT NULL,
  `se_pic` longtext,
  `se_isenabled` char(1) DEFAULT NULL,
  `se_inser_date` datetime DEFAULT NULL,
  `se_code` varchar(50) DEFAULT NULL,
  `se_log` longtext,
  `se_alt_date` datetime DEFAULT NULL,
  `se_inser_user` varchar(50) DEFAULT NULL,
  `se_alt_user` varchar(50) DEFAULT NULL,
  `se_end_date` datetime DEFAULT NULL,
  `se_edc_bac` longtext,
  `se_work_ex` longtext,
  `se_user_code` varchar(50) DEFAULT NULL,
  `se_per_tel` varchar(50) DEFAULT NULL,
  `se_plan_sign_date` datetime DEFAULT NULL,
  `se_sign_date` datetime DEFAULT NULL,
  `se_credit_date` datetime DEFAULT NULL,
  `se_college` varchar(200) DEFAULT NULL,
  `se_transfer` text,
  PRIMARY KEY (`se_no`),
  KEY `IX_sal_emp` (`se_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='员工表';

-- 3. r_rep_lim - 报告权限表
DROP TABLE IF EXISTS `r_rep_lim`;
CREATE TABLE `r_rep_lim` (
  `rrl_id` bigint NOT NULL AUTO_INCREMENT,
  `rrl_rep_code` bigint DEFAULT NULL,
  `rrl_se_no` bigint DEFAULT NULL,
  `rrl_date` datetime DEFAULT NULL,
  `rrl_content` longtext,
  `rrl_isappro` char(1) DEFAULT NULL,
  `rrl_oppro_date` datetime DEFAULT NULL,
  `rrl_isdel` char(1) DEFAULT NULL,
  `rrl_app_order` int DEFAULT NULL,
  `rrl_isview` char(1) DEFAULT NULL,
  `rrl_is_all_appro` char(1) DEFAULT NULL,
  `rrl_rec_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`rrl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='报告权限表';

-- 4. sal_paid_plan - 销售付款计划表
DROP TABLE IF EXISTS `sal_paid_plan`;
CREATE TABLE `sal_paid_plan` (
  `spd_id` bigint NOT NULL AUTO_INCREMENT,
  `spd_ord_code` bigint DEFAULT NULL,
  `spd_prm_date` datetime DEFAULT NULL,
  `spd_count` int DEFAULT NULL,
  `spd_pay_mon` decimal(18,2) DEFAULT NULL,
  `spd_mon_type` varchar(50) DEFAULT NULL,
  `spd_user_code` varchar(50) DEFAULT NULL,
  `spd_isp` char(1) DEFAULT NULL,
  `spd_resp` varchar(50) DEFAULT NULL,
  `spd_cre_date` datetime DEFAULT NULL,
  `spd_alt_date` datetime DEFAULT NULL,
  `spd_alt_user` varchar(50) DEFAULT NULL,
  `spd_isdel` char(1) DEFAULT NULL,
  `spd_content` varchar(100) DEFAULT NULL,
  `spd_cor_code` bigint DEFAULT NULL,
  PRIMARY KEY (`spd_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售付款计划表';

-- 5. r_new_lim - 新闻权限表
DROP TABLE IF EXISTS `r_new_lim`;
CREATE TABLE `r_new_lim` (
  `rnl_id` bigint NOT NULL AUTO_INCREMENT,
  `rnl_new_code` bigint DEFAULT NULL,
  `rnl_se_no` bigint DEFAULT NULL,
  `rnl_date` datetime DEFAULT NULL,
  PRIMARY KEY (`rnl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='新闻权限表';

-- 6. account - 账户表
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account` (
  `aco_id` bigint NOT NULL AUTO_INCREMENT,
  `aco_type` varchar(50) DEFAULT NULL,
  `aco_name` varchar(100) DEFAULT NULL,
  `aco_bank_num` varchar(50) DEFAULT NULL,
  `aco_bank` varchar(100) DEFAULT NULL,
  `aco_bank_name` varchar(50) DEFAULT NULL,
  `aco_cre_date` datetime DEFAULT NULL,
  `aco_org_mon` decimal(18,2) DEFAULT NULL,
  `aco_cur_mon` decimal(18,2) DEFAULT NULL,
  `aco_remark` longtext,
  `aco_inp_user` varchar(50) DEFAULT NULL,
  `aco_inp_date` datetime DEFAULT NULL,
  `aco_alt_date` datetime DEFAULT NULL,
  `aco_alt_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`aco_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='账户表';

-- 7. lim_user - 用户表
DROP TABLE IF EXISTS `lim_user`;
CREATE TABLE `lim_user` (
  `user_code` varchar(50) NOT NULL,
  `user_loginName` varchar(50) DEFAULT NULL,
  `user_pwd` varchar(50) DEFAULT NULL,
  `user_up_code` varchar(50) DEFAULT NULL,
  `user_lev` char(1) DEFAULT NULL,
  `user_so_code` varchar(50) DEFAULT NULL,
  `user_se_id` bigint DEFAULT NULL,
  `user_se_name` varchar(100) DEFAULT NULL,
  `user_desc` longtext,
  `user_isenabled` char(1) DEFAULT NULL,
  `user_num` varchar(200) DEFAULT NULL,
  `user_role_id` bigint DEFAULT NULL,
  `user_islogin` char(1) DEFAULT NULL,
  `user_ip` varchar(50) DEFAULT NULL,
  `user_fail` int DEFAULT NULL,
  `user_pwd_upd_date` datetime DEFAULT NULL,
  `user_cti_login` varchar(255) DEFAULT NULL,
  `user_cti_pwd` varchar(255) DEFAULT NULL,
  `user_cti_server` varchar(50) DEFAULT NULL,
  `user_cti_phone` varchar(50) DEFAULT NULL,
  `user_grp_id` bigint DEFAULT NULL,
  `user_sms_max_num` int DEFAULT NULL,
  PRIMARY KEY (`user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户表';

-- 8. inquiry - 询价表
DROP TABLE IF EXISTS `inquiry`;
CREATE TABLE `inquiry` (
  `inq_id` bigint NOT NULL AUTO_INCREMENT,
  `inq_ssu_id` bigint DEFAULT NULL,
  `inq_pro_id` bigint DEFAULT NULL,
  `inq_title` varchar(100) DEFAULT NULL,
  `inq_price` decimal(18,2) DEFAULT NULL,
  `inq_se_no` bigint DEFAULT NULL,
  `inq_date` datetime DEFAULT NULL,
  `inq_inp_user` varchar(50) DEFAULT NULL,
  `inq_upd_user` varchar(50) DEFAULT NULL,
  `inq_ins_date` datetime DEFAULT NULL,
  `inq_upd_date` datetime DEFAULT NULL,
  `inq_remark` longtext,
  `inq_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`inq_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='询价表';

-- 9. quote - 报价表
DROP TABLE IF EXISTS `quote`;
CREATE TABLE `quote` (
  `quo_id` bigint NOT NULL AUTO_INCREMENT,
  `quo_opp_id` bigint DEFAULT NULL,
  `quo_title` varchar(100) DEFAULT NULL,
  `quo_price` decimal(18,2) DEFAULT NULL,
  `quo_se_no` bigint DEFAULT NULL,
  `quo_remark` longtext,
  `quo_date` datetime DEFAULT NULL,
  `quo_desc` longtext,
  `quo_ins_date` datetime DEFAULT NULL,
  `quo_upd_date` datetime DEFAULT NULL,
  `quo_inp_user` varchar(50) DEFAULT NULL,
  `quo_upd_user` varchar(50) DEFAULT NULL,
  `quo_isdel` char(1) DEFAULT NULL,
  `quo_pro_id` bigint DEFAULT NULL,
  PRIMARY KEY (`quo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='报价表';

-- 10. cus_cor_cus - 客户表
DROP TABLE IF EXISTS `cus_cor_cus`;
CREATE TABLE `cus_cor_cus` (
  `cor_code` bigint NOT NULL AUTO_INCREMENT,
  `cor_num` varchar(50) DEFAULT NULL,
  `cor_user_code` varchar(50) DEFAULT NULL,
  `cor_name` varchar(100) DEFAULT NULL,
  `cor_hot` varchar(50) DEFAULT NULL,
  `cor_mne` varchar(50) DEFAULT NULL,
  `cor_lic_code` varchar(50) DEFAULT NULL,
  `cor_org_code` varchar(50) DEFAULT NULL,
  `cor_star` varchar(50) DEFAULT NULL,
  `cor_cre_lev` varchar(50) DEFAULT NULL,
  `cor_cre_lim` varchar(50) DEFAULT NULL,
  `cor_ind_id` bigint DEFAULT NULL,
  `cor_per_size` varchar(50) DEFAULT NULL,
  `cor_acc_bank` varchar(100) DEFAULT NULL,
  `cor_bank_num` varchar(50) DEFAULT NULL,
  `cor_sou_id` bigint DEFAULT NULL,
  `cor_com_inf` longtext,
  `cor_country` bigint DEFAULT NULL,
  `cor_province` bigint DEFAULT NULL,
  `cor_city` bigint DEFAULT NULL,
  `cor_phone` varchar(50) DEFAULT NULL,
  `cor_fex` varchar(50) DEFAULT NULL,
  `cor_net` varchar(500) DEFAULT NULL,
  `cor_zip_code` varchar(50) DEFAULT NULL,
  `cor_address` longtext,
  `cor_remark` longtext,
  `cor_creat_date` datetime DEFAULT NULL,
  `cor_upd_date` datetime DEFAULT NULL,
  `cor_issuc` char(1) DEFAULT NULL,
  `cor_last_date` datetime DEFAULT NULL,
  `cor_temp_tag` varchar(50) DEFAULT NULL,
  `cor_isdelete` char(1) DEFAULT NULL,
  `cor_spe_write` longtext,
  `cor_upd_user` varchar(50) DEFAULT NULL,
  `cor_typ_id` bigint DEFAULT NULL,
  `cor_ins_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cor_code`),
  KEY `user_index` (`cor_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='客户表';

-- 11. sal_opp - 销售机会表
DROP TABLE IF EXISTS `sal_opp`;
CREATE TABLE `sal_opp` (
  `opp_id` bigint NOT NULL AUTO_INCREMENT,
  `opp_cor_code` bigint DEFAULT NULL,
  `opp_title` varchar(300) DEFAULT NULL,
  `opp_lev` varchar(50) DEFAULT NULL,
  `opp_exe_date` datetime DEFAULT NULL,
  `opp_des` longtext,
  `opp_remark` longtext,
  `opp_ins_date` datetime DEFAULT NULL,
  `opp_isexe` varchar(10) DEFAULT NULL,
  `opp_state` varchar(10) DEFAULT NULL,
  `opp_upd_date` datetime DEFAULT NULL,
  `opp_inp_user` varchar(50) DEFAULT NULL,
  `opp_upd_user` varchar(50) DEFAULT NULL,
  `opp_isdel` char(1) DEFAULT NULL,
  `opp_sign_date` datetime DEFAULT NULL,
  `opp_money` decimal(18,2) DEFAULT NULL,
  `opp_stage` bigint DEFAULT NULL,
  `opp_possible` varchar(50) DEFAULT NULL,
  `opp_sta_remark` varchar(100) DEFAULT NULL,
  `opp_sta_update` datetime DEFAULT NULL,
  `opp_sta_log` longtext,
  `opp_find_date` datetime DEFAULT NULL,
  `opp_user_code` varchar(50) DEFAULT NULL,
  `opp_se_no` bigint DEFAULT NULL,
  PRIMARY KEY (`opp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售机会表';

-- 12. sal_pra - 销售活动表
DROP TABLE IF EXISTS `sal_pra`;
CREATE TABLE `sal_pra` (
  `pra_id` bigint NOT NULL AUTO_INCREMENT,
  `pra_cor_code` bigint DEFAULT NULL,
  `pra_title` varchar(300) DEFAULT NULL,
  `pra_content` longtext,
  `pra_ins_date` datetime DEFAULT NULL,
  `pra_type` varchar(100) DEFAULT NULL,
  `pra_state` varchar(100) DEFAULT NULL,
  `pra_isPrice` varchar(10) DEFAULT NULL,
  `pra_exe_date` datetime DEFAULT NULL,
  `pra_cost_time` varchar(20) DEFAULT NULL,
  `pra_cus_link` varchar(50) DEFAULT NULL,
  `pra_se_no` bigint DEFAULT NULL,
  `pra_back` longtext,
  `pra_remark` longtext,
  `pra_upd_date` datetime DEFAULT NULL,
  `pra_opp_id` bigint DEFAULT NULL,
  `pra_inp_user` varchar(50) DEFAULT NULL,
  `pra_upd_user` varchar(50) DEFAULT NULL,
  `pra_isdel` char(1) DEFAULT NULL,
  `pra_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`pra_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售活动表';

-- 13. pro_task - 项目任务表
DROP TABLE IF EXISTS `pro_task`;
CREATE TABLE `pro_task` (
  `prta_id` bigint NOT NULL AUTO_INCREMENT,
  `prta_se_no` bigint DEFAULT NULL,
  `prta_pro_id` bigint DEFAULT NULL,
  `prta_sta_name` varchar(300) DEFAULT NULL,
  `prta_name` varchar(50) DEFAULT NULL,
  `prta_title` varchar(300) DEFAULT NULL,
  `prta_rel_date` datetime DEFAULT NULL,
  `prta_change_date` datetime DEFAULT NULL,
  `prta_fin_date` datetime DEFAULT NULL,
  `prta_lev` varchar(50) DEFAULT NULL,
  `prta_state` char(1) DEFAULT NULL,
  `prta_cyc` varchar(50) DEFAULT NULL,
  `prta_tag` longtext,
  `prta_desc` longtext,
  `prta_log` longtext,
  `prta_remark` longtext,
  `prta_isdel` char(1) DEFAULT NULL,
  `prta_fct_date` datetime DEFAULT NULL,
  `prta_upd_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`prta_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='项目任务表';

-- 14. cus_serv - 客户服务表
DROP TABLE IF EXISTS `cus_serv`;
CREATE TABLE `cus_serv` (
  `ser_code` bigint NOT NULL AUTO_INCREMENT,
  `ser_cor_code` bigint DEFAULT NULL,
  `ser_title` varchar(300) DEFAULT NULL,
  `ser_cus_link` varchar(50) DEFAULT NULL,
  `ser_method` varchar(100) DEFAULT NULL,
  `ser_content` longtext,
  `ser_exe_date` datetime DEFAULT NULL,
  `ser_cos_time` varchar(50) DEFAULT NULL,
  `ser_state` varchar(10) DEFAULT NULL,
  `ser_se_no` bigint DEFAULT NULL,
  `ser_feedback` longtext,
  `ser_remark` longtext,
  `ser_ins_date` datetime DEFAULT NULL,
  `ser_upd_date` datetime DEFAULT NULL,
  `ser_inp_user` varchar(50) DEFAULT NULL,
  `ser_upd_user` varchar(50) DEFAULT NULL,
  `ser_isdel` char(1) DEFAULT NULL,
  `ser_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ser_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='客户服务表';

-- 15. sal_ord_con - 销售订单表
DROP TABLE IF EXISTS `sal_ord_con`;
CREATE TABLE `sal_ord_con` (
  `sod_code` bigint NOT NULL AUTO_INCREMENT,
  `sod_num` varchar(300) DEFAULT NULL,
  `sod_til` varchar(300) DEFAULT NULL,
  `sod_type_id` bigint DEFAULT NULL,
  `sod_cus_code` bigint DEFAULT NULL,
  `sod_pro_id` bigint DEFAULT NULL,
  `sod_sum_mon` decimal(18,2) DEFAULT NULL,
  `sod_paid_mon` decimal(18,2) DEFAULT NULL,
  `sod_mon_type` varchar(50) DEFAULT NULL,
  `sod_state` varchar(10) DEFAULT NULL,
  `sod_ship_state` varchar(10) DEFAULT NULL,
  `sod_own_code` varchar(50) DEFAULT NULL,
  `sod_deadline` datetime DEFAULT NULL,
  `sod_end_date` datetime DEFAULT NULL,
  `sod_ord_date` datetime DEFAULT NULL,
  `sod_inp_date` datetime DEFAULT NULL,
  `sod_isfail` char(1) DEFAULT NULL,
  `sod_remark` longtext,
  `sod_change_date` datetime DEFAULT NULL,
  `sod_paid_method` varchar(20) DEFAULT NULL,
  `sod_inp_code` varchar(50) DEFAULT NULL,
  `sod_cus_con` varchar(100) DEFAULT NULL,
  `sod_se_no` bigint DEFAULT NULL,
  `sod_con_date` datetime DEFAULT NULL,
  `sod_change_user` varchar(50) DEFAULT NULL,
  `sod_app_date` datetime DEFAULT NULL,
  `sod_app_man` varchar(50) DEFAULT NULL,
  `sod_app_desc` longtext,
  `sod_app_isok` char(1) DEFAULT NULL,
  `sod_content` longtext,
  PRIMARY KEY (`sod_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售订单表';
