package com.frsoft.ccds.system.mapper;

import com.frsoft.ccds.system.domain.BankCase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 银行案件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface BankCaseMapper {
    
    /**
     * 查询银行案件
     * 
     * @param casId 银行案件主键
     * @return 银行案件
     */
    BankCase selectBankCaseByCasId(Long casId);
    
    /**
     * 查询银行案件列表
     * 
     * @param bankCase 银行案件
     * @return 银行案件集合
     */
    List<BankCase> selectBankCaseList(BankCase bankCase);
    
    /**
     * 新增银行案件
     * 
     * @param bankCase 银行案件
     * @return 结果
     */
    int insertBankCase(BankCase bankCase);
    
    /**
     * 修改银行案件
     * 
     * @param bankCase 银行案件
     * @return 结果
     */
    int updateBankCase(BankCase bankCase);
    
    /**
     * 删除银行案件
     * 
     * @param casId 银行案件主键
     * @return 结果
     */
    int deleteBankCaseByCasId(Long casId);
    
    /**
     * 批量删除银行案件
     * 
     * @param casIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBankCaseByCasIds(Long[] casIds);
    
    /**
     * 根据员工编号查询案件列表
     * 
     * @param seNo 员工编号
     * @return 案件列表
     */
    List<BankCase> selectBankCaseListBySeNo(@Param("seNo") Long seNo);
    
    /**
     * 根据案件状态查询案件列表
     * 
     * @param casState 案件状态
     * @return 案件列表
     */
    List<BankCase> selectBankCaseListByState(@Param("casState") Integer casState);
    
    /**
     * 根据客户姓名模糊查询案件列表
     * 
     * @param casName 客户姓名
     * @return 案件列表
     */
    List<BankCase> selectBankCaseListByName(@Param("casName") String casName);
    
    /**
     * 根据客户电话查询案件列表
     * 
     * @param casPhone 客户电话
     * @return 案件列表
     */
    List<BankCase> selectBankCaseListByPhone(@Param("casPhone") String casPhone);
    
    /**
     * 统计案件总数
     * 
     * @param bankCase 查询条件
     * @return 案件总数
     */
    int countBankCase(BankCase bankCase);
    
    /**
     * 分配案件给员工
     * 
     * @param casIds 案件ID数组
     * @param seNo 员工编号
     * @param assignUser 分配用户
     * @return 结果
     */
    int assignBankCaseToEmployee(@Param("casIds") Long[] casIds, 
                                @Param("seNo") Long seNo, 
                                @Param("assignUser") String assignUser);
}
