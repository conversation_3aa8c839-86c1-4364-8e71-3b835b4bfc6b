<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RWoutPro" table="r_wout_pro" schema="dbo" >
        <id name="rwoId" type="java.lang.Long">
            <column name="rwo_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rwo_pro_id" />
        </many-to-one>
        <many-to-one name="wmsWarOut" class="com.psit.struts.entity.WmsWarOut" fetch="select" not-null="false">
            <column name="rwo_wout_id"/>
        </many-to-one>
        <property name="rwoWoutNum" type="java.lang.Double">
            <column name="rwo_wout_num" />
        </property>
        <property name="rwoRemark" type="java.lang.String">
            <column name="rwo_remark" length="1073741823" />
        </property>
    </class>
</hibernate-mapping>
