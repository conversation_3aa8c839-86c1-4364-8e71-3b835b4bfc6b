-- MySQL 8.0 版本的CCDS数据库结构
-- 从MSSQL转换而来

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ccds`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表 (lim_user)
DROP TABLE IF EXISTS `lim_user`;
CREATE TABLE `lim_user` (
  `user_code` varchar(50) NOT NULL COMMENT '用户编码',
  `user_loginName` varchar(50) DEFAULT NULL COMMENT '登录名',
  `user_pwd` varchar(50) DEFAULT NULL COMMENT '密码',
  `user_up_code` varchar(50) DEFAULT NULL COMMENT '上级用户编码',
  `user_lev` char(1) DEFAULT NULL COMMENT '用户级别',
  `user_so_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `user_se_id` bigint DEFAULT NULL COMMENT '员工ID',
  `user_se_name` varchar(100) DEFAULT NULL COMMENT '员工姓名',
  `user_desc` text COMMENT '用户描述',
  `user_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  `user_num` varchar(50) DEFAULT NULL COMMENT '用户编号',
  `user_role_id` bigint DEFAULT NULL COMMENT '角色ID',
  `user_islogin` char(1) DEFAULT '0' COMMENT '是否登录',
  `user_ip` varchar(50) DEFAULT NULL COMMENT '登录IP',
  `user_fail` int DEFAULT 0 COMMENT '失败次数',
  `user_pwd_upd_date` datetime DEFAULT NULL COMMENT '密码更新时间',
  `user_cti_login` varchar(255) DEFAULT NULL COMMENT 'CTI登录名',
  `user_cti_pwd` varchar(255) DEFAULT NULL COMMENT 'CTI密码',
  `user_cti_server` varchar(100) DEFAULT NULL COMMENT 'CTI服务器',
  `user_cti_phone` varchar(50) DEFAULT NULL COMMENT 'CTI电话',
  `user_sms_max_num` int DEFAULT NULL COMMENT '短信最大数量',
  `user_channel` int DEFAULT NULL COMMENT '渠道',
  `user_mdv_id` bigint DEFAULT NULL COMMENT '移动设备ID',
  `user_last_mob_mac` varchar(50) DEFAULT NULL COMMENT '最后移动MAC',
  `user_last_mob_time` datetime DEFAULT NULL COMMENT '最后移动时间',
  `user_grp_id` bigint DEFAULT NULL COMMENT '用户组ID',
  `user_ctis_id` bigint DEFAULT NULL COMMENT 'CTI服务器ID',
  PRIMARY KEY (`user_code`),
  KEY `idx_user_se_id` (`user_se_id`),
  KEY `idx_user_role_id` (`user_role_id`),
  KEY `idx_user_so_code` (`user_so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 员工表 (sal_emp)
DROP TABLE IF EXISTS `sal_emp`;
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT COMMENT '员工编号',
  `se_so_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `se_name` varchar(100) DEFAULT NULL COMMENT '员工姓名',
  `se_ide_code` varchar(50) DEFAULT NULL COMMENT '身份证号',
  `se_pos` varchar(50) DEFAULT NULL COMMENT '职位',
  `se_sex` varchar(50) DEFAULT NULL COMMENT '性别',
  `se_prob` varchar(50) DEFAULT NULL COMMENT '试用期',
  `se_bir_place` varchar(50) DEFAULT NULL COMMENT '出生地',
  `se_acc_place` varchar(100) DEFAULT NULL COMMENT '户籍地',
  `se_birth` varchar(50) DEFAULT NULL COMMENT '出生日期',
  `se_marry` varchar(10) DEFAULT NULL COMMENT '婚姻状况',
  `se_type` varchar(50) DEFAULT NULL COMMENT '员工类型',
  `se_job_lev` bigint DEFAULT NULL COMMENT '职级',
  `se_job_cate` varchar(50) DEFAULT NULL COMMENT '职务类别',
  `se_job_title` varchar(50) DEFAULT NULL COMMENT '职务',
  `se_start_day` datetime DEFAULT NULL COMMENT '入职日期',
  `se_year_pay` varchar(50) DEFAULT NULL COMMENT '年薪',
  `se_cost_center` varchar(50) DEFAULT NULL COMMENT '成本中心',
  `se_email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `se_nation` varchar(50) DEFAULT NULL COMMENT '民族',
  `se_poli_status` varchar(50) DEFAULT NULL COMMENT '政治面貌',
  `se_edu` varchar(50) DEFAULT NULL COMMENT '学历',
  `se_tel` varchar(50) DEFAULT NULL COMMENT '电话',
  `se_phone` varchar(50) DEFAULT NULL COMMENT '手机',
  `se_qq` varchar(50) DEFAULT NULL COMMENT 'QQ',
  `se_msn` varchar(50) DEFAULT NULL COMMENT 'MSN',
  `se_rec_source` varchar(100) DEFAULT NULL COMMENT '招聘来源',
  `se_prov_fund` varchar(50) DEFAULT NULL COMMENT '公积金',
  `se_job_date` datetime DEFAULT NULL COMMENT '转正日期',
  `se_hou_reg` varchar(50) DEFAULT NULL COMMENT '户口性质',
  `se_social_code` varchar(50) DEFAULT NULL COMMENT '社保号',
  `se_rap` varchar(50) DEFAULT NULL COMMENT '紧急联系人',
  `se_address` varchar(500) DEFAULT NULL COMMENT '地址',
  `se_remark` text COMMENT '备注',
  `se_bank_name` varchar(50) DEFAULT NULL COMMENT '银行名称',
  `se_bank_card` varchar(50) DEFAULT NULL COMMENT '银行卡号',
  `se_weal_address` varchar(50) DEFAULT NULL COMMENT '福利地址',
  `se_weal_pos` varchar(50) DEFAULT NULL COMMENT '福利职位',
  `se_isovertime` varchar(50) DEFAULT NULL COMMENT '是否加班',
  `se_attendance` varchar(50) DEFAULT NULL COMMENT '考勤',
  `se_card_num` varchar(50) DEFAULT NULL COMMENT '卡号',
  `se_pic` text COMMENT '照片',
  `se_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  `se_inser_date` datetime DEFAULT NULL COMMENT '创建时间',
  `se_code` varchar(50) DEFAULT NULL COMMENT '员工编码',
  `se_log` text COMMENT '日志',
  `se_alt_date` datetime DEFAULT NULL COMMENT '修改时间',
  `se_inser_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `se_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `se_end_date` datetime DEFAULT NULL COMMENT '离职时间',
  `se_edc_bac` text COMMENT '教育背景',
  `se_work_ex` text COMMENT '工作经历',
  `se_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `se_per_tel` varchar(50) DEFAULT NULL COMMENT '个人电话',
  `se_plan_sign_date` datetime DEFAULT NULL COMMENT '计划签约日期',
  `se_sign_date` datetime DEFAULT NULL COMMENT '签约日期',
  `se_credit_date` datetime DEFAULT NULL COMMENT '征信日期',
  `se_college` varchar(200) DEFAULT NULL COMMENT '毕业院校',
  `se_transfer` text COMMENT '调动记录',
  PRIMARY KEY (`se_no`),
  KEY `idx_se_name` (`se_name`),
  KEY `idx_se_code` (`se_code`),
  KEY `idx_se_user_code` (`se_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 角色表 (lim_role)
DROP TABLE IF EXISTS `lim_role`;
CREATE TABLE `lim_role` (
  `rol_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `rol_name` varchar(100) DEFAULT NULL COMMENT '角色名称',
  `rol_desc` text COMMENT '角色描述',
  `rol_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  `rol_create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `rol_create_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `rol_alt_date` datetime DEFAULT NULL COMMENT '修改时间',
  `rol_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  PRIMARY KEY (`rol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表 (lim_right)
DROP TABLE IF EXISTS `lim_right`;
CREATE TABLE `lim_right` (
  `rig_code` varchar(50) NOT NULL COMMENT '权限编码',
  `rig_name` varchar(100) DEFAULT NULL COMMENT '权限名称',
  `rig_desc` text COMMENT '权限描述',
  `rig_type` varchar(50) DEFAULT NULL COMMENT '权限类型',
  `rig_url` varchar(200) DEFAULT NULL COMMENT '权限URL',
  `rig_parent_code` varchar(50) DEFAULT NULL COMMENT '父权限编码',
  `rig_order` int DEFAULT NULL COMMENT '排序',
  `rig_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`rig_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户权限关系表 (r_user_rig)
DROP TABLE IF EXISTS `r_user_rig`;
CREATE TABLE `r_user_rig` (
  `rur_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `rur_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `rur_rig_code` varchar(50) DEFAULT NULL COMMENT '权限编码',
  `rur_create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `rur_create_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  PRIMARY KEY (`rur_id`),
  KEY `idx_rur_user_code` (`rur_user_code`),
  KEY `idx_rur_rig_code` (`rur_rig_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权限关系表';

-- 组织表 (sal_org)
DROP TABLE IF EXISTS `sal_org`;
CREATE TABLE `sal_org` (
  `so_code` varchar(50) NOT NULL COMMENT '组织编码',
  `so_name` varchar(100) DEFAULT NULL COMMENT '组织名称',
  `so_parent_code` varchar(50) DEFAULT NULL COMMENT '父组织编码',
  `so_type` varchar(50) DEFAULT NULL COMMENT '组织类型',
  `so_level` int DEFAULT NULL COMMENT '组织级别',
  `so_order` int DEFAULT NULL COMMENT '排序',
  `so_desc` text COMMENT '组织描述',
  `so_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  `so_create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `so_create_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `so_alt_date` datetime DEFAULT NULL COMMENT '修改时间',
  `so_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  PRIMARY KEY (`so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织表';

SET FOREIGN_KEY_CHECKS = 1;
