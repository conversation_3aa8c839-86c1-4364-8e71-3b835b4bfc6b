package com.frsoft.ccds.common.utils;

import com.frsoft.ccds.common.exception.BusinessException;

import java.util.regex.Pattern;

/**
 * 数据验证工具类
 * 
 * <AUTHOR>
 */
public class ValidationUtils {
    
    /** 手机号正则表达式 */
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    
    /** 身份证号正则表达式 */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    
    /** 邮箱正则表达式 */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    
    /** 银行卡号正则表达式 */
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile("^[1-9]\\d{12,19}$");

    /**
     * 验证手机号
     * 
     * @param mobile 手机号
     * @return 是否有效
     */
    public static boolean isValidMobile(String mobile) {
        return StringUtils.isNotEmpty(mobile) && MOBILE_PATTERN.matcher(mobile).matches();
    }

    /**
     * 验证身份证号
     * 
     * @param idCard 身份证号
     * @return 是否有效
     */
    public static boolean isValidIdCard(String idCard) {
        return StringUtils.isNotEmpty(idCard) && ID_CARD_PATTERN.matcher(idCard).matches();
    }

    /**
     * 验证邮箱
     * 
     * @param email 邮箱
     * @return 是否有效
     */
    public static boolean isValidEmail(String email) {
        return StringUtils.isNotEmpty(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证银行卡号
     * 
     * @param bankCard 银行卡号
     * @return 是否有效
     */
    public static boolean isValidBankCard(String bankCard) {
        return StringUtils.isNotEmpty(bankCard) && BANK_CARD_PATTERN.matcher(bankCard).matches();
    }

    /**
     * 验证必填字段
     * 
     * @param value 值
     * @param fieldName 字段名
     */
    public static void validateRequired(Object value, String fieldName) {
        if (value == null || (value instanceof String && StringUtils.isEmpty((String) value))) {
            throw new BusinessException(fieldName + "不能为空");
        }
    }

    /**
     * 验证字符串长度
     * 
     * @param value 值
     * @param fieldName 字段名
     * @param minLength 最小长度
     * @param maxLength 最大长度
     */
    public static void validateLength(String value, String fieldName, int minLength, int maxLength) {
        if (StringUtils.isEmpty(value)) {
            return;
        }
        
        int length = value.length();
        if (length < minLength || length > maxLength) {
            throw new BusinessException(String.format("%s长度必须在%d-%d个字符之间", fieldName, minLength, maxLength));
        }
    }

    /**
     * 验证手机号（抛出异常）
     * 
     * @param mobile 手机号
     * @param fieldName 字段名
     */
    public static void validateMobile(String mobile, String fieldName) {
        if (StringUtils.isNotEmpty(mobile) && !isValidMobile(mobile)) {
            throw new BusinessException(fieldName + "格式不正确");
        }
    }

    /**
     * 验证身份证号（抛出异常）
     * 
     * @param idCard 身份证号
     * @param fieldName 字段名
     */
    public static void validateIdCard(String idCard, String fieldName) {
        if (StringUtils.isNotEmpty(idCard) && !isValidIdCard(idCard)) {
            throw new BusinessException(fieldName + "格式不正确");
        }
    }

    /**
     * 验证邮箱（抛出异常）
     * 
     * @param email 邮箱
     * @param fieldName 字段名
     */
    public static void validateEmail(String email, String fieldName) {
        if (StringUtils.isNotEmpty(email) && !isValidEmail(email)) {
            throw new BusinessException(fieldName + "格式不正确");
        }
    }

    /**
     * 验证银行卡号（抛出异常）
     * 
     * @param bankCard 银行卡号
     * @param fieldName 字段名
     */
    public static void validateBankCard(String bankCard, String fieldName) {
        if (StringUtils.isNotEmpty(bankCard) && !isValidBankCard(bankCard)) {
            throw new BusinessException(fieldName + "格式不正确");
        }
    }

    /**
     * 验证数值范围
     * 
     * @param value 值
     * @param fieldName 字段名
     * @param min 最小值
     * @param max 最大值
     */
    public static void validateRange(Number value, String fieldName, Number min, Number max) {
        if (value == null) {
            return;
        }
        
        double doubleValue = value.doubleValue();
        double minValue = min.doubleValue();
        double maxValue = max.doubleValue();
        
        if (doubleValue < minValue || doubleValue > maxValue) {
            throw new BusinessException(String.format("%s必须在%s-%s之间", fieldName, min, max));
        }
    }
}
