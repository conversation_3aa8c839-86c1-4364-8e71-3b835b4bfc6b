<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.CusCorCus" table="cus_cor_cus" schema="dbo" >
        <id name="corCode" type="java.lang.Long">
            <column name="cor_code"/>
            <generator class="identity" />
        </id>
        <property name="corNum" type="java.lang.String">
            <column name="cor_num" length="50" />
        </property>
        <many-to-one name="cusIndustry" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cor_ind_id" />
        </many-to-one>
        <many-to-one name="corSou" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cor_sou_id" />
        </many-to-one>
        <many-to-one name="cusState" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cor_typ_id" />
        </many-to-one>
        <many-to-one name="cusProvince" class="com.frsoft.base.entity.CusProvince" fetch="select" not-null="false">
            <column name="cor_province" />
        </many-to-one>
        <many-to-one name="cusCity" class="com.frsoft.base.entity.CusCity" fetch="select" not-null="false">
            <column name="cor_city" />
        </many-to-one>
        <many-to-one name="cusArea" class="com.frsoft.base.entity.CusArea" fetch="select" not-null="false">
            <column name="cor_country" />
        </many-to-one>
         <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="cor_se_no" length="50" />
        </many-to-one>
        <property name="corName" type="java.lang.String">
            <column name="cor_name" length="100" />
        </property>
        <property name="corHot" type="java.lang.String">
            <column name="cor_hot" length="50" />
        </property>
        <property name="corMne" type="java.lang.String">
            <column name="cor_mne" length="50" />
        </property>
        <property name="corLicCode" type="java.lang.String">
            <column name="cor_lic_code" length="50" />
        </property>
        <property name="corOrgCode" type="java.lang.String">
            <column name="cor_org_code" length="50" />
        </property>
        <property name="corStar" type="java.lang.String">
            <column name="cor_star" length="50" />
        </property>
        <property name="corCreLev" type="java.lang.String">
            <column name="cor_cre_lev" length="50" />
        </property>
        <property name="corCreLim" type="java.lang.String">
            <column name="cor_cre_lim" length="50" />
        </property>
        <property name="corPerSize" type="java.lang.String">
            <column name="cor_per_size" length="50" />
        </property>
        <property name="corAccBank" type="java.lang.String">
            <column name="cor_acc_bank" length="100" />
        </property>
        <property name="corBankNum" type="java.lang.String">
            <column name="cor_bank_num" length="50" />
        </property>
         <property name="corUpdUser" type="java.lang.String">
            <column name="cor_upd_user" length="50" />
        </property>
        <property name="corComInf" type="java.lang.String">
            <column name="cor_com_inf" length="**********" />
        </property>
        <property name="corPhone" type="java.lang.String">
            <column name="cor_phone" length="50" />
        </property>
        <property name="corCellPhone" type="java.lang.String">
            <column name="cor_cell_phone" length="50" />
        </property>
        <property name="corFex" type="java.lang.String">
            <column name="cor_fex" length="50" />
        </property>
        <property name="corNet" type="java.lang.String">
            <column name="cor_net" length="500" />
        </property>
        <property name="corZipCode" type="java.lang.String">
            <column name="cor_zip_code" length="50" />
        </property>
        <property name="corAddress" type="java.lang.String">
            <column name="cor_address" length="**********" />
        </property>
        <property name="corRemark" type="java.lang.String">
            <column name="cor_remark" length="**********" />
        </property>
        <property name="corCreatDate" type="java.util.Date">
            <column name="cor_creat_date" length="23" />
        </property>
         <property name="corInsUser" type="java.lang.String">
            <column name="cor_ins_user" length="50" />
        </property>
        <property name="corUpdDate" type="java.util.Date">
            <column name="cor_upd_date" length="23" />
        </property>
        <property name="corIssuc" type="java.lang.String">
            <column name="cor_issuc" length="1" />
        </property>
        <property name="corLastDate" type="java.util.Date">
            <column name="cor_last_date" length="23" />
        </property>
        <property name="corTempTag" type="java.lang.String">
            <column name="cor_temp_tag" length="50" />
        </property>
        <property name="corIsdelete" type="java.lang.String">
            <column name="cor_isdelete" length="1" />
        </property>
        <property name="corSpeWrite" type="java.lang.String">
            <column name="cor_spe_write" length="200" />
        </property>
        <set name="salOrdCons" inverse="true" order-by="sod_inp_date desc" cascade="all">
            <key>
                <column name="sod_cus_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.cis.entity.SalOrdCon" />
        </set>
        <set name="cusContacts" inverse="true"  order-by="con_cre_date desc"  cascade="all">
            <key>
                <column name="con_cor_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.cis.entity.CusContact" />
        </set>
        <set name="salPras" inverse="true"  order-by="pra_ins_date desc" cascade="all">
            <key>
                <column name="pra_cor_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.cis.entity.SalPra" />
        </set>
        <set name="salPaids" inverse="true" cascade="all">
            <key>
                <column name="sps_id" />
            </key>
            <one-to-many class="com.frsoft.cis.entity.SalPaidPast" />
        </set>
        <set name="attachments" inverse="true"  cascade="all"  where="att_type='allCus'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
   		</set>
    </class>
</hibernate-mapping>
