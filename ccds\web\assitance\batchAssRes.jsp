<%@ page language="java" pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>批量核查</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
		#batchContainer {
			width:930px;
			height:100%;
			border-left:#999999 1px solid;
			border-right:#999999 1px solid;
			padding:10px;
			background:#fff;
		}
		#batchTitle {
			font-size:16px;
			font-style:italic;
			font-weight:bold;
			color:#999999;
			text-align:left;
			padding:5px;
		}
		#batchList {
			text-align:left;
		}
		#operaLayer {
			width:100%;
			padding:5px 5px 10px 5px;
			background-color:#eff7f7;
			text-align:center;
		}
		#operaLayer button {
			margin-left:10px;
			width:200px;
			height:40px;
			font-size:18px;
		}
    </style>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/case.js"></script>
  	<script language="javascript" type="text/javascript">
		var rowsNum;
		function showWarn(obj,rowId){
			if(obj.value==1){
				$("warn"+rowId).readOnly=false;
				$("warn"+rowId).style.backgroundColor="#FFF";
			}
			else{
				$("warn"+rowId).readOnly=true;
				$("warn"+rowId).style.backgroundColor="#CCC";
				$("warn"+rowId).innerHTML="";
			}
		}
		function batchConCp(){
			waitSubmit("saveButton");
			var url = "assitanceAction.do";
			var pars = $("batchForm").serialize(true);
			pars.op = "batchAssRes";
			new Ajax.Request(url,{
				method:"post",
				parameters : pars,
				onSuccess: function(transport){
					var savedNum = transport.responseText;
					if(savedNum!=undefined && savedNum != ""){
						var sucHTML = "成功确认完成"+savedNum+"条案件协催。";
						if(savedNum < rowsNum){
							sucHTML += "（部分协催内容提交失败，请重新提交）";
							restoreSubmit("saveButton");
						}
						else if (savedNum == rowsNum){
							sucHTML += "全部完成，<a href='javascript:void(0)' onclick='window.close()'>点击关闭此页面</a>。";
						}
						$("resultDiv").innerHTML = sucHTML;
						$("resultDiv").show();
						loadList();
					}
				},
				onFailure: function(transport){
					$("resultDiv").innerHTML = "保存失败["+transport.status+"]，请重试。";
					$("resultDiv").show();
					loadList();
				}
			});
		}
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			var caseClass = "", casCode="", casName="";
			dataId = obj.chId;
			if(obj.bankCase){
				var relFunc = "descPop('caseAction.do?op=caseDesc&caseId="+obj.bankCase.casId+"&view=ass')";
				caseClass = getCaseColor(obj.bankCase);
				casCode = "<a href=\"javascript:void(0)\" "+ ((caseClass!="")?("class=\""+caseClass+"\""):"") +" onclick=\""+relFunc+";return false;\">"+obj.bankCase.casCode+"&nbsp;</a>";
				casName = obj.bankCase.casName;
				casM = obj.bankCase.casM;
			}
			var assRes = "<textarea rows='1' name='assRes' style='width:98%;' onBlur='autoShort(this,4000)'></textarea><input type='hidden' name='assId' value='"+obj.chId+"'>";
			var assColor = "<select name='assColor' style='width:98%;' onChange='showWarn(this,"+obj.chId+")'><option value='-1'>不更改</option><option value='1' "+('${assType}'=='3'?"selected":"")+">标红</option><option value='2' "+('${CUS_VER_ID}'=='39'&&'${assType}'!='3'?"selected":"")+">标蓝</option></select>";
			var caseWarn = "<textarea id='warn"+dataId+"' rows='1' name='caseWarn' style='width:98%;' onBlur='autoShort(this,300)' readonly style='background-color:#CCC'></textarea>";
			switch('${assType}'){
				case '1': datas = [casCode, casName, casM, obj.chAdr!=''?obj.chAdr:(obj.chAddress?obj.chAddress.adrAdd:""), obj.chText, obj.chCat1, obj.chCat2, obj.chAppTime, obj.chAppUser, assRes, assColor, caseWarn]; break;
				case '2': datas = [casCode, casName, casM, obj.chAdr!=''?obj.chAdr:(obj.chAddress?obj.chAddress.adrAdd:""), obj.chText,obj.chAppTime, obj.chAppUser, assRes, assColor, caseWarn]; break;	
				case '13': datas = [casCode, casName, casM, obj.chAdr, obj.chText, obj.chAppTime, obj.chAppUser, assRes, assColor, caseWarn]; break;
				default: datas = [casCode, casName, casM,obj.chText,obj.chAppTime, obj.chAppUser, assRes, assColor, caseWarn]; break;	
			}
			rowsNum++;//计算行数
			return [datas,className,dblFunc,dataId];
		}
			
		function loadList(){
			rowsNum = 0;
			var url = "assitanceAction.do";
			var pars = [];
			pars.op = "listBatchAssRes";
			pars.assIds="${assIds}";
			
			var loadFunc = "loadList";
			var cols ;
			
			switch('${assType}'){
				case '1':
					cols = [
						{name:"个案序列号",align:"left"},
						{name:getCasNameTxt("${CUS_VER_ID}")},
						{name:"委案金额",align:"right",renderer:"money"},
						{name:"地址",align:"left"},
						{name:"申请内容",align:"left"},
						{name:"地址类别"},
						{name:"信函类别"},
						{name:"申请时间",renderer:"time"},
						{name:"申请人"},
						{name:"协催内容",align:"left"},
						{name:"案件状态"},
						{name:"案件警告"}
					];
					break;
				case '2':
					cols = [
						{name:"个案序列号",align:"left"},
						{name:getCasNameTxt("${CUS_VER_ID}")},
						{name:"委案金额",align:"right",renderer:"money"},
						{name:"地址",align:"left"},
						{name:"申请内容",align:"left"},
						{name:"申请时间",renderer:"time"},
						{name:"申请人"},
						{name:"协催内容",align:"left"},
						{name:"案件状态"},
						{name:"案件警告"}
					];
					break;
				case '13':
					cols = [
						{name:"个案序列号",align:"left"},
						{name:getCasNameTxt("${CUS_VER_ID}")},
						{name:"委案金额",align:"right",renderer:"money"},
						{name:"发送号码"},
						{name:"短信内容"},
						{name:"申请时间",renderer:"time"},
						{name:"申请人"},
						{name:"协催内容",align:"left"},
						{name:"案件状态"},
						{name:"案件警告"}
					];
					break;	
				default :
					cols = [
						{name:"个案序列号",align:"left"},
						{name:getCasNameTxt("${CUS_VER_ID}")},
						{name:"委案金额",align:"right",renderer:"money"},
						{name:"申请内容",align:"left"},
						{name:"申请时间",renderer:"time"},
						{name:"申请人"},
						{name:"协催内容",align:"left"},
						{name:"案件状态"},
						{name:"案件警告"}
					];
					break;
			}
			gridEl.init(url,pars,cols,loadFunc);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("conCHTab","batchList");
		gridEl.config.hasPage = false;
		gridEl.config.sortable = false;
		createProgressBar();
		window.onload=function(){
			loadList();
		}
  	</script>
  </head>
  
  <body>
  <div id="batchContainer">
  	<div id="batchTitle">批量完成案件协催</div>
    <div id="resultDiv" class="updSuc" style="display:none"></div>
    <form id="batchForm" style="margin:0; padding:0;">
    <div id="batchList" class="dataList"></div>
    <div id="operaLayer">
        <button id="saveButton" class="inputBoxAlign" onClick="batchConCp()">提交结果</button>
    </div>
    </form>
  </div>
  </body>
  
</html>
