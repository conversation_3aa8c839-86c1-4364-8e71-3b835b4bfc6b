-- MySQL 8.0 版本的CCDS数据库结构
-- 从MSSQL自动转换生成

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ccds`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

use ccds;
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`spo_paid_past`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `spo_paid_past`;
CREATE TABLE `spo_paid_past`(
`spa_id` `bigint` IDENTITY(1,1) NOT NULL,
`spa_code` `varchar`(300) NULL,
`spa_spo_id` `bigint` NULL,
`spa_aco_id` `bigint` NULL,
`spa_ssu_id` `bigint` NULL,
`spa_fct_date` `datetime` NULL,
`spa_type_id` `bigint` NULL,
`spa_pay_type` `nvarchar`(50) NULL,
`spa_pay_mon` `decimal`(18, 2) NULL,
`spa_in_name` `nvarchar`(100) NULL,
`spa_inp_user` `nvarchar`(50) NULL,
`spa_se_no` `bigint` NULL,
`spa_isinv` `char`(1) NULL,
`spa_remark` `nvarchar`(max) NULL,
`spa_cre_date` `datetime` NULL,
`spa_isdel` `char`(1) NULL,
`spa_content` `nvarchar`(100) NULL,
`spa_acc_type_id` `bigint` NULL,
`spa_alt_date` `datetime` NULL,
`spa_alt_user` `nvarchar`(50) NULL,
`spa_undo_date` `datetime` NULL,
`spa_undo_user` `nvarchar`(50) NULL,
PRIMARY KEY (`spa_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_emp`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_emp`;
CREATE TABLE `sal_emp`(
`se_no` `bigint` IDENTITY(1,1) NOT NULL,
`se_so_code` `varchar`(50) NULL,
`se_name` `varchar`(100) NULL,
`se_ide_code` `varchar`(50) NULL,
`se_pos` `nvarchar`(50) NULL,
`se_sex` `varchar`(50) NULL,
`se_prob` `varchar`(50) NULL,
`se_bir_place` `nvarchar`(50) NULL,
`se_acc_place` `nvarchar`(100) NULL,
`se_birth` `varchar`(50) NULL,
`se_marry` `varchar`(10) NULL,
`se_type` `varchar`(50) NULL,
`se_job_lev` `bigint` NULL,
`se_job_cate` `varchar`(50) NULL,
`se_job_title` `nvarchar`(50) NULL,
`se_start_day` `datetime` NULL,
`se_year_pay` `nvarchar`(50) NULL,
`se_cost_center` `varchar`(50) NULL,
`se_email` `nvarchar`(50) NULL,
`se_nation` `varchar`(50) NULL,
`se_poli_status` `varchar`(50) NULL,
`se_edu` `nvarchar`(50) NULL,
`se_tel` `varchar`(50) NULL,
`se_phone` `varchar`(50) NULL,
`se_qq` `varchar`(50) NULL,
`se_msn` `nvarchar`(50) NULL,
`se_rec_source` `nvarchar`(100) NULL,
`se_prov_fund` `nvarchar`(50) NULL,
`se_job_date` `datetime` NULL,
`se_hou_reg` `varchar`(50) NULL,
`se_social_code` `nvarchar`(50) NULL,
`se_rap` `varchar`(50) NULL,
`se_address` `nvarchar`(500) NULL,
`se_remark` `nvarchar`(max) NULL,
`se_bank_name` `nvarchar`(50) NULL,
`se_bank_card` `nvarchar`(50) NULL,
`se_weal_address` `nvarchar`(50) NULL,
`se_weal_pos` `varchar`(50) NULL,
`se_isovertime` `varchar`(50) NULL,
`se_attendance` `varchar`(50) NULL,
`se_card_num` `varchar`(50) NULL,
`se_pic` `nvarchar`(max) NULL,
`se_isenabled` `char`(1) NULL,
`se_inser_date` `datetime` NULL,
`se_code` `varchar`(50) NULL,
`se_log` `nvarchar`(max) NULL,
`se_alt_date` `datetime` NULL,
`se_inser_user` `nvarchar`(50) NULL,
`se_alt_user` `nvarchar`(50) NULL,
`se_end_date` `datetime` NULL,
`se_edc_bac` `nvarchar`(max) NULL,
`se_work_ex` `nvarchar`(max) NULL,
`se_user_code` `varchar`(50) NULL,
`se_per_tel` `varchar`(50) NULL,
`se_plan_sign_date` `datetime` NULL,
`se_sign_date` `datetime` NULL,
`se_credit_date` `datetime` NULL,
`se_college` `nvarchar`(200) NULL,
`se_transfer` `text` NULL,
PRIMARY KEY (`se_no`)
)
END
;
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'`sal_emp`') AND name = N'IX_sal_emp')
CREATE INDEX `IX_sal_emp` ON `sal_emp` (`se_name`);WITH (IGNORE_DUP_KEY = OFF)
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_rep_lim`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_rep_lim`;
CREATE TABLE `r_rep_lim`(
`rrl_id` `bigint` IDENTITY(1,1) NOT NULL,
`rrl_rep_code` `bigint` NULL,
`rrl_se_no` `bigint` NULL,
`rrl_date` `datetime` NULL,
`rrl_content` `nvarchar`(max) NULL,
`rrl_isappro` `char`(1) NULL,
`rrl_oppro_date` `datetime` NULL,
`rrl_isdel` `char`(1) NULL,
`rrl_app_order` `int` NULL,
`rrl_isview` `char`(1) NULL,
`rrl_is_all_appro` `char`(1) NULL,
`rrl_rec_user` `nvarchar`(50) NULL,
PRIMARY KEY (`rrl_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_paid_plan`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_paid_plan`;
CREATE TABLE `sal_paid_plan`(
`spd_id` `bigint` IDENTITY(1,1) NOT NULL,
`spd_ord_code` `bigint` NULL,
`spd_prm_date` `datetime` NULL,
`spd_count` `int` NULL,
`spd_pay_mon` `decimal`(18, 2) NULL,
`spd_mon_type` `nvarchar`(50) NULL,
`spd_user_code` `nvarchar`(50) NULL,
`spd_isp` `char`(1) NULL,
`spd_resp` `varchar`(50) NULL,
`spd_cre_date` `datetime` NULL,
`spd_alt_date` `datetime` NULL,
`spd_alt_user` `nvarchar`(50) NULL,
`spd_isdel` `char`(1) NULL,
`spd_content` `nvarchar`(100) NULL,
`spd_cor_code` `bigint` NULL,
PRIMARY KEY (`spd_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_new_lim`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_new_lim`;
CREATE TABLE `r_new_lim`(
`rnl_id` `bigint` IDENTITY(1,1) NOT NULL,
`rnl_new_code` `bigint` NULL,
`rnl_se_no` `bigint` NULL,
`rnl_date` `datetime` NULL,
PRIMARY KEY (`rnl_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`account`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account`(
`aco_id` `bigint` IDENTITY(1,1) NOT NULL,
`aco_type` `nvarchar`(50) NULL,
`aco_name` `nvarchar`(100) NULL,
`aco_bank_num` `nvarchar`(50) NULL,
`aco_bank` `nvarchar`(100) NULL,
`aco_bank_name` `nvarchar`(50) NULL,
`aco_cre_date` `datetime` NULL,
`aco_org_mon` `decimal`(18, 2) NULL,
`aco_cur_mon` `decimal`(18, 2) NULL,
`aco_remark` `nvarchar`(max) NULL,
`aco_inp_user` `nvarchar`(50) NULL,
`aco_inp_date` `datetime` NULL,
`aco_alt_date` `datetime` NULL,
`aco_alt_user` `nvarchar`(50) NULL,
PRIMARY KEY (`aco_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`lim_user`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `lim_user`;
CREATE TABLE `lim_user`(
`user_code` `varchar`(50) NOT NULL,
`user_loginName` `varchar`(50) NULL,
`user_pwd` `varchar`(50) NULL,
`user_up_code` `varchar`(50) NULL,
`user_lev` `char`(1) NULL,
`user_so_code` `varchar`(50) NULL,
`user_se_id` `bigint` NULL,
`user_se_name` `nvarchar`(100) NULL,
`user_desc` `nvarchar`(max) NULL,
`user_isenabled` `char`(1) NULL,
`user_num` `varchar`(200) NULL,
`user_role_id` `bigint` NULL,
`user_islogin` `char`(1) NULL,
`user_ip` `varchar`(50) NULL,
`user_fail` `int` NULL,
`user_pwd_upd_date` datetime NULL,
`user_cti_login` varchar(255) NULL,
`user_cti_pwd` varchar(255) NULL,
`user_cti_server` varchar(50) NULL,
`user_cti_phone` varchar(50) NULL,
`user_grp_id` `bigint` NULL,
`user_sms_max_num` `int` NULL,
PRIMARY KEY (`user_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`inquiry`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `inquiry`;
CREATE TABLE `inquiry`(
`inq_id` `bigint` IDENTITY(1,1) NOT NULL,
`inq_ssu_id` `bigint` NULL,
`inq_pro_id` `bigint` NULL,
`inq_title` `nvarchar`(100) NULL,
`inq_price` `decimal`(18, 2) NULL,
`inq_se_no` `bigint` NULL,
`inq_date` `datetime` NULL,
`inq_inp_user` `nvarchar`(50) NULL,
`inq_upd_user` `nvarchar`(50) NULL,
`inq_ins_date` `datetime` NULL,
`inq_upd_date` `datetime` NULL,
`inq_remark` `nvarchar`(max) NULL,
`inq_isdel` `char`(1) NULL,
PRIMARY KEY (`inq_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`quote`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `quote`;
CREATE TABLE `quote`(
`quo_id` `bigint` IDENTITY(1,1) NOT NULL,
`quo_opp_id` `bigint` NULL,
`quo_title` `nvarchar`(100) NULL,
`quo_price` `decimal`(18, 2) NULL,
`quo_se_no` `bigint` NULL,
`quo_remark` `nvarchar`(max) NULL,
`quo_date` `datetime` NULL,
`quo_desc` `nvarchar`(max) NULL,
`quo_ins_date` `datetime` NULL,
`quo_upd_date` `datetime` NULL,
`quo_inp_user` `nvarchar`(50) NULL,
`quo_upd_user` `nvarchar`(50) NULL,
`quo_isdel` `char`(1) NULL,
`quo_pro_id` `bigint` NULL,
PRIMARY KEY (`quo_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`cus_cor_cus`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `cus_cor_cus`;
CREATE TABLE `cus_cor_cus`(
`cor_code` `bigint` IDENTITY(1,1) NOT NULL,
`cor_num` `varchar`(50) NULL,
`cor_user_code` `varchar`(50) NULL,
`cor_name` `nvarchar`(100) NULL,
`cor_hot` `nvarchar`(50) NULL,
`cor_mne` `nvarchar`(50) NULL,
`cor_lic_code` `varchar`(50) NULL,
`cor_org_code` `varchar`(50) NULL,
`cor_star` `varchar`(50) NULL,
`cor_cre_lev` `varchar`(50) NULL,
`cor_cre_lim` `varchar`(50) NULL,
`cor_ind_id` `bigint` NULL,
`cor_per_size` `varchar`(50) NULL,
`cor_acc_bank` `nvarchar`(100) NULL,
`cor_bank_num` `varchar`(50) NULL,
`cor_sou_id` `bigint` NULL,
`cor_com_inf` `nvarchar`(max) NULL,
`cor_country` `bigint` NULL,
`cor_province` `bigint` NULL,
`cor_city` `bigint` NULL,
`cor_phone` `varchar`(50) NULL,
`cor_fex` `varchar`(50) NULL,
`cor_net` `varchar`(500) NULL,
`cor_zip_code` `varchar`(50) NULL,
`cor_address` `nvarchar`(max) NULL,
`cor_remark` `nvarchar`(max) NULL,
`cor_creat_date` `datetime` NULL,
`cor_upd_date` `datetime` NULL,
`cor_issuc` `char`(1) NULL,
`cor_last_date` `datetime` NULL,
`cor_temp_tag` `nvarchar`(50) NULL,
`cor_isdelete` `char`(1) NULL,
`cor_spe_write` `nvarchar`(max) NULL,
`cor_upd_user` `nvarchar`(50) NULL,
`cor_typ_id` `bigint` NULL,
`cor_ins_user` `nvarchar`(50) NULL,
PRIMARY KEY (`cor_code`)
)
END
;
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'`cus_cor_cus`') AND name = N'user_index')
CREATE INDEX `user_index` ON `cus_cor_cus` (`cor_user_code`);WITH (IGNORE_DUP_KEY = OFF)
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_opp`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_opp`;
CREATE TABLE `sal_opp`(
`opp_id` `bigint` IDENTITY(1,1) NOT NULL,
`opp_cor_code` `bigint` NULL,
`opp_title` `nvarchar`(300) NULL,
`opp_lev` `varchar`(50) NULL,
`opp_exe_date` `datetime` NULL,
`opp_des` `nvarchar`(max) NULL,
`opp_remark` `nvarchar`(max) NULL,
`opp_ins_date` `datetime` NULL,
`opp_isexe` `varchar`(10) NULL,
`opp_state` `varchar`(10) NULL,
`opp_upd_date` `datetime` NULL,
`opp_inp_user` `nvarchar`(50) NULL,
`opp_upd_user` `nvarchar`(50) NULL,
`opp_isdel` `char`(1) NULL,
`opp_sign_date` `datetime` NULL,
`opp_money` `decimal`(18, 2) NULL,
`opp_stage` `bigint` NULL,
`opp_possible` `varchar`(50) NULL,
`opp_sta_remark` `nvarchar`(100) NULL,
`opp_sta_update` `datetime` NULL,
`opp_sta_log` `nvarchar`(max) NULL,
`opp_find_date` `datetime` NULL,
`opp_user_code` `varchar`(50) NULL,
`opp_se_no` `bigint` NULL,
PRIMARY KEY (`opp_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_pra`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_pra`;
CREATE TABLE `sal_pra`(
`pra_id` `bigint` IDENTITY(1,1) NOT NULL,
`pra_cor_code` `bigint` NULL,
`pra_title` `nvarchar`(300) NULL,
`pra_content` `nvarchar`(max) NULL,
`pra_ins_date` `datetime` NULL,
`pra_type` `nvarchar`(100) NULL,
`pra_state` `nvarchar`(100) NULL,
`pra_isPrice` `nvarchar`(10) NULL,
`pra_exe_date` `datetime` NULL,
`pra_cost_time` `nvarchar`(20) NULL,
`pra_cus_link` `nvarchar`(50) NULL,
`pra_se_no` `bigint` NULL,
`pra_back` `nvarchar`(max) NULL,
`pra_remark` `nvarchar`(max) NULL,
`pra_upd_date` `datetime` NULL,
`pra_opp_id` `bigint` NULL,
`pra_inp_user` `nvarchar`(50) NULL,
`pra_upd_user` `nvarchar`(50) NULL,
`pra_isdel` `char`(1) NULL,
`pra_user_code` `varchar`(50) NULL,
PRIMARY KEY (`pra_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`pro_task`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `pro_task`;
CREATE TABLE `pro_task`(
`prta_id` `bigint` IDENTITY(1,1) NOT NULL,
`prta_se_no` `bigint` NULL,
`prta_pro_id` `bigint` NULL,
`prta_sta_name` `nvarchar`(300) NULL,
`prta_name` `nvarchar`(50) NULL,
`prta_title` `nvarchar`(300) NULL,
`prta_rel_date` `datetime` NULL,
`prta_change_date` `datetime` NULL,
`prta_fin_date` `datetime` NULL,
`prta_lev` `varchar`(50) NULL,
`prta_state` `char`(1) NULL,
`prta_cyc` `varchar`(50) NULL,
`prta_tag` `nvarchar`(max) NULL,
`prta_desc` `nvarchar`(max) NULL,
`prta_log` `nvarchar`(max) NULL,
`prta_remark` `nvarchar`(max) NULL,
`prta_isdel` `char`(1) NULL,
`prta_fct_date` `datetime` NULL,
`prta_upd_user` `nvarchar`(50) NULL,
PRIMARY KEY (`prta_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`cus_serv`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `cus_serv`;
CREATE TABLE `cus_serv`(
`ser_code` `bigint` IDENTITY(1,1) NOT NULL,
`ser_cor_code` `bigint` NULL,
`ser_title` `nvarchar`(300) NULL,
`ser_cus_link` `nvarchar`(50) NULL,
`ser_method` `nvarchar`(100) NULL,
`ser_content` `nvarchar`(max) NULL,
`ser_exe_date` `datetime` NULL,
`ser_cos_time` `varchar`(50) NULL,
`ser_state` `varchar`(10) NULL,
`ser_se_no` `bigint` NULL,
`ser_feedback` `nvarchar`(max) NULL,
`ser_remark` `nvarchar`(max) NULL,
`ser_ins_date` `datetime` NULL,
`ser_upd_date` `datetime` NULL,
`ser_inp_user` `nvarchar`(50) NULL,
`ser_upd_user` `nvarchar`(50) NULL,
`ser_isdel` `char`(1) NULL,
`ser_user_code` `varchar`(50) NULL,
PRIMARY KEY (`ser_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_ord_con`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_ord_con`;
CREATE TABLE `sal_ord_con`(
`sod_code` `bigint` IDENTITY(1,1) NOT NULL,
`sod_num` `varchar`(300) NULL,
`sod_til` `nvarchar`(300) NULL,
`sod_type_id` `bigint` NULL,
`sod_cus_code` `bigint` NULL,
`sod_pro_id` `bigint` NULL,
`sod_sum_mon` `decimal`(18, 2) NULL,
`sod_paid_mon` `decimal`(18, 2) NULL,
`sod_mon_type` `varchar`(50) NULL,
`sod_state` `varchar`(10) NULL,
`sod_ship_state` `varchar`(10) NULL,
`sod_own_code` `varchar`(50) NULL,
`sod_deadline` `datetime` NULL,
`sod_end_date` `datetime` NULL,
`sod_ord_date` `datetime` NULL,
`sod_inp_date` `datetime` NULL,
`sod_isfail` `char`(1) NULL,
`sod_remark` `nvarchar`(max) NULL,
`sod_change_date` `datetime` NULL,
`sod_paid_method` `nvarchar`(20) NULL,
`sod_inp_code` `nvarchar`(50) NULL,
`sod_cus_con` `nvarchar`(100) NULL,
`sod_se_no` `bigint` NULL,
`sod_con_date` `datetime` NULL,
`sod_change_user` `nvarchar`(50) NULL,
`sod_app_date` `datetime` NULL,
`sod_app_man` `nvarchar`(50) NULL,
`sod_app_desc` `nvarchar`(max) NULL,
`sod_app_isok` `char`(1) NULL,
`sod_content` `nvarchar`(max) NULL,
PRIMARY KEY (`sod_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_line`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_line`;
CREATE TABLE `wms_line`(
`wli_id` `bigint` IDENTITY(1,1) NOT NULL,
`wli_type_code` `varchar`(50) NULL,
`wli_type` `varchar`(50) NULL,
`wli_stro_code` `varchar`(50) NULL,
`wli_wpr_id` `bigint` NULL,
`wli_in_num` `decimal`(18, 2) NULL,
`wli_out_num` `decimal`(18, 2) NULL,
`wli_date` `datetime` NULL,
`wli_state` `char`(1) NULL,
`wli_man` `varchar`(50) NULL,
`wli_wms_id` `bigint` NULL,
`wli_isdel` `char`(1) NULL,
`wli_num` `decimal`(18, 2) NULL,
PRIMARY KEY (`wli_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_ship_pro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_ship_pro`;
CREATE TABLE `r_ship_pro`(
`rshp_id` `bigint` IDENTITY(1,1) NOT NULL,
`rshp_ship_code` `varchar`(50) NULL,
`rshp_pro_id` `bigint` NULL,
PRIMARY KEY (`rshp_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`doc_template`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `doc_template`;
CREATE TABLE `doc_template`(
`tmp_id` `bigint` IDENTITY(1,1) NOT NULL,
`tmp_name` `nvarchar`(50) NULL,
`tmp_html` `varchar`(max) NULL,
`tmp_type` `varchar`(50) NULL,
`tmp_mark` `varchar`(100) NULL,
PRIMARY KEY (`tmp_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_stro_pro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_stro_pro`;
CREATE TABLE `r_stro_pro`(
`rsp_id` `bigint` IDENTITY(1,1) NOT NULL,
`rsp_stro_code` `varchar`(50) NULL,
`rsp_pro_id` `bigint` NULL,
`rsp_pro_num` `decimal`(18, 2) NULL,
PRIMARY KEY (`rsp_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`address`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address`(
`adr_id` `bigint` IDENTITY(1,1) NOT NULL,
`adr_state` `int` NULL,
`adr_name` `nvarchar`(50) NULL,
`adr_add` `nvarchar`(200) NULL,
`adr_cas_id` `bigint` NULL,
`adr_cat` `nvarchar`(50) NULL,
`adr_remark` `nvarchar`(max) NULL,
`adr_isdel` `char`(1) NULL,
`adr_num` `int` NULL,
`adr_check_app` `int` NULL,
`adr_mail_app` `int` NULL,
`adr_vis_app` `int` NULL,
`adr_rel` `nvarchar`(50) NULL,
`adr_mail_count` `int` NULL,
`adr_isnew` `int` NULL,
PRIMARY KEY (`adr_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_product`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_product`;
CREATE TABLE `wms_product`(
`wpr_id` `bigint` IDENTITY(1,1) NOT NULL,
`wpr_name` `nvarchar`(100) NULL,
`wpr_type_id` `bigint` NULL,
`wpr_model` `nvarchar`(100) NULL,
`wpr_unit` `bigint` NULL,
`wpr_color` `varchar`(50) NULL,
`wpr_size` `varchar`(50) NULL,
`wpr_provider` `nvarchar`(100) NULL,
`wpr_up_lim` `int` NULL,
`wpr_low_lim` `int` NULL,
`wpr_cost_prc` `decimal`(18, 2) NULL,
`wpr_sale_prc` `decimal`(18, 2) NULL,
`wpr_pic` `varchar`(max) NULL,
`wpr_cuser_code` `nvarchar`(50) NULL,
`wpr_cre_date` `datetime` NULL,
`wpr_euser_code` `nvarchar`(50) NULL,
`wpr_edit_date` `datetime` NULL,
`wpr_desc` `nvarchar`(max) NULL,
`wpr_remark` `nvarchar`(max) NULL,
`wpr_states` `char`(1) NULL,
`wpr_range` `nvarchar`(max) NULL,
`wpr_technology` `nvarchar`(max) NULL,
`wpr_problem` `nvarchar`(max) NULL,
`wpr_isdel` `char`(1) NULL,
`wpr_code` `varchar`(50) NULL,
`wpr_iscount` `char`(1) NULL,
PRIMARY KEY (`wpr_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_spo_pro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_spo_pro`;
CREATE TABLE `r_spo_pro`(
`rpp_id` `bigint` IDENTITY(1,1) NOT NULL,
`rpp_spo_id` `bigint` NULL,
`rpp_pro_id` `bigint` NULL,
`rpp_num` `decimal`(18, 2) NULL,
`rpp_price` `decimal`(18, 2) NULL,
`rpp_sum_mon` `decimal`(18, 2) NULL,
`rpp_remark` `nvarchar`(max) NULL,
`rpp_out_num` `decimal`(18, 2) NULL,
`rpp_real_num` `decimal`(18, 2) NULL,
PRIMARY KEY (`rpp_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`cus_province`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `cus_province`;
CREATE TABLE `cus_province`(
`prv_id` `bigint` IDENTITY(1,1) NOT NULL,
`prv_area_id` `bigint` NULL,
`prv_name` `nvarchar`(100) NULL,
`prv_isenabled` `varchar`(10) NULL,
PRIMARY KEY (`prv_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_supplier`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_supplier`;
CREATE TABLE `sal_supplier`(
`ssu_id` `bigint` IDENTITY(1,1) NOT NULL,
`ssu_code` `varchar`(300) NULL,
`ssu_name` `nvarchar`(100) NULL,
`ssu_phone` `varchar`(50) NULL,
`ssu_fex` `varchar`(50) NULL,
`ssu_email` `varchar`(50) NULL,
`ssu_net` `varchar`(200) NULL,
`ssu_add` `nvarchar`(max) NULL,
`ssu_prd` `nvarchar`(max) NULL,
`ssu_county` `bigint` NULL,
`ssu_pro` `bigint` NULL,
`ssu_city` `bigint` NULL,
`ssu_zip_code` `varchar`(50) NULL,
`ssu_bank` `nvarchar`(50) NULL,
`ssu_bank_code` `varchar`(50) NULL,
`ssu_isdel` `char`(1) NULL,
`ssu_remark` `nvarchar`(max) NULL,
`ssu_inp_user` `nvarchar`(50) NULL,
`ssu_cre_date` `datetime` NULL,
`ssu_alt_date` `datetime` NULL,
`ssu_alt_user` `nvarchar`(50) NULL,
`ssu_bank_name` `nvarchar`(50) NULL,
`ssu_type_id` `bigint` NULL,
PRIMARY KEY (`ssu_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_all_task`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_all_task`;
CREATE TABLE `sal_all_task`(
`sat_id` `bigint` IDENTITY(1,1) NOT NULL,
`sat_date` `varchar`(50) NULL,
`sat_se_no` `bigint` NULL,
`sat_inp_date` `datetime` NULL,
`sat_alt_date` `datetime` NULL,
`sat_inp_name` `nvarchar`(50) NULL,
`sat_alt_name` `nvarchar`(50) NULL,
`sat_ht_mon` `decimal`(18, 2) NULL,
`sat_paid_mon` `decimal`(18, 2) NULL,
`sat_cus_num` `int` NULL,
PRIMARY KEY (`sat_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`type_list`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `type_list`;
CREATE TABLE `type_list`(
`typ_id` `bigint` IDENTITY(1,1) NOT NULL,
`typ_name` `nvarchar`(50) NULL,
`typ_desc` `varchar`(max) NULL,
`typ_type` `varchar`(50) NULL,
`typ_isenabled` `char`(1) NULL,
PRIMARY KEY (`typ_id`)
)
END
;
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'`type_list`') AND name = N'IX_type_list')
CREATE INDEX `IX_type_list` ON `type_list` (`typ_name`);WITH (IGNORE_DUP_KEY = OFF)
;
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'`type_list`') AND name = N'IX_type_list_1')
CREATE INDEX `IX_type_list_1` ON `type_list` (`typ_type`);WITH (IGNORE_DUP_KEY = OFF)
;
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'`type_list`') AND name = N'IX_type_list_2')
CREATE INDEX `IX_type_list_2` ON `type_list` (`typ_isenabled`);WITH (IGNORE_DUP_KEY = OFF)
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`pho_red`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `pho_red`;
CREATE TABLE `pho_red`(
`pr_id` `bigint` IDENTITY(1,1) NOT NULL,
`pr_typ_id` `bigint` NULL,
`pr_contact` `nvarchar`(200) NULL,
`pr_cas_id` `bigint` NULL,
`pr_content` `nvarchar`(max) NULL,
`pr_time` `datetime` NULL,
`pr_se_no` `bigint` NULL,
`pr_con_type` `nvarchar`(50) NULL,
`pr_pa_id` `bigint` NULL,
`pr_name` `nvarchar`(50) NULL,
`pr_cat` `int` NULL,
`pr_ptp_date` `datetime` NULL,
`pr_ptp_num` `decimal`(18, 2) NULL,
`pr_rel` `nvarchar`(50) NULL,
`pr_state_id` `bigint` NULL,
`pr_negotiation` `nvarchar`(50) NULL,
`pr_cc_id` `bigint` NULL,
`pr_call_id` `varchar`(max) NULL,
`pr_sms_id` `varchar`(max) NULL,
PRIMARY KEY (`pr_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`spo_paid_plan`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `spo_paid_plan`;
CREATE TABLE `spo_paid_plan`(
`spp_id` `bigint` IDENTITY(1,1) NOT NULL,
`spp_spo_id` `bigint` NULL,
`spp_prm_date` `datetime` NULL,
`spp_pay_mon` `decimal`(18, 2) NULL,
`spp_inp_user` `nvarchar`(50) NULL,
`spp_resp` `varchar`(50) NULL,
`spp_isp` `char`(1) NULL,
`spp_cre_date` `datetime` NULL,
`spp_alt_date` `datetime` NULL,
`spp_alt_user` `nvarchar`(50) NULL,
`spp_isdel` `char`(1) NULL,
`spp_content` `nvarchar`(100) NULL,
PRIMARY KEY (`spp_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`empBankStat`') AND type in (N'P', N'PC'))
BEGIN
EXEC dbo.sp_executesql @statement = N'
-- =============================================
-- Author:		GM
-- Create date: 2011-1-7
-- Description:	电催员在案明细统计
-- =============================================
CREATE PROCEDURE `empBankStat`(
@sqlAppend  varchar(8000)
)
AS
BEGIN
--申明字符串变量，以供动态拼装
declare @sql longtext,@sql1 varchar(8000)
--将银行列放入临时表
set @sql1=''select typ_name as bank_name into ##tempBank 
from bank_case inner join type_list on cas_typ_bid=typ_id group by typ_name ''
if object_id(''tempdb..##tempBank'') is not null
begin
drop table ##tempBank
end
exec(@sql1)
--拼装SQL命令
set @sql = ''select se_no,max(se_code),max(se_name) as head''
--将银行旋转为表头
select @sql = @sql + '',
sum(case typ_name when ''''''+bank_name+'''''' then 1 else 0 end) [''+bank_name+''],
sum(case typ_name when ''''''+bank_name+'''''' then cas_m else 0 end) [''+bank_name+'']'' from (select bank_name from ##tempBank)as a 
--加上合计
select @sql = @sql+'',
count(cas_id)as 合计 , sum(cas_m)as 合计
from bank_case inner join sal_emp on cas_se_no=se_no inner join type_list on cas_typ_bid=typ_id 
where cas_state!=3 '' + @sqlAppend 
+ '' group by se_no with rollup''
print(@sql)
exec(@sql)
END
' 
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`pro_actor`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `pro_actor`;
CREATE TABLE `pro_actor`(
`act_id` `bigint` IDENTITY(1,1) NOT NULL,
`act_pro_id` `bigint` NULL,
`act_se_no` `bigint` NULL,
`act_isdel` `char`(1) NULL,
`act_duty` `nvarchar`(100) NULL,
PRIMARY KEY (`act_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sup_contact`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sup_contact`;
CREATE TABLE `sup_contact`(
`scn_id` `bigint` IDENTITY(1,1) NOT NULL,
`scn_ssu_id` `bigint` NULL,
`scn_name` `nvarchar`(50) NULL,
`scn_sex` `nvarchar`(50) NULL,
`scn_dep` `nvarchar`(max) NULL,
`scn_service` `nvarchar`(100) NULL,
`scn_phone` `varchar`(50) NULL,
`scn_work_pho` `varchar`(50) NULL,
`scn_home_pho` `varchar`(50) NULL,
`scn_fex` `varchar`(50) NULL,
`scn_zip_code` `varchar`(50) NULL,
`scn_email` `varchar`(100) NULL,
`scn_qq` `varchar`(50) NULL,
`scn_msn` `varchar`(100) NULL,
`scn_add` `nvarchar`(max) NULL,
`scn_oth_link` `nvarchar`(max) NULL,
`scn_remark` `nvarchar`(max) NULL,
`scn_inp_user` `nvarchar`(50) NULL,
`scn_upd_user` `nvarchar`(50) NULL,
`scn_cre_date` `datetime` NULL,
`scn_mod_date` `datetime` NULL,
`scn_isdel` `char`(1) NULL,
PRIMARY KEY (`scn_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`pro_task_lim`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `pro_task_lim`;
CREATE TABLE `pro_task_lim`(
`ptl_id` `bigint` IDENTITY(1,1) NOT NULL,
`ptl_prta_id` `bigint` NULL,
`ptl_se_no` `bigint` NULL,
`ptl_name` `nvarchar`(50) NULL,
`ptl_isfin` `char`(1) NULL,
`ptl_fin_date` `datetime` NULL,
`ptl_isdel` `char`(1) NULL,
`ptl_desc` `nvarchar`(max) NULL,
PRIMARY KEY (`ptl_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`cus_contact`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `cus_contact`;
CREATE TABLE `cus_contact`(
`con_id` `bigint` IDENTITY(1,1) NOT NULL,
`con_cor_code` `bigint` NULL,
`con_name` `nvarchar`(50) NULL,
`con_sex` `nvarchar`(10) NULL,
`con_dep` `nvarchar`(max) NULL,
`con_service` `nvarchar`(100) NULL,
`con_lev` `varchar`(50) NULL,
`con_phone` `varchar`(50) NULL,
`con_work_pho` `varchar`(50) NULL,
`con_home_pho` `varchar`(50) NULL,
`con_fex` `varchar`(50) NULL,
`con_zip_code` `varchar`(50) NULL,
`con_email` `varchar`(100) NULL,
`con_qq` `varchar`(50) NULL,
`con_msn` `varchar`(100) NULL,
`con_add` `nvarchar`(max) NULL,
`con_oth_link` `nvarchar`(max) NULL,
`con_bir` `datetime` NULL,
`con_hob` `nvarchar`(100) NULL,
`con_taboo` `nvarchar`(100) NULL,
`con_edu` `nvarchar`(100) NULL,
`con_photo` `varchar`(max) NULL,
`con_remark` `nvarchar`(max) NULL,
`con_cre_date` `datetime` NULL,
`con_mod_date` `datetime` NULL,
`con_inp_user` `nvarchar`(50) NULL,
`con_upd_user` `nvarchar`(50) NULL,
`con_isdel` `char`(1) NULL,
`con_type` `nvarchar`(50) NULL,
`con_user_code` `varchar`(50) NULL,
PRIMARY KEY (`con_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_shipment`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_shipment`;
CREATE TABLE `wms_shipment`(
`wsh_code` `varchar`(50) NOT NULL,
`wsh_wout_code` `varchar`(50) NULL,
`wsh_ord_code` `bigint` NULL,
`wsh_state` `char`(1) NULL,
`wsh_out_date` `datetime` NULL,
`wsh_inp_date` `datetime` NULL,
`wsh_user_code` `varchar`(50) NULL,
`wsh_rec_man` `nvarchar`(50) NULL,
`wsh_type` `nvarchar`(50) NULL,
`wsh_cost` `decimal`(18, 2) NULL,
`wsh_remark` `nvarchar`(max) NULL,
PRIMARY KEY (`wsh_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_wms_wms`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_wms_wms`;
CREATE TABLE `r_wms_wms`(
`rww_id` `bigint` IDENTITY(1,1) NOT NULL,
`wch_id` `bigint` NULL,
`rww_pro_id` `bigint` NULL,
`rww_num` `decimal`(18, 2) NULL,
`rww_remark` `nvarchar`(max) NULL,
PRIMARY KEY (`rww_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`cus_area`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `cus_area`;
CREATE TABLE `cus_area`(
`are_id` `bigint` IDENTITY(1,1) NOT NULL,
`are_name` `nvarchar`(100) NULL,
`are_isenabled` `varchar`(10) NULL,
PRIMARY KEY (`are_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`casePaidRateStat`') AND type in (N'P', N'PC'))
BEGIN
EXEC dbo.sp_executesql @statement = N'
-- =============================================
-- Author:		GM
-- Create date: 2012-5-20
-- Description:	单月还款率统计
-- =============================================
CREATE PROCEDURE `casePaidRateStat`(
@sqlAppend  varchar(8000)
)
AS
BEGIN
if object_id(''tempdb..##tempPaidRate'') is not null
begin
drop table ##tempPaidRate
end
if object_id(''tempdb..##tempPaidBat'') is not null
begin
drop table ##tempPaidBat
end
declare @paidSql longtext,@sql longtext
set @paidSql = ''select pa_se_no as se_id,cas_id,cas_cbat_id as bat_id,sum(pa_paid_num) as paid_sum,max(cas_m) as cas_m
into ##tempPaidRate
from case_paid 
inner join bank_case on pa_cas_id=cas_id 
''+@sqlAppend+''
group by cas_cbat_id,cas_id,pa_se_no''
--print(@paidSql)
exec(@paidSql)
select bat_id,max(cbat_code) as bat_code 
into ##tempPaidBat 
from ##tempPaidRate inner join case_bat on bat_id = cbat_id 
group by bat_id
--拼装SQL命令
set @sql = ''select max(se_name) as ''''head''''''
--将银行旋转为表头
select @sql = @sql + '',
sum(case bat_id when ''+convert(varchar(100),bat_id)+'' then paid_sum else 0 end) as ''''''+bat_code+'''''',
sum(case bat_id when ''+convert(varchar(100),bat_id)+'' then cas_m else 0 end) as ''''''+bat_code+'''''' '' from (select bat_id,bat_code from ##tempPaidBat)as pb 
--加上合计
select @sql = @sql+'',
sum(paid_sum)as 合计 , sum(cas_m)as 合计
from ##tempPaidRate inner join sal_emp on se_id=se_no
group by se_no with rollup''
--print(@sql)
exec(@sql)
END
' 
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_wout_pro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_wout_pro`;
CREATE TABLE `r_wout_pro`(
`rwo_id` `bigint` IDENTITY(1,1) NOT NULL,
`rwo_wout_id` `bigint` NULL,
`rwo_pro_id` `bigint` NULL,
`rwo_wout_num` `decimal`(18, 2) NULL,
`rwo_remark` `nvarchar`(max) NULL,
PRIMARY KEY (`rwo_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`case_int`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `case_int`;
CREATE TABLE `case_int`(
`cin_id` `bigint` IDENTITY(1,1) NOT NULL,
`cin_cas_id` `bigint` NULL,
`cin_name` `nvarchar`(50) NULL,
`cin_m_cat` `nvarchar`(200) NULL,
`cin_m` `decimal`(18, 2) NULL,
`cin_principal` `nvarchar`(200) NULL,
`cin_int` `nvarchar`(200) NULL,
`cin_overdue_paid` `nvarchar`(200) NULL,
`cin_over_limit` `nvarchar`(200) NULL,
`cin_service` `nvarchar`(200) NULL,
`cin_year` `nvarchar`(200) NULL,
`cin_other` `nvarchar`(200) NULL,
`cin_out` `nvarchar`(200) NULL,
`cin_ins_time` `datetime` NULL,
`cin_end_date` `varchar`(100) NULL,
`cin_damages_amt` `nvarchar`(200) NULL,
PRIMARY KEY (`cin_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_war_in`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_war_in`;
CREATE TABLE `wms_war_in`(
`wwi_id` `bigint` IDENTITY(1,1) NOT NULL,
`wwi_code` `varchar`(50) NULL,
`wwi_title` `nvarchar`(max) NULL,
`wwi_stro_code` `varchar`(50) NULL,
`wwi_user_code` `varchar`(50) NULL,
`wwi_state` `char`(1) NULL,
`wwi_remark` `nvarchar`(max) NULL,
`wwi_isdel` `char`(1) NULL,
`wwi_inp_name` `nvarchar`(50) NULL,
`wwi_alt_name` `nvarchar`(50) NULL,
`wwi_inp_time` `datetime` NULL,
`wwi_alt_time` `datetime` NULL,
`wwi_in_date` `datetime` NULL,
`wwi_app_date` `datetime` NULL,
`wwi_app_man` `nvarchar`(50) NULL,
`wwi_app_desc` `nvarchar`(max) NULL,
`wwi_app_isok` `char`(1) NULL,
`wwi_spo_code` `bigint` NULL,
`wwi_can_date` `datetime` NULL,
`wwi_can_man` `nvarchar`(50) NULL,
PRIMARY KEY (`wwi_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_win_pro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_win_pro`;
CREATE TABLE `r_win_pro`(
`rwi_id` `bigint` IDENTITY(1,1) NOT NULL,
`rwi_win_id` `bigint` NULL,
`rwi_pro_id` `bigint` NULL,
`rwi_win_num` `decimal`(18, 2) NULL,
`rwi_remark` `nvarchar`(max) NULL,
PRIMARY KEY (`rwi_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_stro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_stro`;
CREATE TABLE `wms_stro`(
`wms_code` `varchar`(50) NOT NULL,
`wms_name` `nvarchar`(300) NULL,
`wms_type_id` `bigint` NULL,
`wms_loc` `nvarchar`(max) NULL,
`wms_cre_date` `datetime` NULL,
`wms_user_code` `varchar`(50) NULL,
`wms_remark` `nvarchar`(max) NULL,
`wms_isenabled` `char`(1) NULL,
PRIMARY KEY (`wms_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`case_grp`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `case_grp`;
CREATE TABLE `case_grp`(
`cg_id` `bigint` IDENTITY(1,1) NOT NULL,
`cg_name` `nvarchar`(30) NULL,
`cg_user_code` `varchar`(50) NULL,
PRIMARY KEY (`cg_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`lim_right`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `lim_right`;
CREATE TABLE `lim_right`(
`rig_code` `varchar`(50) NOT NULL,
`rig_fun_code` `varchar`(50) NULL,
`rig_ope_code` `varchar`(50) NULL,
`rig_wms_name` `nvarchar`(300) NULL,
PRIMARY KEY (`rig_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`cus_city`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `cus_city`;
CREATE TABLE `cus_city`(
`city_id` `bigint` IDENTITY(1,1) NOT NULL,
`city_prv_id` `bigint` NULL,
`city_name` `nvarchar`(100) NULL,
`city_isenabled` `varchar`(10) NULL,
PRIMARY KEY (`city_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_change`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_change`;
CREATE TABLE `wms_change`(
`wch_id` `bigint` IDENTITY(1,1) NOT NULL,
`wch_code` `varchar`(50) NULL,
`wch_title` `nvarchar`(max) NULL,
`wch_state` `char`(1) NULL,
`wch_in_date` `datetime` NULL,
`wch_out_wms` `varchar`(50) NULL,
`wch_in_wms` `varchar`(50) NULL,
`wch_rec_man` `varchar`(50) NULL,
`wch_remark` `nvarchar`(max) NULL,
`wch_checkIn` `varchar`(50) NULL,
`wch_checkOut` `varchar`(50) NULL,
`wch_out_date` `datetime` NULL,
`wch_isdel` `char`(1) NULL,
`wch_in_time` `datetime` NULL,
`wch_out_time` `datetime` NULL,
`wch_inp_name` `nvarchar`(50) NULL,
`wch_inp_date` `datetime` NULL,
`wch_alt_name` `nvarchar`(50) NULL,
`wch_alt_date` `datetime` NULL,
`wch_app_date` `datetime` NULL,
`wch_app_man` `nvarchar`(50) NULL,
`wch_app_desc` `nvarchar`(max) NULL,
`wch_app_isok` `char`(1) NULL,
`wch_mat_name` `nvarchar`(50) NULL,
`wch_can_date` `datetime` NULL,
`wch_can_man` `nvarchar`(50) NULL,
PRIMARY KEY (`wch_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`attachment`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `attachment`;
CREATE TABLE `attachment`(
`att_id` `bigint` IDENTITY(1,1) NOT NULL,
`att_name` `nvarchar`(max) NULL,
`att_size` `bigint` NULL,
`att_path` `varchar`(max) NULL,
`att_isJunk` `char`(1) NULL,
`att_date` `datetime` NULL,
`att_type` `varchar`(100) NULL,
`att_fk_id` `bigint` NULL,
`att_file_type` `varchar`(100) NULL,
PRIMARY KEY (`att_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`lim_function`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `lim_function`;
CREATE TABLE `lim_function`(
`fun_code` `varchar`(50) NOT NULL,
`fun_desc` `nvarchar`(max) NULL,
`fun_type` `varchar`(50) NULL,
PRIMARY KEY (`fun_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`bank_case`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `bank_case`;
CREATE TABLE `bank_case`(
`cas_id` `bigint` IDENTITY(1,1) NOT NULL,
`cas_code` `varchar`(100) NULL,
`cas_group` `varchar`(50) NULL,
`cas_state` `int` NULL,
`cas_typ_hid` `bigint` NULL,
`cas_out_state` `int` NULL,
`cas_cbat_id` `bigint` NULL,
`cas_m` `decimal`(18, 2) NULL,
`cas_ptp_m` `decimal`(18, 2) NULL,
`cas_cp_m` `decimal`(18, 2) NULL,
`cas_paid_m` `decimal`(18, 2) NULL,
`cas_date` `datetime` NULL,
`cas_typ_bid` `bigint` NULL,
`cas_name` `nvarchar`(50) NULL,
`cas_sex` `nvarchar`(1) NULL,
`cas_ca_cd` `varchar`(50) NULL,
`cas_num` `varchar`(50) NULL,
`cas_area_1``nvarchar`(50) NULL,
`cas_area_2``nvarchar`(50) NULL,
`cas_area_3``nvarchar`(50) NULL,
`cas_post_code` `varchar`(50) NULL,
`cas_se_no` `bigint` NULL,
`cas_ins_user` `nvarchar`(25) NULL,
`cas_ins_time` `datetime` NULL,
`cas_alt_user` `nvarchar`(25) NULL,
`cas_alt_time` `datetime` NULL,
`cas_tremark` `nvarchar`(300) NULL,
`cas_warn` `nvarchar`(300) NULL,
`cas_acc_num` `varchar`(100) NULL,
`cas_card_cat` `nvarchar`(200) NULL,
`cas_principal` `nvarchar`(200) NULL,
`cas_min_paid` `nvarchar`(200) NULL,
`cas_cred_lim` `nvarchar`(200) NULL,
`cas_delay_lv` `nvarchar`(200) NULL,
`cas_gua_m` `nvarchar`(200) NULL,
`cas_m_cat` `nvarchar`(200) NULL,
`cas_pre_rec` `nvarchar`(max) NULL,
`cas_exc_lim` `nvarchar`(200) NULL,
`cas_unit_name` `nvarchar`(200) NULL,
`cas_m_p` `double` NULL,
`cas_name_1` `nvarchar`(50) NULL,
`cas_name_2` `nvarchar`(50) NULL,
`cas_name_3` `nvarchar`(50) NULL,
`cas_num_1` `nvarchar`(200) NULL,
`cas_num_2` `nvarchar`(200) NULL,
`cas_num_3` `nvarchar`(200) NULL,
`cas_re_1` `nvarchar`(200) NULL,
`cas_re_2` `nvarchar`(200) NULL,
`cas_re_3` `nvarchar`(200) NULL,
`cas_con_com1` `nvarchar`(200) NULL,
`cas_pr_time` `datetime` NULL,
`cas_remark` `nvarchar`(max) NULL,
`cas_con_com2` `nvarchar`(200) NULL,
`cas_con_com3` `nvarchar`(200) NULL,
`cas_app_1` `int` NULL,
`cas_app_2` `int` NULL,
`cas_app_3` `int` NULL,
`cas_app_4` `int` NULL,
`cas_app_5` `int` NULL,
`cas_app_6` `int` NULL,
`cas_app_7` `int` NULL,
`cas_app_8` `int` NULL,
`cas_app_9` `int` NULL,
`cas_app_10` `int` NULL,
`cas_app_11` `int` NULL,
`cas_app_12` `int` NULL,
`cas_app_13` `int` NULL,
`cas_app_14` `int` NULL,
`cas_remark2` `nvarchar`(max) NULL,
`cas_remark3` `nvarchar`(max) NULL,
`cas_remark4` `nvarchar`(max) NULL,
`cas_ptp_c` `int` NULL,
`cas_remark5` `nvarchar`(max) NULL,
`cas_card_bank` `nvarchar`(200) NULL,
`cas_tip_time` `datetime` NULL,
`cas_hom_pho` `varchar`(50) NULL,
`cas_work_pho` `varchar`(50) NULL,
`cas_mob_pho` `varchar`(50) NULL,
`cas_hom_add` `varchar`(max) NULL,
`cas_work_add` `varchar`(max) NULL,
`cas_mail_add` `varchar`(max) NULL,
`cas_reg_add` `varchar`(max) NULL,
`cas_con_pho1` `varchar`(50) NULL,
`cas_con_mob1` `varchar`(50) NULL,
`cas_con_add1` `varchar`(max) NULL,
`cas_con_pho2` `varchar`(50) NULL,
`cas_con_mob2` `varchar`(50) NULL,
`cas_con_add2` `varchar`(max) NULL,
`cas_loan_type` `nvarchar`(200) NULL,
`cas_coll_type` `nvarchar`(200) NULL,
`cas_int` `nvarchar`(200) NULL,
`cas_overdue_paid` `nvarchar`(200) NULL,
`cas_cre_paid` `nvarchar`(200) NULL,
`cas_paid_lim` `nvarchar`(200) NULL,
`cas_paid_date` `nvarchar`(200) NULL,
`cas_con_date` `nvarchar`(200) NULL,
`cas_rai_date` `nvarchar`(200) NULL,
`cas_stop_date` `nvarchar`(200) NULL,
`cas_cre_date` `nvarchar`(200) NULL,
`cas_remark6` `nvarchar`(max) NULL,
`cas_note` `nvarchar`(max) NULL,
`cas_con_pho3` `varchar`(50) NULL,
`cas_con_mob3` `varchar`(50) NULL,
`cas_con_add3` `varchar`(max) NULL,
`cas_con_pho4` `varchar`(50) NULL,
`cas_con_mob4` `varchar`(50) NULL,
`cas_con_add4` `varchar`(max) NULL,
`cas_name_4` `nvarchar`(50) NULL,
`cas_num_4` `nvarchar`(200) NULL,
`cas_re_4` `nvarchar`(200) NULL,
`cas_con_com4` `nvarchar`(200) NULL,
`cas_file_no` `nvarchar`(100) NULL,
`cas_remark7` `nvarchar`(max) NULL,
`cas_remark8` `nvarchar`(max) NULL,
`cas_email` `varchar`(100) NULL,
`cas_is_oth` `int` NULL,
`cas_is_newpr` `int` NULL,
`cas_is_newpaid` `int` NULL,
`cas_is_paidover` `int` NULL,
`cas_is_updint` `int` NULL,
`cas_rmb` `varchar`(100) NULL,
`cas_gb` `varchar`(100) NULL,
`cas_my` `varchar`(100) NULL,
`cas_pos` `nvarchar`(200) NULL,
`cas_part` `nvarchar`(200) NULL,
`cas_backdate_p` `datetime` NULL,
`cas_backdate` `datetime` NULL,
`cas_back_p` `double` NULL,
`cas_con_wpho1` `varchar`(50) NULL,
`cas_con_wpho2` `varchar`(50) NULL,
`cas_con_wpho3` `varchar`(50) NULL,
`cas_con_wpho4` `varchar`(50) NULL,
`cas_name_u` `nvarchar`(50) NULL,
`cas_num_u` `nvarchar`(200) NULL,
`cas_re_u` `nvarchar`(200) NULL,
`cas_con_u_com` `nvarchar`(200) NULL,
`cas_con_u_wpho` `varchar`(50) NULL,
`cas_con_u_pho` `varchar`(50) NULL,
`cas_con_u_mob` `varchar`(50) NULL,
`cas_con_u_add` `varchar`(max) NULL,
`cas_back_m` `decimal`(18, 2) NULL,
`cas_name_5` `nvarchar`(50) NULL,
`cas_num_5` `nvarchar`(200) NULL,
`cas_re_5` `nvarchar`(200) NULL,
`cas_con_com_5` `nvarchar`(200) NULL,
`cas_con_wpho_5` `varchar`(50) NULL,
`cas_con_pho_5` `varchar`(50) NULL,
`cas_con_mob_5` `varchar`(50) NULL,
`cas_con_add_5` `varchar`(max) NULL,
`cas_loan_date` `varchar`(200) NULL,
`cas_app_no` `varchar`(100) NULL,
`cas_paid_count` `varchar`(100) NULL,
`cas_so_pcno` `varchar`(100) NULL,
`cas_so_no` `varchar`(100) NULL,
`cas_overdue_date` `varchar`(200) NULL,
`cas_pback_p` `double` NULL,
`cas_wpost_code` `varchar`(50) NULL,
`cas_deadline` `varchar`(200) NULL,
`cas_is_host` `nvarchar`(50) NULL,
`cas_bill_date` `varchar`(200) NULL,
`cas_last_paid` `nvarchar`(200) NULL,
`cas_count` `varchar`(100) NULL,
`cas_left_pri` `varchar`(100) NULL,
`cas_assign_ids` `varchar`(max) NULL,
`cas_assign_names` `nvarchar`(max) NULL,
`cas_last_assign_time` `datetime` NULL,
`cas_overdue_days` `int` NULL,
`cas_overdue_days_str` `varchar`(200) NULL,
`cas_bir` `varchar`(50) NULL,
`cas_mpost_code` `varchar`(50) NULL,
`cas_perm_crline` `varchar`(50) NULL,
`cas_alt_hold` `nvarchar`(50) NULL,
`cas_cycle` `varchar`(50) NULL,
`cas_noout` `varchar`(50) NULL,
`cas_field_type` `varchar`(50) NULL,
`cas_cl_area_id` `bigint` NULL,
`cas_pr_count` `int` NULL,
`cas_overdue_m` `varchar`(200) NULL,
`cas_overdue_num` `varchar`(200) NULL,
`cas_overdue_once` `int` NULL,
`cas_loan_rate` `varchar`(200) NULL,
`cas_month_paid` `varchar`(200) NULL,
`cas_last_vis` `datetime` NULL,
`cas_fst_cl_paid_date` `datetime` NULL,
`cas_last_cl_paid_date` `datetime` NULL,
`cas_color` `int` NULL,
`cas_cc_id` `bigint` NULL,
`cas_is_newass` `int` NULL,
`cas_reg_post_code` `varchar`(50) NULL,
`cas_last_m` `decimal`(18,2) NULL,
`cas_last_int_date` `datetime` NULL,
`cas_loan_end_date` `varchar`(200) NULL,
`cas_over_limit` `nvarchar`(200) NULL,
`cas_num_type` `nvarchar`(50) NULL,
`cas_last_end_date` `varchar`(100) NULL,
`cas_assign_times` `varchar`(max) NULL,
`cas_cl_count` `nvarchar`(100) NULL,
PRIMARY KEY (`cas_id`)
)
END
;
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'`bank_case`') AND name = N'IX_bank_case_1')
CREATE INDEX `IX_bank_case_1` ON `bank_case` (`cas_se_no`);WITH (IGNORE_DUP_KEY = OFF)
;
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'`bank_case`') AND name = N'IX_case_code')
CREATE INDEX `IX_case_code` ON `bank_case` (`cas_code`);WITH (IGNORE_DUP_KEY = OFF)
;
create index IX_cnum on bank_case(cas_num);
create index IX_cacd on bank_case(cas_ca_cd);
create index IX_bank on bank_case(cas_typ_bid);
create index IX_cl_area on bank_case(cas_cl_area_id);
create index IX_bat on bank_case(cas_cbat_id);
create index IX_file_no on bank_case(cas_file_no);
create index IX_acc_date on bank_case(cas_acc_num, cas_date);
create index IX_cacd_date on bank_case(cas_ca_cd, cas_date);
;
;
;
DROP TABLE IF EXISTS `oth_card`;
CREATE TABLE `oth_card`(
`ocd_id` `bigint` IDENTITY(1,1) NOT NULL,
`ocd_cas_id` `bigint` NULL,
`ocd_area` `nvarchar`(50) NULL,
`ocd_acct` `varchar`(50) NULL,
`ocd_id_no` `varchar`(50) NULL,
`ocd_card` `varchar`(50) NULL,
`ocd_name` `nvarchar`(50) NULL,
`ocd_h_pho` `varchar`(50) NULL,
`ocd_o_pho` `varchar`(50) NULL,
`ocd_addr` `nvarchar`(200) NULL,
`ocd_employer` `nvarchar`(100) NULL,
`ocd_alt_name` `nvarchar`(50) NULL,
`ocd_alt_h_pho` `varchar`(50) NULL,
`ocd_alt_o_pho` `varchar`(50) NULL,
`ocd_con_name` `nvarchar`(50) NULL,
`ocd_con_pho` `varchar`(50) NULL,
`ocd_bir` `varchar`(50) NULL,
`ocd_r_addr` `nvarchar`(200) NULL,
`ocd_cyc` `varchar`(50) NULL,
`ocd_blk` `varchar`(50) NULL,
`ocd_m_post` `varchar`(50) NULL,
`ocd_msg` `nvarchar`(200) NULL,
`ocd_cre_man` `nvarchar`(50) NULL,
`ocd_cre_time` `datetime` NULL,
`ocd_upd_man` `nvarchar`(50) NULL,
`ocd_upd_time` `datetime` NULL,
PRIMARY KEY (`ocd_id`)
)
;
;
;
;
DROP TABLE IF EXISTS `pa_bank_case`;
CREATE TABLE `pa_bank_case`(
`cas_id` `bigint` NOT NULL,
`cas_policy_man` `nvarchar`(50) NULL,
`cas_day_paid` `varchar`(100) NULL,
`cas_cla_date` `varchar`(100) NULL,
`cas_cla_m` `varchar`(100) NULL,
`cas_eff_date` `varchar`(100) NULL,
`cas_premiums` `varchar`(100) NULL,
`cas_manage_cost` `varchar`(100) NULL,
`cas_penalty` `varchar`(100) NULL,
`cas_fail_cost` `varchar`(100) NULL,
`cas_is_wd` `varchar`(100) NULL,
`cas_acc_name` `nvarchar`(100) NULL,
`cas_house_type1` `nvarchar`(100) NULL,
`cas_house_own1` `nvarchar`(100) NULL,
`cas_hfax1` `varchar`(100) NULL,
`cas_addr2` `nvarchar`(500) NULL,
`cas_house_type2` `nvarchar`(100) NULL,
`cas_house_own2` `nvarchar`(100) NULL,
`cas_pho2` `varchar`(100) NULL,
`cas_hfax2` `varchar`(100) NULL,
`cas_com2` `nvarchar`(200) NULL,
`cas_com2_addr` `nvarchar`(500) NULL,
`cas_wpho2` `varchar`(100) NULL,
`cas_fax1` `varchar`(100) NULL,
`cas_fax2` `varchar`(100) NULL,
`cas_com_type` `nvarchar`(50) NULL,
`cas_com_date` `varchar`(100) NULL,
`cas_tax_no` `varchar`(100) NULL,
`cas_com_no` `varchar`(100) NULL,
`cas_con_mob4b` `varchar`(100) NULL,
`cas_con_wpho4` `varchar`(100) NULL,
`cas_con_mob1b` `varchar`(100) NULL,
`cas_con_mob2b` `varchar`(100) NULL,
`cas_con_mob3b` `varchar`(100) NULL,
PRIMARY KEY (`cas_id`)
)
;
;
;
;
DROP TABLE IF EXISTS `js_bank_case`;
CREATE TABLE `js_bank_case`(
`cas_id` `bigint` NOT NULL,
`jsc_overdue_p` `varchar`(200) NULL,
`jsc_un_overdue_p` `varchar`(200) NULL,
`jsc_man` `nvarchar`(100) NULL,
`jsc_cd` `varchar`(200) NULL,
`jsc_is_married` `nvarchar`(50) NULL,
`jsc_oth_pho` `varchar`(200) NULL,
`jsc_ide` `nvarchar`(100) NULL,
`jsc_home_prop` `nvarchar`(100) NULL,
`jsc_live_with` `nvarchar`(100) NULL,
`jsc_haddr_able` `varchar`(200) NULL,
`jsc_waddr_able` `varchar`(200) NULL,
`jsc_com_prop` `nvarchar`(100) NULL,
`jsc_age1` `varchar`(50) NULL,
`jsc_pos1` `nvarchar`(200) NULL,
`jsc_age2` `varchar`(50) NULL,
`jsc_pos2` `nvarchar`(200) NULL,
`jsc_age3` `varchar`(50) NULL,
`jsc_pos3` `nvarchar`(200) NULL,
`jsc_cost` `varchar`(200) NULL,
`jsc_posu` `nvarchar`(200) NULL,
PRIMARY KEY (`cas_id`)
)
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`project`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project`(
`pro_id` `bigint` IDENTITY(1,1) NOT NULL,
`pro_user_code` `varchar`(50) NULL,
`pro_typ_id` `bigint` NULL,
`pro_title` `nvarchar`(300) NULL,
`pro_state` `varchar`(50) NULL,
`pro_cre_date` `datetime` NULL,
`pro_fin_date` `datetime` NULL,
`pro_desc` `nvarchar`(max) NULL,
`pro_remark` `nvarchar`(max) NULL,
`pro_inp_user` `nvarchar`(50) NULL,
`pro_upd_user` `nvarchar`(50) NULL,
`pro_ins_date` `datetime` NULL,
`pro_mod_date` `datetime` NULL,
`pro_isdel` `char`(1) NULL,
`pro_cor_code` `bigint` NULL,
`pro_period` `varchar`(50) NULL,
`pro_pro` `nvarchar`(300) NULL,
`pro_pro_log` `nvarchar`(max) NULL,
PRIMARY KEY (`pro_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_war_out`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_war_out`;
CREATE TABLE `wms_war_out`(
`wwo_id` `bigint` IDENTITY(1,1) NOT NULL,
`wwo_ord_code` `bigint` NULL,
`wwo_title` `nvarchar`(max) NULL,
`wwo_stro_code` `varchar`(50) NULL,
`wwo_user_code` `varchar`(50) NULL,
`wwo_inp_date` `datetime` NULL,
`wwo_out_date` `datetime` NULL,
`wwo_state` `char`(1) NULL,
`wwo_remark` `nvarchar`(max) NULL,
`wwo_isdel` `char`(1) NULL,
`wwo_inp_name` `nvarchar`(50) NULL,
`wwo_alt_name` `nvarchar`(50) NULL,
`wwo_user_name` `nvarchar`(50) NULL,
`wwo_res_name` `nvarchar`(50) NULL,
`wwo_alt_date` `datetime` NULL,
`wwo_code` `varchar`(50) NULL,
`wwo_app_isok` `char`(1) NULL,
`wwo_app_date` `datetime` NULL,
`wwo_app_man` `nvarchar`(50) NULL,
`wwo_app_desc` `nvarchar`(max) NULL,
`wwo_can_date` `datetime` NULL,
`wwo_can_man` `nvarchar`(50) NULL,
PRIMARY KEY (`wwo_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_wms_change`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_wms_change`;
CREATE TABLE `r_wms_change`(
`rwc_id` `bigint` IDENTITY(1,1) NOT NULL,
`rwc_pro_id` `bigint` NULL,
`rwc_different` `decimal`(18, 2) NULL,
`rmc_type` `varchar`(50) NULL,
`rmc_remark` `nvarchar`(max) NULL,
`rwc_wmc_code` `bigint` NULL,
`rmc_wms_count` `decimal`(18, 2) NULL,
`rmc_real_num` `decimal`(18, 2) NULL,
PRIMARY KEY (`rwc_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_user_rig`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_user_rig`;
CREATE TABLE `r_user_rig`(
`rur_id` `bigint` IDENTITY(1,1) NOT NULL,
`rur_user_code` `varchar`(50) NULL,
`rur_rig_code` `varchar`(50) NULL,
`rur_type` `varchar`(50) NULL,
PRIMARY KEY (`rur_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`case_bat`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `case_bat`;
CREATE TABLE `case_bat`(
`cbat_id` `bigint` IDENTITY(1,1) NOT NULL,
`cbat_code` `varchar`(50) NULL,
`cbat_typ_bid` `bigint` NULL,
`cbat_date` `datetime` NULL,
`cbat_type_id` `bigint` NULL,
`cbat_backdate_p` `datetime` NULL,
`cbat_backdate` `datetime` NULL,
`cbat_ins_user` `nvarchar`(25) NULL,
`cbat_ins_date` `datetime` NULL,
`cbat_state` `int` NULL,
`cbat_num` `int` NULL,
`cbat_mon` `decimal`(18, 2) NULL,
`cbat_log` `nvarchar`(max) NULL,
`cbat_xls` `varchar`(max) NULL,
`cbat_up_date` `datetime` NULL,
`cbat_remark` `nvarchar`(max) NULL,
`cbat_tips` `nvarchar`(200) NULL,
`cbat_area_id` `bigint` NULL,
`cbat_target` `double` NULL,
PRIMARY KEY (`cbat_id`)
)
END
;
;
;
;
DROP TABLE IF EXISTS `extra_inf`;
CREATE TABLE `extra_inf`(
`exi_id` `bigint` IDENTITY(1,1) NOT NULL,
`exi_id_number` `varchar`(50) NULL,
`exi_type` `nvarchar`(50) NULL,
`exi_content` `text` NULL,
`exi_cre_time` `datetime` NULL,
`exi_cre_man` `nvarchar`(25) NULL,
`exi_upd_time` `datetime` NULL,
`exi_upd_man` `nvarchar`(25) NULL,
`exi_name` `nvarchar`(50) NULL,
`exi_remark` `text` NULL,
PRIMARY KEY (`exi_id`)
) TEXTIMAGE_ON `PRIMARY`
;
;
;
;
DROP TABLE IF EXISTS `case_collection`;
CREATE TABLE `case_collection`(
`cc_id` `bigint` IDENTITY(1,1) NOT NULL,
`cc_cas_ids` `varchar`(max) NULL,
`cc_cbat_id` `bigint` NULL,
`cc_id_no` `varchar`(50) NULL,
PRIMARY KEY (`cc_id`)
)
;
;
;
;
DROP TABLE IF EXISTS `user_area`;
CREATE TABLE `user_area`(
`uar_id` `bigint` IDENTITY(1,1) NOT NULL,
`uar_user_code` `varchar`(50) NULL,
`uar_area_id` `bigint` NULL,
PRIMARY KEY (`uar_id`)
)
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`pro_stage`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `pro_stage`;
CREATE TABLE `pro_stage`(
`sta_id` `bigint` IDENTITY(1,1) NOT NULL,
`sta_pro_id` `bigint` NULL,
`sta_title` `nvarchar`(300) NULL,
`sta_aim` `nvarchar`(300) NULL,
`sta_start_date` `datetime` NULL,
`sta_end_date` `datetime` NULL,
`sta_remark` `nvarchar`(max) NULL,
`sta_ins_date` `datetime` NULL,
`sta_mod_date` `datetime` NULL,
`sta_isdel` `char`(1) NULL,
`sta_inp_user` `nvarchar`(50) NULL,
`sta_upd_user` `nvarchar`(50) NULL,
PRIMARY KEY (`sta_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_org`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_org`;
CREATE TABLE `sal_org`(
`so_code` `varchar`(50) NOT NULL,
`so_name` `nvarchar`(50) NULL,
`so_con_area` `nvarchar`(max) NULL,
`so_loc` `nvarchar`(max) NULL,
`so_user_code` `varchar`(50) NULL,
`so_emp_num` `varchar`(50) NULL,
`so_resp` `nvarchar`(max) NULL,
`so_org_code` `varchar`(50) NULL,
`so_remark` `nvarchar`(max) NULL,
`so_isenabled` `char`(1) NULL,
`so_up_code` `varchar`(50) NULL,
`so_cost_center` `nvarchar`(100) NULL,
`so_org_nature` `nvarchar`(100) NULL,
PRIMARY KEY (`so_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_ord_pro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_ord_pro`;
CREATE TABLE `r_ord_pro`(
`rop_id` `bigint` IDENTITY(1,1) NOT NULL,
`rop_ord_code` `bigint` NULL,
`rop_pro_id` `bigint` NULL,
`rop_num` `decimal`(18, 2) NULL,
`rop_real_price` `decimal`(18, 2) NULL,
`rop_remark` `nvarchar`(max) NULL,
`rop_price` `decimal`(18, 2) NULL,
`rop_zk` `varchar`(50) NULL,
`rop_out_num` `decimal`(18, 2) NULL,
`rop_real_num` `decimal`(18, 2) NULL,
PRIMARY KEY (`rop_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`message`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `message`;
CREATE TABLE `message`(
`me_code` `bigint` IDENTITY(1,1) NOT NULL,
`me_title` `nvarchar`(100) NULL,
`me_content` `nvarchar`(max) NULL,
`me_se_no` `bigint` NULL,
`me_date` `datetime` NULL,
`me_issend` `char`(1) NULL,
`me_isdel` `char`(1) NULL,
`me_ins_user` `nvarchar`(50) NULL,
`me_rec_name` `nvarchar`(max) NULL,
PRIMARY KEY (`me_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`lock_table`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `lock_table`;
CREATE TABLE `lock_table`(
`table_name` `varchar`(50) NOT NULL,
`table_max` `bigint` NULL,
PRIMARY KEY (`table_name`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_mess_lim`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_mess_lim`;
CREATE TABLE `r_mess_lim`(
`rml_id` `bigint` IDENTITY(1,1) NOT NULL,
`rml_me_code` `bigint` NULL,
`rml_se_no` `bigint` NULL,
`rml_date` `datetime` NULL,
`rml_isdel` `char`(1) NULL,
`rml_isread` `char`(1) NULL,
`rml_isreply` `char`(1) NULL,
`rml_rec_user` `nvarchar`(50) NULL,
`rml_state` `char`(1) NULL,
PRIMARY KEY (`rml_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_pro_type`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_pro_type`;
CREATE TABLE `wms_pro_type`(
`wpt_id` `bigint` IDENTITY(1,1) NOT NULL,
`wpt_name` `nvarchar`(50) NULL,
`wpt_desc` `nvarchar`(max) NULL,
`wpt_isenabled` `varchar`(10) NULL,
`wpt_up_id` `bigint` NULL,
PRIMARY KEY (`wpt_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`news`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news`(
`new_code` `bigint` IDENTITY(1,1) NOT NULL,
`new_title` `nvarchar`(100) NULL,
`new_type` `nvarchar`(100) NULL,
`new_se_no` `bigint` NULL,
`new_content` `nvarchar`(max) NULL,
`new_istop` `char`(1) NULL,
`new_date` `datetime` NULL,
`new_ins_user` `nvarchar`(50) NULL,
`new_upd_user` `nvarchar`(50) NULL,
`new_upd_date` `datetime` NULL,
PRIMARY KEY (`new_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`lim_operate`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `lim_operate`;
CREATE TABLE `lim_operate`(
`ope_code` `varchar`(50) NOT NULL,
`ope_desc` `nvarchar`(max) NULL,
PRIMARY KEY (`ope_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`schedule`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `schedule`;
CREATE TABLE `schedule`(
`sch_id` `bigint` IDENTITY(1,1) NOT NULL,
`sch_type` `bigint` NULL,
`sch_title` `nvarchar`(100) NULL,
`sch_start_date` `datetime` NULL,
`sch_se_no` `bigint` NULL,
`sch_start_time` `varchar`(50) NULL,
`sch_date` `datetime` NULL,
`sch_state` `varchar`(50) NULL,
`sch_end_time` `varchar`(50) NULL,
`sch_ins_user` `nvarchar`(50) NULL,
`sch_upd_user` `nvarchar`(50) NULL,
`sch_upd_date` `datetime` NULL,
PRIMARY KEY (`sch_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_invoice`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_invoice`;
CREATE TABLE `sal_invoice`(
`sin_id` `bigint` IDENTITY(1,1) NOT NULL,
`sin_ord_code` `bigint` NULL,
`sin_con` `nvarchar`(max) NULL,
`sin_type` `bigint` NULL,
`sin_mon` `decimal`(18, 2) NULL,
`sin_date` `datetime` NULL,
`sin_remark` `nvarchar`(max) NULL,
`sin_code` `varchar`(100) NULL,
`sin_isPaid` `char`(1) NULL,
`sin_isPlaned` `nvarchar`(50) NULL,
`sin_user_code` `nvarchar`(50) NULL,
`sin_resp` `nvarchar`(50) NULL,
`sin_mon_type` `nvarchar`(50) NULL,
`sin_alt_user` `nvarchar`(50) NULL,
`sin_cre_date` `datetime` NULL,
`sin_alt_date` `datetime` NULL,
`sin_isdel` `char`(1) NULL,
`sin_spo_id` `bigint` NULL,
`sin_isrecieve` `char`(1) NULL,
PRIMARY KEY (`sin_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`report`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `report`;
CREATE TABLE `report`(
`rep_code` `bigint` IDENTITY(1,1) NOT NULL,
`rep_title` `nvarchar`(100) NULL,
`rep_content` `nvarchar`(max) NULL,
`rep_se_no` `bigint` NULL,
`rep_appro_content` `nvarchar`(max) NULL,
`rep_isappro` `char`(1) NULL,
`rep_date` `datetime` NULL,
`rep_type` `bigint` NULL,
`rep_isdel` `char`(1) NULL,
`rep_issend` `char`(1) NULL,
`rep_send_title` `nvarchar`(100) NULL,
`rep_ins_user` `nvarchar`(50) NULL,
`rep_rec_name` `nvarchar`(max) NULL,
PRIMARY KEY (`rep_code`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`acc_trans`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `acc_trans`;
CREATE TABLE `acc_trans`(
`atr_id` `bigint` IDENTITY(1,1) NOT NULL,
`atr_code` `varchar`(50) NULL,
`atr_date` `datetime` NULL,
`atr_mon` `decimal`(18, 2) NULL,
`atr_type_id` `bigint` NULL,
`atr_in_aco` `bigint` NULL,
`atr_out_aco` `bigint` NULL,
`atr_remark` `nvarchar`(max) NULL,
`atr_isdel` `char`(1) NULL,
`atr_inp_user` `nvarchar`(50) NULL,
`atr_cre_date` `datetime` NULL,
`atr_undo_user` `nvarchar`(50) NULL,
`atr_undo_date` `datetime` NULL,
`atr_content` `nvarchar`(100) NULL,
PRIMARY KEY (`atr_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`acc_line`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `acc_line`;
CREATE TABLE `acc_line`(
`acl_id` `bigint` IDENTITY(1,1) NOT NULL,
`acl_aco_id` `bigint` NULL,
`acl_type` `nvarchar`(100) NULL,
`acl_note_id` `varchar`(300) NULL,
`acl_mon` `decimal`(18, 2) NULL,
`acl_cur_mon` `decimal`(18, 2) NULL,
`acl_cre_date` `datetime` NULL,
`acl_isInv` `char`(1) NULL,
`acl_content` `nvarchar`(100) NULL,
`acl_user` `nvarchar`(50) NULL,
`acl_other` `nvarchar`(100) NULL,
PRIMARY KEY (`acl_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`vis_record`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `vis_record`;
CREATE TABLE `vis_record`(
`vr_id` `bigint` IDENTITY(1,1) NOT NULL,
`vr_state` `int` NULL,
`vr_adr_id` `bigint` NULL,
`vr_cas_id` `bigint` NULL,
`vr_num` `int` NULL,
`vr_typ_id1` `bigint` NULL,
`vr_typ_id2` `bigint` NULL,
`vr_typ_id3` `bigint` NULL,
`vr_typ_id` `bigint` NULL,
`vr_name` `nvarchar`(50) NULL,
`vr_sex` `nvarchar`(1) NULL,
`vr_age` `int` NULL,
`vr_req` `nvarchar`(max) NULL,
`vr_remark` `nvarchar`(max) NULL,
`vr_report` `nvarchar`(max) NULL,
`vr_est_date` `datetime` NULL,
`vr_rel_date` `datetime` NULL,
`vr_app_user` `nvarchar`(25) NULL,
`vr_app_time` `datetime` NULL,
`vr_bk_time` `datetime` NULL,
`vr_rec_user` `nvarchar`(max) NULL,
`vr_adr` `nvarchar`(200) NULL,
`vr_rs` `varchar`(50) NULL,
`vr_is_prt` `char`(1) NULL,
PRIMARY KEY (`vr_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_task`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_task`;
CREATE TABLE `sal_task`(
`st_id` `bigint` IDENTITY(1,1) NOT NULL,
`st_title` `nvarchar`(200) NULL,
`st_se_no` `bigint` NULL,
`st_name` `nvarchar`(50) NULL,
`st_rel_date` `datetime` NULL,
`st_fin_date` `datetime` NULL,
`st_lev` `varchar`(50) NULL,
`st_cyc` `nvarchar`(50) NULL,
`st_type_id` `bigint` NULL,
`st_stu` `char`(1) NULL,
`st_mon` `decimal`(18, 2) NULL,
`st_tag` `nvarchar`(max) NULL,
`st_remark` `nvarchar`(max) NULL,
`st_change_date` `datetime` NULL,
`st_log` `nvarchar`(max) NULL,
`st_isdel` `char`(1) NULL,
`st_fct_date` `datetime` NULL,
`st_upd_user` `nvarchar`(50) NULL,
`st_start_date` `datetime` NULL,
PRIMARY KEY (`st_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`vis_rec_ass`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `vis_rec_ass`;
CREATE TABLE `vis_rec_ass`(
`vra_id` `bigint` IDENTITY(1,1) NOT NULL,
`vra_vr_id` `bigint` NULL,
`vra_user_code` `varchar`(50) NULL,
PRIMARY KEY (`vra_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`acc_lock`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `acc_lock`;
CREATE TABLE `acc_lock`(
`table_name` `varchar`(50) NOT NULL,
`table_max` `bigint` NULL,
PRIMARY KEY (`table_name`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`comment`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `comment`;
CREATE TABLE `comment`(
`cot_id` `bigint` IDENTITY(1,1) NOT NULL,
`cot_content` `nvarchar`(max) NULL,
`cot_cas_id` `bigint` NULL,
`cot_user` `nvarchar`(25) NULL,
`cot_time` `datetime` NULL,
`cot_state` `int` NULL,
PRIMARY KEY (`cot_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`ta_lim`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `ta_lim`;
CREATE TABLE `ta_lim`(
`ta_lim_id` `bigint` IDENTITY(1,1) NOT NULL,
`ta_se_no` `bigint` NULL,
`ta_isdel` `char`(1) NULL,
`ta_task_id` `bigint` NULL,
`ta_fin_date` `datetime` NULL,
`ta_isfin` `char`(1) NULL,
`ta_desc` `nvarchar`(max) NULL,
`ta_name` `nvarchar`(50) NULL,
PRIMARY KEY (`ta_lim_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`phone_list`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `phone_list`;
CREATE TABLE `phone_list`(
`phl_id` `bigint` IDENTITY(1,1) NOT NULL,
`phl_state` `int` NULL,
`phl_name` `nvarchar`(50) NULL,
`phl_num` `varchar`(50) NULL,
`phl_cas_id` `bigint` NULL,
`phl_cat` `nvarchar`(50) NULL,
`phl_count` `int` NULL,
`phl_remark` `nvarchar`(max) NULL,
`phl_isdel` `char`(1) NULL,
`phl_isnew` `int` NULL,
`phl_upd_time` `datetime` NULL,
`phl_rel` `nvarchar`(50) NULL,
PRIMARY KEY (`phl_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_inq_pro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_inq_pro`;
CREATE TABLE `r_inq_pro`(
`rqp_id` `bigint` IDENTITY(1,1) NOT NULL,
`rqp_inq_id` `bigint` NULL,
`rqp_wpr_id` `bigint` NULL,
`rqp_num` `decimal`(18, 2) NULL,
`rqp_price` `decimal`(18, 2) NULL,
`rqp_all_price` `decimal`(18, 2) NULL,
`rqp_remark` `nvarchar`(max) NULL,
PRIMARY KEY (`rqp_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`case_hp`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `case_hp`;
CREATE TABLE `case_hp`(
`ch_id` `bigint` IDENTITY(1,1) NOT NULL,
`ch_chk_state` `int` NULL,
`ch_typ` `int` NULL,
`ch_text` `nvarchar`(max) NULL,
`ch_cat_1` `nvarchar`(20) NULL,
`ch_cat_2` `nvarchar`(20) NULL,
`ch_adr_id` `bigint` NULL,
`ch_msg_state` `int` NULL,
`ch_cas_id` `bigint` NULL,
`ch_res` `nvarchar`(max) NULL,
`ch_app_user` `nvarchar`(20) NULL,
`ch_sur_user` `nvarchar`(20) NULL,
`ch_app_time` `datetime` NULL,
`ch_sur_time` `datetime` NULL,
`ch_remark` `nvarchar`(max) NULL,
`ch_cont_user` `nvarchar`(20) NULL,
`ch_adr` `nvarchar`(1000) NULL,
`ch_count` `int` NULL,
`ch_upd_time` `datetime` NULL,
`ch_upd_man` `nvarchar`(25) NULL,
PRIMARY KEY (`ch_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`lim_role`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `lim_role`;
CREATE TABLE `lim_role`(
`rol_id` `bigint` IDENTITY(1,1) NOT NULL,
`rol_name` `nvarchar`(50) NULL,
`rol_lev` `int` NULL,
`rol_desc` `varchar`(max) NULL,
`rol_grp_id` `bigint`,
PRIMARY KEY (`rol_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`r_quo_pro`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `r_quo_pro`;
CREATE TABLE `r_quo_pro`(
`rup_id` `bigint` IDENTITY(1,1) NOT NULL,
`rup_quo_id` `bigint` NULL,
`rup_wpr_id` `bigint` NULL,
`rup_num` `decimal`(18, 2) NULL,
`rup_price` `decimal`(18, 2) NULL,
`rup_all_price` `decimal`(18, 2) NULL,
`rup_remark` `nvarchar`(max) NULL,
PRIMARY KEY (`rup_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_paid_past`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_paid_past`;
CREATE TABLE `sal_paid_past`(
`sps_id` `bigint` IDENTITY(1,1) NOT NULL,
`sps_ord_code` `bigint` NULL,
`sps_fct_date` `datetime` NULL,
`sps_count` `int` NULL,
`sps_type_id` `bigint` NULL,
`sps_pay_type` `nvarchar`(50) NULL,
`sps_pay_mon` `decimal`(18, 2) NULL,
`sps_mon_type` `nvarchar`(50) NULL,
`sps_user_code` `nvarchar`(50) NULL,
`sps_se_no` `bigint` NULL,
`sps_isinv` `char`(1) NULL,
`sps_remark` `nvarchar`(max) NULL,
`sps_alt_user` `nvarchar`(50) NULL,
`sps_cre_date` `datetime` NULL,
`sps_alt_date` `datetime` NULL,
`sps_isdel` `char`(1) NULL,
`sps_code` `varchar`(300) NULL,
`sps_aco_id` `bigint` NULL,
`sps_out_name` `nvarchar`(100) NULL,
`sps_content` `nvarchar`(100) NULL,
`sps_acc_type_id` `bigint` NULL,
`sps_undo_date` `datetime` NULL,
`sps_undo_user` `nvarchar`(50) NULL,
`sps_cus_id` `bigint` NULL,
PRIMARY KEY (`sps_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`case_paid`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `case_paid`;
CREATE TABLE `case_paid`(
`pa_id` `bigint` IDENTITY(1,1) NOT NULL,
`pa_state` `int` NULL,
`pa_cas_id` `bigint` NULL,
`pa_ptp_d` `datetime` NULL,
`pa_ptp_num` `decimal`(18, 2) NULL,
`pa_cp_time` `datetime` NULL,
`pa_cp_num` `decimal`(18, 2) NULL,
`pa_comt_user` `nvarchar`(25) NULL,
`pa_comt_time` `datetime` NULL,
`pa_paid_time` `datetime` NULL,
`pa_paid_num` `decimal`(18, 2) NULL,
`pa_sur_user` `nvarchar`(25) NULL,
`pa_sur_time` `datetime` NULL,
`pa_sur_remark` `nvarchar`(max) NULL,
`pa_writer` `nvarchar`(25) NULL,
`pa_wri_time` `datetime` NULL,
`pa_alt_user` `nvarchar`(25) NULL,
`pa_alt_time` `datetime` NULL,
`pa_del_user` `nvarchar`(25) NULL,
`pa_del_time` `datetime` NULL,
`pa_m_paid` `decimal`(18, 2) NULL,
`pa_cpm_paid` `decimal`(18, 2) NULL,
`pa_se_no` `bigint` NULL,
`pa_cm_paid` `decimal`(18, 2) NULL,
`pa_back_paid` `decimal`(18, 2) NULL,
`pa_pback_paid` `decimal`(18, 2) NULL,
`pa_last_debt_m` `decimal`(18,2) NULL,
`pa_left_amt` `decimal`(18,2) NULL,
PRIMARY KEY (`pa_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`sal_pur_ord`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `sal_pur_ord`;
CREATE TABLE `sal_pur_ord`(
`spo_id` `bigint` IDENTITY(1,1) NOT NULL,
`spo_til` `nvarchar`(300) NULL,
`spo_code` `varchar`(300) NULL,
`spo_con_date` `datetime` NULL,
`spo_sup_id` `bigint` NULL,
`spo_type_id` `bigint` NULL,
`spo_proj_id` `bigint` NULL,
`spo_sum_mon` `decimal`(18, 2) NULL,
`spo_paid_mon` `decimal`(18, 2) NULL,
`spo_user_code` `varchar`(50) NULL,
`spo_content` `nvarchar`(max) NULL,
`spo_isend` `char`(1) NULL,
`spo_isdel` `char`(1) NULL,
`spo_remark` `nvarchar`(max) NULL,
`spo_inp_user` `nvarchar`(50) NULL,
`spo_cre_date` `datetime` NULL,
`spo_alt_date` `datetime` NULL,
`spo_alt_user` `nvarchar`(50) NULL,
`spo_app_date` `datetime` NULL,
`spo_app_man` `nvarchar`(50) NULL,
`spo_app_desc` `nvarchar`(max) NULL,
`spo_app_isok` `char`(1) NULL,
`spo_se_no` `bigint` NULL,
PRIMARY KEY (`spo_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`hurr_rec`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `hurr_rec`;
CREATE TABLE `hurr_rec`(
`hur_id` `bigint` IDENTITY(1,1) NOT NULL,
`hur_cat` `varchar`(50) NULL,
`hur_cas_id` `bigint` NULL,
`hur_oper` `nvarchar`(25) NULL,
`hur_op_time` `datetime` NULL,
`hur_op_cont` `nvarchar`(max) NULL,
`hur_re_id` `bigint` NULL,
PRIMARY KEY (`hur_id`)
)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`wms_check`') AND type in (N'U'))
BEGIN
DROP TABLE IF EXISTS `wms_check`;
CREATE TABLE `wms_check`(
`wmc_id` `bigint` IDENTITY(1,1) NOT NULL,
`wmc_code` `varchar`(50) NULL,
`wmc_title` `nvarchar`(max) NULL,
`wmc_stro_code` `varchar`(50) NULL,
`wmc_user_code` `varchar`(50) NULL,
`wmc_date` `datetime` NULL,
`wmc_state` `char`(1) NULL,
`wmc_remark` `nvarchar`(max) NULL,
`wmc_isdel` `char`(1) NULL,
`wmc_inp_name` `nvarchar`(50) NULL,
`wmc_alt_name` `nvarchar`(50) NULL,
`wmc_inp_date` `datetime` NULL,
`wmc_alt_date` `datetime` NULL,
`wmc_app_date` `datetime` NULL,
`wmc_app_man` `nvarchar`(50) NULL,
`wmc_app_isok` `char`(1) NULL,
`wmc_app_desc` `nvarchar`(max) NULL,
`wmc_can_date` `datetime` NULL,
`wmc_can_man` `nvarchar`(50) NULL,
PRIMARY KEY (`wmc_id`)
)
END
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_address_adr_num`') AND parent_object_id = OBJECT_ID(N'`address`'))
Begin
ALTER TABLE `address` ADD  CONSTRAINT `DF_address_adr_num`  DEFAULT ((0)) FOR `adr_num`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_address_adr_check_app`') AND parent_object_id = OBJECT_ID(N'`address`'))
Begin
ALTER TABLE `address` ADD  CONSTRAINT `DF_address_adr_check_app`  DEFAULT ((0)) FOR `adr_check_app`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_address_adr_mail_app`') AND parent_object_id = OBJECT_ID(N'`address`'))
Begin
ALTER TABLE `address` ADD  CONSTRAINT `DF_address_adr_mail_app`  DEFAULT ((0)) FOR `adr_mail_app`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_address_adr_vis_app`') AND parent_object_id = OBJECT_ID(N'`address`'))
Begin
ALTER TABLE `address` ADD  CONSTRAINT `DF_address_adr_vis_app`  DEFAULT ((0)) FOR `adr_vis_app`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_bank_case_cas_m`') AND parent_object_id = OBJECT_ID(N'`bank_case`'))
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_m`  DEFAULT ((0)) FOR `cas_m`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_bank_case_cas_ptp_m`') AND parent_object_id = OBJECT_ID(N'`bank_case`'))
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_ptp_m`  DEFAULT ((0)) FOR `cas_ptp_m`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_bank_case_cas_cp_m`') AND parent_object_id = OBJECT_ID(N'`bank_case`'))
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_cp_m`  DEFAULT ((0)) FOR `cas_cp_m`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_bank_case_cas_paid_m`') AND parent_object_id = OBJECT_ID(N'`bank_case`'))
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_paid_m`  DEFAULT ((0)) FOR `cas_paid_m`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_bank_case_cas_gua_m`') AND parent_object_id = OBJECT_ID(N'`bank_case`'))
Begin
ALTER TABLE `bank_case` ADD  CONSTRAINT `DF_bank_case_cas_gua_m`  DEFAULT ((0)) FOR `cas_gua_m`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_case_bat_cbat_mon`') AND parent_object_id = OBJECT_ID(N'`case_bat`'))
Begin
ALTER TABLE `case_bat` ADD  CONSTRAINT `DF_case_bat_cbat_mon`  DEFAULT ((0)) FOR `cbat_mon`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_case_paid_pa_ptp_num`') AND parent_object_id = OBJECT_ID(N'`case_paid`'))
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_ptp_num`  DEFAULT ((0)) FOR `pa_ptp_num`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_case_paid_pa_cp_num`') AND parent_object_id = OBJECT_ID(N'`case_paid`'))
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_cp_num`  DEFAULT ((0)) FOR `pa_cp_num`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_case_paid_pa_paid_num`') AND parent_object_id = OBJECT_ID(N'`case_paid`'))
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_paid_num`  DEFAULT ((0)) FOR `pa_paid_num`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_case_paid_pa_m_paid`') AND parent_object_id = OBJECT_ID(N'`case_paid`'))
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_m_paid`  DEFAULT ((0)) FOR `pa_m_paid`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_case_paid_pa_cpm_paid`') AND parent_object_id = OBJECT_ID(N'`case_paid`'))
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_cpm_paid`  DEFAULT ((0)) FOR `pa_cpm_paid`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_case_paid_pa_cm_paid`') AND parent_object_id = OBJECT_ID(N'`case_paid`'))
Begin
ALTER TABLE `case_paid` ADD  CONSTRAINT `DF_case_paid_pa_cm_paid`  DEFAULT ((0)) FOR `pa_cm_paid`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_cus_cor_cus_cor_isdelete`') AND parent_object_id = OBJECT_ID(N'`cus_cor_cus`'))
Begin
ALTER TABLE `cus_cor_cus` ADD  CONSTRAINT `DF_cus_cor_cus_cor_isdelete`  DEFAULT ((1)) FOR `cor_isdelete`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_lim_user_user_fail`') AND parent_object_id = OBJECT_ID(N'`lim_user`'))
Begin
ALTER TABLE `lim_user` ADD  CONSTRAINT `DF_lim_user_user_fail`  DEFAULT ((0)) FOR `user_fail`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_r_ord_pro_rop_real_price`') AND parent_object_id = OBJECT_ID(N'`r_ord_pro`'))
Begin
ALTER TABLE `r_ord_pro` ADD  CONSTRAINT `DF_r_ord_pro_rop_real_price`  DEFAULT ((0.00)) FOR `rop_real_price`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_sal_all_task_sat_mon`') AND parent_object_id = OBJECT_ID(N'`sal_all_task`'))
Begin
ALTER TABLE `sal_all_task` ADD  CONSTRAINT `DF_sal_all_task_sat_mon`  DEFAULT ((0.00)) FOR `sat_paid_mon`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_sal_invoice_sin_mon`') AND parent_object_id = OBJECT_ID(N'`sal_invoice`'))
Begin
ALTER TABLE `sal_invoice` ADD  CONSTRAINT `DF_sal_invoice_sin_mon`  DEFAULT ((0.00)) FOR `sin_mon`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_sal_order_sod_sum_mon`') AND parent_object_id = OBJECT_ID(N'`sal_ord_con`'))
Begin
ALTER TABLE `sal_ord_con` ADD  CONSTRAINT `DF_sal_order_sod_sum_mon`  DEFAULT ((0.00)) FOR `sod_sum_mon`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_sal_ord_con_sod_paid_mon`') AND parent_object_id = OBJECT_ID(N'`sal_ord_con`'))
Begin
ALTER TABLE `sal_ord_con` ADD  CONSTRAINT `DF_sal_ord_con_sod_paid_mon`  DEFAULT ((0.0)) FOR `sod_paid_mon`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_sal_order_sod_isfail`') AND parent_object_id = OBJECT_ID(N'`sal_ord_con`'))
Begin
ALTER TABLE `sal_ord_con` ADD  CONSTRAINT `DF_sal_order_sod_isfail`  DEFAULT ((0)) FOR `sod_isfail`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_sal_paid_spd_pay_mon`') AND parent_object_id = OBJECT_ID(N'`sal_paid_plan`'))
Begin
ALTER TABLE `sal_paid_plan` ADD  CONSTRAINT `DF_sal_paid_spd_pay_mon`  DEFAULT ((0.00)) FOR `spd_pay_mon`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_sal_task_st_mon`') AND parent_object_id = OBJECT_ID(N'`sal_task`'))
Begin
ALTER TABLE `sal_task` ADD  CONSTRAINT `DF_sal_task_st_mon`  DEFAULT ((0.00)) FOR `st_mon`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_wms_product_wpr_cost_prc`') AND parent_object_id = OBJECT_ID(N'`wms_product`'))
Begin
ALTER TABLE `wms_product` ADD  CONSTRAINT `DF_wms_product_wpr_cost_prc`  DEFAULT ((0.00)) FOR `wpr_cost_prc`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_wms_product_wpr_sale_prc`') AND parent_object_id = OBJECT_ID(N'`wms_product`'))
Begin
ALTER TABLE `wms_product` ADD  CONSTRAINT `DF_wms_product_wpr_sale_prc`  DEFAULT ((0.00)) FOR `wpr_sale_prc`
End
;
IF Not EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'`DF_wms_shipment_wsh_cost`') AND parent_object_id = OBJECT_ID(N'`wms_shipment`'))
Begin
ALTER TABLE `wms_shipment` ADD  CONSTRAINT `DF_wms_shipment_wsh_cost`  DEFAULT ((0.00)) FOR `wsh_cost`
End
;
;
;
DROP TABLE IF EXISTS `loan_season`;
CREATE TABLE `loan_season`(
`lse_id` `bigint` IDENTITY(1,1) NOT NULL,
`lse_name` `nvarchar`(50) NULL,
PRIMARY KEY (`lse_id`)
)
;
;
;
DROP TABLE IF EXISTS `loan_cus`;
CREATE TABLE `loan_cus`(
`lc_id` `bigint` IDENTITY(1,1) NOT NULL,
`lc_name` `nvarchar`(50) NULL,
`lc_card_num` `varchar`(50) NULL,
`lc_card_type` `nvarchar`(20) NULL,
`lc_bank` `nvarchar`(100) NULL,
`lc_due_m` `decimal`(18, 2) NULL CONSTRAINT `DF__bank_cus__bc_m__084C046C`  DEFAULT ((0.00)),
`lc_principal` `decimal`(18, 2) NULL CONSTRAINT `DF__bank_cus__bc_pri__094028A5`  DEFAULT ((0.00)),
`lc_time_lim` `nvarchar`(50) NULL,
`lc_quality` `nvarchar`(20) NULL,
`lc_due_num` `varchar`(50) NULL,
`lc_overdue` `varchar`(50) NULL,
`lc_due_date` `datetime` NULL,
`lc_end_date` `datetime` NULL,
`lc_company` `nvarchar`(100) NULL,
`lc_com_addr` `nvarchar`(300) NULL,
`lc_com_pho` `varchar`(50) NULL,
`lc_home_addr` `nvarchar`(300) NULL,
`lc_home_pho` `varchar`(50) NULL,
`lc_manager` `nvarchar`(50) NULL,
`lc_num` `varchar`(50) NULL,
`lc_lse_id` `bigint` NULL,
`lc_remark` `nvarchar`(200) NULL,
`lc_ins_user` `nvarchar`(25) NULL,
`lc_ins_time` `datetime` NULL,
`lc_alt_user` `nvarchar`(25) NULL,
`lc_alt_time` `datetime` NULL,
`lc_risk_adv` `nvarchar`(500) NULL,
`lc_risk_per` `nvarchar`(500) NULL,
`lc_risk_com` `nvarchar`(500) NULL,
`lc_risk_biz` `nvarchar`(500) NULL,
`lc_risk_que` `varchar`(50) NULL,
`lc_op_state` `nvarchar`(200) NULL,
`lc_risk_rs` `nvarchar`(500) NULL,
PRIMARY KEY (`lc_id`)
)
;
;
ALTER TABLE `loan_cus`  WITH CHECK ADD  CONSTRAINT `FK_loan_cus_loan_season` FOREIGN KEY(`lc_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
;
;
DROP TABLE IF EXISTS `loan_com`;
CREATE TABLE `loan_com`(
`lcm_id` `bigint` IDENTITY(1,1) NOT NULL,
`lcm_lse_id` `bigint` NULL,
`lcm_company` `nvarchar`(100) NULL,
`lcm_content` `nvarchar`(max) NULL,
`lcm_ins_user` `nvarchar`(25) NULL,
`lcm_ins_time` `datetime` NULL,
`lcm_alt_user` `nvarchar`(25) NULL,
`lcm_alt_time` `datetime` NULL,
PRIMARY KEY (`lcm_id`)
)
;
ALTER TABLE `loan_com`  WITH CHECK ADD  CONSTRAINT `FK_loan_com_loan_season` FOREIGN KEY(`lcm_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
;
;
;
DROP TABLE IF EXISTS `loan_per`;
CREATE TABLE `loan_per`(
`lp_id` `bigint` IDENTITY(1,1) NOT NULL,
`lp_lse_id` `bigint` NULL,
`lp_card_num` `varchar`(50) NULL,
`lp_content` `nvarchar`(max) NULL,
`lp_ins_user` `nvarchar`(25) NULL,
`lp_ins_time` `datetime` NULL,
`lp_alt_user` `nvarchar`(25) NULL,
`lp_alt_time` `datetime` NULL,
`lp_name` `nvarchar`(50) NULL,
PRIMARY KEY (`lp_id`)
)
;
;
ALTER TABLE `loan_per`  WITH CHECK ADD  CONSTRAINT `FK_loan_per_loan_season` FOREIGN KEY(`lp_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
;
;
;
DROP TABLE IF EXISTS `loan_police`;
CREATE TABLE `loan_police`(
`lpol_id` `bigint` IDENTITY(1,1) NOT NULL,
`lpol_lse_id` `bigint` NULL,
`lpol_name` `nvarchar`(50) NULL,
`lpol_card_num` `varchar`(50) NULL,
`lpol_content` `nvarchar`(max) NULL,
`lpol_ins_user` `nvarchar`(25) NULL,
`lpol_ins_time` `datetime` NULL,
`lpol_alt_user` `nvarchar`(25) NULL,
`lpol_alt_time` `datetime` NULL,
`lpol_card_type` `nvarchar`(20) NULL,
PRIMARY KEY (`lpol_id`)
)
;
;
ALTER TABLE `loan_police`  WITH CHECK ADD  CONSTRAINT `FK_loan_police_loan_season` FOREIGN KEY(`lpol_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
;
;
;
DROP TABLE IF EXISTS `loan_reg_inf`;
CREATE TABLE `loan_reg_inf`(
`lreg_id` `bigint` IDENTITY(1,1) NOT NULL,
`lreg_company` `nvarchar`(100) NULL,
`lreg_content` `nvarchar`(max) NULL,
`lreg_ins_user` `nvarchar`(25) NULL,
`lreg_ins_time` `datetime` NULL,
`lreg_alt_user` `nvarchar`(25) NULL,
`lreg_alt_time` `datetime` NULL,
`lreg_lse_id` `bigint` NULL,
`lreg_state` `nvarchar`(50) NULL,
`lreg_last_year` `varchar`(50) NULL,
`lreg_boss_name` `nvarchar`(500) NULL,
`lreg_ord_code` `varchar`(50) NULL,
`lreg_law_man` `nvarchar`(50) NULL,
PRIMARY KEY (`lreg_id`)
)
;
;
ALTER TABLE `loan_reg_inf`  WITH CHECK ADD  CONSTRAINT `FK_loan_reg_inf_loan_season` FOREIGN KEY(`lreg_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
;
;
;
DROP TABLE IF EXISTS `loan_sd_rec`;
CREATE TABLE `loan_sd_rec`(
`lsd_id` `bigint` IDENTITY(1,1) NOT NULL,
`lsd_lse_id` `bigint` NULL,
`lsd_name` `nvarchar`(50) NULL,
`lsd_card_num` `varchar`(50) NULL,
`lsd_sear_num` `nvarchar`(50) NULL,
`lsd_date` `nvarchar`(50) NULL,
`lsd_m` `nvarchar`(50) NULL,
`lsd_ins_user` `nvarchar`(25) NULL,
`lsd_ins_time` `datetime` NULL,
`lsd_alt_user` `nvarchar`(25) NULL,
`lsd_alt_time` `datetime` NULL,
PRIMARY KEY (`lsd_id`)
)
;
;
ALTER TABLE `loan_sd_rec`  WITH CHECK ADD  CONSTRAINT `FK_loan_sd_rec_loan_season` FOREIGN KEY(`lsd_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
;
;
;
DROP TABLE IF EXISTS `house_inf`;
CREATE TABLE `house_inf`(
`hoi_id` `bigint` IDENTITY(1,1) NOT NULL,
`hoi_lse_id` `bigint` NULL,
`hoi_name` `nvarchar`(50) NULL,
`hoi_id_no` `varchar`(50) NULL,
`hoi_house_no` `nvarchar`(500) NULL,
`hoi_type` `nvarchar`(100) NULL,
`hoi_com` `nvarchar`(200) NULL,
`hoi_ins_user` `nvarchar`(50) NULL,
`hoi_ins_time` `datetime` NULL,
`hoi_alt_user` `nvarchar`(50) NULL,
`hoi_alt_time` `datetime` NULL,
PRIMARY KEY (`hoi_id`)
)
;
;
;
DROP TABLE IF EXISTS `neg_inf`;
CREATE TABLE `neg_inf`(
`nei_id` `bigint` IDENTITY(1,1) NOT NULL,
`nei_lse_id` `bigint` NULL,
`nei_name` `nvarchar`(100) NULL,
`nei_inf` `nvarchar`(500) NULL,
`nei_ins_user` `nvarchar`(50) NULL,
`nei_ins_time` `datetime` NULL,
`nei_alt_user` `nvarchar`(50) NULL,
`nei_alt_time` `datetime` NULL,
PRIMARY KEY (`nei_id`)
)
;
DROP TABLE IF EXISTS `car_loan`;
CREATE TABLE `car_loan`(
`cal_cas_id` `bigint` NULL,
`cal_price` `varchar`(200) NULL,
`cal_lice` `varchar`(200) NULL,
`cal_make` `nvarchar`(200) NULL,
`cal_vin` `varchar`(200) NULL,
`cal_engine_no` `varchar`(200) NULL,
)
;
create table sys_pref(
syp_id bigint primary key identity(1,1) NOT NULL,
syp_name	varchar(50),
syp_is_def	int,
syp_is_app	int,
syp_pwd_len	int,
syp_pwd_rule	varchar(50),
syp_pwd_upd_days	int,
syp_login_fail	int,
syp_offline_days	int,
syp_has_captcha int,
syp_hide_back	int,
syp_global_org	int,
syp_cl_no_hide int
)
;
;
;
DROP TABLE IF EXISTS `law_act`;
CREATE TABLE `law_act`(
`lwa_id` `bigint` IDENTITY(1,1) NOT NULL,
`lwa_lwc_id` `bigint` NULL,
`lwa_emp_id` `bigint` NULL,
`lwa_content` `nvarchar`(max) NULL,
`lwa_time` `datetime` NULL,
`lwa_cre_man` `nvarchar`(50) NULL,
`lwa_cre_time` `datetime` NULL,
`lwa_upd_man` `nvarchar`(50) NULL,
`lwa_upd_time` `datetime` NULL,
`lwa_remark` `nvarchar`(max) NULL,
`lwa_proc` `varchar`(50) NULL,
PRIMARY KEY (`lwa_id`)
)
;
;
;
DROP TABLE IF EXISTS `law_case`;
CREATE TABLE `law_case`(
`lwc_id` `bigint` IDENTITY(1,1) NOT NULL,
`lwc_state` `varchar`(50) NULL,
`lwc_name` `nvarchar`(100) NULL,
`lwc_id_code` `varchar`(50) NULL,
`lwc_consigner` `nvarchar`(100) NULL,
`lwc_defendant` `nvarchar`(100) NULL,
`lwc_cost` `decimal`(18, 2) NULL,
`lwc_target` `nvarchar`(50) NULL,
`lwc_emp_id` `bigint` NULL,
`lwc_type_id` `bigint` NULL,
`lwc_date` `datetime` NULL,
`lwc_contact` `varchar`(200) NULL,
`lwc_court_id` `bigint` NULL,
`lwc_filing_date` `datetime` NULL,
`lwc_no` `varchar`(100) NULL,
`lwc_judge` `nvarchar`(50) NULL,
`lwc_judge_contact` `varchar`(200) NULL,
`lwc_fst_hearing_date` `datetime` NULL,
`lwc_end_date` `datetime` NULL,
`lwc_act_date` `datetime` NULL,
`lwc_act_no` `varchar`(100) NULL,
`lwc_act_end_date` `datetime` NULL,
`lwc_remark` `nvarchar`(500) NULL,
`lwc_cre_man` `nvarchar`(50) NULL,
`lwc_cre_time` `datetime` NULL,
`lwc_upd_man` `nvarchar`(50) NULL,
`lwc_upd_time` `datetime` NULL,
`lwc_paid` `decimal`(18, 2) NULL,
`lwc_proc_st_id`	bigint NULL,
`lwc_cas_id` bigint NULL,
`lwc_legal_pay_date` `datetime` NULL,
`lwc_presv_pay_date` `datetime` NULL,
`lwc_asset_presv` `nvarchar`(max) NULL,
`lwc_serv_rs` `nvarchar`(100) NULL,
`lwc_judgment` `nvarchar`(max) NULL,
`lwc_proc` `varchar`(50) NULL,
`lwc_approve_time` `datetime` NULL,
`lwc_approve_man` `nvarchar`(25) NULL,
PRIMARY KEY (`lwc_id`)
)
;
;
;
;
DROP TABLE IF EXISTS `law_paid`;
CREATE TABLE `law_paid`(
`lwpa_id` `bigint` IDENTITY(1,1) NOT NULL,
`lwpa_amt` `decimal`(18, 2) NULL,
`lwpa_type_id` `bigint` NULL,
`lwpa_date` `datetime` NULL,
`lwpa_name` `nvarchar`(100) NULL,
`lwpa_man` `nvarchar`(50) NULL,
`lwpa_pay_med` `int` NULL,
`lwpa_file_code` `varchar`(100) NULL,
`lwpa_remark` `nvarchar`(500) NULL,
`lwpa_cre_man` `nvarchar`(50) NULL,
`lwpa_cre_time` `datetime` NULL,
`lwpa_upd_man` `nvarchar`(50) NULL,
`lwpa_upd_time` `datetime` NULL,
`lwpa_lwc_id` `bigint` NULL,
PRIMARY KEY (`lwpa_id`)
)
;
DROP TABLE IF EXISTS `lim_group`;
CREATE TABLE `lim_group`(
`grp_id` `bigint` IDENTITY(1,1) NOT NULL,
`grp_name` `nvarchar`(50) NULL,
`grp_desc` `nvarchar`(200) NULL,
`grp_cre_time` `datetime` NULL,
`grp_cre_man` `nvarchar`(50) NULL,
`grp_upd_time` `datetime` NULL,
`grp_upd_man` `nvarchar`(50) NULL,
PRIMARY KEY (`grp_id`)
)
DROP TABLE IF EXISTS `r_group_rig`;
CREATE TABLE `r_group_rig`(
`rgr_id` `bigint` IDENTITY(1,1) NOT NULL,
`rgr_grp_id` `bigint` NULL,
`rgr_rig_code` `varchar`(50) NULL,
PRIMARY KEY (`rgr_id`)
)
;
-- 现金巴士批次表
;
;
;
DROP TABLE IF EXISTS `cashbus_batch`;
CREATE TABLE `cashbus_batch`(
`id` `bigint` NOT NULL,
`batchname` `varchar`(255) NOT NULL,
`description` `text` NULL,
`withdraw` `tinyint(1)` NULL,
`type` `varchar`(255) NULL,
`withdrawTime` `datetime` NULL,
`ext1` `varchar`(100) NULL,
`ext2` `varchar`(100) NULL,
`ext3` `varchar`(100) NULL,
PRIMARY KEY (`id`)
) TEXTIMAGE_ON `PRIMARY`
;
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'batchname'
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'description'
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否撤案' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'withdraw'
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类型 weiwai, yuzhengxin, zhengshizhengxin' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'type'
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'撤案日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'withdrawTime'
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'现金巴士批次' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch'
;
-- 现金巴士批次表结束
;
;
;
DROP TABLE IF EXISTS `user_log`;
CREATE TABLE `user_log`(
`ulg_id` `bigint` IDENTITY(1,1) NOT NULL,
`ulg_type`	`varchar`(50),
`ulg_oper`	`nvarchar`(50),
`ulg_op_time`	`datetime`,
`ulg_op_content`	`nvarchar`(max),
`ulg_user`	`varchar`(50),
PRIMARY KEY (`ulg_id`)
)
;
;
;
;
DROP TABLE IF EXISTS `type_list_connect`;
CREATE TABLE `type_list_connect`(
`tlc_id` `bigint` IDENTITY(1,1) NOT NULL,
`parent_typ_id` `bigint` NOT NULL,
`child_typ_id` `bigint` NOT NULL,
PRIMARY KEY (`tlc_id`)
)
;
;
CREATE PROCEDURE `empExcLim`(
@excLimIds  varchar(8000),
@sqlAppend  varchar(8000)
)
AS
BEGIN
--申明字符串变量，以供动态拼装
declare @sql longtext,@sql1 varchar(8000)
set @sql1='select exclims.typ_name as exc_lim into ##tempExcLim from type_list as exclims where exclims.typ_type=''excLimType'' '+ @excLimIds +' '
if object_id('tempdb..##tempExcLim') is not null
begin
drop table ##tempExcLim
end
exec(@sql1)
--拼装SQL命令
set @sql = 'select se_no,max(se_name) as head'
select @sql = @sql + ',
sum(case cas_exc_lim when '''+exc_lim+''' then 1 else 0 end) ['+exc_lim+'],
sum(case cas_exc_lim when '''+exc_lim+''' then cas_m else 0 end) ['+exc_lim+'],
sum(case cas_exc_lim when '''+exc_lim+''' then cas_paid_m else 0 end) ['+exc_lim+']' from (select exc_lim from ##tempExcLim)as a 
--加上合计
select @sql = @sql+' 
from bank_case inner join sal_emp on cas_se_no=se_no inner join type_list on cas_typ_bid=typ_id 
inner join case_bat on cas_cbat_id = cbat_id 
where cas_state!=3 ' + @sqlAppend
+ ' group by se_no with rollup'
--print(@sql)
exec(@sql)
END
;
;
;
CREATE PROCEDURE `empState` 
@sqlAppend  varchar(8000)
AS
BEGIN
declare @sql longtext,@sql1 varchar(8000)
set @sql1='select typ_name as state_name into ##tempHurState 
from type_list where typ_type=''caseState'' '
if object_id('tempdb..##tempHurState') is not null
begin
drop table ##tempHurState
end
exec(@sql1)
set @sql = 'select se_no,max(se_code),max(se_name) as head'
select @sql = @sql + ',
sum(case hstate.typ_name when '''+state_name+''' then 1 else 0 end) ['+state_name+'] ' from (select state_name from ##tempHurState)as a 
select @sql = @sql+',
count(cas_id)as 合计 
from bank_case left join sal_emp on cas_se_no=se_no inner join type_list as hstate on cas_typ_hid=hstate.typ_id '+ 
@sqlAppend+
' group by se_no '
--print(@sql)
exec(@sql)
END
;
;
;
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'`empPhoCat`') AND type in (N'P', N'PC'))
BEGIN
EXEC dbo.sp_executesql @statement = N'
-- =============================================
-- Author:		GM
-- Create date: 2011-1-26
-- Description:	电催员电话类别统计
-- =============================================
CREATE PROCEDURE `empPhoCat`(
@sqlAppend  varchar(8000)
) 
AS
BEGIN
--申明字符串变量，以供动态拼装
declare @sql longtext,@sql1 varchar(8000)
--将电话类别列放入临时表
set @sql1=''select typ_name as p_cat_name into ##tempPhoCat 
from pho_red inner join type_list on pr_typ_id=typ_id group by typ_name ''
if object_id(''tempdb..##tempPhoCat'') is not null
begin
drop table ##tempPhoCat
end
exec(@sql1)
--拼装SQL命令
set @sql = ''select se_no,max(se_code),max(se_name) as head''
--将电话类别旋转为表头
select @sql = @sql + '',
sum(case typ_name when ''''''+p_cat_name+'''''' then 1 else 0 end) [''+p_cat_name+''] '' from (select p_cat_name from ##tempPhoCat)as a 
--加上合计
select @sql = @sql+'',
count(pr_id)as 合计 
from pho_red inner join sal_emp on pr_se_no=se_no inner join type_list on pr_typ_id=typ_id ''+ @sqlAppend
+'' group by se_no with rollup''
print(@sql)
exec(@sql)
END
' 
END
;
;
;
-- =============================================
-- Author:		GM
-- Create date: 2015-8-13
-- Description:	批次账龄催收员分组催收状态统计
-- =============================================
CREATE PROCEDURE `batExcLimEmpByStateStat`(
@sqlAppend  varchar(8000)
)
AS
BEGIN
if object_id('tempdb..##tempClState') is not null
begin
drop table ##tempClState
end
declare @stateSql longtext,@sql longtext
set @stateSql = 'select clstate.typ_id as state_id, max(clstate.typ_name) as state_name
into ##tempClState
from type_list as clstate
inner join bank_case as b on clstate.typ_id=b.cas_typ_hid 
inner join case_bat on b.cas_cbat_id = cbat_id
left join type_list as batBank on cas_typ_bid = batBank.typ_id 
'+@sqlAppend+'
group by clstate.typ_id'
--print(@stateSql)
exec(@stateSql)
--拼装SQL命令
set @sql = 'select b.cas_cbat_id , b.cas_se_no, 
case when grouping(b.cas_cbat_id)=1 then ''合计'' else max(cbat_code) end as ''head'', 
case when grouping(b.cas_cbat_id)=1 then ''-'' else (case when grouping(b.cas_exc_lim)=1 then ''小计'' else b.cas_exc_lim end) end as ''逾期账龄'', 
case when grouping(b.cas_exc_lim)=1 or grouping(b.cas_cbat_id)=1 then ''-'' else (case when grouping(b.cas_se_no)=1 then ''小计'' else max(se_name) end) end as ''催收员'', 
case when grouping(b.cas_se_no)=1 then ''-'' else max(so_name) end as ''部门'',
case when grouping(b.cas_cbat_id)=1 then null else datediff(day,max(cbat_date),getdate()) end as ''委案天数'',
case when grouping(b.cas_cbat_id)=1 then null else max(cbat_date) end as ''委案日期'',
case when grouping(b.cas_cbat_id)=1 then null else max(cbat_backdate_p) end as ''预计退案日'',
case when grouping(b.cas_cbat_id)=1 then null else max(cbat_backdate) end as ''实际退案日'', 
case when grouping(b.cas_cbat_id)=1 then null else max(carea.typ_name) end as ''催收区域'', 
case when grouping(b.cas_cbat_id)=1 then null else max(bType.typ_name) end as ''案件类型'',
sum(b.cas_m) as ''委案金额'',count(b.cas_id) as ''委案户数'',
case when sum(b.cas_paid_m)>0 then sum(b.cas_m)-sum(b.cas_paid_m) else sum(b.cas_m) end as ''余额'',
sum(b.cas_paid_m) as ''还款案件'',
case when sum(b.cas_m)>0 then sum(b.cas_paid_m)/sum(b.cas_m) else 0 end as ''还款案件'',
sum(case when b.cas_paid_m > 0 then 1 else 0 end) as ''还款案件'', 
case when count(b.cas_id)>0 then sum(case when b.cas_paid_m is not null and b.cas_paid_m > 0 then 1 else 0 end)/count(b.cas_id) else 0 end as ''还款案件'',
sum(case when b.cas_paid_m>= b.cas_m then b.cas_m else 0 end) as ''还清案件'',
case when sum(b.cas_m)>0 then sum(case when b.cas_paid_m>= b.cas_m then b.cas_m else 0 end)/sum(b.cas_m) else 0 end as ''还清案件'',
sum(case when b.cas_paid_m>= b.cas_m then 1 else 0 end) as ''还清案件'', 
case when count(b.cas_id)>0 then sum(case when b.cas_paid_m>= b.cas_m then 1 else 0 end)/count(b.cas_id) else 0 end as ''还清案件'',
sum(case when b.cas_paid_m>0 and b.cas_paid_m< b.cas_m then b.cas_m else 0 end) as ''部分还款案件'',
case when sum(b.cas_m)>0 then sum(case when b.cas_paid_m>0 and b.cas_paid_m< b.cas_m then b.cas_m else 0 end)/sum(b.cas_m) else 0 end as ''部分还款案件'',
sum(case when b.cas_paid_m>0 and b.cas_paid_m< b.cas_m then 1 else 0 end) as ''部分还款案件'', 
case when count(b.cas_id)>0 then sum(case when b.cas_paid_m>0 and b.cas_paid_m< b.cas_m then 1 else 0 end)/count(b.cas_id) else 0 end as ''部分还款案件'',
sum(case when b.cas_paid_m=0 or b.cas_paid_m is null then b.cas_m else 0 end) as ''未还款案件'',
case when sum(b.cas_m)>0 then sum(case when b.cas_paid_m=0 or b.cas_paid_m is null then b.cas_m else 0 end)/sum(b.cas_m) else 0 end as ''未还款案件'',
sum(case when b.cas_paid_m=0 or b.cas_paid_m is null then 1 else 0 end) as ''未还款案件'', 
case when count(b.cas_id)>0 then sum(case when b.cas_paid_m=0 or b.cas_paid_m is null then 1 else 0 end)/count(b.cas_id) else 0 end as ''未还款案件'' '
select @sql = @sql + ',
sum(case b.cas_typ_hid when '+convert(varchar(100),temp.state_id)+' then b.cas_m else 0 end) as '''+temp.state_name+''',
case when sum(b.cas_m)>0 then sum(case b.cas_typ_hid when '+convert(varchar(100),temp.state_id)+' then b.cas_m else 0 end)/sum(b.cas_m) else 0 end as '''+temp.state_name+''',
sum(case b.cas_typ_hid when '+convert(varchar(100),temp.state_id)+' then 1 else 0 end) as '''+temp.state_name+''',  
case when count(b.cas_id)>0 then sum(case b.cas_typ_hid when '+convert(varchar(100),temp.state_id)+' then 1 else 0 end)/count(b.cas_id) else 0 end as '''+temp.state_name+''' '  from (select state_id,state_name from ##tempClState)as temp 
select @sql = @sql+'
from bank_case as b
inner join case_bat on b.cas_cbat_id = cbat_id
left join sal_emp on b.cas_se_no = se_no
left join sal_org on se_so_code = so_code
left join type_list as carea on cbat_area_id = carea.typ_id
left join type_list as batBank on cbat_typ_bid = batBank.typ_id
left join type_list as bType on cbat_type_id = bType.typ_id 
'+@sqlAppend+'
group by b.cas_cbat_id,b.cas_exc_lim,b.cas_se_no with rollup'
--print(@sql)
exec(@sql)
END
;

SET FOREIGN_KEY_CHECKS = 1;
