<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>更新评语内容</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
	<script type="text/javascript" >
    	function check(){
			var errStr = "";
			if(isEmpty("content")){
				errStr+="- 未填写评语内容！\n";
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				waitSubmit("doSave");
				waitSubmit("doCancel");
				return $("create").submit();
			}
		}
  </script>
  </head>
  
  <body>

  <div class="inputDiv">
	<form id="create" action="caseAction.do" method="post">
  		<input type="hidden" name="op" value="updComment" />
  		<input type="hidden" name="cotId"  value="${comment.cotId}"/>
        <table class="dashTab inputForm" cellpadding="0" cellspacing="0">
        	<tr class="noBorderBot">
            	<th>评语内容：</th>
                <td colspan="3"><textarea class="inputSize2L" rows="6" id="content" name="content" onBlur="autoShort(this,500)">${comment.cotContent}</textarea></td>
           	</tr>		
           	<tr class="submitTr">
            	<td colspan="4">
                	<input id="doSave" class="butSize1" type="button" value="保存" onClick="check()" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                </td>
            </tr>
        </table>
	</form>
    </div>
  </body>
</html>
