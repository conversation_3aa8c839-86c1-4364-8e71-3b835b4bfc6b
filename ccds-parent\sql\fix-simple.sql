-- 简单的数据库修复脚本
-- 解决sal_emp表字段缺失问题

USE `ccds`;

-- 删除并重新创建sal_emp表
DROP TABLE IF EXISTS `sal_emp`;

-- 重新创建员工表
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT COMMENT '员工编号',
  `se_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `se_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `se_so_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `se_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `se_phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `se_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `se_position` varchar(50) DEFAULT NULL COMMENT '职位',
  `se_entry_date` date DEFAULT NULL COMMENT '入职日期',
  PRIMARY KEY (`se_no`),
  <PERSON>EY `idx_se_user_code` (`se_user_code`),
  KEY `idx_se_so_code` (`se_so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 插入默认员工数据
INSERT INTO `sal_emp` (`se_name`, `se_user_code`, `se_so_code`, `se_isenabled`, `se_position`) 
VALUES ('系统管理员', 'admin', 'ORG001', '1', '管理员');

-- 验证表结构
DESCRIBE sal_emp;

-- 验证数据
SELECT * FROM sal_emp;

COMMIT;
