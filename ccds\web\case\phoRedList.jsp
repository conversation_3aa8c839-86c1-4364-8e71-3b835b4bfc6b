 <%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>催收记录列表</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
	<style type="text/css">
	#listTop th label{
		font-weight:normal;
		font-size:12px;
		color:#666;
	}
	</style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/case.js"></script>
    <script type="text/javascript" src="case/caseDesc.js?v=********"></script>
    <script type="text/javascript">
     	/*function outRedList(){
			expCRecOfCase("${CUS_VER_ID}","${casId}");
     	}*/
		
    	function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			if(obj.bankCase.casId!="${casId}"){
				className = 'brown';
			}
			var funcCol = "<img onClick=\"parent.casePopDiv(38,'"+obj.prId+"')\" class=\"hand\" src=\"images/content/edit.gif\" alt=\"编辑\"/>&nbsp;&nbsp;<img onClick=\"parent.caseDelDiv(4,'"+obj.prId+"','1')\" class='hand' src='images/content/del.gif' alt='删除'/>";
			var phoneNum = obj.prContact;
			if(obj.prCat==0){
				phoneNum = "<a href=\"javascript:void(0)\" onClick=\"parent.showPRInputDiv('"+encodeString(obj.prContact)+"','"+encodeString(obj.prName)+"','"+obj.prConType+"','"+encodeString(obj.prRel)+"');return false;\">"+encodeString(obj.prContact)+"</a>&nbsp;";
				/********* 暂时屏蔽 
				if(obj.prCallId!=""){
					phoneNum += "&nbsp;<img onClick=\"window.open('connectCTI.do?op=toGetRecordByCallId&callId="+obj.prCallId+"')\" class=\"hand\" src=\"images/content/sound.png\" alt=\"查看录音\"/>";
				} */
			}
			datas = [convPrCat(obj.prCat),formatDate(obj.prTime)+"<br/>"+obj.prTime.substring(11), obj.prName, obj.prRel, phoneNum, obj.prConType, obj.prContent, obj.typeList?obj.typeList.typName:"", obj.prNegotiation, obj.prPtpDate, obj.prPtpNum, obj.prReduceM, obj.prReduceState, obj.salEmp?obj.salEmp.seName:"",obj.prStateType?obj.prStateType.typName:"",funcCol];
			if("${CUS_VER_ID}"=="403"){
				datas = [convPrCat(obj.prCat),obj.prClType,formatDate(obj.prTime)+"<br/>"+obj.prTime.substring(11), obj.prName, obj.prRel, phoneNum, 
				         obj.prConType, obj.prContent, obj.typeList?obj.typeList.typName:"", obj.prNegotiation, obj.prPtpDate, obj.prPtpNum, obj.prReduceM,
		        		 obj.prReduceState, obj.prIsAnswered=='1'?'是':(obj.prIsAnswered=='0'?'否':''), obj.prIsSelfAnswered=='1'?'是':(obj.prIsSelfAnswered=='0'?'否':''),
        				 obj.prCaseConState,obj.salEmp?obj.salEmp.seName:"",obj.prStateType?obj.prStateType.typName:"", obj.prNxtClDate, obj.prNxtClType, funcCol];
			}
			/*if('${operational}'=='1'){
				datas.pop();
			}*/
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars = [];
			pars.op = "showPhoRed";
			pars.casId="${casId}";
			var isShowOth = "0";//默认本案催记
			if($("batPr").checked){
				isShowOth = "3";
			}
			else if($("sameIDPr").checked){
				isShowOth = "1";
			}
			else if($("sameCardPr").checked){
				isShowOth = "2";
			}
			pars.isShowOth = isShowOth;
			pars.isFullList = "1";
			var loadFunc = "loadList";
			var cols;
			/*if(smallScreen){
				cols=[
					{name:"催收措施",width:'5%'},
					{name:"时间",width:'9%'},
					{name:"对象姓名",width:'6%'},
					{name:"电话/地址",align:"left",width:'14%'},
					{name:"类型",width:'5%'},
					{name:"催收记录",align:'left',width:'27%'},
					{name:"催收结果",width:'6%'},
					{name:"承诺日期",renderer:'date',width:'9%'},
					{name:"承诺金额",renderer:'moneyOrEmt',align:'right',width:'8%'},	
					{name:"催收员",width:'6%'},
					{name:"操作",width:'5%'}
				];
			}
			else{*/
				cols=[
					{name:"催收措施",width:'5%'},
					{name:"时间",width:'8%'},
					{name:"对象姓名",width:'6%'},
					{name:"关系",width:'6%'},
					{name:"电话/地址",align:"left",width:'10%'},
					{name:"类型",width:'5%'},
					{name:"催收记录",align:'left',width:'12%'},
					{name:"催收结果",width:'5%'},
					{name:"谈判方式",width:'5%'},
					{name:"承诺日期",renderer:'date',width:'8%'},
					{name:"承诺金额",renderer:'moneyOrEmt',align:'right',width:'6%'},	
					{name:"减免金额",renderer:'moneyOrEmt',align:'right',width:'6%'},
					{name:"减免状态",width:'5%'},	
					{name:"催收员",width:'4%'},
					{name:"催收状态",width:'5%'},
					{name:"操作",width:'4%'}
				];
			/*}*/
			/*if('${operational}'=='1'){
				cols.pop();
			}*/
			if("${CUS_VER_ID}"=="403"){
				cols = [
					{name:"催收措施",width:'2%'},
					{name:"催收类型",width:'2%'},
					{name:"时间",width:'6%'},
					{name:"对象姓名",width:'5%'},
					{name:"关系",width:'4%'},
					{name:"电话/地址",align:"left",width:'8%'},
					{name:"类型",width:'5%'},
					{name:"催收记录",align:'left',width:'12%'},
					{name:"催收结果",width:'5%'},
					{name:"谈判方式",width:'5%'},
					{name:"承诺日期",renderer:'date',width:'4%'},
					{name:"承诺金额",renderer:'moneyOrEmt',align:'right',width:'5%'},	
					{name:"减免金额",renderer:'moneyOrEmt',align:'right',width:'5%'},
					{name:"减免状态",width:'4%'},	
					{name:"是否接听",width:'2%'},
					{name:"是否本人接听",width:'2%'},
					{name:"案件状态",width:'4%'},
					{name:"催收员",width:'4%'},
					{name:"催收状态",width:'4%'},
					{name:"下次跟进日期",renderer:'date',width:'4%'},
					{name:"下次催收类型",width:'4%'},
					{name:"操作",width:'4%'}
				];
			}
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("phoRedListTab","dataList");
    	gridEl.config.sortable=false;
		//gridEl.config.isResize=false;
		gridEl.config.isShort=false;
		createProgressBar();
   		window.onload=function(){
			if('${operational}'!='0'){
				$("saveOthPr").show();
			}
			$("listTop").show();
			loadList();
		}
    </script>
</head> 
  
  <body>
  	<div class="divWithScroll2 innerIfm">
  		<table id="listTop" class="normal ifmTopTab" cellpadding="0" cellspacing="0" style="display:none;">
  			<tr>
            	<th>
            	<input type="radio" id="selfPr" class="inputBoxAlign" name="prCaseGroup" checked onclick="loadList()"/><label for="selfPr">本案催记</label>&nbsp;&nbsp;<input type="radio" id="batPr" class="inputBoxAlign" name="prCaseGroup" onclick="loadList()"/><label for="batPr">同批次共债催记</label>&nbsp;&nbsp;<input type="radio"  class="inputBoxAlign" id="sameIDPr" name="prCaseGroup" onclick="loadList()" /><label for="sameIDPr">共债催记</label>&nbsp;&nbsp;<input type="radio" class="inputBoxAlign" id="sameCardPr" name="prCaseGroup" onclick="loadList()" /><label for="sameCardPr">同卡催记</label>
            	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span  class="inputBoxAlign" style="font-size:12px; font-weight:normal;">棕色催记为共债/共卡催记</span></th>
                <td style="width:260px; vertical-align:top;">
                 <a id="saveOthPr" style="display:none" href="javascript:void(0)" onClick="parent.casePopDiv(310,'${casId}');return false;" class="newBlueButton">添加辅助催记</a>&nbsp;
                 <a id="out" href="javascript:void(0)" onClick="parent.casePopDiv(63,'${casId}');return false;" class="newBlueButton">导出本案催记</a>
  				</td>
  			</tr>
  		</table>
         <!--<script type="text/javascript">displayLimAllow("hu041","saveOthPr");</script>-->
  		<div id="dataList" class="dataList"></div>
        <input type="hidden" id="isShowOth" />
    </div>
  </body>
</html>