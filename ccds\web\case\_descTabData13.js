function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','C_DT'],
		['ID_NO','C_CODE','AREA','STATE'],
		['AMT', 'BACK_AMT',['S_OWED',3]],
		['PAID','S_PD_BACK','S_PBACK_P','S_PBACK_M'],
		['M_CAT','AGE','PTP','CP'],
		['EMP','ASS_TM','ASS_HIS','BACK_DT'],
		['MOB','PHO','HOM','H_POST'],
		['COM','W_PHO','W_ADDR','W_POST'],
		['REG','M_ADDR','EMAIL','DAYS'],
		['TIPS_DT',['TREMARK',5]]
	);
}
function getLayout2(){
	return  new Array(
		['ACC','F_BANK','HOST','BILL_DT'],
		['RMB','GB','MY','APP_NO'],
		['START_DT','LAST_C_DT','LAST_P_DT','LAST_P'],
		['LAST_R_DT','STOP_DT','C_L','G_AMT'],
		['LOAN_T','DELAY_LV','MIN_P','P_L'],
		['P_COUNT','CL_T','PRI','LEFT_PRI'],
		['OV_INT','OV_P','LOAN_DT','OV_DT'],
		['SC_PC_NO','SC_NO','POS','PART'],
		['COUNT','PROD','BIZ','FILE_NO'],
		[['C6_NAME',1,'联合贷款人姓名'],['C6_ID_NO',1,'联合贷款人证件'],['C6_HM_PHO',1,'联合贷款人家庭电话'],['C6_W_PHO',1,'联合贷款人单位电话']],
		[['C6_MOB',1,'联合贷款人手机'],['C6_COM',1,'联合贷款人单位'],['C6_ADR',3,'联合贷款人地址']],
		['C1_NAME','C1_ID_NO','C1_HM_PHO','C1_W_PHO'],
		['C1_MOB','C1_COM',['C1_ADR',3]],
		['C2_NAME','C2_ID_NO','C2_HM_PHO','C2_W_PHO'],
		['C2_MOB','C2_COM',['C2_ADR',3]],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		['C4_NAME','C4_ID_NO','C4_HM_PHO','C4_W_PHO'],
		['C4_MOB','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		[['O_REC',3],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}
