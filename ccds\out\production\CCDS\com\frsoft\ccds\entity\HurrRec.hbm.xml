<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.HurrRec" table="hurr_rec" schema="dbo" >
        <id name="hurId" type="java.lang.Long">
            <column name="hur_id" />
            <generator class="identity" />
        </id>
        <property name="hurCat" type="java.lang.String">
            <column name="hur_cat" length="50" />
        </property>
        <many-to-one name="bankCase" class="com.frsoft.ccds.entity.BankCase" fetch="select">
            <column name="hur_cas_id" />
        </many-to-one>
        <property name="hurOper" type="java.lang.String">
            <column name="hur_oper" length="25" />
        </property>
        <property name="hurOpTime" type="java.util.Date">
            <column name="hur_op_time" length="23" />
        </property>
        <property name="hurOpCont" type="java.lang.String">
            <column name="hur_op_cont" length="**********" />
        </property>
        <property name="hurReId" type="java.lang.Long">
            <column name="hur_re_id" />
        </property>
    </class>
</hibernate-mapping>
