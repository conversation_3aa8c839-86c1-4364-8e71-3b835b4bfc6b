package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 类型列表实体类
 * 对应数据库表: type_list
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class TypeList {
    
    /** 类型ID */
    private Long typId;
    
    /** 类型名称 */
    private String typName;
    
    /** 类型描述 */
    private String typDesc;
    
    /** 类型分类 */
    private String typType;
    
    /** 是否启用 */
    private String typIsenabled;
}
