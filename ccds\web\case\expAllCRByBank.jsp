<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<base href="<%=basePath%>"></base>

		<title>导出查询结果催记(银行模板导出)</title>
		<link rel="shortcut icon" href="favicon.ico" />
		<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
        <style type="text/css">
        /*
		.scrollInFloatDiv{
			height:420px; width:300px;
		}
		#bankUl {
			font-weight:normal;
		}
		#bankUl ul{
			padding:0;
			margin:0;
			list-style:none;
		}
		#bankUl ul li label {
			padding:2px;
			width:230px;
		}
		*/
		.listSearch .inputSize2 {
			width:100px;
		}
     	#typeTable {
			width:100%;
		}
		#typeTable td {
			padding:5px;
			font-size:12px;
		}
		#chooseTypeLayer {
			text-align:left;
		}
		#buttonLayer {
			text-align:left;
			padding:5px;
		}
		#buttonLayer button {
			width:100px;
		}
		
		.chooseStepInf {
			padding:10px;
			margin-bottom:10px;
			font-size:18px;
			font-weight:bold;
			color:#666;
			text-align:left;
			background:#FFFFCC;
		}
		#chooseBanks li{
			width:250px;
			height:60px;
			text-align:left;
		}
		#chooseBanks li img {
			border:1px solid #ccc;
			cursor:pointer;
		}
        </style>
		<script type="text/javascript" src="js/prototype.js"></script>
		<script type="text/javascript" src="js/common.js"></script>
		<script type="text/javascript" src="js/formCheck.js"></script>
		<script type="text/javascript" src="js/case.js"></script>
		<script type="text/javascript" src="js/config.js"></script>
		<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
		<script language="javaScript" type="text/javascript">
			function loadBankImgs(){
				var appendHTML = [];
				var bankImgs = [
					["ccb","images/dsIcon/bCase/banks/CCB.gif"],
					["icbc","images/dsIcon/bCase/banks/ICBC.gif"],
					["abc","images/dsIcon/bCase/banks/ABC.gif"],
					["boc","images/dsIcon/bCase/banks/BOC.gif"],
					["bocom","images/dsIcon/bCase/banks/BOCOM.gif"],
					["cmb","images/dsIcon/bCase/banks/CMB.gif"],
					["ceb","images/dsIcon/bCase/banks/CEB.gif"],
					["cgb","images/dsIcon/bCase/banks/CGB.gif"],
					["citi","images/dsIcon/bCase/banks/CITI.gif"],
					["cib","images/dsIcon/bCase/banks/CIB.gif"],
					["cmbc","images/dsIcon/bCase/banks/CMBC.gif"],
					["pa","images/dsIcon/bCase/banks/PA.gif"],
					["spdb","images/dsIcon/bCase/banks/SPDB.gif"],
					["nbcb","images/dsIcon/bCase/banks/NBCB.gif"],
					["hccb","images/dsIcon/bCase/banks/HCCB.gif"],
					["njbc","images/dsIcon/bCase/banks/NJBC.gif"],
					["jsbc","images/dsIcon/bCase/banks/JSBC.gif"],
					["bosh","images/dsIcon/bCase/banks/BOSH.gif"],
					["psbc","images/dsIcon/bCase/banks/PSBC.gif"],
					["czb","images/dsIcon/bCase/banks/CZB.gif"],
					["hfb","images/dsIcon/bCase/banks/HFB.gif"],
					["bjrcb","images/dsIcon/bCase/banks/BJRCB.gif"],
					["hmc","images/dsIcon/bCase/banks/HMC.gif"],
					["zxwd","images/dsIcon/bCase/banks/ZXWD.gif"],
					["yx","images/dsIcon/bCase/banks/YX.gif"],
					["tyt","images/dsIcon/bCase/banks/TYT.gif"],
					["cfp","images/dsIcon/bCase/banks/CFP.gif"],
					["saic","images/dsIcon/bCase/banks/SAIC.gif"],
					["nwd","images/dsIcon/bCase/banks/NWD.gif"],
					["uc","images/dsIcon/bCase/banks/UC.gif"],
					["vc","images/dsIcon/bCase/banks/VC.gif"],
					["nnbk","images/dsIcon/bCase/banks/NNBK.gif"],
					["mbp","images/dsIcon/bCase/banks/MBP.gif"]
				];
				
				if("${CUS_VER_ID}"=="25"){
					bankImgs.push(["shjs","images/dsIcon/bCase/banks/GCS.jpg"]);
				}
				else if("${CUS_VER_ID}"=="51"){
					bankImgs =  [
						["icbc","images/dsIcon/bCase/banks/ICBC.gif"],
						["citi","images/dsIcon/bCase/banks/CITI.gif"],
						["saic","images/dsIcon/bCase/banks/SAIC.gif"]
					];
				}
				else if("${CUS_VER_ID}"=="73"){
					bankImgs.push(["syyk","images/dsIcon/bCase/banks/SYYK.gif"]);
				}
				else if("${CUS_VER_ID}"=="114"){
					bankImgs.push(["shdzzy","images/dsIcon/bCase/banks/SHDZZY.gif"]);
				}
				else if("${CUS_VER_ID}"=="239"){
					bankImgs.push(["bjjh","images/dsIcon/bCase/banks/BJJH.gif"]);
				}
				else if("${CUS_VER_ID}"=="233"){
					bankImgs.push(["ohbc","images/dsIcon/bCase/banks/OHBC.gif"]);
					bankImgs.push(["yhbc","images/dsIcon/bCase/banks/YHBC.gif"]);
					bankImgs.push(["shyj","images/dsIcon/bCase/banks/SHYJ.gif"]);
				}
				for(var i=0;i<bankImgs.length;i++){
					appendHTML.push("<li><img onMouseOver='overImg(1,this)' onMouseOut='overImg(0,this)' onClick=\"toNextStep('"+bankImgs[i][0]+"')\" src='"+bankImgs[i][1]+"'/></li>");
				}
				$("chooseBanks").innerHTML = appendHTML.join("");
			}
			function overImg(isOver,obj){
				if(isOver=="1"){
					obj.style.border = "1px solid #fdc51b";
					obj.style.background = "#fefbf2";
				}
				else{
					obj.style.border = "1px solid #ccc";
					obj.style.background = "none";
				}
			}
			
			function toNextStep(bank){
				$("chooseBankLayer").hide();
				var typeArray =null;
				var checkedId = "";
				switch(bank){
				case 'icbc':
					typeArray = [
						[["工行催记(excel)","icbcRow"],["工行催收情况(excel)","icbc2Row"],["工行催记(word)","icbcWord"],["工行案件信息(word)","icbcInfWord"],["工行外访反馈报告(word)","icbcVisWord"]]
					];
					checkedId = "icbcRow";
					break;
				case 'abc':
					typeArray = [
						[["农行催记(excel)","abc2Row"],["农行催记旧版(excel)","abcRow"],["农行催记(txt)","abcTxtRow"],["农行委案催收小结(word)","abcWord"]]
					];
					checkedId = "abc2Row";
					break;
				case 'boc':
					typeArray = [
						[["中行贷款催记","bocRow"],["中行信用卡催记","boc2Row"],["中银催收记录","boc3Row"],["中行个贷催记","bocCaseRow"],["广东中行催记","boc4Row"],["银行卡透支登记簿(word)","bocWord"]]
					];
					checkedId = "bocRow";
					break;
				case 'bocom':
					typeArray = [
						[["交行催记(excel)","bocomXlsRow"],["交行催记(txt)","bocomRow"],["交行在催案件(正常及暂停案件)(txt)","bocomClCase"]]
					];
					checkedId = "bocomRow";
					break;
				case 'cmb':
					typeArray = [
						[["招行催记1","cmbTab"],["招行催记2","cmb2Tab"]]
					];
					checkedId = "cmbTab";
					break;
				case 'ccb':
					typeArray = [
						[["建行催记(excel)","ccbRow"],["建行催记(word)","ccbWord"],["建行行动记录","ccbTab"],["建行案件信息(word)","ccbInfoWord"],["建行催收档案(word)","ccbFileWord"]]
					];
					checkedId = "ccbRow";
					break;
				case 'ceb':
					typeArray = [
						[["光大催记","gdRow"],["光大案件信息","gdCase"]]
					];
					checkedId = "gdRow";
					break;
				case 'cgb':
					typeArray = [
						[["广发催记","gfRow"]]
					];
					if("${CUS_VER_ID}"=="2"){
						typeArray = [
							[["广发催记","gfRow"],["广发月底统计","gfMonRow"]]
						];
					}
					checkedId = "gfRow";
					break;
				case 'citi':
					typeArray = [
						[["中信催记1","citiRow2"],["中信催记2","citiRow"]]
					];
					checkedId = "citiRow2";
					break;
				case 'hccb':
					typeArray = [
						[["杭州银行催记","hccbRow"]]
					];
					checkedId = "hccbRow";
					break;
				case 'hmc':
					typeArray = [
						[["捷信催记","hmcPrRow"],["捷信周报(案件)","hmcRow"],["捷信周报(催记)","hmcWeeklyPrRow"]]
					];
					checkedId = "hmcPrRow";
					break;
				case 'nbcb':
					typeArray = [
						[["宁波银行催记","nbcbRow"]]
					];
					checkedId = "nbcbRow";
					break;
				case 'shjs':
					typeArray = [
						[["商业案件系统数据","shjsRow"],["案件跟进记录","shjsTab"]]
					];
					checkedId = "shjsRow";
					break;
				case 'saic':
					typeArray = [
						[["上汽反馈表","saicCase"]]
					];
					checkedId = "saicCase";
					break;
				case 'zxwd':
					typeArray = [
						[["跟进情况表","zxFollow"],["退件报告","zxBack"],["催收周报(所有催记)","zxWeek"],["催收周报(最新催记)","zxWeekNew"]]
					];
					checkedId = "zxFollow";
					break;
				case 'pa':
					typeArray = [
						[["平安CGI","paRow"],["平安信用卡","paTab"]]
					];
					checkedId = "paRow";
					break;
				case 'cfp':
					typeArray = [
						[["友诚催记","cfpTab"]]
					];
					checkedId = "cfpTab";
					break;
				case 'yx':
					typeArray = [
						[["宜信催记","yxRow"]]
					];
					checkedId = "yxRow";
					break;
				case 'syyk':
					typeArray = [
						[["盈科催记","syykRow"]]
					];
					checkedId = "syykRow";
					break;
				case 'shdzzy':
					typeArray = [
						[["德载中怡催记","shdzzyWord"]]
					];
					checkedId = "shdzzyWord";
					break;
				case 'cib':
					typeArray = [
						[["兴业催记(txt)","cibRow"]]
					];
					checkedId = "cibRow";
					break;
				case 'tyt':
					typeArray = [
						[["催记跟进表","tytPrRow"]]
					];//,["在催案件联系情况表","tytCaseRow"]
					checkedId = "tytPrRow";
					break;
				case 'spdb':
					typeArray = [
						[["浦发催记反馈表","spdbPrRow"],["浦发案件反馈表","spdbCaseRow"]]
					];
					checkedId = "spdbPrRow";
					break;
				case 'nwd':
					typeArray = [
						[["你我贷周报","nwdCaseRow"]]
					];
					checkedId = "nwdCaseRow";
					break;
				case 'cmbc':
					typeArray = [
						[["民生催记","cmbcPrRow"]]
					];
					checkedId = "cmbcPrRow";
					break;
				case 'uc':
					typeArray = [
						[["友信人人贷催记","rrdPrRow"]]
					];
					checkedId = "rrdPrRow";
					break;
				case 'vc':
					typeArray = [
						[["维信理财催记","vcPrRow"]]
					];
					checkedId = "vcPrRow";
					break;
				case 'bjjh':
					typeArray = [
						[["车贷结案报告(word)","bjjhWord"]]
					];
					checkedId = "bjjhWord";
					break;
				case 'njbc':
					typeArray = [
						[["南京银行催记","njbcPrRow"]]
					];
					checkedId = "njbcPrRow";
					break;
				case 'jsbc':
					typeArray = [
						[["江苏银行催记","jsbcRow"]]
					];
					checkedId = "jsbcPrRow";
					break;
				case 'ohbc':
					typeArray = [
						[["温鸥合行催记","ohbcRow"]]
					];
					checkedId = "ohbcPrRow";
					break;
				case 'yhbc':
					typeArray = [
						[["玉环合行催记","yhbcRow"]]
					];
					checkedId = "yhbcPrRow";
					break;
				case 'shyj':
					typeArray = [
						[["风险管理处电子档案","shyjWord"]]
					];
					checkedId = "shyjWord";
					break;
				case 'psbc':
					typeArray = [
						[["个贷催记","psbcLoanTab"],["信用卡催记","psbcCredTab"]]
					];
					checkedId = "psbcLoanTab";
					break;
				case 'nnbk':
					typeArray = [
						[["诺诺镑客周报","nnbkCaseRow"]]
					];
					checkedId = "nnbkCaseRow";
					break;
				case 'bosh':
					typeArray = [
						[["上海银行案件信息(word)","boshWord"]]
					];
					checkedId = "boshWord";
					break;
				case 'mbp':
					typeArray = [
						[["手机贷催记1","mbpPrRow1"],["手机贷催记2","mbpPrRow2"]]
					];
					checkedId = "mbpPrRow1";
					break;
					
				case 'czb':
					typeArray = [
						[["浙商银行催记","czbPrRow"]]
					];
					checkedId = "czbPrRow";
					break;

				case 'hfb':
					typeArray = [
						[["汇丰银行催记","hfbPrRow"]]
					];
					checkedId = "hfbPrRow";
					break;
					
				case 'bjrcb':
					typeArray = [
						[["北农商行催记","bjrcbPrRow"]]
					];
					checkedId = "bjrcbPrRow";
					break;
				}
				var tabHtmls = [];
				var colSpan = 1;
				if(typeArray!=null&&typeArray!=undefined&&typeArray.length>0){
					colSpan = typeArray[0].length;
					tabHtmls.push("<tr><td colspan='"+colSpan+"'><input type='radio' name='type' id='defaultType' "+(checkedId==""?"checked":"")+" value='default' /><label for='defaultType'>标准催记&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label></td></tr>");
					for(var i=0;i<typeArray.length;i++){
						tabHtmls.push("<tr>");
						for(var j=0;j<typeArray[i].length;j++){
							tabHtmls.push("<td "+(typeArray[i][j].length>2?"colspan="+typeArray[i][j][2]:"")+" ><input type='radio' name='type' id='"+typeArray[i][j][1]+"Type' value='"+typeArray[i][j][1]+"' "+(checkedId==typeArray[i][j][1]?"checked":"")+" /><label for='"+typeArray[i][j][1]+"Type'>"+typeArray[i][j][0]+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label></td>");
						}
						tabHtmls.push("</tr>");
					}
				}
				else{
					tabHtmls.push("<tr><td><input type='radio' name='type' id='defaultType' checked value='default' /><label for='defaultType'>标准催记</label></td></tr>");
				}
				tabHtmls.push("</table>");
				$("chooseTypeLayer").innerHTML = tabHtmls.join("");
				
				$("searchLayer").show();
			}
			
			function toExpAllCR(){
				if(isEmpty("bankName")){
					alert("未选择"+getBankTxt("${CUS_VER_ID}")+"！");
					return false;
				}
				else{
					createConfirmWindow("","导出催记报告","caseAction.do?op=toExpCRConfirm",250,100);
					//formSubmit('searchForm');
				}
			}
			
			function getFormArgs(){
				var pars = $("searchForm").serialize(true);
				return pars;
			}
			
			createProgressBar();
			window.onload=function(){
				loadBankImgs();
				loadPaidState('paidState');
				loadCaseState('caseState');
				$("caseState").value="u";//默认未退案
				$("casNameTxt").innerHTML=getCasNameTxt("${CUS_VER_ID}");
				var bankNameTxt = getBankTxt("${CUS_VER_ID}");
				$("bankTxt").innerHTML=bankNameTxt;
				$("bankTxt1").innerHTML=bankNameTxt;
				$("casCaCdTxt").innerHTML = getCasCaCdTxt("${CUS_VER_ID}");
				closeProgressBar();
			}

  	</script>
	</head>


  <body>
 	<div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>数据管理 > 催记报告 <span id="changeFuncBt" onMouseOver="popFuncMenu(['case',4],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['case',4],true)" onMouseOut="popFuncMenu(['case',4],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
 			</div>
          <table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                        	<div id="tabType1" class="tabTypeWhite" onClick="self.location.href='caseAction.do?op=toExpAllCRByBank'">催记报告</div>
                        </div>
                    </th>
                </tr>
            </table>
			<script type="text/javascript">loadTabTypeWidth();</script>
			<div id="listContent">
            	<div id="chooseBankLayer">
                    <div class="chooseStepInf">① 选择<span id="bankTxt"></span>：</div>
                    <ul id="chooseBanks" class="ulHor"></ul>
                </div>
                <div id="searchLayer" style="display:none">
                	<div class="chooseStepInf">② 填写查询条件：</div>
                    <form class="listSearchForm" id="searchForm" method="post" >
                    <div class="listSearch">
                        <table cellpadding="0" cellspacing="0">
                            <tr>
                            	<th class="required"><span id="bankTxt1"></span>：<span class='red'>*</span></th>
                                <td><input type="hidden" id="bankId" name="bankId" /><input style="width:116px;" class="inputSize2 inputBoxAlign" type="text" id="bankName" name="bankName" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty bankList}"><select class="inputSize2 inputBoxAlign" style="width:70px" onChange="setBankNameAndIdFromSel(this,'bankId','bankName')"><option value="">请选择</option><c:forEach items="${bankList}" var="bList"><option value="${bList.typId}">${bList.typName}</option></c:forEach></select></c:if><c:if test="${empty bankList}"><select class="inputSize2 inputBoxAlign" disabled="disabled" style="width:70px"><option>未添加</option></select></c:if></td>
                                <th>催收区域：</th>
                           		<td>
                                <c:if test="${!empty userAreaList}">
                                <select name='clArea' class="inputSize2">
                                    <option value="<c:forEach items="${userAreaList}" var="uArea">${uArea.uarArea.typId},</c:forEach>">全部</option>
                                    <c:forEach items="${userAreaList}" var="userArea">
                                    <option value="${userArea.uarArea.typId}">${userArea.uarArea.typName}</option>
                                    </c:forEach>
                                </select>
                                </c:if>
                                <c:if test="${empty userAreaList}">
                                    <c:if test="${!empty areaList}">
                                    <select name='clArea' class="inputSize2">
                                        <option value="">全部</option>
                                        <c:forEach items="${areaList}" var="area">
                                        <option value="${area.typId}">${area.typName}</option>
                                        </c:forEach>
                                    </select>
                                    </c:if>
                                    <c:if test="${empty areaList}">
                                    <select name='clArea' class="inputSize2" style="width:145px">
                                        <option value="">未添加</option>
                                    </select>
                                    </c:if>
                                </c:if></td>
                                <th>案件状态：</th>
                                <td><select id="caseState" name="casState" class="inputSize2 "><option value="u">未退案</option></select></td>
                                <th>部门：</th>
                                <td><select id="org" name="org" class="inputSize2 inputBoxAlign" onChange="loadOrgEmp(this)"><option value="">全部</option><c:if test="${!empty orgList}"><c:forEach items="${orgList}" var="oList"><option value="${oList.soCode}">${oList.soName}</option></c:forEach></c:if></select></td>
                                <th>个案序列号：</th>
                                <td><input class="inputSize2 inputBoxAlign" type="text" id="caseCode" name="caseCode" onBlur="autoShort(this,100)"/></td>
                            </tr>
                            <tr>
                            	<th>催收员：</th>
                                <td><input style="width:116px;" class="inputSize2 inputBoxAlign" type="text" id="empName" name="empName" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty empList}"><select id="empSel" class="inputSize2 inputBoxAlign" style="width:70px" onChange="setEmpNameFromSel(this,'empName')"><option value="">请选择</option><c:forEach items="${empList}" var="eList"><option value="${eList.seName}">${eList.seName}</option></c:forEach></select></c:if><c:if test="${empty empList}"><select class="inputSize2 inputBoxAlign" style="width:70px" disabled="disabled"><option>未添加</option></select></c:if></td>
                                <th>批次号：</th>
                                <td colspan="5"><input class="inputSize2" style=" width:365px" type="text" id="batCodeStr" name="batCode" onDblClick="cleanBatCodeInput()"/><input type="hidden" id="batIds" name="batIds"/>&nbsp;<button class="butSize2" onClick="showBatList()" style="width:40px">选择</button></td>
                                <th>案件类型：</th>
                                <td><c:if test="${!empty caseTypeList}"><select id="caseTypeId" name="caseTypeId" class="inputSize2 inputBoxAlign"><option value="">请选择</option><c:forEach items="${caseTypeList}" var="cTypeList"><option value="${cTypeList.typId}">${cTypeList.typName}</option></c:forEach></select></c:if><c:if test="${empty caseTypeList}"><select id="caseTypeId" name="caseTypeId" class="inputSize2 inputBoxAlign" disabled="disabled"><option>未添加</option></select></c:if></td>
                            </tr>
                            <tr>
                            	<th>委案日期：</th>
                                <td><input name="caseDateStart" id="cdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('cdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'cdEnd\')}'})"/>&nbsp;到&nbsp;<input name="caseDateEnd" id="cdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'cdStart\')}'})"/></td>
                                <th>逾期账龄：</th>
                                <td colspan="5"><input class="inputSize2" style=" width:365px" type="text" id="excLimStr" name="excLim" onDblClick="cleanExcLimInput()"/><input type="hidden" id="excLimIds" name="excLimIds"/>&nbsp;<button class="butSize2" onClick="showExcLimList('bankId')" style="width:40px">选择</button></td>
                                <th>催收状态：</th>
                                <td><c:if test="${!empty caseStateList}"><select id="caseStateId" name="caseStateId" class="inputSize2 inputBoxAlign"><option value="">请选择</option><option value="NULL">新案</option><c:forEach items="${caseStateList}" var="cStateList"><option value="${cStateList.typId}">${cStateList.typName}</option></c:forEach></select></c:if><c:if test="${empty caseStateList}"><select id="caseStateId" name="caseStateId" class="inputSize2 inputBoxAlign" disabled="disabled"><option>未添加</option></select></c:if></td>
                            </tr>
                            <tr>
                            	<th>催收日期：</th>
                                <td><input name="clDateStart" id="clDateStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('clDateEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'clDateEnd\')}'})"/>&nbsp;到&nbsp;<input name="clDateEnd" id="clDateEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'clDateStart\')}'})"/></td>
                                <th>地区：</th>
                                <td colspan="3">
                                <input style="width:40px;" class="inputSize2 inputBoxAlign" type="text" id="area1" name="area1" onBlur="autoShort(this,100)"/>&nbsp;<select class="inputBoxAlign inputSize4" style="width:51px;" id="area1Sel" onChange="setCityValue(1)"><c:if test="${!empty provList}"><c:forEach var="provList" items="${provList}"><option value="${provList.areId}">${provList.areName}</option></c:forEach></c:if></select>&nbsp;<input style="width:40px;" class="inputSize2 inputBoxAlign" type="text" id="area2" name="area2" onBlur="autoShort(this,100)"/>&nbsp;<select class="inputBoxAlign inputSize4" style="width:51px;" id="area2Sel" onChange="setCityValue(2)"></select>&nbsp;<input style="width:40px;" class="inputSize2 inputBoxAlign" type="text" id="area3" name="area3" onBlur="autoShort(this,100)"/>&nbsp;<select class="inputBoxAlign inputSize4" style="width:51px;" id="area3Sel" onChange="setCityValue(3)"></select></td>
                                <th><span id="casNameTxt"></span>：</th>
                                <td><input class="inputSize2 inputBoxAlign" type="text" id="caseName" name="caseName" onBlur="autoShort(this,50)"/></td>
                                <th>证件号：</th>
                                <td><input class="inputSize2 inputBoxAlign" type="text" id="caseNum" name="caseNum" onBlur="autoShort(this,25)"/></td>
                                
                            </tr>
                            <tr>
                            	<th>预计退案日：</th>
                            	<td><input name="planDateStart" id="pdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('pdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'pdEnd\')}'})"/>&nbsp;到&nbsp;<input name="planDateEnd" id="pdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'pdStart\')}'})"/></td>
                                <th>分配状态：</th>
                                <td><select id="assignState" name="assignState"  class="inputSize2 inputBoxAlign">
                                    <option value="">全部</option>
                                    <option value="0">未分配</option>
                                    <option value="1">已分配</option>
                                </select></td>
                                <th>还款情况：</th>
                                <td><select id="paidState" name="paidState" class="inputSize2 inputBoxAlign"></select></td>
                                <th><span id="casCaCdTxt"></span>：</th>
                                <td><input class="inputSize2 inputBoxAlign" type="text" id="caseCaCd" name="caseCaCd" onBlur="autoShort(this,50)"/></td>
                                <th>账号：</th>
                                <td><input class="inputSize2 inputBoxAlign" type="text" id="accNum" name="accNum" onBlur="autoShort(this,25)"/></td>
                            </tr>
                            <tr>
                            	<th>实际退案日：</th>
                                <td><input name="backDateStart" id="backdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('backdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'backdEnd\')}'})"/>&nbsp;到&nbsp;<input name="backDateEnd" id="backdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'backdStart\')}'})"/></td>
                                <th>最后跟进日：</th>
                                <td colspan="2"><input name="prTimeStart" id="ptStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('ptEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'ptEnd\')}'})"/>&nbsp;到&nbsp;<input name="prTimeEnd" id="ptEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'ptStart\')}'})"/></td>
                                <th>委案金额：</th>
                        		<td  colspan="2"><input class="inputSize2" style="width:63px" type="text" name="casMMin" onBlur="checkIsNum(this)"/>&nbsp;&nbsp;-&nbsp;&nbsp;<input class="inputSize2" style="width:63px" type="text" name="casMMax" onBlur="checkIsNum(this)"/></td>
                                <th>档案号：</th>
                        		<td><input class="inputSize2" type="text" id="fileNo" name="fileNo" onBlur="autoShort(this,100)"/></td>
                            </tr>
                        </table>
                        </div>
                       	<div class="chooseStepInf">③ 选择模板导出：</div>
                        <div id="chooseTypeLayer"></div>
                        <div id="buttonLayer"><button onClick="toExpAllCR()">导出数据</button></div>
                        </form>
                        <!--
                                <th rowspan="2" style="vertical-align:top"><span id="bankTxt"></span>：</th>
                                <td colspan="5" rowspan="2" style="vertical-align:top"><div class='blue' style="font-weight:normal">[<a href="javascript:void(0)" onClick="floatTipsLayer('bankUl',15,-76);">点击选择</a>]</div><div id="bankUl" class="floatListDiv" style="display:none">
                        <div class='blue' style='padding:2px; text-align:right'>[<a href="javascript:void(0)" onClick="floatTipsLayer('bankUl');return false;">点击关闭</a>]</div>
                        <c:if test="${!empty bankList}">
                        <div class="scrollInFloatDiv">
                        <ul>
                        <c:forEach items="${bankList}" var="bList">
                        <li><input type="checkbox" class="inputBoxAlign" id="bankIdCB${bList.typId}" name="bankIds" value="${bList.typId}" onClick="addBankName(this)" /><label id="bankIdLA${bList.typId}" for="bankIdCB${bList.typId}">${bList.typName}</label></li>
                        </c:forEach>
                        </ul>
                        </div>
                        
                        </c:if>
                        <c:if test="${empty bankList}">未添加委托方</c:if>
                    </div><textarea class="inputSize2 lockBack" readonly style="width:520px;" id="bankNames" rows="2"></textarea></td>
                    -->
				</div>
			</div>
		</div>
  	</div>
	</body>
</html>
