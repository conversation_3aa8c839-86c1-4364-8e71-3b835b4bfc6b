<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.SpoPaidPlan" table="spo_paid_plan" schema="dbo" >
        <id name="sppId" type="java.lang.Long">
            <column name="spp_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salPurOrd" class="com.psit.struts.entity.SalPurOrd" fetch="select" not-null="false">
            <column name="spp_spo_id" />
        </many-to-one>
         <property name="sppContent" type="java.lang.String">
            <column name="spp_content" length="100" />
        </property>
        <property name="sppPrmDate" type="java.util.Date">
            <column name="spp_prm_date" length="23" />
        </property>
        <property name="sppPayMon" type="java.lang.Double">
            <column name="spp_pay_mon" precision="18" />
        </property>
        <property name="sppInpUser" type="java.lang.String">
            <column name="spp_inp_user" length="50" />
        </property>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select" not-null="false">
            <column name="spp_resp" length="50" />
        </many-to-one>
        <property name="sppIsp" type="java.lang.String">
            <column name="spp_isp" length="1" />
        </property>
        <property name="sppCreDate" type="java.util.Date">
            <column name="spp_cre_date" length="23" />
        </property>
        <property name="sppAltDate" type="java.util.Date">
            <column name="spp_alt_date" length="23" />
        </property>
        <property name="sppAltUser" type="java.lang.String">
            <column name="spp_alt_user" length="50" />
        </property>
        <property name="sppIsdel" type="java.lang.String">
            <column name="spp_isdel" length="1" />
        </property>
    </class>
</hibernate-mapping>
