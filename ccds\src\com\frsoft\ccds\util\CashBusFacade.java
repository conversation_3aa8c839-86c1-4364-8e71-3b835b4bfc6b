package com.frsoft.ccds.util;

import com.psit.struts.DAO.CashBusLoanCaseDAO;
import com.psit.struts.DAO.CashBusWorkOrderDAO;
import com.psit.struts.entity.CashBusLoanCase;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 现金巴士接口对接
 * Created by comfan on 2017/7/20.
 */
public class CashBusFacade {
    private HashMap headers;
    private String urlPrefix;
    private HttpsUtils httpsUtils;
    protected Logger log;
    private CashBusWorkOrderDAO workOrderDao;
    private int defaultPullSize = 200;
    private CashBusLoanCaseDAO loanCaseDAO;

    protected static final String GET_METHOD  = "get";
    protected static final String POST_METHOD = "post";
    protected static final String PUT_METHOD  = "put";

    public CashBusFacade(HashMap headers, String urlPrefix,HttpsUtils  httpsUtils) {
        this.headers = headers;
        this.urlPrefix = urlPrefix;
        this.httpsUtils = httpsUtils;
        log = Logger.getLogger("transMsgLog");
    }

    public HttpsUtils getHttpsUtils() {
        return httpsUtils;
    }

    public void setHttpsUtils(HttpsUtils httpsUtils) {
        this.httpsUtils = httpsUtils;
    }

    public HashMap getHeaders() {
        return headers;
    }

    public void setHeaders(HashMap headers) {
        this.headers = headers;
    }

    public String getUrlPrefix() {
        return urlPrefix;
    }

    public void setUrlPrefix(String urlPrefix) {
        this.urlPrefix = urlPrefix;
    }

    public CashBusWorkOrderDAO getWorkOrderDao() {
        return workOrderDao;
    }

    public void setWorkOrderDao(CashBusWorkOrderDAO workOrderDao) {
        this.workOrderDao = workOrderDao;
    }

    public int getDefaultPullSize() {
        return defaultPullSize;
    }

    public void setDefaultPullSize(int defaultPullSize) {
        this.defaultPullSize = defaultPullSize;
    }

    public CashBusLoanCaseDAO getLoanCaseDAO() {
        return loanCaseDAO;
    }

    public void setLoanCaseDAO(CashBusLoanCaseDAO loanCaseDAO) {
        this.loanCaseDAO = loanCaseDAO;
    }

    /**
     * 获取所有批次
     * @return  所有批次JSON对象
     */
    public JSONObject getAllBatchs(){
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        final String response = httpsUtils.request(urlPrefix + "outsource/allBatch", GET_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 获取批次（借款）信息
     * @param batchName 批次ID(BatchRefId)
     * @param offset    首条偏移
     * @param limit     获取条数
     * @return  批次内（借款）信息
     */
    public JSONObject getBatch(final String batchName,final int offset,final int limit){
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId" , batchName);
        httpParams.put("offset"     , String.valueOf(offset));
        httpParams.put("limit"      , String.valueOf(limit));
        final String response = httpsUtils.request(urlPrefix + "outsource/batch", GET_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 获取工单信息
     * @param offset    首条偏移
     * @param limit     获取条数
     * @return  工单接口返回消息
     */
    public JSONObject getWorkOrder(final int offset, final int limit) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("offset"     , String.valueOf(offset));
        httpParams.put("limit"      , String.valueOf(limit));
        final String response = httpsUtils.request(urlPrefix + "outsource/notice", GET_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    public JSONObject dayFaildReplayment(final long day){
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("date"      , String.valueOf(day));
        final String response = httpsUtils.request(urlPrefix + "outsource/dayFailedRepaymentLog", GET_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 提交入账申请
     * @param batchName         批次名
     * @param loanRefId         借款ID
     * @param repaymentMethod   还款方式
     * @param collectedAmount   还款金额
     * @param picUrl            还款截图（可选）
     * @param comment           备注（还款账号等）
     * @param repaymentTime     还款时间(日期)
     * @return
     */
    public JSONObject repaymentRequset(final String batchName, final String loanRefId, final String repaymentMethod, final int collectedAmount, final String picUrl, final String comment, final long repaymentTime) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId"         , batchName);
        httpParams.put("loanRefId"          , loanRefId);
        httpParams.put("repaymentMethod"    , repaymentMethod);
        httpParams.put("collectedAmount"    , String.valueOf(collectedAmount));
        if(StringUtils.isNotBlank(picUrl)) {
            httpParams.put("picUrl"         , picUrl);
        }
        httpParams.put("comment"            , comment);
        httpParams.put("repaymentTime"      , String.valueOf(repaymentTime));
        final String response = httpsUtils.request(urlPrefix + "outsource/repaymentRequest", POST_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 查询入账状态
     * @param batchRefId    批次名,ID
     * @param loanRefId     借款ID
     * @param auditStatus   审核状态
     * @param timeStart     （还款）开始时间
     * @param timeEnd       （还款）截止时间
     * @param limit         页容量
     * @param offset        页偏移量
     * @return  返回JSON对象
     */
    public JSONObject repaymentRequestList(final String batchRefId, final String loanRefId, final String auditStatus, final long timeStart, final long timeEnd, final int limit, final int offset) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId"         , batchRefId);
        httpParams.put("loanRefId"          , loanRefId);
        httpParams.put("auditStatus"        , auditStatus);
        httpParams.put("startTime"          , String.valueOf(timeStart) );
        httpParams.put("endTime"            , String.valueOf(timeEnd==0?new Date().getTime():timeEnd) );
        httpParams.put("offset"             , String.valueOf(offset) );
        httpParams.put("limit"              , String.valueOf(limit) );

        final String response = httpsUtils.request(urlPrefix + "outsource/repaymentRequestList", GET_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 查询还款明细
     * @param batchRefId    批次名（ID）
     * @param loanRefId     借款ID
     * @return  还款明细JSON对象
     */
    public JSONObject repaymentDetail(final String batchRefId, final String loanRefId) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId"         , batchRefId);
        httpParams.put("loanRefId"          , loanRefId);

        final String response = httpsUtils.request(urlPrefix + "outsource/repaymentDetail", GET_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 请求敏感信息
     * @param batchRefId            批次名(ID)
     * @param loanRefId             借款ID
     * @param applicationCategory   申请敏感信息类型 （idcard:身份证, contract:合同）
     * @param applictionTime        申请时间
     * @return  申请结果JSON对象
     */
    public JSONObject sensitiveInfoRequest(final String batchRefId, final String loanRefId, final String applicationCategory,final long applictionTime) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId"         , batchRefId);
        httpParams.put("loanRefId"          , loanRefId);
        httpParams.put("applicationCategory", applicationCategory);
        httpParams.put("applicationTime"    , String.valueOf(applictionTime));

        final String response = httpsUtils.request(urlPrefix + "outsource/sensitiveInfoRequest", POST_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 敏感信息审核结果
     * @param batchRefId    批次名（ID）
     * @param loanRefId     借款ID
     * @param auditStatus   审核状态
     * @param startTime     开始时间
     * @param endTime       截止时间
     * @param offset        页偏移量
     * @param limit         页容量
     * @return  审核结果 JSON对象
     */
    public JSONObject sensitiveInfoRequestStat(final String batchRefId, final String loanRefId, final String auditStatus,final long startTime,final long endTime,final int offset, final int limit) {
            final HashMap<String, String> httpParams = new HashMap<String, String>();
            httpParams.put("batchRefId"         , batchRefId);
            httpParams.put("loanRefId"          , loanRefId);
            httpParams.put("auditStatus"        , auditStatus);
            httpParams.put("startTime"          , String.valueOf(startTime));
            httpParams.put("endTime"            , String.valueOf(endTime));
            httpParams.put("offset"             , String.valueOf(offset));
            httpParams.put("limit"              , String.valueOf(limit));

            final String response = httpsUtils.request(urlPrefix + "outsource/sensitiveInfoRequest", GET_METHOD, httpParams, headers);
            return JSONFromString(response);
    }

    /**
     * 获取敏感信息
     * @param batchRefId    批次名（ID）
     * @param loanRefId     借款ID
     * @return  敏感信息
     */
    public JSONObject sensitiveInfo(final String batchRefId, final String loanRefId) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId"         , batchRefId);
        httpParams.put("loanRefId"          , loanRefId);

        final String response = httpsUtils.request(urlPrefix + "outsource/sensitiveInfo", POST_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 催记
     * @param batchRefId    批次名（ID）
     * @param loanRefId     借款ID
     * @param content       催收记录内容
     * @return  结果JSON
     */
    public JSONObject record(final String batchRefId, final String loanRefId , final String content) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId"         , batchRefId);
        httpParams.put("loanRefId"          , loanRefId);
        httpParams.put("content"            , content);

        final String response = httpsUtils.request(urlPrefix + "outsource/collectionLog", POST_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 批次内日还款情况
     * @param batchRefId    批次名(ID)
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return  还款情况JSON对象
     */
    public JSONObject dayRepayment(final String batchRefId , final long startTime ,  final long endTime) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId"         , batchRefId);
        httpParams.put("startTime"          , String.valueOf(startTime));
        httpParams.put("endTime"            , String.valueOf(endTime));

        final String response = httpsUtils.request(urlPrefix + "outsource/dayRepaymentLog", GET_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 月度对账表
     * @param batchRefId    批次名（ID）
     * @param startTime     开始时间
     * @param endTime       截止时间
     * @param offset        页偏移量
     * @param limit         页容量
     * @return  月对账表JSON对象
     */
    public JSONObject monthReconciliation(final String batchRefId, final long startTime,final long endTime,final int offset,final int limit) {
        final HashMap<String, String> httpParams = new HashMap<String, String>();
        httpParams.put("batchRefId"         , batchRefId);
        httpParams.put("startTime"          , String.valueOf(startTime));
        httpParams.put("endTime"            , String.valueOf(endTime));
        httpParams.put("offset"             , String.valueOf(offset));
        httpParams.put("limit"              , String.valueOf(limit));

        final String response = httpsUtils.request(urlPrefix + "outsource/monthReconciliation", GET_METHOD, httpParams, headers);
        return JSONFromString(response);
    }

    /**
     * 请求资源
     * @param url   URL
     * @return  输入流
     */
    public InputStream getResource(final String url) {
        return httpsUtils.requestInput(url, GET_METHOD);
    }

    /**
     * 返回字符串转JSON对象，失败时记录日志
     * @param jsonString    json字符串
     * @return  JSON对象
     */
    public final JSONObject JSONFromString(final String jsonString) {
        try {
            return JSONObject.fromObject(jsonString);
        } catch (Throwable e) {
            log.error(String.format("现金巴士接口返回非标准数据！[%s]", jsonString), e);
            throw new RuntimeException(String.format("现金巴士接口返回非标准数据！[%s]",StringUtils.left(jsonString,20)) , e);
        }
    }

    /**
     * 从现金巴士接口拉取批次下案件信息
     * @param batchName 批次名
     * @return  Boolean
     */
    public boolean loadLoanCaseToData(final String batchName){
        int limit       = 0;
        int offset      = 0;
        int scopeLimit  = getDefaultPullSize();
        if (StringUtils.isBlank(batchName)) {
            return false;
        }
        while (limit > offset || limit == 0) {
            log.debug(String.format("需要获取  从%s获取 %s条  ", offset, scopeLimit));
            if (offset < limit || limit == 0) {
                try {
                    JSONObject resJson = getBatch(batchName, offset, scopeLimit);
                    if ("0000".equals(resJson.optString("resultCode"))) {
                        JSONObject result = resJson.optJSONObject("result");
                        JSONArray resultList = result.optJSONArray("resultList");
                        limit = result.optInt("totalCount", -1);
                        log.debug(String.format("limit of totalCount  %s   resultListSize  %s ", limit , resultList.size()));
                        if (resultList != null && resultList.size() > 0) {
                            for(int index = 0 ; index < resultList.size() ; index ++) {
                                JSONObject item = resultList.optJSONObject(index);
                                if (item != null) {
                                    CashBusLoanCase loanCase = new CashBusLoanCase();
                                    try {
                                        final String loanRefId = item.optString("loanRefId");
                                        List<CashBusLoanCase> byLoanRefId = loanCaseDAO.findByLoanRefId(batchName, loanRefId);
                                        if (byLoanRefId != null && byLoanRefId.size() > 0) {
                                            loanCase = byLoanRefId.get(0);
                                        }
                                        loanCase.setSrcJson(item.toString());
                                        loanCase.setAge(item.optInt("age"));
                                        loanCase.setAmount(item.optInt("amount"));
                                        loanCase.setBankCardNo(item.optString("bankCardNo"));
                                        loanCase.setBankName(item.optString("bankName"));
                                        loanCase.setBatchName(batchName);
                                        loanCase.setDays(item.optInt("days"));
                                        loanCase.setLoanRefId(loanRefId);
                                        loanCase.setMaritalStatus(item.optString("maritalStatus"));
                                        loanCase.setOriginalAmountDue(item.optInt("originalAmountDue"));
                                        loanCase.setRelativePhone(item.optString("relativePhone"));
                                        loanCase.setRelativeType(item.optString("relativeType"));
                                        loanCase.setSocialPhone(item.optString("socialPhone"));
                                        loanCase.setSocialType(item.optString("socialType"));
                                        loanCase.setUserCardNo(item.optString("userCardNo"));
                                        loanCase.setUserPhone(item.optString("userPhone"));
                                        loanCase.setWorkCity(item.optString("workCity"));
                                        loanCase.setWorkPhone(item.optString("workPhone"));
                                        loanCase.setName(item.optString("name"));
                                        if (byLoanRefId != null && byLoanRefId.size() > 0) {
                                            loanCaseDAO.update(loanCase);
                                        }else{
                                            loanCaseDAO.save(loanCase);
                                        }
                                    } catch (Exception e) {
                                        log.error(String.format("拉取现金巴士数据保存失败! %s ", loanCase), e);
                                    }
                                }
                            }

                        }else{
                            if (limit == 0 && offset == 0) {
                                limit = -1;
                                log.error(String.format("出现错误！   并且未获取到值   总记录数   %s    从%s开始获取", limit, offset));
                                return false;
                            }
                        }
                    } else {
                        if (limit == 0 || offset == 0) {
                            limit = -1;
                            log.error(String.format("出现错误！   并且未获取到值   总记录数   %s    从%s开始获取", limit, offset));
                            return false;
                        }
                    }
                } catch (Exception ex) {
                    log.error(" 刷新数据出现错误  ", ex);
                }
                offset += scopeLimit;
            }
        }
        return true;
    }

    /**
     * 从数据库获取现金巴士数据
     * @param batchName 批次名
     * @return  JSON对象，参考
     * @see #getBatch(String, int, int)
     */
    public JSONObject getLoanCase(final String batchName,final String searchKey , final int offest , final int limit) {
        JSONObject retJson = new JSONObject();
        retJson.put("resultCode", "0000");
        retJson.put("resultDesc", "成功");
        JSONObject result = new JSONObject();
        JSONArray resultList = new JSONArray();
        List<CashBusLoanCase> byBatchName = new ArrayList<CashBusLoanCase>();
        long byBatchNameCount = 0l;
        if(StringUtils.isBlank(searchKey)) {
            byBatchName = loanCaseDAO.findByBatchName(batchName, offest, limit);
            byBatchNameCount = loanCaseDAO.findByBatchNameCount(batchName);
        }else{
            byBatchName = loanCaseDAO.search(batchName, searchKey, offest, limit);
            byBatchNameCount = loanCaseDAO.searchCount(batchName, searchKey);
        }
        result.put("totalCount", byBatchNameCount);
        if (byBatchName != null && byBatchName.size() > 0) {
            for(int i = 0 ; i < byBatchName.size(); i++) {
                CashBusLoanCase loanCase = byBatchName.get(i);
                try {
                    resultList.add(JSONObject.fromObject(loanCase.getSrcJson()));
                } catch (Exception e) {
                    log.error(String.format("得到JSON失败!  \n  %s ", loanCase), e);
                }
            }
        }
        result.put("resultList", resultList);
        retJson.put("result", result);
        return retJson;
    }

}
