function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','MONTH_P'],
		['ID_NO','C_CODE','START_DT','ACC'],
		['AMT','PAID_LEFT','MIN_P','P_L'],
		['LAST_M','PRI','LEFT_PRI','M_CAT'],
		['OV_INT','OV_P','PROD','BIZ'],
		['STATE','TIPS_DT','BACK_DT','O_REC'],
		['AGE','DAYS','PTP','CP'],
		['EMP','ASS_TM','ASS_HIS','PR_COUNT'],
		['MOB','PHO','W_PHO','AREA'],
		['REG','HOM','W_ADDR','M_ADDR'],
		['C1_NAME','C2_NAME','C3_NAME','C4_NAME'],
		['C1_HM_PHO','C2_HM_PHO','C3_HM_PHO','C4_HM_PHO'],
		['C1_W_PHO','C2_W_PHO','C3_W_PHO','C4_W_PHO'],
		['C1_MOB','C2_MOB','C3_MOB','C4_MOB']
	);
}

function getLayout2(){
	return  new Array(
		['C_DT','F_BANK','EMAIL','H_POST'],
		['P_COUNT', 'HOST','BILL_DT','M_POST'],
		['ALT_HOLD','RMB','GB','MY'],
		['LAST_P','LAST_P_DT','LAST_C_DT','LAST_R_DT'],
		['LOAN_DT','OV_DT','APP_NO','FILE_NO'],
		['C_L','G_AMT','LOAN_T','STOP_DT'],
		['COM','W_POST','POS','PART'],
		['SC_PC_NO','SC_NO','CL_T','DELAY_LV'],
		['CL_AREA','C1_ID_NO','C1_COM','C1_ADR'],
		['C2_ID_NO','C2_COM',['C2_ADR',3]],
		['C3_ID_NO','C3_COM',['C3_ADR',3]],
		['C4_ID_NO','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		['C6_NAME','C6_ID_NO','C6_HM_PHO','C6_W_PHO'],
		['C6_MOB','C6_COM',['C6_ADR',3]],
		['NOOUT','CYCLE','P_CRLINE','BACK_AMT'],
		['COUNT','DEAD_L','REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',5],'TREMARK']
	);
}