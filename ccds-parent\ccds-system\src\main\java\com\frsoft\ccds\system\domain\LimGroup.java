package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 组实体类
 * 对应数据库表: lim_group
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class LimGroup {
    
    /** 组ID */
    private Long grpId;
    
    /** 组名称 */
    private String grpName;
    
    /** 组描述 */
    private String grpDesc;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date grpCreTime;
    
    /** 创建人 */
    private String grpCreMan;
}
