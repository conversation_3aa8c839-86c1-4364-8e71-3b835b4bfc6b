<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RSpoPro" table="r_spo_pro" schema="dbo" >
        <id name="rppId" type="java.lang.Long">
            <column name="rpp_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salPurOrd" class="com.psit.struts.entity.SalPurOrd" fetch="select" not-null="false">
            <column name="rpp_spo_id" />
        </many-to-one>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rpp_pro_id" />
        </many-to-one>
        <property name="rppNum" type="java.lang.Double">
            <column name="rpp_num" precision="18" />
        </property>
        <property name="rppPrice" type="java.lang.Double">
            <column name="rpp_price" precision="18" />
        </property>
        <property name="rppSumMon" type="java.lang.Double">
            <column name="rpp_sum_mon" precision="18" />
        </property>
        <property name="rppRemark" type="java.lang.String">
            <column name="rpp_remark" length="1073741823" />
        </property>
        <property name="rppOutNum" type="java.lang.Double">
            <column name="rpp_out_num" precision="18" />
        </property>
        <property name="rppRealNum" type="java.lang.Double">
            <column name="rpp_real_num" precision="18" />
        </property>
    </class>
</hibernate-mapping>
