<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.RMessLim" table="r_mess_lim" schema="dbo">
        <id name="rmlId" type="java.lang.Long">
            <column name="rml_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="message" class="com.frsoft.base.entity.Message" fetch="select" not-null="false">
            <column name="rml_me_code" length="50" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select"  not-null="false">
            <column name="rml_se_no"/>
        </many-to-one>
        <property name="rmlDate" type="java.util.Date">
            <column name="rml_date" length="23" />
        </property>
        <property name="rmlIsdel" type="java.lang.String">
            <column name="rml_isdel" length="1" />
        </property>
        <property name="rmlState" type="java.lang.String">
            <column name="rml_state" length="1" />
        </property>
        <property name="rmlRecUser" type="java.lang.String">
            <column name="rml_rec_user" length="50" />
        </property>
    </class>
</hibernate-mapping>
