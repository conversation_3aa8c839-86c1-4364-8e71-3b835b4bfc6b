<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.TypeListConnect" table="type_list_connect" schema="dbo" >
        <id name="tlcId" type="java.lang.Long">
            <column name="tlc_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="parentType" class="com.frsoft.base.entity.TypeList" not-null="true">
            <column name="parent_typ_id" />
        </many-to-one>
        <many-to-one name="childType" class="com.frsoft.base.entity.TypeList" not-null="true">
            <column name="child_typ_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
