<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<base href="<%=basePath%>"></base>

		<title>查询案件</title>
		<link rel="shortcut icon" href="favicon.ico" />
		<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" /> 
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<link rel="stylesheet" type="text/css" href="css/style.css" /> 
		<script type="text/javascript" src="js/prototype.js"></script>
		<script type="text/javascript" src="js/common.js"></script>
		<script type="text/javascript" src="js/formCheck.js"></script>
		<script type="text/javascript" src="js/case.js?v=20160128"></script>
		<script type="text/javascript" src="js/config.js"></script>
		<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
		<script language="javaScript" type="text/javascript">
		

		/*
					if(jsonData!=""&&jsonData.list&&jsonData.list.length>0){
						var listObj = jsonData.list;
						listHTML.push("<ul>");
						for(var i=0; i<listObj.length; i++){
							var obj = listObj[i];
							listHTML.push("<li>ID：<a href=\"javascript:void(0)\" onclick=\"descPop('planAction.do?op=toDescProdCar&pcaId="+obj.pcaId+"');return false;\">"+obj.pcaId+"</a>"+(obj.pcaChassisNo?",&nbsp;底盘号："+obj.pcaChassisNo:"")+",&nbsp;厂家返利："+obj.pcaPlanBack+"</li>");
						}
						listHTML.push("</ul>");
					}
					else{
						listHTML.push("<div style='text-align:center; color:#666'>无对应车辆</div>");
					}*/
			var listCount = 0;
			/*function check(){
				if(isEmpty("bankName")&&isEmpty("batCodeStr")&&isEmpty("excLimStr")&&isEmpty("caseCode")&&isEmpty("staTim")&&isEmpty("endTim")&&isEmpty("caseName")&&isEmpty("caseCaCd")&&isEmpty("caseNum")&&isEmpty("seName")&&isEmpty("fileNo")){
					alert("未输入查询条件！");
					return false;
				}
				else{
					formSubmit('searchForm');
				}
			}*/
			
			function toAssignC(){
				if(checkBoxIsEmpty("priKey")){
					var casIds = getBacthIds("-",false)[0];
					if(casIds!=''){
						openPostWindow("caseAction.do",[["op","toAssignCases"],["casIds",casIds.substring(0,casIds.length-1)]],"assCaseWin");
					}
				}
			}
			
			function opListAll(type){
				if(listCount>0){
					switch(type){
					case "0":
						batchOp(12,false);
						break;
					case "1":
						batchOp(14,false);
						break;
					case "2":
						batchOp(17,false);
						break;
					}
				}
				else{
					alert("当前列表为空！");
					return false;
				}
			}
			function searCase(){
				$("mark").value = '0';
				loadList();
				initListSumLayer();
			}
			function searKey(){
				$("mark").value = '1';
				loadList();
				initListSumLayer();
			}
			
			function listLoadCallBack(jsonData){
				if(jsonData=="error"){
					alert("ID范围格式输入有误，请重新输入！");
				}
			}
			function dataMapper(obj){
				var datas,className,dblFunc,dataId;
				dataId = obj.casId+"-"+obj.casState;
				var dblFunc = "descPop('caseAction.do?op=caseDesc&caseId="+obj.casId+"&view=case')";
				className = getCaseColor(obj);
				var caseCode  = "<a href=\"javascript:void(0)\" onclick=\""+dblFunc+";return false;\">"+obj.casCode+"</a>";

				var casOutState = getVisState(obj.casOutState);
				
				if("${SYS_CODE}"=="S"&&"${CUS_VER_ID}"!="20"){
					datas = [obj.casId, obj.casClArea?obj.casClArea.typName:"", obj.typeList?obj.typeList.typName:"新案", obj.caseBat?obj.caseBat.cbatCode:"", caseCode, obj.casDate, obj.casName, obj.casNum, obj.casM,(obj.casM>0&&obj.casPaidM>0)?((obj.casM>obj.casPaidM)?(obj.casM-obj.casPaidM):0):obj.casM, obj.casPrTime, obj.salEmp?obj.salEmp.seName:"", obj.casPrCount, obj.casLastAssignTime, casOutState, obj.casExcLim, obj.casTremark ];
				}
				else{
					datas = [obj.casId, obj.casClArea?obj.casClArea.typName:"",obj.typeList?obj.typeList.typName:"新案",  obj.caseBat?obj.caseBat.cbatCode:"", caseCode, obj.casDate, obj.casBackdateP, obj.casName, obj.casNum, obj.casCaCd, obj.casM,(obj.casM>0&&obj.casPaidM>0)?((obj.casM>obj.casPaidM)?(obj.casM-obj.casPaidM):0):obj.casM, obj.casPrTime, obj.casLastPr, obj.salEmp?obj.salEmp.seName+(obj.salEmp.salOrg?"("+obj.salEmp.salOrg.soName+")":""):"", obj.casPrCount, obj.casLastAssignTime, obj.casPtpM, obj.casCpM, obj.casPaidM, casOutState, obj.casExcLim, obj.casAssignNames, obj.casTremark ];
				}
				listCount++;
				return [datas,className,dblFunc,dataId];
			}
			function getFormArgs(){
				var pars;
				var mark = $("mark").value;
				if(mark == '1'){
					pars = $("searchForm1").serialize(true);
					pars.clArea = $("clArea").value;
				}
				else{
					pars = $("searchForm").serialize(true);
				}
				pars.op = "seaCase";
				return pars;
			}
			function loadList(sortCol,isDe,pageSize,curP){
				var url = "caseAction.do";
				var pars = getFormArgs();
				var loadFunc = "loadList";
				var cols=[];
				if("${SYS_CODE}"=="S"&&"${CUS_VER_ID}"!="20"){
					cols=[
						{name:"ID"},
						{name:"催收区域"},
						{name:"催收状态"},
						{name:"批次号",align:"left"},
						{name:"个案序列号",align:"left"},
						{name:"委案日期",renderer:"date"},
						{name:getCasNameTxt("${CUS_VER_ID}")},
						{name:"证件号"},
						{name:"委案金额",renderer:"money",align:"right"},
						{name:"委案余额",isSort:false,align:"right",renderer:"money"},
						{name:"上次通电",renderer:"time"},
						{name:"催收员"},
						{name:"跟进次数"},
						{name:"分配时间",renderer:"time"},
						{name:"外访状态"},
						{name:"账龄"},
						{name:"催收小结",align:"left"}
					];
				}
				else{
					cols=[
						{name:"ID"},
						{name:"催收区域"},
						{name:"催收状态"},
						{name:"批次号",align:"left"},
						{name:"个案序列号",align:"left"},
						{name:"委案日期",renderer:"date"},
						{name:"预计退案日",renderer:"date"},
						{name:getCasNameTxt("${CUS_VER_ID}")},
						{name:"证件号"},
						/*{name:"地区（省市区）"},*/
						{name:getCasCaCdTxt("${CUS_VER_ID}"),align:"left"},
						{name:"委案金额",renderer:"money",align:"right"},
						{name:"委案余额",isSort:false,align:"right",renderer:"money"},
						{name:"上次通电",renderer:"time"},
						{name:"最新催记",isSort:false},
						{name:"催收员"},
						{name:"跟进次数"},
						{name:"分配时间",renderer:"time"},
						{name:"PTP金额",align:"right",renderer:"money"},
						{name:"CP金额",align:"right",renderer:"money"},
						{name:"已还款",align:"right",renderer:"money"},
						{name:"外访状态"},
						{name:"账龄"},
						{name:"分配历史",isSort:false},
						{name:"催收小结",align:"left"}
					];
				}
				gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
				gridEl.loadData(dataMapper,"",listLoadCallBack);
			}
			
			function initListSumLayer(){
				var url ="caseAction.do" ;
				pars = getFormArgs();
				pars.op = "getSeaCaseSum";
				new Ajax.Request(url,{
					method : 'post',
					parameters : pars,
					onSuccess : function(response){
						var resp = response.responseText.split('|');
						for(var i=0; i<resp.length; i++){
							if(resp[i]=='null'||resp[i]==""){
								resp[i] = "0";
							}
						}
						var listAmon=resp[0], listMSum=resp[1], listPaidSum=resp[2], listPaidCount=resp[3], listCpSum=resp[4], listPtpSum=resp[5];
						$("listSumLayer").innerHTML = "列表案量："+listAmon+"件，列表金额：￥"+changeTwoDecimal(listMSum)+"，列表还款案量："+listPaidCount+"件，列表还款额：￥"+changeTwoDecimal(listPaidSum)+"，列表CP值：￥"+changeTwoDecimal(listCpSum)+"，列表PTP值：￥"+changeTwoDecimal(listPtpSum);
						if($("caseListSum").style.display=='none'){
							$("caseListSum").show();
						}
					},
					onfailure : function(response){
						if (transport.status == 404)
							alert("您访问的url地址不存在！");
						else
							alert("Error: status code is " + transport.status);
					}
				});
			}
			
	    	var gridEl = new MGrid("seaLXTab","dataList");
	    	gridEl.config.hasCheckBox =true;
			createProgressBar();
			window.onload=function(){
				createCancelButton(searCase,'searchForm',-50,5,'searButton','after');
				//createCancelButton(loadList,'searchForm1',-50,5,'keySearchBtn','after');
				loadPaidState('paidState');
				loadCaseState('caseState');
				$("caseState").value="u";//默认未退案
				loadRedBlueState('redBlueSt');
				$("casNameTxt").innerHTML=getCasNameTxt("${CUS_VER_ID}");
				$("casCaCdTxt").innerHTML=getCasCaCdTxt("${CUS_VER_ID}");
				$("bankTxt").innerHTML=getBankTxt("${CUS_VER_ID}");
				closeProgressBar();
			}

  	</script>
	</head>


  <body>
 	<div id="mainbox">
    	<div id="contentbox">
        	<div id="idRangeTip" class="floatTipsDiv" style="display:none;">&nbsp;用,分隔ID号，用-表示ID范围（,和-都为英文符号），示例：1085,1099,1100-1200,1202&nbsp;</div>
            <div id="mulTextTip" class="floatTipsDiv" style="display:none;">&nbsp;可输入多个查询文本，多个文本之间请换行（单个查询是模糊查询，多个查询是精确查询）&nbsp;</div>
        	<div id="hasRepInBatTip" class="floatTipsDiv" style="display:none;">&nbsp;查询是否在同批次下有共债案件，需先在批次管理执行归并案件之后才可查询&nbsp;</div>
        	<div id="title">
            	<table>
                	<tr>
                    	<th>数据管理 > 案件管理 <span id="changeFuncBt" onMouseOver="popFuncMenu(['case',2],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['case',2],true)" onMouseOut="popFuncMenu(['case',2],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
 </div>
          <table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                        	<div id="tabType1" class="tabTypeWhite" onClick="self.location.href='caseAction.do?op=toSearch'">案件管理</div>
                        </div>
                    </th>
                </tr>
            </table>
			<script type="text/javascript">loadTabTypeWidth();</script>
			<div id="listContent">
				<div class="listSearch">
                	<form class="listSearchForm" id="searchForm"  onSubmit="searCase();return false;" >
                    <table cellpadding="0" cellspacing="0">
                        <tr>
                        	<th>催收区域：</th>
                            <td>
                                <c:if test="${!empty userAreaList}">
                                <select name='clArea' class="inputSize2">
                                    <option value="<c:forEach items="${userAreaList}" var="uArea">${uArea.uarArea.typId},</c:forEach>">全部</option>
                                    <c:forEach items="${userAreaList}" var="userArea">
                                    <option value="${userArea.uarArea.typId}">${userArea.uarArea.typName}</option>
                                    </c:forEach>
                                </select>
                                </c:if>
                                <c:if test="${empty userAreaList}">
                                    <c:if test="${!empty areaList}">
                                    <select name='clArea' class="inputSize2">
                                        <option value="">全部</option>
                                        <c:forEach items="${areaList}" var="area">
                                        <option value="${area.typId}">${area.typName}</option>
                                        </c:forEach>
                                    </select>
                                    </c:if>
                                    <c:if test="${empty areaList}">
                                    <select name='clArea' class="inputSize2">
                                        <option value="">未添加</option>
                                    </select>
                                    </c:if>
                                </c:if></td>
                        	<th>部门：</th>
                            <td><select id="org" name="org" class="inputSize2 inputBoxAlign" onChange="loadOrgEmp(this)"><option value="">全部</option><c:if test="${!empty orgList}"><c:forEach items="${orgList}" var="oList"><option value="${oList.soCode}">${oList.soName}</option></c:forEach></c:if></select></td>
                            <th>催收员：</th>
                            <td><input style="width:116px;" class="inputSize2 inputBoxAlign" type="text" id="empName" name="empName" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty empList}"><select class="inputSize2 inputBoxAlign" id='empSel' style="width:70px" onChange="setEmpNameFromSel(this,'empName')"><option value="">请选择</option><c:forEach items="${empList}" var="eList"><option value="${eList.seName}">${eList.seName}</option></c:forEach></select></c:if><c:if test="${empty empList}"><select class="inputSize2 inputBoxAlign" style="width:70px" disabled="disabled"><option>未添加</option></select></c:if></td>
                           <th>开户行：</th>
                           <td><input class="inputSize2 inputBoxAlign" style="width:82px;" type="text" name="cardBankName" onBlur="autoShort(this,100)"/></td>
                           <th><span id="bankTxt"></span>：</th>
                           <td colspan="3"><input type="hidden" id="bankId" name="bankId" /><input class="inputSize2 inputBoxAlign" style="width:142px" type="text" id="bankName" name="bankName" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty bankList}"><select class="inputSize2 inputBoxAlign" style="width:140px" onChange="setBankNameAndIdFromSel(this,'bankId','bankName')"><option value="">请选择</option><c:forEach items="${bankList}" var="bList"><option value="${bList.typId}">${bList.typName}</option></c:forEach></select></c:if><c:if test="${empty bankList}"><select class="inputSize2 inputBoxAlign" style="width:140px" disabled="disabled"><option>未添加</option></select></c:if></td>
                            
                        </tr>
                        <tr>
                        	<th>案件状态：</th>
                            <td><select id="caseState" name="casState" class="inputSize2 "><option value="u">未退案</option></select></td>
                            <th>逾期账龄：</th>
                            <td colspan="3"><input class="inputSize2" style=" width:300px" type="text" id="excLimStr" name="excLim" onDblClick="cleanExcLimInput()"/><input type="text" style="display:none" id="excLimIds" name="excLimIds"/>&nbsp;<button class="butSize2" onClick="showExcLimList('bankId')" style="width:40px">选择</button></td>
                            <th>批次号：</th>
                            <td colspan="5"><input class="inputSize2" style=" width:388px" type="text" id="batCodeStr" name="batCode" onDblClick="cleanBatCodeInput()"/><input type="text" style="display:none" id="batIds" name="batIds"/>&nbsp;<button class="butSize2" onClick="showBatList()" style="width:40px">选择</button></td>
                            <!--<th>催收员：</th>
                            <td><input class="inputSize2 inputBoxAlign" type="text" id="seName" name="seName" onBlur="autoShort(this,25)"/></td>-->
                        </tr>
                        <tr>
                        	<th>催收状态：</th>
                            <td><c:if test="${!empty caseStateList}"><select id="caseStateId" name="caseStateId" class="inputSize2 inputBoxAlign"><option value="">请选择</option><option value="NULL">新案</option><c:forEach items="${caseStateList}" var="cStateList"><option value="${cStateList.typId}">${cStateList.typName}</option></c:forEach></select></c:if><c:if test="${empty caseStateList}"><select id="caseStateId" name="caseStateId" class="inputSize2 inputBoxAlign" disabled="disabled"><option>未添加</option></select></c:if></td>
                            <th>地区：</th>
                            <td colspan="3"><input style="width:55px;" class="inputSize2 inputBoxAlign" type="text" id="area1" name="area1" onBlur="autoShort(this,100)"/>&nbsp;<select class="inputBoxAlign inputSize4" style="width:60px;" id="area1Sel" onChange="setCityValue(1)"><c:if test="${!empty provList}"><c:forEach var="provList" items="${provList}"><option value="${provList.areId}">${provList.areName}</option></c:forEach></c:if></select>&nbsp;<input style="width:55px;" class="inputSize2 inputBoxAlign" type="text" id="area2" name="area2" onBlur="autoShort(this,100)"/>&nbsp;<select class="inputBoxAlign inputSize4" style="width:60px;" id="area2Sel" onChange="setCityValue(2)"></select>&nbsp;<input style="width:55px;" class="inputSize2 inputBoxAlign" type="text" id="area3" name="area3" onBlur="autoShort(this,100)"/>&nbsp;<select class="inputBoxAlign inputSize4" style="width:60px;" id="area3Sel" onChange="setCityValue(3)"></select></td>
                        	<th rowspan="2">案件ID&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('idRangeTip',0,10)" onMouseOut="floatTipsLayer('idRangeTip')"/>：</th>
                            <td colspan="5" rowspan="2"><textarea style="width:433px" rows="2" class="inputSize2 inputBoxAlign" id="idRange" name="idRange" onBlur="formatIdRangeInput(this)"></textarea></td>
                        </tr>
                        <tr>
                        	<th>标色状态：</th>
                        	<td><select id="redBlueSt" name="redBlueSt" class="inputSize2"></select></td>
                        	<th>案件类型：</th>
                            <td><c:if test="${!empty caseTypeList}"><select id="caseTypeId" name="caseTypeId" class="inputSize2 inputBoxAlign"><option value="">请选择</option><c:forEach items="${caseTypeList}" var="cTypeList"><option value="${cTypeList.typId}">${cTypeList.typName}</option></c:forEach></select></c:if><c:if test="${empty caseTypeList}"><select id="caseTypeId" name="caseTypeId" class="inputSize2 inputBoxAlign" disabled="disabled"><option>未添加</option></select></c:if></td>
                            <th>委案日期：</th>
                            <td><input name="caseDateStart" id="cdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('cdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'cdEnd\')}'})"/>&nbsp;到&nbsp;<input name="caseDateEnd" id="cdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'cdStart\')}'})"/></td>
                            
                        </tr>
                        <tr>
                            <th>还款情况：</th>
                            <td><select id="paidState" name="paidState" class="inputSize2 inputBoxAlign"></select></td>
                        	<th>车架号：</th>
                            <td><input class="inputSize2 inputBoxAlign" type="text" id="calVin" name="calVin" onBlur="autoShort(this,100)"/></td>
                            <th>还款日期：</th>
                        	<td><input name="paidDateStart" id="paidDateStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('paidDateEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'paidDateEnd\')}'})"/>&nbsp;到&nbsp;<input name="paidDateEnd" id="paidDateEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'paidDateStart\')}'})"/></td>
                            
                        	<th>委案金额：</th>
                        	<td colspan="3"><input class="inputSize2" style="width:90px" type="text" name="casMMin" onBlur="checkIsNum(this)"/>&nbsp;-&nbsp;<input class="inputSize2" style="width:90px" type="text" name="casMMax" onBlur="checkIsNum(this)"/></td>
                            <th rowspan="3"><span id="casCaCdTxt"></span>&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                            <td rowspan="3"><textarea style="width:168px" rows="5" class="inputSize2 inputBoxAlign" id="caseCaCd" name="caseCaCd"></textarea></td>
                            
                        </tr>
                        <tr>
                        	<th>分配状态：</th>
                            <td><select id="assignState" name="assignState"  class="inputSize2 inputBoxAlign">
                            	<option value="">全部</option>
                                <option value="0">未分配</option>
                                <option value="1">已分配</option>
                            </select></td>
                            
                            <th>牌照号：</th>
                            <td><input class="inputSize2 inputBoxAlign" type="text" id="calLice" name="calLice" onBlur="autoShort(this,100)"/></td>
                            <th>预计退案日：</th>
                            <td><input name="planDateStart" id="pdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('pdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'pdEnd\')}'})"/>&nbsp;到&nbsp;<input name="planDateEnd" id="pdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'pdStart\')}'})"/></td>
                            <th>最后跟进日：</th>
                            <td colspan="3"><input name="prTimeStart" id="ptStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('ptEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'ptEnd\')}'})"/>&nbsp;到&nbsp;<input name="prTimeEnd" id="ptEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'ptStart\')}'})"/></td>
                            
                            
                        </tr>
                        <tr>
                        	<th>历史催收员：</th>
                            <td colspan="3"><select name="isInLastEmp"  style="width:70px" class="inputSize2 inputBoxAlign"><option value="1">包括</option><option value="0">不包括</option></select>&nbsp;<input style="width:90px;" class="inputSize2 inputBoxAlign" type="text" id="lastEmp" name="lastEmp" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty empList}"><select class="inputSize2 inputBoxAlign" style="width:80px" onChange="setEmpNameFromSel(this,'lastEmp')"><option value="">请选择</option><c:forEach items="${empList}" var="eList"><option value="${eList.seName}">${eList.seName}</option></c:forEach></select></c:if><c:if test="${empty empList}"><select class="inputSize2 inputBoxAlign" style="width:80px" disabled="disabled"><option>未添加</option></select></c:if></td>
                         	 
                           
                            <th>实际退案日：</th>
                            <td><input name="backDateStart" id="backdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('backdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'backdEnd\')}'})"/>&nbsp;到&nbsp;<input name="backDateEnd" id="backdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'backdStart\')}'})"/></td>
                            <th>逾期天数：</th>
                            <td><input class="inputSize2 inputBoxAlign" style="width:40px;" type="text" name="ovdDaysMin"  onBlur="checkIsInt(this)"/>&nbsp;-&nbsp;<input class="inputSize2 inputBoxAlign" style="width:40px;" type="text" name="ovdDaysMax"  onBlur="checkIsInt(this)"/></td>
                            <th>币种：</th>
                        	<td><input class="inputSize2" style="width:40px;" type="text" id="casMCat" name="casMCat" onBlur="autoShort(this,200)"/></td>
                        </tr>
                        <tr> 
                        	<th><span id="casNameTxt"></span>&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                            <td><textarea rows="5" class="inputSize2 inputBoxAlign"id="caseName" name="caseName"></textarea></td>
                        	<th>档案号：</th>
                            <td><textarea rows="5" class="inputSize2 inputBoxAlign"id="fileNo" name="fileNo"></textarea></td>
                        	<th>账号&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                            <td><textarea style="width:188px" rows="5" class="inputSize2 inputBoxAlign" id="accNum" name="accNum"></textarea></td>
                        	<th>个案序列号&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                            <td colspan="3"><textarea style="width:188px" rows="5" class="inputSize2 inputBoxAlign" id="caseCode" name="caseCode"></textarea></td>
                        	<th>证件号&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                            <td><textarea style="width:168px" rows="5" class="inputSize2 inputBoxAlign" id="caseNum" name="caseNum"></textarea></td>
                        </tr>
                        <tr>
                        	<th>催收手别：</th>
                            <td><input class="inputSize2 inputBoxAlign" type="text" name="clCount"  onBlur="autoShort(this,200)"/></td>
                            <th>批次共债&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('hasRepInBatTip',0,10)" onMouseOut="floatTipsLayer('hasRepInBatTip')"/>：</th>
                            <td><select class="inputSize2 inputBoxAlign" name="hasRepInBat"><option></option><option value="1">是</option><option value="0">否</option></select></td>
                            <td colspan="20" style="text-align:right">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="submit" id="searButton" class="butSize3 inputBoxAlign" value="案件查询"/></td>
                        </tr>
                    </table>
                     		
                    <!--
                            <th rowspan="2" style="vertical-align:top"><span id="bankTxt"></span>：</th>
                            <td colspan="5" rowspan="2" style="vertical-align:top"><div class='blue' style="font-weight:normal">[<a href="javascript:void(0)" onClick="floatTipsLayer('bankUl',15,-76);">点击选择</a>]</div><div id="bankUl" class="floatListDiv" style="display:none">
                    <div class='blue' style='padding:2px; text-align:right'>[<a href="javascript:void(0)" onClick="floatTipsLayer('bankUl');return false;">点击关闭</a>]</div>
                    <c:if test="${!empty bankList}">
                    <div class="scrollInFloatDiv">
                    <ul>
                    <c:forEach items="${bankList}" var="bList">
                    <li><input type="checkbox" class="inputBoxAlign" id="bankIdCB${bList.typId}" name="bankIds" value="${bList.typId}" onClick="addBankName(this)" /><label id="bankIdLA${bList.typId}" for="bankIdCB${bList.typId}">${bList.typName}</label></li>
                    </c:forEach>
                    </ul>
                    </div>
                    
                    </c:if>
                    <c:if test="${empty bankList}">未添加银行</c:if>
                </div><textarea class="inputSize2 lockBack" readonly style="width:520px;" id="bankNames" rows="2"></textarea></td>
                 -->
                    </form>
                    </div>
                   <div class="listSearch">
                        <form style="margin:0px; padding:0px;" id="searchForm1"  onSubmit="searKey();return false;" >
                            <input type="text" id="mark" name="mark" style="display:none"/>
                            <span class="gray">查询在电催记录中包含关键字的案件</span>
                            关键字：<input style="width:300px" class="inputSize2 inputBoxAlign" type="text" id="keyWord" name="keyWord" onBlur="autoShort(this,25)"/> &nbsp; 
                            <input type="submit" id="keySearchBtn" class="butSize3 inputBoxAlign" value="关键字查询"/>&nbsp;&nbsp;
                        </form>
                    </div>
	                <div class="rsOpBarLayer bottomBar">
                    	<li><img src='images/content/group_go.gif' class="imgAlign" />&nbsp;分案操作：
                            <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(0)">快速分案</span>
                            <span class="grayBack" style="width:110px" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="opListAll('1')">查询结果快速分案</span>
                            <span class="grayBack" style="width:110px" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="opListAll('2')">查询结果自动分案</span>
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(2)">手动推荐分案</span>
                    	</li>
                        <li><img src='images/content/page_green.gif' class="imgAlign" />&nbsp;案件操作：
                            <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(3)">暂停案件</span>
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(4)">关闭案件</span>
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(5)">退案</span>
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(6)">恢复案件</span>
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(7)">添加评语</span>
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(18)">案件标色</span>
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(8)">修改催收状态</span>
                           
                    <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(21)">申请协催</span>
                           <span id="batDelCase" style="display:none" class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(11)">删除案件</span>
                       </li>
                       <li><img src='images/content/database_go.gif' class="imgAlign" />&nbsp;导出操作：
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="opListAll('0')">导出查询结果</span>
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(13,true)">导出所选案件</span>
                           <!--<span class="grayBack" style="width:100px" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="outHur('0')">按银行导出催记</span>-->
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(20,true)">导出所选电话</span>
                           <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(15,true)">导出所选催记</span>
                               <script type="text/javascript">displayLimAllow("ca017","batDelCase");</script>
                       </li>
	                </div>
                    <div class="bottomBar" style="height:20px;"><span id="caseListSum" class="listTopSum" style="display:none"><span class="listSumTitle">查询结果统计：</span><span id="listSumLayer"></span></span></div>
                    <div id="dataList" class="dataList"></div>
				</div>
			</div>
    </div>
</body>
</html>
