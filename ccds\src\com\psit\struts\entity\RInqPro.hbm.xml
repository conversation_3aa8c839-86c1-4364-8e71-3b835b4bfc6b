<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RInqPro" table="r_inq_pro" schema="dbo" >
        <id name="rqpId" type="java.lang.Long">
            <column name="rqp_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="inquiry" class="com.psit.struts.entity.Inquiry" fetch="select" not-null="false">
            <column name="rqp_inq_id" />
        </many-to-one>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rqp_wpr_id" />
        </many-to-one>
        <property name="rqpNum" type="java.lang.Double">
            <column name="rqp_num" precision="18" />
        </property>
        <property name="rqpPrice" type="java.lang.Double">
            <column name="rqp_price" precision="18" />
        </property>
        <property name="rqpAllPrice" type="java.lang.Double">
            <column name="rqp_all_price" length="50" />
        </property>
        <property name="rqpRemark" type="java.lang.String">
            <column name="rqp_remark" length="1073741823" />
        </property>
    </class>
</hibernate-mapping>
