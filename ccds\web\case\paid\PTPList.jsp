<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>PTP、CP列表</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/cus.css"/>
	<style type="text/css">
    	body{
			background:#fff;
		}
		.rowstable th{
			width:auto !important;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    
    <script type="text/javascript">

		function initPage(){
			//可操作案件
			if('${operational}'!='0'){
				$("newCpButton").show();
			}
		}
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			var date="";
			if(obj.paPtpD != undefined){
				date = obj.paPtpD;
			}
			var funcCol = "<a href=\"javascript:void(0)\" title=\"转CP\" onclick=\"parent.casePopDiv(10,['"+obj.paId+"','"+obj.paPtpNum+"','"+date+"']);return false;\">[转CP]</a>&nbsp;<a href=\"javascript:void(0)\" title=\"作废PTP\" onclick=\"parent.casePopDiv(14,'"+obj.paId+"');return false;\" >[作废]</a>&nbsp;";
			if(obj.paState=='3'){
				className = "grayLineT";
				funcCol = "&nbsp;";
			}
			datas=[obj.paPtpNum, obj.paPtpD, obj.paWriter, funcCol];
			if('${operational}'=='0'){ datas.pop(); }
			return [datas,className,dblFunc,dataId];
		}
		
		function dataMapper1(obj){
			var datas,className,dblFunc,dataId;
			var funcCol ="";
			if(obj.paState == '4' || obj.paState == '5'){
				className = "grayLineT";
			}
			if(obj.paState == '1'){
				funcCol  = "<a href=\"javascript:void(0)\" title=\"作废CP\" onclick=\"parent.casePopDiv(36,'"+obj.paId+"');return false;\" >[作废]</a>&nbsp;";
			}
			datas = [obj.paCpNum, getCpTime("${CUS_VER_ID}", obj.paCpTime), obj.paPayMan, obj.paPayMethod, obj.paPaidNum, obj.paPaidTime, obj.paSurTime, obj.paSurRemark, funcCol ];
			if('${operational}'=='0'){ datas.pop(); }
			return [datas,className,dblFunc,dataId];
		}
		
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars =[];
			pars.op = "listPTP";
			pars.casId="${casId}";
			var pars1 =[];
			pars1.op="listUnPTP";
			pars1.casId="${casId}";
			
			var loadFunc = "loadList";
			var cols=[
				{name:"PTP金额",align:"right",renderer:"money"},
				{name:"PTP时间",renderer:"date"},
				{name:"录入人"},
				{name:"操作",isSort:false}
			];
			
			var cols1=[
				{name:"CP金额",align:"right",renderer:"moneyOrEmt"},
				{name:"CP时间"},
				{name:"还款人"},
				{name:"还款方式"},
				{name:"确认还款",renderer:"moneyOrEmt",align:"right"},
				{name:"还款日期",renderer:"date"},
				{name:"确认时间",renderer:"time"},
				{name:"备注"},
				{name:"操作"}
			];
			if('${operational}'=='0'){	cols.pop(); cols1.pop(); };
			gridE1.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridE1.loadData(dataMapper);
			
			gridE2.init(url,pars1,cols1,loadFunc,sortCol,isDe,pageSize,curP);
			gridE2.loadData(dataMapper1);
		}

    	var gridE1 = new MGrid("ptpListTab${operational}","dataList");
    	var gridE2 = new MGrid("cpListTab${operational}","dataList1");
    	gridE1.config.sortable=false;
    	gridE2.config.sortable=false;
		createProgressBar();
		window.onload=function(){
			initPage();
			loadList();
		}
    </script>
 </head>
  
  <body>   
  <div class="divWithScroll2 innerIfm">
    <table class="normal" style="width:98%" cellpadding="0" cellspacing="0" >
       	<tr>
       		<td style=" vertical-align:top; width:35%; padding-right:2px;">
			 	<div class="bold gray">PTP记录<!--<a href="javascript:void(0)" id="newPtpButton" style="display:none" onClick="parent.casePopDiv(8,'${casId}');return false;" class="newBlueButton">新增PTP记录</a>--></div> 
			   	<div id="dataList" class="dataList"></div>
	  		</td>
	  		<td style=" vertical-align:top; width:55%">
		 		<div class="bold gray">CP记录&nbsp;&nbsp;
                    <a href="javascript:void(0)" id="newCpButton" style="display:none" onClick="parent.casePopDiv(15,'${casId}');return false;">[新增]</a>
          		</div> 
                <div id="dataList1" class="dataList"></div>
	  		</td>
	  	</tr>
	</table>	
    </div>	
  </body>
</html>
					