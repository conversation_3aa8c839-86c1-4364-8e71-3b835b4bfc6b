<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.HouseInf" table="house_inf" schema="dbo" >
        <id name="hoiId" type="java.lang.Long">
            <column name="hoi_id" />
            <generator class="identity" />
        </id>
        <property name="hoiName" type="java.lang.String">
            <column name="hoi_name" length="50" />
        </property>
        <property name="hoiIdNo" type="java.lang.String">
            <column name="hoi_id_no" length="50" />
        </property>
        <property name="hoiHouseNo" type="java.lang.String">
            <column name="hoi_house_no" length="500" />
        </property>
        <property name="hoiType" type="java.lang.String">
            <column name="hoi_type" length="100" />
        </property>
        <property name="hoiCom" type="java.lang.String">
            <column name="hoi_com" length="200" />
        </property>
         <property name="hoiInsUser" type="java.lang.String">
            <column name="hoi_ins_user" length="50" />
        </property>
        <property name="hoiInsTime" type="java.util.Date">
            <column name="hoi_ins_time" length="23" />
        </property>
        <property name="hoiAltUser" type="java.lang.String">
            <column name="hoi_alt_user" length="25" />
        </property>
        <property name="hoiAltTime" type="java.util.Date">
            <column name="hoi_alt_time" length="23" />
        </property>
        <many-to-one name="hoiSeason" class="com.frsoft.loan.entity.LoanSeason">
            <column name="hoi_lse_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
