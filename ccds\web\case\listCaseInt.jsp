<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>利息列表</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
    	function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			datas = [obj.cinMCat,obj.cinM,obj.cinEndDate,obj.cinPrincipal, obj.cinInt, obj.cinDamagesAmt, obj.cinOverduePaid, obj.cinOverLimit,obj.cinService, obj.cinYear, obj.cinOther, obj.cinOut, obj.cinInsTime ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars = [];
			pars.op = "listCaseInt";
			pars.caseId="${caseId}";
			var loadFunc = "loadList";
			var cols=[
				{name:"币种",width:'5%'},
				{name:"总欠款金额",width:'10%',renderer:'money'},
				{name:"截止日期",width:'9%'},
				{name:"本金",width:'8%'},
				{name:"利息",width:'7%'},
				{name:"违约金",width:'7%'},
				{name:"滞纳金",width:'7%'},
				{name:"超限费",width:'7%'},
				{name:"服务费",width:'7%'},
				{name:"年费",width:'7%'},	
				{name:"其他费用",width:'7%'},
				{name:"表外息",width:'7%'},
				{name:"导入时间",renderer:'date',width:'12%'}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("caseIntListTab","dataList");
    	gridEl.config.sortable=false;
		gridEl.config.isResize=false;
		createProgressBar();
   		window.onload=function(){
			loadList();
		}
    </script>
</head> 
  
  <body>
  	<div class="divWithScroll2 innerIfm">
  		<div id="dataList" class="dataList"></div>
    </div>
  </body>
</html>