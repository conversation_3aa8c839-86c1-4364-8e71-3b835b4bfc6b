<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.WmsLine" table="wms_line" schema="dbo" >
        <id name="wliId" type="java.lang.Long">
            <column name="wli_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="wli_wpr_id"/>
        </many-to-one>
        <many-to-one name="wmsStro" class="com.psit.struts.entity.WmsStro" fetch="select" not-null="false">
            <column name="wli_stro_code" length="50" />
        </many-to-one>
        <property name="wliTypeCode" type="java.lang.String">
            <column name="wli_type_code" length="50" />
        </property>
        <property name="wliType" type="java.lang.String">
            <column name="wli_type" length="50" />
        </property>
        <property name="wliInNum" type="java.lang.Double">
            <column name="wli_in_num" precision="18" />
        </property>
        <property name="wliOutNum" type="java.lang.Double">
            <column name="wli_out_num" precision="18" />
        </property>
        <property name="wliDate" type="java.util.Date">
            <column name="wli_date" length="23" />
        </property>
        <property name="wliState" type="java.lang.String">
            <column name="wli_state" length="1" />
        </property>
        <property name="wliMan" type="java.lang.String">
            <column name="wli_man" length="50" />
        </property>
         <property name="wliWmsId" type="java.lang.Long">
            <column name="wli_wms_id" />
        </property>
        <property name="wliIsdel" type="java.lang.String">
            <column name="wli_isdel" length="1" />
        </property>
        <property name="wliNum" type="java.lang.Double">
            <column name="wli_num" precision="18" />
        </property>
    </class>
</hibernate-mapping>
