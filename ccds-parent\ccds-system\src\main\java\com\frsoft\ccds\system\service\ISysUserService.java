package com.frsoft.ccds.system.service;

import com.frsoft.ccds.system.domain.SysUser;
import java.util.List;

/**
 * 用户 业务层
 */
public interface ISysUserService {
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合
     */
    List<SysUser> selectUserList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param loginName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByLoginName(String loginName);

    /**
     * 通过用户编码查询用户
     *
     * @param userCode 用户编码
     * @return 用户对象信息
     */
    SysUser selectUserByUserCode(String userCode);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 修改用户密码
     *
     * @param userCode 用户编码
     * @param password 密码
     * @return 结果
     */
    int resetUserPassword(String userCode, String password);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserStatus(SysUser user);

    /**
     * 批量删除用户信息
     *
     * @param userCodes 需要删除的用户编码
     * @return 结果
     */
    int deleteUserByUserCodes(String[] userCodes);

    /**
     * 批量删除用户信息
     *
     * @param userCodes 需要删除的用户编码
     * @return 结果
     */
    int deleteUserByUserCodes(String[] userCodes);

    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName);
}