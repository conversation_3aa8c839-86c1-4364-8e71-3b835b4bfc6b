<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.LoanSdRec" table="loan_sd_rec" schema="dbo" >
        <id name="lsdId" type="java.lang.Long">
            <column name="lsd_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="lsdLoanSeason" class="com.frsoft.loan.entity.LoanSeason" fetch="select">
            <column name="lsd_lse_id" />
        </many-to-one>
        <property name="lsdName" type="java.lang.String">
            <column name="lsd_name" length="50" />
        </property>
        <property name="lsdCardNum" type="java.lang.String">
            <column name="lsd_card_num" length="50" />
        </property>
        <property name="lsdSearNum" type="java.lang.String">
            <column name="lsd_sear_num" length="50" />
        </property>
        <property name="lsdDate" type="java.lang.String">
            <column name="lsd_date" length="50" />
        </property>
        <property name="lsdM" type="java.lang.String">
            <column name="lsd_m" length="50" />
        </property>
        <property name="lsdInsUser" type="java.lang.String">
            <column name="lsd_ins_user" length="25" />
        </property>
        <property name="lsdInsTime" type="java.util.Date">
            <column name="lsd_ins_time" length="23" />
        </property>
        <property name="lsdAltUser" type="java.lang.String">
            <column name="lsd_alt_user" length="25" />
        </property>
        <property name="lsdAltTime" type="java.util.Date">
            <column name="lsd_alt_time" length="23" />
        </property>
    </class>
</hibernate-mapping>
