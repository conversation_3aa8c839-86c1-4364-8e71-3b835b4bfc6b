package cn.comfan.test.carbus;

import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * Created by comfa on 2017/7/24.
 */
public class TreeMapTest {
    public static void main(String[] args) {
        TreeMap<String, String> tm = new TreeMap<String, String>();
        tm.put("aa", "a1");
        tm.put("ab", "ab1");
        tm.put("ac", "ac1");
        tm.put("bb", "b1");
        tm.put("ba", "aa1");
        tm.put("bc", "bc1");
        tm.put("b", "bb1");
        Set<Map.Entry<String, String>> entries = tm.tailMap("").entrySet();
        for (Map.Entry<String, String> entry : entries) {
            if (StringUtils.contains(entry.getValue(), "c")) {
                entry.setValue(entry.getValue() + "-----------------------");
            }
        }
        System.out.println(tm);
    }
}
