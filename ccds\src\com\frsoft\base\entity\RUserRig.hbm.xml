<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.RUserRig" table="r_user_rig" schema="dbo" >
        <id name="rurId" type="java.lang.Long">
            <column name="rur_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="limRight" class="com.frsoft.base.entity.LimRight" fetch="select" not-null="false">
            <column name="rur_rig_code" length="50" />
        </many-to-one>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select" not-null="false">
            <column name="rur_user_code" length="50" />
        </many-to-one>
        <property name="rurType" type="java.lang.String">
            <column name="rur_type" length="50" />
        </property>
    </class>
</hibernate-mapping>
