<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.OthCard" table="oth_card" schema="dbo" >
        <id name="ocdId" type="java.lang.Long">
            <column name="ocd_id" />
            <generator class="identity" />
        </id>
        <property name="ocdArea" type="java.lang.String">
            <column name="ocd_area" length="50" />
        </property>
        <property name="ocdAcct" type="java.lang.String">
            <column name="ocd_acct" length="50" />
        </property>
        <property name="ocdIdNo" type="java.lang.String">
            <column name="ocd_id_no" length="50" />
        </property>
        <property name="ocdCard" type="java.lang.String">
            <column name="ocd_card" length="50" />
        </property>
        <property name="ocdName" type="java.lang.String">
            <column name="ocd_name" length="50" />
        </property>
        <property name="ocdHPho" type="java.lang.String">
            <column name="ocd_h_pho" length="50" />
        </property>
        <property name="ocdOPho" type="java.lang.String">
            <column name="ocd_o_pho" length="50" />
        </property>
        <property name="ocdAddr" type="java.lang.String">
            <column name="ocd_addr" length="200" />
        </property>
        <property name="ocdEmployer" type="java.lang.String">
            <column name="ocd_employer" length="100" />
        </property>
        <property name="ocdAltName" type="java.lang.String">
            <column name="ocd_alt_name" length="50" />
        </property>
        <property name="ocdAltHPho" type="java.lang.String">
            <column name="ocd_alt_h_pho" length="50" />
        </property>
        <property name="ocdAltOPho" type="java.lang.String">
            <column name="ocd_alt_o_pho" length="50" />
        </property>
        <property name="ocdConName" type="java.lang.String">
            <column name="ocd_con_name" length="50" />
        </property>
        <property name="ocdConPho" type="java.lang.String">
            <column name="ocd_con_pho" length="50" />
        </property>
        <property name="ocdBir" type="java.lang.String">
            <column name="ocd_bir" length="50" />
        </property>
        <property name="ocdRAddr" type="java.lang.String">
            <column name="ocd_r_addr" length="200" />
        </property>
        <property name="ocdCyc" type="java.lang.String">
            <column name="ocd_cyc" length="50" />
        </property>
        <property name="ocdBlk" type="java.lang.String">
            <column name="ocd_blk" length="50" />
        </property>
        <property name="ocdMPost" type="java.lang.String">
            <column name="ocd_m_post" length="50" />
        </property>
        <property name="ocdMsg" type="java.lang.String">
            <column name="ocd_msg" length="200" />
        </property>
        <property name="ocdCreMan" type="java.lang.String">
            <column name="ocd_cre_man" length="50" />
        </property>
        <property name="ocdUpdMan" type="java.lang.String">
            <column name="ocd_upd_man" length="50" />
        </property>
        <property name="ocdCreTime" type="java.util.Date">
            <column name="ocd_cre_time" length="23" />
        </property>
        <property name="ocdUpdTime" type="java.util.Date">
            <column name="ocd_upd_time" length="23" />
        </property>
        <many-to-one name="ocdCase" class="com.frsoft.ccds.entity.BankCase">
        	<column name="ocd_cas_id"></column>
        </many-to-one>
    </class>
</hibernate-mapping>
