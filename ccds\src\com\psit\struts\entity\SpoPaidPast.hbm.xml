<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.SpoPaidPast" table="spo_paid_past" schema="dbo" >
        <id name="spaId" type="java.lang.Long">
            <column name="spa_id" />
            <generator class="identity" />
        </id>
        <property name="spaCode" type="java.lang.String">
            <column name="spa_code" length="300" />
        </property>
        <many-to-one name="salPurOrd" class="com.psit.struts.entity.SalPurOrd" fetch="select" not-null="false">
            <column name="spa_spo_id" />
        </many-to-one>
        <many-to-one name="account" class="com.psit.struts.entity.Account" fetch="select" not-null="false">
            <column name="spa_aco_id" />
        </many-to-one>
        <many-to-one name="salSupplier" class="com.psit.struts.entity.SalSupplier" fetch="select" not-null="false">
            <column name="spa_ssu_id" />
        </many-to-one>
        <property name="spaFctDate" type="java.util.Date">
            <column name="spa_fct_date" length="23" />
        </property>
        <many-to-one name="typeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="spa_type_id" />
        </many-to-one>
        <many-to-one name="outTypeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
        	<column name="spa_acc_type_id" />
        </many-to-one>
        <property name="spaContent" type="java.lang.String">
        	<column name="spa_content" length="100" />
        </property>
        <property name="spaPayType" type="java.lang.String">
            <column name="spa_pay_type" length="50" />
        </property>
        <property name="spaPayMon" type="java.lang.Double">
            <column name="spa_pay_mon" precision="18" />
        </property>
        <property name="spaInName" type="java.lang.String">
            <column name="spa_in_name" length="100" />
        </property>
        <property name="spaInpUser" type="java.lang.String">
            <column name="spa_inp_user" length="50" />
        </property>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="spa_se_no"/>
        </many-to-one>
        <property name="spaIsinv" type="java.lang.String">
            <column name="spa_isinv" length="1" />
        </property>
        <property name="spaRemark" type="java.lang.String">
            <column name="spa_remark" length="1073741823" />
        </property>
        <property name="spaCreDate" type="java.util.Date">
            <column name="spa_cre_date" length="23" />
        </property>
         <property name="spaAltDate" type="java.util.Date">
            <column name="spa_alt_date" length="23" />
        </property>
        <property name="spaAltUser" type="java.lang.String">
            <column name="spa_alt_user" length="50" />
        </property>
        <property name="spaIsdel" type="java.lang.String">
            <column name="spa_isdel" length="1" />
        </property>
        <property name="spaUndoDate" type="java.util.Date">
            <column name="spa_undo_date" length="23" />
        </property>
        <property name="spaUndoUser" type="java.lang.String">
            <column name="spa_undo_user" length="50" />
        </property>
    </class>
</hibernate-mapping>
