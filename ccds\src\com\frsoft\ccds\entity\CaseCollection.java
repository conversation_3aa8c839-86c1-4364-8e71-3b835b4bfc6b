package com.frsoft.ccds.entity;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.*;

/**
 * 案件催收记录实体类
 * 对应数据库表: dbo.case_collection
 */
@Entity
@Table(name = "case_collection", schema = "dbo")
@Getter
@Setter
public class CaseCollection {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cc_id")
    private Long ccId;
    
    /**
     * 关联案件ID集合
     */
    @Column(name = "cc_cas_ids", length = 1073741823)
    private String ccCasIds;
    
    /**
     * 证件号码
     */
    @Column(name = "cc_id_no", length = 50)
    private String ccIdNo;
    
    /**
     * 关联的案件批次
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cc_cbat_id")
    private CaseBat ccCbat;
}