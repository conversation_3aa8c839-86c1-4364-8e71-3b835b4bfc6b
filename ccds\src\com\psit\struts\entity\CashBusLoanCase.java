package com.psit.struts.entity;

import java.io.Serializable;

/**
 * 现金巴士借款案件
 */
public class CashBusLoanCase implements Serializable {
    private long id;
    private String loanRefId;
    private String batchName;
    private String userCardNo;
    private int days;
    private int originalAmountDue;
    private String bankName;
    private String relativeType;
    private String socialType;
    private String maritalStatus;
    private String workCity;
    private String workPhone;
    private int age;
    private String userPhone;
    private int amount;
    private String bankCardNo;
    private String relativePhone;
    private String socialPhone;
    private String srcJson;
    private String name;

    public CashBusLoanCase(){

    }

    public CashBusLoanCase(long id) {
        this.id = id;
    }

    public CashBusLoanCase(long id, String loanRefId, String batchName, String userCardNo, int days, int originalAmountDue, String bankName, String relativeType, String socialType, String maritalStatus, String workCity, String workPhone, int age, String userPhone, int amount, String bankCardNo, String relativePhone, String socialPhone, String srcJson, String name) {
        this.id = id;
        this.loanRefId = loanRefId;
        this.batchName = batchName;
        this.userCardNo = userCardNo;
        this.days = days;
        this.originalAmountDue = originalAmountDue;
        this.bankName = bankName;
        this.relativeType = relativeType;
        this.socialType = socialType;
        this.maritalStatus = maritalStatus;
        this.workCity = workCity;
        this.workPhone = workPhone;
        this.age = age;
        this.userPhone = userPhone;
        this.amount = amount;
        this.bankCardNo = bankCardNo;
        this.relativePhone = relativePhone;
        this.socialPhone = socialPhone;
        this.srcJson = srcJson;
        this.name = name;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getLoanRefId() {
        return loanRefId;
    }

    public void setLoanRefId(String loanRefId) {
        this.loanRefId = loanRefId;
    }

    public String getUserCardNo() {
        return userCardNo;
    }

    public void setUserCardNo(String userCardNo) {
        this.userCardNo = userCardNo;
    }

    public int getDays() {
        return days;
    }

    public void setDays(int days) {
        this.days = days;
    }

    public int getOriginalAmountDue() {
        return originalAmountDue;
    }

    public void setOriginalAmountDue(int originalAmountDue) {
        this.originalAmountDue = originalAmountDue;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getRelativeType() {
        return relativeType;
    }

    public void setRelativeType(String relativeType) {
        this.relativeType = relativeType;
    }

    public String getSocialType() {
        return socialType;
    }

    public void setSocialType(String socialType) {
        this.socialType = socialType;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public String getWorkCity() {
        return workCity;
    }

    public void setWorkCity(String workCity) {
        this.workCity = workCity;
    }

    public String getWorkPhone() {
        return workPhone;
    }

    public void setWorkPhone(String workPhone) {
        this.workPhone = workPhone;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getRelativePhone() {
        return relativePhone;
    }

    public void setRelativePhone(String relativePhone) {
        this.relativePhone = relativePhone;
    }

    public String getSocialPhone() {
        return socialPhone;
    }

    public void setSocialPhone(String socialPhone) {
        this.socialPhone = socialPhone;
    }

    public String getSrcJson() {
        return srcJson;
    }

    public void setSrcJson(String srcJson) {
        this.srcJson = srcJson;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "CashBusLoanCase{" +
                "id=" + id +
                ", loanRefId='" + loanRefId + '\'' +
                ", batchName='" + batchName + '\'' +
                ", userCardNo='" + userCardNo + '\'' +
                ", days=" + days +
                ", originalAmountDue=" + originalAmountDue +
                ", bankName='" + bankName + '\'' +
                ", relativeType='" + relativeType + '\'' +
                ", socialType='" + socialType + '\'' +
                ", maritalStatus='" + maritalStatus + '\'' +
                ", workCity='" + workCity + '\'' +
                ", workPhone='" + workPhone + '\'' +
                ", age=" + age +
                ", userPhone='" + userPhone + '\'' +
                ", amount=" + amount +
                ", bankCardNo='" + bankCardNo + '\'' +
                ", relativePhone='" + relativePhone + '\'' +
                ", socialPhone='" + socialPhone + '\'' +
                ", srcJson='" + srcJson + '\'' +
                '}';
    }
}
