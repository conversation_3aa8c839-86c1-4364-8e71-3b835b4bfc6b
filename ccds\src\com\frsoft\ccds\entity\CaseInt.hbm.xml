<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.CaseInt" table="case_int" schema="dbo" >
        <id name="cinId" type="java.lang.Long">
            <column name="cin_id" />
            <generator class="identity" />
        </id>
        <property name="cinName" type="java.lang.String">
            <column name="cin_name" length="50" />
        </property>
        <property name="cinMCat" type="java.lang.String">
            <column name="cin_m_cat" length="200" />
        </property>
        <property name="cinM" type="java.lang.Double">
            <column name="cin_m" precision="18" />
        </property>
        <property name="cinPrincipal" type="java.lang.String">
            <column name="cin_principal" length="200" />
        </property>
        <property name="cinInt" type="java.lang.String">
            <column name="cin_int" length="200" />
        </property>
        <property name="cinOverduePaid" type="java.lang.String">
            <column name="cin_overdue_paid" length="200" />
        </property>
        <property name="cinOverLimit" type="java.lang.String">
            <column name="cin_over_limit" length="200" />
        </property>
        <property name="cinService" type="java.lang.String">
            <column name="cin_service" length="200" />
        </property>
        <property name="cinYear" type="java.lang.String">
            <column name="cin_year" length="200" />
        </property>
        <property name="cinOther" type="java.lang.String">
            <column name="cin_other" length="200" />
        </property>
        <property name="cinOut" type="java.lang.String">
            <column name="cin_out" length="200" />
        </property>
        <property name="cinInsTime" type="java.util.Date">
            <column name="cin_ins_time" length="23" />
        </property>
        <property name="cinEndDate" type="java.lang.String">
            <column name="cin_end_date" length="100" />
        </property>
        <property name="cinDamagesAmt" type="java.lang.String">
            <column name="cin_damages_amt" length="200" />
        </property>
         <many-to-one name="cinCase" class="com.frsoft.ccds.entity.BankCase" fetch="select" not-null="false">
        	<column name="cin_cas_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
