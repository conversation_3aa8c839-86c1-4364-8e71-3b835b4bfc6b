-- 催收系统测试数据脚本
-- 用于开发和测试环境

USE `ccds`;

-- 清理测试数据（保留系统数据）
DELETE FROM `case_paid` WHERE `pa_id` > 0;
DELETE FROM `pho_red` WHERE `pr_id` > 0;
DELETE FROM `bank_case` WHERE `cas_id` > 0;
DELETE FROM `sal_emp` WHERE `se_user_code` != 'admin';
DELETE FROM `sys_user` WHERE `user_code` != 'admin';

-- 重置自增ID
ALTER TABLE `case_paid` AUTO_INCREMENT = 1;
ALTER TABLE `pho_red` AUTO_INCREMENT = 1;
ALTER TABLE `bank_case` AUTO_INCREMENT = 1;
ALTER TABLE `sal_emp` AUTO_INCREMENT = 1;

-- 插入测试用户
INSERT INTO `sys_user` (`user_code`, `user_login_name`, `user_pwd`, `user_name`, `user_isenabled`) VALUES
('USER001', 'zhang<PERSON>', 'e10adc3949ba59abbe56e057f20f883e', '张三', '1'),
('USER002', 'lisi', 'e10adc3949ba59abbe56e057f20f883e', '李四', '1'),
('USER003', 'wangwu', 'e10adc3949ba59abbe56e057f20f883e', '王五', '1'),
('USER004', 'zhaoliu', 'e10adc3949ba59abbe56e057f20f883e', '赵六', '1'),
('USER005', 'sunqi', 'e10adc3949ba59abbe56e057f20f883e', '孙七', '1');

-- 插入测试员工
INSERT INTO `sal_emp` (`se_name`, `se_user_code`, `se_so_code`, `se_isenabled`, `se_position`, `se_phone`, `se_email`, `se_entry_date`) VALUES
('张三', 'USER001', 'ORG001', '1', '催收专员', '***********', '<EMAIL>', '2024-01-01'),
('李四', 'USER002', 'ORG001', '1', '催收专员', '***********', '<EMAIL>', '2024-01-01'),
('王五', 'USER003', 'ORG001', '1', '高级催收专员', '***********', '<EMAIL>', '2024-01-01'),
('赵六', 'USER004', 'ORG001', '1', '催收主管', '***********', '<EMAIL>', '2024-01-01'),
('孙七', 'USER005', 'ORG001', '1', '催收经理', '***********', '<EMAIL>', '2024-01-01');

-- 插入测试案件
INSERT INTO `bank_case` (
    `cas_code`, `cas_group`, `cas_state`, `cas_name`, `cas_phone`, `cas_m`, `cas_paid_m`,
    `cas_se_no`, `cas_ins_time`, `cas_ins_user`, `cas_id_no`, `cas_address`, 
    `cas_email`, `cas_bank`, `cas_card_no`, `cas_overdue_date`, `cas_overdue_days`
) VALUES
('CASE001', 'A组', 1, '陈小明', '***********', 50000.00, 0.00, 2, NOW(), 'admin', '320101199001011234', '江苏省南京市玄武区中山路1号', '<EMAIL>', '工商银行', '6222021234567890123', '2024-01-15', 30),
('CASE002', 'A组', 1, '李小红', '***********', 80000.00, 10000.00, 2, NOW(), 'admin', '320101199002021234', '江苏省南京市鼓楼区中央路2号', '<EMAIL>', '建设银行', '6222021234567890124', '2024-01-10', 35),
('CASE003', 'B组', 0, '王小刚', '***********', 120000.00, 0.00, NULL, NOW(), 'admin', '320101199003031234', '江苏省南京市秦淮区太平南路3号', '<EMAIL>', '农业银行', '6222021234567890125', '2024-01-20', 25),
('CASE004', 'B组', 1, '张小丽', '***********', 60000.00, 20000.00, 3, NOW(), 'admin', '320101199004041234', '江苏省南京市建邺区江东中路4号', '<EMAIL>', '中国银行', '6222021234567890126', '2024-01-05', 40),
('CASE005', 'C组', 2, '刘小强', '13912345682', 90000.00, 90000.00, 4, NOW(), 'admin', '320101199005051234', '江苏省南京市雨花台区雨花路5号', '<EMAIL>', '交通银行', '6222021234567890127', '2024-01-01', 44),
('CASE006', 'A组', 1, '赵小美', '13912345683', 75000.00, 5000.00, 2, NOW(), 'admin', '320101199006061234', '江苏省南京市栖霞区仙林大道6号', '<EMAIL>', '招商银行', '6222021234567890128', '2024-01-12', 33),
('CASE007', 'B组', 0, '孙小华', '13912345684', 110000.00, 0.00, NULL, NOW(), 'admin', '320101199007071234', '江苏省南京市江宁区胜太路7号', '<EMAIL>', '民生银行', '6222021234567890129', '2024-01-18', 27),
('CASE008', 'C组', 1, '周小军', '13912345685', 85000.00, 15000.00, 4, NOW(), 'admin', '320101199008081234', '江苏省南京市浦口区江浦街道8号', '<EMAIL>', '光大银行', '6222021234567890130', '2024-01-08', 37),
('CASE009', 'A组', 3, '吴小芳', '13912345686', 95000.00, 30000.00, 3, NOW(), 'admin', '320101199009091234', '江苏省南京市六合区雄州街道9号', '<EMAIL>', '华夏银行', '6222021234567890131', '2024-01-03', 42),
('CASE010', 'B组', 1, '郑小伟', '13912345687', 70000.00, 8000.00, 3, NOW(), 'admin', '320101199010101234', '江苏省南京市溧水区永阳街道10号', '<EMAIL>', '平安银行', '6222021234567890132', '2024-01-14', 31);

-- 插入催收记录
INSERT INTO `pho_red` (
    `pr_typ_id`, `pr_contact`, `pr_cas_id`, `pr_content`, `pr_time`, `pr_se_no`,
    `pr_con_type`, `pr_result`, `pr_next_time`, `pr_remark`
) VALUES
(1, '陈小明', 1, '电话联系客户，了解还款意愿', '2024-01-16 09:00:00', 2, '电话', '承诺还款', '2024-01-20 09:00:00', '客户表示本月底前还款'),
(1, '李小红', 2, '电话催收，客户已部分还款', '2024-01-17 10:30:00', 2, '电话', '部分还款', '2024-01-25 10:00:00', '客户已还1万，承诺月底还清余款'),
(1, '王小刚', 3, '电话无人接听', '2024-01-21 14:00:00', 2, '电话', '无人接听', '2024-01-22 14:00:00', '多次拨打无人接听'),
(1, '张小丽', 4, '电话联系，客户配合度较高', '2024-01-18 11:00:00', 3, '电话', '承诺还款', '2024-01-28 11:00:00', '客户承诺分期还款'),
(2, '刘小强', 5, '发送催收短信', '2024-01-19 16:00:00', 4, '短信', '已发送', NULL, '短信已发送，等待客户回复'),
(1, '赵小美', 6, '电话催收，客户态度良好', '2024-01-20 09:30:00', 2, '电话', '承诺还款', '2024-01-30 09:30:00', '客户表示理解，承诺尽快还款'),
(1, '孙小华', 7, '电话联系客户家属', '2024-01-22 15:00:00', 3, '电话', '联系家属', '2024-01-25 15:00:00', '联系到客户家属，了解情况'),
(1, '周小军', 8, '电话催收，客户有还款计划', '2024-01-23 10:00:00', 4, '电话', '承诺还款', '2024-02-01 10:00:00', '客户制定了详细的还款计划'),
(1, '吴小芳', 9, '电话联系，客户暂时困难', '2024-01-24 14:30:00', 3, '电话', '困难协商', '2024-02-05 14:30:00', '客户目前有困难，协商延期还款'),
(1, '郑小伟', 10, '电话催收，客户积极配合', '2024-01-25 11:30:00', 3, '电话', '承诺还款', '2024-01-31 11:30:00', '客户积极配合，承诺月底前还款');

-- 插入还款记录
INSERT INTO `case_paid` (
    `pa_state`, `pa_cas_id`, `pa_ptp_d`, `pa_ptp_num`, `pa_paid_date`, `pa_paid_num`,
    `pa_se_no`, `pa_remark`, `pa_type`, `pa_method`, `pa_ins_time`, `pa_ins_user`
) VALUES
(1, 2, '2024-01-15 00:00:00', 10000.00, '2024-01-15 14:30:00', 10000.00, 2, '客户主动还款', '部分还款', '银行转账', NOW(), 'USER002'),
(0, 1, '2024-01-31 00:00:00', 50000.00, NULL, NULL, 2, '客户承诺月底前全额还款', '承诺还款', '银行转账', NOW(), 'USER002'),
(1, 4, '2024-01-20 00:00:00', 20000.00, '2024-01-20 16:00:00', 20000.00, 3, '客户按承诺还款', '部分还款', '银行转账', NOW(), 'USER003'),
(1, 5, '2024-01-02 00:00:00', 90000.00, '2024-01-02 10:00:00', 90000.00, 4, '客户全额还清', '全额还款', '银行转账', NOW(), 'USER004'),
(0, 6, '2024-02-01 00:00:00', 70000.00, NULL, NULL, 2, '客户承诺下月初还清余款', '承诺还款', '银行转账', NOW(), 'USER002'),
(1, 6, '2024-01-18 00:00:00', 5000.00, '2024-01-18 09:00:00', 5000.00, 2, '客户先还部分金额', '部分还款', '银行转账', NOW(), 'USER002'),
(0, 8, '2024-02-05 00:00:00', 70000.00, NULL, NULL, 4, '客户制定分期还款计划', '承诺还款', '银行转账', NOW(), 'USER004'),
(1, 8, '2024-01-22 00:00:00', 15000.00, '2024-01-22 11:30:00', 15000.00, 4, '客户按计划第一期还款', '部分还款', '银行转账', NOW(), 'USER004'),
(0, 9, '2024-02-10 00:00:00', 65000.00, NULL, NULL, 3, '协商延期还款', '承诺还款', '银行转账', NOW(), 'USER003'),
(1, 9, '2024-01-10 00:00:00', 30000.00, '2024-01-10 15:20:00', 30000.00, 3, '客户前期还款', '部分还款', '银行转账', NOW(), 'USER003');

COMMIT;

-- 查询统计信息
SELECT '=== 数据统计 ===' AS info;
SELECT '用户数量' AS type, COUNT(*) AS count FROM sys_user;
SELECT '员工数量' AS type, COUNT(*) AS count FROM sal_emp;
SELECT '案件数量' AS type, COUNT(*) AS count FROM bank_case;
SELECT '催收记录数量' AS type, COUNT(*) AS count FROM pho_red;
SELECT '还款记录数量' AS type, COUNT(*) AS count FROM case_paid;

SELECT '=== 案件状态分布 ===' AS info;
SELECT 
    CASE cas_state 
        WHEN 0 THEN '待分配'
        WHEN 1 THEN '催收中'
        WHEN 2 THEN '已结案'
        WHEN 3 THEN '暂停催收'
        WHEN 4 THEN '法务处理'
        ELSE '未知'
    END AS status,
    COUNT(*) AS count
FROM bank_case 
GROUP BY cas_state;

SELECT '=== 员工案件分配情况 ===' AS info;
SELECT 
    e.se_name AS employee_name,
    COUNT(c.cas_id) AS case_count,
    SUM(c.cas_m) AS total_amount,
    SUM(c.cas_paid_m) AS paid_amount
FROM sal_emp e
LEFT JOIN bank_case c ON e.se_no = c.cas_se_no
WHERE e.se_user_code != 'admin'
GROUP BY e.se_no, e.se_name
ORDER BY case_count DESC;
