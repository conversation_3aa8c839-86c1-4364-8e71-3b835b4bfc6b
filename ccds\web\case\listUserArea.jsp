<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>保存账号地区</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
    	body{
			background:#fff;
		}
		#areaUl {
			margin:0;
			padding:0;
			list-style:none;
			text-align:left;
		}
		#areaUl li {
			padding:2px 0 2px 10px;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript">
		function initPage(){
			<c:if test="${!empty userAreaList}">
				<c:forEach var="userArea" items="${userAreaList}">
					$("area${userArea.uarArea.typId}").checked=true;
				</c:forEach>
			</c:if>
		
		}
		
		function submitForm(){
			$("saveForm").submit();
		}
		
		createProgressBar();
		window.onload=function(){
			initPage();
			closeProgressBar();
		}
  	</script>
  </head>
  <body> 
  	<div>
    	<c:if test="${!empty areaList}">
            <form id="saveForm" action="caseAction.do" style="padding:0; margin:0">
                <input type="hidden" name="op" value="saveUserArea" />
                <input type="hidden" name="userCode" value="${userCode}" />
            	<div class="submitLayer" style="padding:4px"><button onClick="submitForm()">保存</button></div>
                <div class="scrollBarNoStyle" style="height:200px">
                 <ul id="areaUl">
                    <c:forEach var="area" items="${areaList}">
                    <li><input type="checkbox" name="areaId" id="area${area.typId}" value="${area.typId}"/><label for="area${area.typId}">${area.typName}</label></li>
                    </c:forEach>
               	 </ul>
                </div>
               
        	</form>
        </c:if>
        <c:if test="${empty areaList}">
        	<span class="gray">未添加催收区域</span>
        </c:if>
    </div>
  </body>
</html>
