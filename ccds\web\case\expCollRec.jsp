<%@ page language="java" pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
     <base href="<%=basePath%>"></base>  
     <title>选择催收记录导出类型</title>
     <!-- 定制版 -->
     <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	 <meta http-equiv="cache-control" content="no-cache">
	 <meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <style type="text/css">
	 	#chooseLayer {
			padding:10px 0 10px 0;
			text-align:center;
		}
		#topWords {
			padding:0 0 6px 10px;
			text-align:left; color:#666;
		}
     	#typeTable {
			width:100%;
		}
		#typeTable td {
			padding:5px;
			font-size:12px;
		}

		.exportSubmitTd {
			padding:10px 0 0 0;
			background:#FFFF99; 
			 text-align:center;
		}
     </style>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/common.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	 <script type="text/javascript">
		function check(){
			
			if(!$("defaultType").checked){
				toExportData($("outForm").action,$("outForm").serialize(true)); 
			}
			else{
				waitSubmit("out");
				waitSubmit("doCancel");
				return $("outForm").submit();
			}
		}

　		/*function close(){
			setTimeout("closeDiv('')",3000);
		}*/ 

		function initPage(){
			var typeArray = null;
			var formArgs,height=null;
			var checkedId = "";
			switch('${expType}'){
			case 'caseBat':
				if("${cbatId}"=="selected"){
					formArgs = { "cbatId":getBacthIds() };
				}
				else{
					formArgs = { "cbatId":"${cbatId}" };
				}
				formArgs.op ="expCRByBat";
				break;
			case 'selected':
				formArgs = {
					"caseIds":"${caseIds}"
				};
				formArgs.op ="expCRByCase";
				break;
			/*case 'bank':
				formArgs = {
					"bankName":"${bankName}",
					"startTime":"${startTime}",
					"endTime":"${endTime}"
				};
				formArgs.op ="expCRByCaseType";
				break;*/
			}
			switch("${CUS_VER_ID}"){
			/*case '1':
				typeArray = [
					[["word","table"],["excel","row"]]
				];
				height = 170;
				break;*/
			case '2':
				if('${expType}'=='caseBat'){
					typeArray = [
						[["建行催记","ccbRow"],["建行案件信息与对应催记","dfTab"],["建行材料反馈表","ccbBack"]]
					];
				}
				else{
					typeArray = [
						[["建行催记","ccbRow"]]
					];
				}
				height = 170;
				break;
			case '3':
				typeArray = [
					[["建行催记(excel)","ccbRow"],["建行催记(word)","ccbWord"],["广发催记","gfRow"]],
					[["光大催记","gdRow"],["平安银行","paRow"],["交通银行","jtRow"]],
					[["中信银行","zxRow"],["捷信","jxRow"],["亚联财","ylcRow"]],
					[["安信","axRow",3]]
				];
				height = 350;
				break;
			case '10':
				typeArray = [
					[["光大催记","gdRow"],["农行委案催收小结(word)","abcWord"],["跟进情况表","zxFollow"]],
					[["退件报告","zxBack"],["催收周报","zxWeek"]]
				];
				height = 200;
				checkedId = "gdRow";
				break;
			case '18':
				typeArray = [
					[["建行催记(excel)","ccbRow"],["建行催记(word)","ccbWord"]]
				];
				height = 170;
				break;
			case '25':
				typeArray = [
					[["商业案件系统数据","shjsRow"],["案件跟进记录","shjsTab"]]
				];
				height = 170;
				break;
			}
			if(height!=null){
				loadPopWinHeight(height);
			}
			createHideInput("outForm",formArgs);
			createTypeTable(typeArray,checkedId);
		}
		
		function createTypeTable(typeArray,checkedId){
			var tabHtmls = [];
			var colSpan = 1;
			tabHtmls.push("<div id='topWords'>请选择要导出的类型：</div><table id='typeTable' cellpadding='0' cellspacing='0'>");
			if(typeArray!=null&&typeArray!=undefined&&typeArray.length>0){
				colSpan = typeArray[0].length;
				tabHtmls.push("<tr><td colspan='"+colSpan+"'><input type='radio' name='type' id='defaultType' checked value='default' /><label for='defaultType'>标准催记</label></td></tr>");
				for(var i=0;i<typeArray.length;i++){
					tabHtmls.push("<tr>");
					
					for(var j=0;j<typeArray[i].length;j++){
						tabHtmls.push("<td "+(typeArray[i][j].length>2?"colspan="+typeArray[i][j][2]:"")+" ><input type='radio' name='type' id='"+typeArray[i][j][1]+"Type' value='"+typeArray[i][j][1]+"' "+(checkedId==typeArray[i][j][1]?"checked":"")+" /><label for='"+typeArray[i][j][1]+"Type'>"+typeArray[i][j][0]+"</label></td>");
					}
					tabHtmls.push("</tr>");
				}
			}
			else{
				tabHtmls.push("<tr><td><input type='radio' name='type' id='defaultType' checked value='default' /><label for='defaultType'>标准催记</label></td></tr>");
			}
			tabHtmls.push("<tr><td colspan='"+colSpan+"'  id='submitTd' class='exportSubmitTd'><div><input type='button' class='butSize1' id='out' value='导出' onClick='check()'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type='button' class='butSize1' id='doCancel'  value='取消' onClick='cancel()'/></div></td></tr></table>");
			$("chooseLayer").innerHTML = tabHtmls.join("");
		}
		
		window.onload=function(){
			initPage();
		}
	 </script>
</head>
  <body>
  <div class="inputDiv">
  	<form action="caseAction.do" method="post" id="outForm">
        <div id="chooseLayer"></div>    
	</form>
  </div>
  </body>
</html>
