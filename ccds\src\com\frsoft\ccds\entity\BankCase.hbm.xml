<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.BankCase" table="bank_case" schema="dbo" >
        <id name="casId" type="java.lang.Long">
            <column name="cas_id" />
            <generator class="identity" />
        </id>
        <property name="casCcId" type="java.lang.Long">
            <column name="cas_cc_id" />
        </property>
		<property name="casCode" type="java.lang.String">
            <column name="cas_code" length="100" />
        </property>
        <property name="casGroup" type="java.lang.String">
            <column name="cas_group" length="50" />
        </property>
        <property name="casState" type="java.lang.Integer">
            <column name="cas_state" />
        </property>
        <property name="casOutState" type="java.lang.Integer">
            <column name="cas_out_state" />
        </property>
     <property name="casM" type="java.lang.Double">
            <column name="cas_m" precision="18" />
        </property>
        <property name="casLastM" type="java.lang.Double">
            <column name="cas_last_m" precision="18" />
        </property>
        <property name="casLastIntDate" type="java.util.Date">
            <column name="cas_last_int_date" precision="18" />
        </property>
        <property name="casRmb" type="java.lang.String">
            <column name="cas_rmb" length="50" />
        </property>
        <property name="casGb" type="java.lang.String">
            <column name="cas_gb" length="50" />
        </property>
        <property name="casMy" type="java.lang.String">
            <column name="cas_my" length="50" />
        </property>
        <property name="casPtpM" type="java.lang.Double">
            <column name="cas_ptp_m" precision="18" />
        </property>
        <property name="casCpM" type="java.lang.Double">
            <column name="cas_cp_m" precision="18" />
        </property>
        <property name="casPaidM" type="java.lang.Double">
            <column name="cas_paid_m" precision="18" />
        </property>
        <property name="casDate" type="java.util.Date">
            <column name="cas_date" length="23" />
        </property>
        <property name="casName" type="java.lang.String">
            <column name="cas_name" length="50" />
        </property>
        <property name="casSex" type="java.lang.String">
            <column name="cas_sex" length="1" />
        </property>
        <property name="casCaCd" type="java.lang.String">
            <column name="cas_ca_cd" length="50" />
        </property>
        <property name="casNum" type="java.lang.String">
            <column name="cas_num" length="50" />
        </property>
        <property name="casPostCode" type="java.lang.String">
            <column name="cas_post_code" length="100" />
        </property>
        <property name="casInsUser" type="java.lang.String">
            <column name="cas_ins_user" length="25" />
        </property>
        <property name="casInsTime" type="java.util.Date">
            <column name="cas_ins_time" length="23" />
        </property>
        <property name="casTipTime" type="java.util.Date">
        	<column name="cas_tip_time" length="23"></column>
        </property>
        <property name="casAltUser" type="java.lang.String">
            <column name="cas_alt_user" length="25" />
        </property>
        <property name="casAltTime" type="java.util.Date">
            <column name="cas_alt_time" length="23" />
        </property>
        <property name="casPrTime" type="java.util.Date">
            <column name="cas_pr_time" length="23" />
        </property>
        <property name="casTremark" type="java.lang.String">
            <column name="cas_tremark" length="300" />
        </property>
        <property name="casWarn" type="java.lang.String">
            <column name="cas_warn" length="300" />
        </property>
        <property name="casAccNum" type="java.lang.String">
            <column name="cas_acc_num" length="100" />
        </property>
        <property name="casCardCat" type="java.lang.String">
            <column name="cas_card_cat" length="50" />
        </property>
        <property name="casPrincipal" type="java.lang.String">
            <column name="cas_principal" length="200" />
        </property>
        <property name="casMinPaid" type="java.lang.String">
            <column name="cas_min_paid" length="200" />
        </property>
        <property name="casCredLim" type="java.lang.String">
            <column name="cas_cred_lim" length="200" />
        </property>
        <property name="casDelayLv" type="java.lang.String">
            <column name="cas_delay_lv" length="200" />
        </property>
        <property name="casGuaM" type="java.lang.String">
            <column name="cas_gua_m" precision="200" />
        </property>
        <property name="casMCat" type="java.lang.String">
            <column name="cas_m_cat" length="200" />
        </property>
        <property name="casPreRec" type="java.lang.String">
            <column name="cas_pre_rec" length="**********" />
        </property>
        <property name="casExcLim" type="java.lang.String">
            <column name="cas_exc_lim" length="200" />
        </property>
        <property name="casUnitName" type="java.lang.String">
            <column name="cas_unit_name" length="200" />
        </property>
        <property name="casMP" type="java.lang.Double">
            <column name="cas_m_p" precision="15" scale="0" />
        </property>
        <property name="casName1" type="java.lang.String">
            <column name="cas_name_1" length="50" />
        </property>
        <property name="casNum1" type="java.lang.String">
            <column name="cas_num_1" length="200" />
        </property>
        <property name="casRe1" type="java.lang.String">
            <column name="cas_re_1" length="200" />
        </property>
        <property name="casConCom1" type="java.lang.String">
            <column name="cas_con_com1" length="200" />
        </property>
        <property name="casConPho1" type="java.lang.String">
            <column name="cas_con_pho1" length="100" />
        </property>
        <property name="casConMob1" type="java.lang.String">
            <column name="cas_con_mob1" length="100" />
        </property>
        <property name="casConAdd1" type="java.lang.String">
            <column name="cas_con_add1" length="500" />
        </property>
        <property name="casName2" type="java.lang.String">
            <column name="cas_name_2" length="50" />
        </property>
        <property name="casName3" type="java.lang.String">
            <column name="cas_name_3" length="50" />
        </property>
        <property name="casName4" type="java.lang.String">
            <column name="cas_name_4" length="50" />
        </property>
        <property name="casNum2" type="java.lang.String">
            <column name="cas_num_2" length="200" />
        </property>
        <property name="casNum3" type="java.lang.String">
            <column name="cas_num_3" length="200" />
        </property>
        <property name="casNum4" type="java.lang.String">
            <column name="cas_num_4" length="200" />
        </property>
        <property name="casRe2" type="java.lang.String">
            <column name="cas_re_2" length="200" />
        </property>
        <property name="casRe3" type="java.lang.String">
            <column name="cas_re_3" length="200" />
        </property>
        <property name="casRe4" type="java.lang.String">
            <column name="cas_re_4" length="200" />
        </property>
        <property name="casConCom2" type="java.lang.String">
            <column name="cas_con_com2" length="200" />
        </property>
        <property name="casConCom3" type="java.lang.String">
            <column name="cas_con_com3" length="200" />
        </property>
        <property name="casConCom4" type="java.lang.String">
            <column name="cas_con_com4" length="200" />
        </property>
        <property name="casRemark" type="java.lang.String">
            <column name="cas_remark" length="**********" />
        </property>
        <property name="casRemark2" type="java.lang.String">
            <column name="cas_remark2" length="**********" />
        </property>
        <property name="casRemark3" type="java.lang.String">
            <column name="cas_remark3" length="**********" />
        </property>
        <property name="casRemark4" type="java.lang.String">
            <column name="cas_remark4" length="**********" />
        </property>
        <property name="casRemark5" type="java.lang.String">
            <column name="cas_remark5" length="**********" />
        </property>
        <property name="casRemark6" type="java.lang.String">
            <column name="cas_remark6" length="**********" />
        </property>
        <property name="casRemark7" type="java.lang.String">
            <column name="cas_remark7" length="**********" />
        </property>
        <property name="casRemark8" type="java.lang.String">
            <column name="cas_remark8" length="**********" />
        </property>
        <property name="casCardBank" type="java.lang.String">
            <column name="cas_card_bank" length="200" />
        </property>
        <property name="casApp1" type="java.lang.Integer">
        	<column name="cas_app_1" />
        </property>
        <property name="casApp2" type="java.lang.Integer">
        	<column name="cas_app_2" />
        </property>
        <property name="casApp3" type="java.lang.Integer">
        	<column name="cas_app_3" />
        </property>
        <property name="casApp4" type="java.lang.Integer">
        	<column name="cas_app_4" />
        </property>
        <property name="casApp5" type="java.lang.Integer">
        	<column name="cas_app_5" />
        </property>
        <property name="casApp6" type="java.lang.Integer">
        	<column name="cas_app_6" />
        </property>
        <property name="casApp7" type="java.lang.Integer">
        	<column name="cas_app_7" />
        </property>
        <property name="casApp8" type="java.lang.Integer">
        	<column name="cas_app_8" />
        </property>
        <property name="casApp9" type="java.lang.Integer">
        	<column name="cas_app_9" />
        </property>
        <property name="casApp10" type="java.lang.Integer">
        	<column name="cas_app_10" />
        </property>
        <property name="casApp11" type="java.lang.Integer">
        	<column name="cas_app_11" />
        </property>
        <property name="casApp12" type="java.lang.Integer">
        	<column name="cas_app_12" />
        </property>
        <property name="casApp13" type="java.lang.Integer">
        	<column name="cas_app_13" />
        </property>
        <property name="casApp14" type="java.lang.Integer">
        	<column name="cas_app_14" />
        </property>
        <property name="casHomPho" type="java.lang.String">
            <column name="cas_hom_pho" length="100" />
        </property>
        <property name="casWorkPho" type="java.lang.String">
            <column name="cas_work_pho" length="100" />
        </property>
        <property name="casMobPho" type="java.lang.String">
            <column name="cas_mob_pho" length="100" />
        </property>
        <property name="casHomAdd" type="java.lang.String">
            <column name="cas_hom_add" length="500" />
        </property>
        <property name="casWorkAdd" type="java.lang.String">
            <column name="cas_work_add" length="500" />
        </property>
        <property name="casMailAdd" type="java.lang.String">
            <column name="cas_mail_add" length="500" />
        </property>
        <property name="casRegAdd" type="java.lang.String">
            <column name="cas_reg_add" length="500" />
        </property>
        <property name="casConPho2" type="java.lang.String">
            <column name="cas_con_pho2" length="100" />
        </property>
        <property name="casConMob2" type="java.lang.String">
            <column name="cas_con_mob2" length="100" />
        </property>
        <property name="casConAdd2" type="java.lang.String">
            <column name="cas_con_add2" length="500" />
        </property>
        <property name="casConPho3" type="java.lang.String">
            <column name="cas_con_pho3" length="100" />
        </property>
        <property name="casConMob3" type="java.lang.String">
            <column name="cas_con_mob3" length="100" />
        </property>
        <property name="casConAdd3" type="java.lang.String">
            <column name="cas_con_add3" length="500" />
        </property>
        <property name="casConPho4" type="java.lang.String">
            <column name="cas_con_pho4" length="100" />
        </property>
        <property name="casConMob4" type="java.lang.String">
            <column name="cas_con_mob4" length="100" />
        </property>
        <property name="casConAdd4" type="java.lang.String">
            <column name="cas_con_add4" length="500" />
        </property>
        <property name="casLoanType" type="java.lang.String">
            <column name="cas_loan_type" length="200" />
        </property>
        <property name="casCollType" type="java.lang.String">
            <column name="cas_coll_type" length="200" />
        </property>
        <property name="casInt" type="java.lang.String">
            <column name="cas_int" length="200" />
        </property>
        <property name="casOverduePaid" type="java.lang.String">
            <column name="cas_overdue_paid" length="200" />
        </property>
        <property name="casPaidDate" type="java.lang.String">
            <column name="cas_paid_date" length="200" />
        </property>
        <property name="casConDate" type="java.lang.String">
            <column name="cas_con_date" length="200" />
        </property>
		<property name="casRaiDate" type="java.lang.String">
            <column name="cas_rai_date" length="200" />
        </property>
		<property name="casStopDate" type="java.lang.String">
            <column name="cas_stop_date" length="200" />
        </property>
		<property name="casCreDate" type="java.lang.String">
            <column name="cas_cre_date" length="200" />
        </property>
		<property name="casPaidLim" type="java.lang.String">
            <column name="cas_paid_lim" length="200" />
        </property>
        <property name="casNote" type="java.lang.String">
            <column name="cas_note" />
        </property>
        <property name="casFileNo" type="java.lang.String">
            <column name="cas_file_no" length="100" />
        </property>
        <property name="casEmail" type="java.lang.String">
        	<column name="cas_email" length="100"/>
        </property>
        <property name="casIsOth" type="java.lang.Integer">
        	<column name="cas_is_oth" />
        </property>
        <property name="casIsNewpr" type="java.lang.Integer">
        	<column name="cas_is_newpr" />
        </property>
        <property name="casIsNewPaid" type="java.lang.Integer">
        	<column name="cas_is_newpaid" />
        </property>
        <property name="casIsPaidover" type="java.lang.Integer">
        	<column name="cas_is_paidover" />
        </property>
        <property name="casIsUpdint" type="java.lang.Integer">
        	<column name="cas_is_updint" />
        </property>
        <property name="casPos" type="java.lang.String">
            <column name="cas_pos" length="200" />
        </property>
        <property name="casPart" type="java.lang.String">
            <column name="cas_part" length="200" />
        </property>
        <property name="casPtpC" type="java.lang.Integer">
        	<column name="cas_ptp_c" />
        </property>
        <property name="casBackdate" type="java.util.Date">
            <column name="cas_backdate" length="23" />
        </property>
        <property name="casBackdateP" type="java.util.Date">
        	<column name="cas_backdate_p" length="23"></column>
        </property>
        <property name="casBackP" type="java.lang.Double">
            <column name="cas_back_p" precision="15" scale="0" />
        </property>
        <property name="casConWpho1" type="java.lang.String">
            <column name="cas_con_wpho1" length="100" />
        </property>
        <property name="casConWpho2" type="java.lang.String">
            <column name="cas_con_wpho2" length="100" />
        </property>
        <property name="casConWpho3" type="java.lang.String">
            <column name="cas_con_wpho3" length="100" />
        </property>
        <property name="casConWpho4" type="java.lang.String">
            <column name="cas_con_wpho4" length="100" />
        </property>
        <property name="casNameU" type="java.lang.String">
            <column name="cas_name_u" length="50" />
        </property>
        <property name="casNumU" type="java.lang.String">
            <column name="cas_num_u" length="200" />
        </property>
        <property name="casReU" type="java.lang.String">
            <column name="cas_re_u" length="200" />
        </property>
        <property name="casConUCom" type="java.lang.String">
            <column name="cas_con_u_com" length="200" />
        </property>
        <property name="casConUWpho" type="java.lang.String">
            <column name="cas_con_u_wpho" length="100" />
        </property>
        <property name="casConUPho" type="java.lang.String">
            <column name="cas_con_u_pho" length="100" />
        </property>
        <property name="casConUMob" type="java.lang.String">
            <column name="cas_con_u_mob" length="100" />
        </property>
        <property name="casConUAdd" type="java.lang.String">
            <column name="cas_con_u_add" length="500" />
        </property>
        <property name="casBackM" type="java.lang.Double">
            <column name="cas_back_m" precision="18" />
        </property>
        <property name="casName5" type="java.lang.String">
            <column name="cas_name_5" length="50" />
        </property>
        <property name="casNum5" type="java.lang.String">
            <column name="cas_num_5" length="200" />
        </property>
        <property name="casRe5" type="java.lang.String">
            <column name="cas_re_5" length="200" />
        </property>
        <property name="casConCom5" type="java.lang.String">
            <column name="cas_con_com_5" length="200" />
        </property>
        <property name="casConWpho5" type="java.lang.String">
            <column name="cas_con_wpho_5" length="100" />
        </property>
        <property name="casConPho5" type="java.lang.String">
            <column name="cas_con_pho_5" length="100" />
        </property>
        <property name="casConMob5" type="java.lang.String">
            <column name="cas_con_mob_5" length="100" />
        </property>
        <property name="casConAdd5" type="java.lang.String">
            <column name="cas_con_add_5" length="500" />
        </property>
        <property name="casLoanDate" type="java.lang.String">
            <column name="cas_loan_date" length="200" />
        </property>
        <property name="casOverdueDate" type="java.lang.String">
            <column name="cas_overdue_date" length="200" />
        </property>
        <property name="casPaidCount" type="java.lang.String">
            <column name="cas_paid_count" length="100" />
        </property>
        <property name="casAppNo" type="java.lang.String">
            <column name="cas_app_no" length="100" />
        </property>
        <property name="casSoPcno" type="java.lang.String">
            <column name="cas_so_pcno" length="100" />
        </property>
        <property name="casSoNo" type="java.lang.String">
            <column name="cas_so_no" length="100" />
        </property>
        <property name="casPbackP" type="java.lang.Double">
            <column name="cas_pback_p" precision="15" scale="0" />
        </property>
        <property name="casWpostCode" type="java.lang.String">
            <column name="cas_wpost_code" length="100" />
        </property>
        <property name="casDeadline" type="java.lang.String">
            <column name="cas_deadline" length="200" />
        </property>
        <property name="casBillDate" type="java.lang.String">
            <column name="cas_bill_date" length="200" />
        </property>
        <property name="casIsHost" type="java.lang.String">
            <column name="cas_is_host" length="50" />
        </property>
        <property name="casLastPaid" type="java.lang.String">
            <column name="cas_last_paid" length="200" />
        </property>
        <property name="casCount" type="java.lang.String">
            <column name="cas_count" length="100" />
        </property>
        <property name="casLeftPri" type="java.lang.String">
            <column name="cas_left_pri" length="100" />
        </property>
        <property name="casAssignIds" type="java.lang.String">
            <column name="cas_assign_ids" length="**********" />
        </property>
        <property name="casAssignNames" type="java.lang.String">
            <column name="cas_assign_names" length="**********" />
        </property>
        <property name="casLastAssignTime" type="java.util.Date">
            <column name="cas_last_assign_time" length="23" />
        </property>
        <property name="casLastVis" type="java.util.Date">
            <column name="cas_last_vis" length="23" />
        </property>
        <property name="casFstClPaidDate" type="java.util.Date">
            <column name="cas_fst_cl_paid_date" length="23" />
        </property>
        <property name="casLastClPaidDate" type="java.util.Date">
            <column name="cas_last_cl_paid_date" length="23" />
        </property>
        <property name="casOverdueDays" type="java.lang.Integer">
            <column name="cas_overdue_days" />
        </property>
        <property name="casOverdueDaysStr" type="java.lang.String">
            <column name="cas_overdue_days_str" length="200" />
        </property>
        <property name="casBir" type="java.lang.String">
            <column name="cas_bir" length="50" />
        </property>
        <property name="casMpostCode" type="java.lang.String">
            <column name="cas_mpost_code" length="100" />
        </property>
        <property name="casPermCrline" type="java.lang.String">
            <column name="cas_perm_crline" length="50" />
        </property>
        <property name="casAltHold" type="java.lang.String">
            <column name="cas_alt_hold" length="50" />
        </property>
        <property name="casCycle" type="java.lang.String">
            <column name="cas_cycle" length="50" />
        </property>
        <property name="casNoout" type="java.lang.String">
            <column name="cas_noout" length="50" />
        </property>
        <property name="casFieldType" type="java.lang.String">
            <column name="cas_field_type" length="50" />
        </property>
        <property name="casPrCount" type="java.lang.Integer">
        	<column name="cas_pr_count" />
        </property>
        <property name="casOverdueNum" type="java.lang.String">
        	<column name="cas_overdue_num" length="100"/>
        </property>
        <property name="casOverdueOnce" type="java.lang.Integer">
        	<column name="cas_overdue_once" />
        </property>
        <property name="casLoanRate" type="java.lang.String">
            <column name="cas_loan_rate" length="100"/>
        </property>
        <property name="casMonthPaid" type="java.lang.String">
            <column name="cas_month_paid" length="100" />
        </property>
        <property name="casOverdueM" type="java.lang.String">
            <column name="cas_overdue_m" length="100" />
        </property>
        <property name="casColor" type="java.lang.Integer">
        	<column name="cas_color" />
        </property>
        <property name="casIsNewass" type="java.lang.Integer">
        	<column name="cas_is_newass" />
       	</property>
        <property name="casRegPostCode" type="java.lang.String">
        	<column name="cas_reg_post_code" length="100" />
        </property>
        <property name="casLoanEndDate" type="java.lang.String">
        	<column name="cas_loan_end_date" length="200" />
        </property>
        <property name="casOverLimit" type="java.lang.String">
            <column name="cas_over_limit" length="200" />
        </property>
         <property name="casArea1" type="java.lang.String">
            <column name="cas_area_1" length="50" />
        </property>
         <property name="casArea2" type="java.lang.String">
            <column name="cas_area_2" length="50" />
        </property>
         <property name="casArea3" type="java.lang.String">
            <column name="cas_area_3" length="50" />
        </property>
        <property name="casNumType" type="java.lang.String">
            <column name="cas_num_type" length="50" />
        </property>
        <property name="casLastEndDate" type="java.lang.String">
            <column name="cas_last_end_date" length="50" />
        </property>
        <property name="casAssignTimes" type="java.lang.String">
            <column name="cas_assign_times" length="**********"  />
        </property>
        <property name="casClCount" type="java.lang.String">
            <column name="cas_cl_count" length="200" />
        </property>
        <property name="casAccName" column="cas_acc_name" type="java.lang.String" length="50" ></property>
   		<property name="casManageCost" column="cas_manage_cost" type="java.lang.String" length="50" ></property>
   		<property name="casPenalty" column="cas_penalty" type="java.lang.String" length="50" ></property>
   		<property name="casAge" column="cas_age" type="java.lang.String" length="50" ></property>
   		<property name="casQq" column="cas_qq" type="java.lang.String" length="50" ></property>
   		<property name="casOvdPrinc" column="cas_ovd_princ" type="java.lang.String" length="50" ></property>
   		<property name="casOvdDamage" column="cas_ovd_damage" type="java.lang.String" length="50" ></property>
   		<property name="casLastPr" column="cas_last_pr" type="java.lang.String" length="4000" ></property>
        <property name="casRecycleDate" type="java.util.Date">
            <column name="cas_recycle_date" length="23" />
        </property>
        
   		<many-to-one name="casClArea" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cas_cl_area_id" />
        </many-to-one>
        <many-to-one name="typeListBank" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cas_typ_bid" />
        </many-to-one>
        <many-to-one name="typeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cas_typ_hid" />
        </many-to-one>
       <!-- 
       <many-to-one name="casProv" class="com.frsoft.base.entity.CusArea" fetch="select" not-null="false">
            <column name="cas_typ_id1" />
        </many-to-one>
        <many-to-one name="casCity" class="com.frsoft.base.entity.CusProvince" fetch="select" not-null="false">
            <column name="cas_typ_id2" />
        </many-to-one>
        <many-to-one name="casArea" class="com.frsoft.base.entity.CusCity" fetch="select" not-null="false">
            <column name="cas_typ_id3" />
        </many-to-one>
        -->
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="cas_se_no" />
        </many-to-one>
        <set name="caseHelpset" cascade="all" inverse="true">
			<key column="ch_cas_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.CaseHp"/>
		</set>
		<set name="visRecSet" cascade="all" inverse="true">
			<key column="vr_cas_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.VisRecord"/>
		</set>
		<set name="casepaidSet" cascade="all" inverse="true">
			<key column="pa_cas_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.CasePaid"/>
		</set>
		<set name="comments" cascade="all" inverse="true" order-by="cot_id desc">
			<key column="cot_cas_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.Comment"/>
		</set>
		<set name="phoListSet" cascade="all" inverse="true">
			<key column="phl_cas_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.PhoneList"/>
		</set>
		<set name="addressSet" cascade="all" inverse="true">
			<key column="adr_cas_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.Address"/>
		</set>
		<set name="hurrRecSet" cascade="all" inverse="true" order-by="hur_op_time">
			<key column="hur_cas_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.HurrRec"/>
		</set>
		<set name="phoRedSet" cascade="all" inverse="true" order-by="pr_time">
			<key column="pr_cas_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.PhoRed"/>
		</set>
		<set name="attachments" inverse="true"  cascade="all" where="att_type='bankCase'" order-by="att_id">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
    	</set>
        <many-to-one name="caseBat" class="com.frsoft.ccds.entity.CaseBat" fetch="select">
            <column name="cas_cbat_id" />
        </many-to-one>
        
        <joined-subclass name="com.frsoft.ccds.entity.CarIsrClaimCase" table="car_isr_claim_case">
    		<key column="cip_cas_id"></key>
        	<property name="cipClaimDate" column="cip_claim_date" type="java.util.Date"  length="23"></property>
        	<property name="cipAccDate" column="cip_acc_date" type="java.util.Date"  length="23"></property>
    		<property name="cipAccPlace" column="cip_acc_place" type="java.lang.String" length="200" ></property>
    		<property name="cipAccCause" column="cip_acc_cause" type="java.lang.String" length="500" ></property>
    		<property name="cipDept" column="cip_dept" type="java.lang.String" length="200" ></property>
    		<property name="cipDest" column="cip_dest" type="java.lang.String" length="200" ></property>
    		<property name="cipGoods" column="cip_goods" type="java.lang.String" length="200" ></property>
    		<property name="cipDriver1" column="cip_driver1" type="java.lang.String" length="50" ></property>
    		<property name="cipOwner" column="cip_owner" type="java.lang.String" length="50" ></property>
    		<property name="cipAffiliated" column="cip_affiliated" type="java.lang.String" length="50" ></property>
    		<property name="cipLicence1" column="cip_licence1" type="java.lang.String" length="50" ></property>
    		<property name="cipPhone1" column="cip_phone1" type="java.lang.String" length="50" ></property>
    		<property name="cipPolicyNo1" column="cip_policy_no1" type="java.lang.String" length="50" ></property>
    		<property name="cipIsrCompany1" column="cip_isr_company1" type="java.lang.String" length="200" ></property>
    		<property name="cipIsrType1" column="cip_isr_type1" type="java.lang.String" length="100" ></property>
    		<property name="cipStatus1" column="cip_status1" type="java.lang.String" length="200" ></property>
    		<property name="cipRspblt1" column="cip_rspblt1" type="java.lang.String" length="500" ></property>
    		<property name="cipDriver2" column="cip_driver2" type="java.lang.String" length="50" ></property>
    		<property name="cipLicence2" column="cip_licence2" type="java.lang.String" length="50" ></property>
    		<property name="cipPhone2" column="cip_phone2" type="java.lang.String" length="50" ></property>
    		<property name="cipStatus2" column="cip_status2" type="java.lang.String" length="200" ></property>
    		<property name="cipIsrCompany2" column="cip_isr_company2" type="java.lang.String" length="200" ></property>
			<property name="cipPolicyNo2" column="cip_policy_no2" type="java.lang.String" length="50" ></property>
    		<property name="cipIsrType2" column="cip_isr_type2" type="java.lang.String" length="100" ></property>
    		<property name="cipRspblt2" column="cip_rspblt2" type="java.lang.String" length="500" ></property>
    		<property name="cipClaimStatus" column="cip_claim_status" type="java.lang.String" length="200" ></property>
    	</joined-subclass>
    	
    	<joined-subclass name="com.frsoft.ccds.entity.InsuranceCase" table="insurance_case">
    		<key column="isc_cas_id"></key>
    		<property name="iscNo" column="isc_no" type="java.lang.String" length="50" ></property>
    		<property name="iscDep" column="isc_dep" type="java.lang.String" length="200" ></property>
    		<property name="iscLoanNo" column="isc_loan_no" type="java.lang.String" length="50" ></property>
    		<property name="iscBaseAmt" column="isc_base_amt" type="java.lang.Double" precision="18" ></property>
    		<property name="iscTotalAmt" column="isc_total_amt" type="java.lang.Double" precision="18" ></property>
    		<property name="iscArea" column="isc_area" type="java.lang.String" length="100" ></property>
    		<property name="iscClaimDays" column="isc_claim_days" type="java.lang.String" length="25" ></property>
    		<property name="iscRecDays" column="isc_rec_days" type="java.lang.String" length="25" ></property>
    		<property name="iscPricipal" column="isc_pricipal" type="java.lang.Double" precision="18" ></property>
    		<property name="iscPremium" column="isc_premium" type="java.lang.Double" precision="18" ></property>
    		<property name="iscInterest" column="isc_interest" type="java.lang.Double" precision="18" ></property>
    		<property name="iscBackAmt" column="isc_back_amt" type="java.lang.Double" precision="18" ></property>
			<property name="iscSbsStatus" column="isc_sbs_status" type="java.lang.String" length="100" ></property>
			<property name="iscClaimAmt" column="isc_claim_amt" type="java.lang.Double" precision="18" ></property>
			<property name="iscPhoneStatus" column="isc_phone_status" type="java.lang.String" length="100" ></property>
			<property name="iscStore" column="isc_store" type="java.lang.String" length="100" ></property>
			<property name="iscLiveInf" column="isc_live_inf" type="java.lang.String" length="500" ></property>
			<property name="iscBrandNo" column="isc_brand_no" type="java.lang.String" length="50" ></property>
			<property name="iscBrandName" column="isc_brand_name" type="java.lang.String" length="50" ></property>
			<property name="iscCarModel" column="isc_car_model" type="java.lang.String" length="50" ></property>
			<property name="iscCarLicence" column="isc_car_licence" type="java.lang.String" length="50" ></property>
			<property name="iscSource" column="isc_source" type="java.lang.String" length="100" ></property>
			<property name="iscPremiumTopay" column="isc_premium_topay" type="java.lang.Double" precision="18" ></property>
    	</joined-subclass>
    	
    	<joined-subclass name="com.frsoft.ccds.entity.PaBankCase" table="pa_bank_case">
    		<key column="cas_id"></key>
    		<!-- <property name="casBackP" column="cas_back_p" type="java.lang.Double" precision="15" scale="0" ></property>
    		<property name="casPos" column="cas_pos" type="java.lang.String" length="200" ></property>
    		<property name="casLoanDate" column="cas_loan_date" type="java.lang.String" length="200" ></property>
    		<property name="casOverdueDate" column="cas_overdue_date" type="java.lang.String" length="200" ></property>
    		<property name="casPaidCount" column="cas_paid_count" type="java.lang.String" length="100" ></property>
    		<property name="casSoNo" column="cas_so_no" type="java.lang.String" length="100" ></property> -->
    		<property name="casPolicyMan" column="cas_policy_man" type="java.lang.String" length="50" ></property>
    		<property name="casDayPaid" column="cas_day_paid" type="java.lang.String" length="100" ></property>
    		<property name="casClaDate" column="cas_cla_date" type="java.lang.String" length="100" ></property>
    		<property name="casClaM" column="cas_cla_m" type="java.lang.String" length="100" ></property>
    		<property name="casEffDate" column="cas_eff_date" type="java.lang.String" length="100" ></property>
    		<property name="casPremiums" column="cas_premiums" type="java.lang.String" length="100" ></property>
    		<property name="casFailCost" column="cas_fail_cost" type="java.lang.String" length="100" ></property>
    		<property name="casIsWd" column="cas_is_wd" type="java.lang.String" length="100" ></property>
    		<property name="casHouseType1" column="cas_house_type1" type="java.lang.String" length="100" ></property>
    		<property name="casHouseOwn1" column="cas_house_own1" type="java.lang.String" length="100" ></property>
    		<property name="casHfax1" column="cas_hfax1" type="java.lang.String" length="100" ></property>
    		<property name="casAddr2" column="cas_addr2" type="java.lang.String" length="500" ></property>
    		<property name="casHouseType2" column="cas_house_type2" type="java.lang.String" length="100" ></property>
    		<property name="casHouseOwn2" column="cas_house_own2" type="java.lang.String" length="100" ></property>
    		<property name="casPho2" column="cas_pho2" type="java.lang.String" length="100" ></property>
    		<property name="casHfax2" column="cas_hfax2" type="java.lang.String" length="100" ></property>
    		<property name="casCom2" column="cas_com2" type="java.lang.String" length="200" ></property>
    		<property name="casCom2Addr" column="cas_com2_addr" type="java.lang.String" length="500" ></property>
    		<property name="casWpho2" column="cas_wpho2" type="java.lang.String" length="100" ></property>
    		<property name="casFax1" column="cas_fax1" type="java.lang.String" length="100" ></property>
    		<property name="casFax2" column="cas_fax2" type="java.lang.String" length="100" ></property>
    		<property name="casComType" column="cas_com_type" type="java.lang.String" length="50" ></property>
    		<property name="casComDate" column="cas_com_date" type="java.lang.String" length="100" ></property>
    		<property name="casTaxNo" column="cas_tax_no" type="java.lang.String" length="100" ></property>
    		<property name="casComNo" column="cas_com_no" type="java.lang.String" length="100" ></property>
    		<property name="casConMob4b" column="cas_con_mob4b" type="java.lang.String" length="100" ></property>
    		<!-- <property name="casConWpho4" column="cas_con_wpho4" type="java.lang.String" length="100" ></property> -->
			<property name="casConMob1b" column="cas_con_mob1b" type="java.lang.String" length="100" ></property>
			<property name="casConMob2b" column="cas_con_mob2b" type="java.lang.String" length="100" ></property>
			<property name="casConMob3b" column="cas_con_mob3b" type="java.lang.String" length="100" ></property>
    	</joined-subclass>
    	
    	<joined-subclass name="com.frsoft.ccds.shjs.entity.JsBankCase" table="js_bank_case">
    		<key column="cas_id"></key>
    		<property name="jscOverdueP" type="java.lang.String">
	            <column name="jsc_overdue_p" length="200" />
	        </property>
	        <property name="jscUnOverdueP" type="java.lang.String">
	            <column name="jsc_un_overdue_p" length="200" />
	        </property>
	        <property name="jscMan" type="java.lang.String">
	            <column name="jsc_man" length="100" />
	        </property>
	        <property name="jscCd" type="java.lang.String">
	            <column name="jsc_cd" length="200" />
	        </property>
	        <property name="jscIsMarried" type="java.lang.String">
	            <column name="jsc_is_married" length="50" />
	        </property>
	        <property name="jscOthPho" type="java.lang.String">
	            <column name="jsc_oth_pho" length="200" />
	        </property>
	        <property name="jscIde" type="java.lang.String">
	            <column name="jsc_ide" length="100" />
	        </property>
	        <property name="jscHomeProp" type="java.lang.String">
	            <column name="jsc_home_prop" length="100" />
	        </property>
	        <property name="jscLiveWith" type="java.lang.String">
	            <column name="jsc_live_with" length="100" />
	        </property>
	        <property name="jscHaddrAble" type="java.lang.String">
	            <column name="jsc_haddr_able" length="200" />
	        </property>
	        <property name="jscWaddrAble" type="java.lang.String">
	            <column name="jsc_waddr_able" length="200" />
	        </property>
	        <property name="jscComProp" type="java.lang.String">
	            <column name="jsc_com_prop" length="100" />
	        </property>
	        <property name="jscAge1" type="java.lang.String">
	            <column name="jsc_age1" length="50" />
	        </property>
	        <property name="jscPos1" type="java.lang.String">
	            <column name="jsc_pos1" length="200" />
	        </property>
	        <property name="jscAge2" type="java.lang.String">
	            <column name="jsc_age2" length="50" />
	        </property>
	        <property name="jscPos2" type="java.lang.String">
	            <column name="jsc_pos2" length="200" />
	        </property>
	        <property name="jscAge3" type="java.lang.String">
	            <column name="jsc_age3" length="50" />
	        </property>
	        <property name="jscPos3" type="java.lang.String">
	            <column name="jsc_pos3" length="200" />
	        </property>
	        <property name="jscCost" type="java.lang.String">
	            <column name="jsc_cost" length="200" />
	        </property>
	        <property name="jscPosu" type="java.lang.String">
	            <column name="jsc_posu" length="200" />
	        </property>
    	</joined-subclass>
    	
    	<joined-subclass name="com.frsoft.ccds.entity.CarLoan" table="car_loan">
    		<key column="cal_cas_id"></key>
    		 <property name="calPrice" type="java.lang.String">
	            <column name="cal_price" length="100" />
	        </property>
	        <property name="calLice" type="java.lang.String">
	            <column name="cal_lice" length="100" />
	        </property>
	        <property name="calMake" type="java.lang.String">
	            <column name="cal_make" length="200" />
	        </property>
	        <property name="calVin" type="java.lang.String">
	            <column name="cal_vin" length="100" />
	        </property>
	        <property name="calEngineNo" type="java.lang.String">
	            <column name="cal_engine_no" length="100" />
	        </property>
    	</joined-subclass>
    </class>
</hibernate-mapping>
