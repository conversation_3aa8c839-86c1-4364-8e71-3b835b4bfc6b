<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>  
    <title>添加评语</title>
    <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/common.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	 <script type="text/javascript">
		function check(){
			var errStr = "";
			if(isEmpty("comment")){
				errStr+="- 未填写评语！\n";
			}
			else if(checkLength("comment",500)){
				errStr+="- 评语不能超过500个字！\n";
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				waitSubmit("save","保存中...");
				waitSubmit("doCancel");
				return $("commentForm").submit();
			}
		}
		/*
		function loadCaseState(){
			var casState = parent.document.getElementById("caseState").value;
			if(casState == '1' || casState == '2' || casState == '3'){
				$("red").disabled = true;
				$("blue").disabled = true;
				$("normal").disabled = true;
				$("stateTr").style.color='#999';
			}
		}
		*/
		window.onload = function(){
			$("none").checked = true;
			//loadCaseState();
		}
	 </script>
</head>
  <body>
  <div class="inputDiv">
  	<form action="caseAction.do" method="post" id="commentForm">
  		<input type="hidden" name="op" value="saveComment">
        <input type="hidden" id="casId" name="casId"  value="${casId}" />
		<table class="dashTab inputForm" cellpadding="0" cellspacing="0">
			<tbody>
                <tr>
                	<th class="required">评语内容：<span class='red'>*</span></th>
                    <td colspan="3">
                    <textarea class="inputSize2L" rows="10" id="comment" name="comment" onBlur="autoShort(this,500)"></textarea>
                    </td>
                </tr>
                <tr class="noBorderBot" id="stateTr">
                	<th>案件状态：</th>
                   	<td colspan="3" class="longTd">
                    	<input type="radio" id="normal" name="color" value="0" /><label for="normal">正常</label>
                    	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    <input type="radio" id="red" name="color" value="1" /><label for="red">标红</label>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    <input type="radio" id="blue" name="color" value="2" /><label for="blue">标蓝</label>
	                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    <input type="radio" id="none" name="color" value="-1"/><label for="none">不更改</label>
                    </td>
                </tr>    
                <tr class="submitTr">    
                    <td colspan="4">
	                    <input type="button" class="butSize1" id="save" value="保存" onClick="check()">
	                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"/>
                    </td>
                </tr>	
            </tbody>					
	  </table>
	</form>
  </div>
  </body>
</html>
