<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.CaseBat" table="case_bat" schema="dbo" >
        <id name="cbatId" type="java.lang.Long">
            <column name="cbat_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="caseTypeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cbat_type_id" />
        </many-to-one>
        <many-to-one name="bankTypeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cbat_typ_bid" />
        </many-to-one>
        <many-to-one name="cbatArea" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="cbat_area_id" />
        </many-to-one>
        <property name="cbatCode" type="java.lang.String">
            <column name="cbat_code" length="50" />
        </property>
        <property name="cbatDate" type="java.util.Date">
            <column name="cbat_date" length="23" />
        </property>
        <property name="cbatBackdateP" type="java.util.Date">
            <column name="cbat_backdate_p" length="23" />
        </property>
        <property name="cbatBackdate" type="java.util.Date">
            <column name="cbat_backdate" length="23" />
        </property>
        <property name="cbatInsUser" type="java.lang.String">
            <column name="cbat_ins_user" length="25" />
        </property>
        <property name="cbatInsDate" type="java.util.Date">
            <column name="cbat_ins_date" length="23" />
        </property>
        <property name="cbatState" type="java.lang.Integer">
            <column name="cbat_state" />
        </property>
        <property name="cbatNum" type="java.lang.Integer">
            <column name="cbat_num" />
        </property>
        <property name="cbatMon" type="java.lang.Double">
            <column name="cbat_mon" precision="18" />
        </property>
        <property name="cbatTarget" type="java.lang.Double">
            <column name="cbat_target" precision="15" scale="0" />
        </property>
        <property name="cbatLog" type="java.lang.String">
            <column name="cbat_log" length="**********" />
        </property>
        <property name="cbatXls" type="java.lang.String">
            <column name="cbat_xls" />
        </property>
        <property name="cbatUpDate" type="java.util.Date">
            <column name="cbat_up_date" length="23" />
        </property>
        <property name="cbatRemark" type="java.lang.String">
            <column name="cbat_remark" length="**********" />
        </property>
        <property name="cbatTips" type="java.lang.String">
            <column name="cbat_tips" length="200" />
        </property>
        <set name="bankcaSet" cascade="all" inverse="true" order-by="cas_id">
            <key>
                <column name="cas_cbat_id" />
            </key>
            <one-to-many class="com.frsoft.ccds.entity.BankCase" />
        </set>
    </class>
</hibernate-mapping>
