version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:5.7
    container_name: ccds-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: ccds
      MYSQL_USER: ccds
      MYSQL_PASSWORD: ccds123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - ccds-network

  # 催收系统应用
  ccds-app:
    build: .
    container_name: ccds-app
    restart: always
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: *************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: ccds
      SPRING_DATASOURCE_PASSWORD: ccds123
    volumes:
      - app_logs:/app/logs
      - app_upload:/opt/ccds/upload
    networks:
      - ccds-network

volumes:
  mysql_data:
  app_logs:
  app_upload:

networks:
  ccds-network:
    driver: bridge
