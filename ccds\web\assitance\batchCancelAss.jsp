<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>批量撤销协助</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
    	body{
			background:#fff;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
		function loadDelObj(){
			var innerObj="所选案件协催";
			$("delObj").innerHTML=innerObj;
		}
		
    	function delConfirm(){
			waitSubmit("delete","撤销中...");
			waitSubmit("doCancel");
			var subForm=$("sub");//获得提交的form对象
			var op=$("op");
			$("ids").value=getBacthIds("-")[0];//给隐藏域赋值
			subForm.action="assitanceAction.do";
			op.value="batchCancelAss";
			subForm.submit();
		}

	window.onload=function(){
		loadDelObj();
	}
  </script>
  </head>
  
  <body> 
  	<div id="delConfirm"> 
        <br/>
        <form id="sub" action="" method="post" style="margin:0px">
        <input type="hidden" id="op" name="op" value=""/>
        <input type="hidden" id="ids" name="ids"/>
        &nbsp;&nbsp;&nbsp;确定要撤销<span id="delObj"></span>吗？<br/><br/>
        <button id="delete" class ="butSize1" onClick="delConfirm()">确定</button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <button id="doCancel" class ="butSize1" onClick="cancel()">取消</button>
        </form>		 
        
    </div> 
	</body>
</html>
