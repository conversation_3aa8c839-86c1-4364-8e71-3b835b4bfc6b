<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
     <base href="<%=basePath%>"></base>  
     <title>案件协催导出</title>
     <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	 <meta http-equiv="cache-control" content="no-cache">
	 <meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/common.js"></script>
     <script type="text/javascript" src="js/config.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	 <script type="text/javascript">
		function outCheck(){
			var args = parent.getFormArgs();
			var casNameTxt = getCasNameTxt("${CUS_VER_ID}");
			var bankTxt = getBankTxt("${CUS_VER_ID}");
			var outHeader = "ID,批次号,"+bankTxt+",个案序列号,"+casNameTxt+", 证件号,卡号,卡类,账号,币种,档案号,申请单号,委案金额,还款金额,本金,最后还款日,委案日期,催收员,信用额度,家庭地址,家庭号码,单位名称,单位地址,手机,单位号码,最新欠款,最新欠款导入时间,";
			switch(args.assType){
			case "1":
				outHeader+="寄信地址,地址类别,申请内容,申请时间,协催时间,协催人,协催内容";break;
			case "2":
				outHeader+="地址,申请内容,申请时间,协催时间,协催人,协催内容";break;
			case "13":
				outHeader+="发送号码,短信内容,申请时间,协催时间,协催人,协催内容";break;
			default:
				outHeader+="申请内容,申请时间,协催时间,协催人,协催内容";
			}
			$("colNames").value=outHeader;
			args.op="outCaseAss";
			createHideInput(args);
			//waitSubmit("out");
			//waitSubmit("doCancel");
            //return $("outForm").submit();
			
			toExportData($("outForm").action,$("outForm").serialize(true)); 
		}
		
		function createHideInput(formArgs){
			var inputObj = null;
			for(var i in formArgs){
				inputObj = document.createElement("input");
				inputObj.type = "hidden";
				inputObj.name = i;
				inputObj.value = formArgs[i];
				$("outForm").appendChild(inputObj);
			}
		}

　		function close(){
			setTimeout("closeDiv('')",3000);
		} 

	 </script>
</head>
  <body>
  <div class="inputDiv">
        <form action="assitanceAction.do" method="post" id="outForm">
       		<input type="hidden" id="colNames" name="colNames" />
            <div style="background:#FFFF99; padding:5px;" id="submitTd" class="exportSubmitTd">
                <input type="button" class="butSize1" id="out" value="导出" onClick="outCheck()">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"/>
            </div>
        </form>
  </div>
  
  </body>
</html>
