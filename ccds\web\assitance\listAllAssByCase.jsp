<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>协催记录列表</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/caseAss.js"></script>
	<script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript">
    	function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			datas = [obj.chTypStr,obj.chText,obj.chAppTime,obj.chAppUser,obj.chSurTime, obj.chSurUser,obj.chRes ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "assitanceAction.do";
			var pars = [];
			pars.op = "listAllAssByCase";
			pars.caseId="${caseId}";
			var loadFunc = "loadList";
			var cols=[
				{name:"协催类型",width:'8%'},
				{name:"申请内容",width:'12%'},
				{name:"申请时间",renderer:"time",width:'12%'},
				{name:"申请人",width:'8%'},
				{name:"协催时间",renderer:'time',width:'12%'},
				{name:"协催人",width:'8%'},
				{name:"协催内容",width:'40%'}
			];

			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("allCaseAssListTab","dataList");
    	gridEl.config.sortable=false;
		gridEl.config.isResize=false;
		createProgressBar();
   		window.onload=function(){
			if('${operational}'!='0'){
				$("listTop").show();
			}
			loadList();
		}
    </script>
</head> 
  
  <body>
  	<div class="divWithScroll2 innerIfm">
    	<table id="listTop" class="normal ifmTopTab" cellpadding="0" cellspacing="0">
  			<tr>
            	<th>&nbsp;</th>
                <td style="width:120px; vertical-align:top;">
                 <a id="out" href="javascript:void(0)" onClick="parent.casePopDiv(312,'${caseId}');return false;" class="newBlueButton">添加协催记录</a>
  				</td>
  			</tr>
  		</table>
  		<div id="dataList" class="dataList"></div>
    </div>
  </body>
</html>