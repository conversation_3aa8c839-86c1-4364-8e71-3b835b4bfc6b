<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.SalPurOrd" table="sal_pur_ord" schema="dbo" >
        <id name="spoId" type="java.lang.Long">
            <column name="spo_id" />
            <generator class="identity" />
        </id>
        <property name="spoTil" type="java.lang.String">
            <column name="spo_til" length="300" />
        </property>
        <property name="spoCode" type="java.lang.String">
            <column name="spo_code" length="300" />
        </property>
        <property name="spoConDate" type="java.util.Date">
            <column name="spo_con_date" length="23" />
        </property>
        <many-to-one name="salSupplier" class="com.psit.struts.entity.SalSupplier" fetch="select" not-null="false">
        	<column name="spo_sup_id"/>
        </many-to-one>
        <many-to-one name="TypeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="spo_type_id" />
        </many-to-one>
        <many-to-one name="project" class="com.psit.struts.entity.Project" fetch="select" not-null="false">
            <column name="spo_proj_id" />
        </many-to-one>
        <property name="spoSumMon" type="java.lang.Double">
            <column name="spo_sum_mon" precision="18" />
        </property>
        <property name="spoPaidMon" type="java.lang.Double">
            <column name="spo_paid_mon" precision="18" />
        </property>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" not-null="false">
            <column name="spo_user_code" length="50" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" not-null="false">
            <column name="spo_se_no"/>
        </many-to-one>
        <property name="spoContent" type="java.lang.String">
            <column name="spo_content" length="1073741823" />
        </property>
        <property name="spoIsend" type="java.lang.String">
            <column name="spo_isend" length="1" />
        </property>
        <property name="spoIsdel" type="java.lang.String">
            <column name="spo_isdel" length="1" />
        </property>
        <property name="spoRemark" type="java.lang.String">
            <column name="spo_remark" length="1073741823" />
        </property>
        <property name="spoInpUser" type="java.lang.String">
            <column name="spo_inp_user" length="50" />
        </property>
        <property name="spoCreDate" type="java.util.Date">
            <column name="spo_cre_date" length="23" />
        </property>
        <property name="spoAltDate" type="java.util.Date">
            <column name="spo_alt_date" length="23" />
        </property>
        <property name="spoAltUser" type="java.lang.String">
            <column name="spo_alt_user" length="50" />
        </property>
        <property name="spoAppDate" type="java.util.Date">
            <column name="spo_app_date" length="23" />
        </property>
        <property name="spoAppMan" type="java.lang.String">
            <column name="spo_app_man" length="50" />
        </property>
        <property name="spoAppDesc" type="java.lang.String">
            <column name="spo_app_desc" length="1073741823" />
        </property>
        <property name="spoAppIsok" type="java.lang.String">
            <column name="spo_app_isok" length="1" />
        </property>
        <set name="rspoPros" inverse="true" order-by="rpp_id" cascade="all">
        	<key>
        		<column name="rpp_spo_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.RSpoPro"/>
        </set>
        <set name="spoPaidPlans" inverse="true" order-by="spp_cre_date desc" cascade="all">
        	<key>
        		<column name="spp_spo_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.SpoPaidPlan"/>
        </set>
        <set name="spoPaidPasts" inverse="true" order-by="spa_cre_date desc" cascade="all">
        	<key>
        		<column name="spa_spo_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.SpoPaidPast"/>
        </set>
        <set name="salInvoices" inverse="true" order-by="sin_cre_date desc" cascade="all">
        	<key>
        		<column name="sin_spo_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.SalInvoice"/>
        </set>
        <set name="attachments" inverse="true"  order-by="att_date desc"  cascade="all" where="att_type='spo'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
        <set name="wmsWarIns" inverse="true"  order-by="wwi_in_date desc"  cascade="all">
            <key>
                <column name="wwi_spo_code" />
            </key>
            <one-to-many class="com.psit.struts.entity.WmsWarIn" />
        </set>
    </class>
</hibernate-mapping>
