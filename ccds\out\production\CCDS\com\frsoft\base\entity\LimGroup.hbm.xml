<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
<class name="com.frsoft.base.entity.LimGroup" table="lim_group" schema="dbo">
	<id name="grpId" type="java.lang.Long">
		<column name="grp_id" />
		<generator class="identity"/> 
	</id>
	<property name="grpName" type="java.lang.String">
		<column name="grp_name" length="50" />
	</property>
	<property name="grpDesc" type="java.lang.String">
		<column name="grp_desc" length="200" />
	</property>
	<property name="grpCreMan" type="java.lang.String">
		<column name="grp_cre_man" length="50"/>
	</property>
	<property name="grpUpdMan" type="java.lang.String">
		<column name="grp_upd_man" length="50"/>
	</property>
	<property name="grpCreTime" type="java.util.Date">
		<column name="grp_cre_time" length="23"/>
	</property>
	<property name="grpUpdTime" type="java.util.Date">
		<column name="grp_upd_time" length="23"/>
	</property>
</class>

</hibernate-mapping>
