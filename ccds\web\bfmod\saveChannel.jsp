<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>更新通道</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/> 
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript">
		function check(){
			var errStr = "";
			if(isEmpty("tel")){
				errStr+="- 未填写电话！\n";
			}
			/* 
			if(isEmpty("ip")){
				errStr+="- 未填写IP！\n";
			}
			if(isEmpty("userCode")){
				errStr+="- 未选择账号！\n";
			} */
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				$("serverId").value = parent.document.getElementById("serverId").value;
				waitSubmit("doSave");
				waitSubmit("doCancel");
				return $("saveForm").submit();
			}
		}
		
  	</script>
  </head>
  <body>
  	<div class="inputDiv">
        <form id="saveForm" action="ctiServerAction.do" method="post">
            <input type="hidden" name="op" value="saveChannel" />
            <input type="hidden" id="serverId" name="serverId" />
            <input type="hidden" name="channelNo" value="${channelNo}" />
            <table class="dashTab inputForm single" cellpadding="0" cellspacing="0">
                <tbody>
                    <tr>
                    	<th>通道号：</th>
                        <td>${channelNo}</td>
                    </tr>
                    <tr>
                        <th class="required">电话：<span class='red'>*</span></th>
                        <td><input type="text" class="inputSize2 inputBoxAlign" name="tel" value='<c:out value="${tel}"/>' onBlur="autoShort(this,50)">
                       	</td>
                    </tr>  
                    <tr class="noBorderBot">
                    	<th>IP：</th>
                        <td><input type="text" class="inputSize2 inputBoxAlign" name="ip" value='<c:out value="${ip}"/>' onBlur="autoShort(this,50)">
                       	</td>
                    	<%-- <th  class="required">选择账号：<span class='red'>*</span></th>
	                    <td style="font-weight:normal">
	                        <input type="hidden" name="oldUserCode" value='<c:out value="${uCode}"/>'/>
	                        <input type="hidden" name="uCode" id="userCode" value='<c:out value="${uCode}"/>'/>
                            <input id="seName"  value='<c:out value="${uName}"/>' class="inputSize2 lockBack" style="width:120px" type="text" title="此处文本无法编辑，双击可清空文本"  ondblClick="clearInput(this,'userCode')" readonly/>&nbsp;
                            <button id="selUp" class="butSize2" onClick="parent.addDivBrow(13,'updChannel')">选择</button>
				     	</td> --%>
                    </tr>                                                                      
                    <tr class="submitTr">
                        <td colspan="4">
                        <input id="cbatSave" class="butSize1" type="button" value="保存" onClick="check()" />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                        </td>
                    </tr>                   
                </tbody>
            </table>
        </form>
    </div>
  </body>
</html>
