<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.PhoneList" table="phone_list" schema="dbo" >
        <id name="phlId" type="java.lang.Long">
            <column name="phl_id" />
            <generator class="identity" />
        </id>
        <property name="phlState" type="java.lang.Integer">
            <column name="phl_state" />
        </property>
        <property name="phlName" type="java.lang.String">
            <column name="phl_name" length="50" />
        </property>
        <property name="phlNum" type="java.lang.String">
            <column name="phl_num" length="100" />
        </property>
        <many-to-one name="bankCase" class="com.frsoft.ccds.entity.BankCase" fetch="select" not-null="false">
            <column name="phl_cas_id" />
        </many-to-one>
        <property name="phlCat" type="java.lang.String">
            <column name="phl_cat" length="50" />
        </property>
        <property name="phlCount" type="java.lang.Integer">
            <column name="phl_count" />
        </property>
        <property name="phlRemark" type="java.lang.String">
            <column name="phl_remark" length="**********" />
        </property>
        <property name="phlIsdel" type="java.lang.String">
            <column name="phl_isdel" length="1" />
        </property>
        <property name="phlUpdTime" type="java.util.Date">
            <column name="phl_upd_time" length="23" />
        </property>
        <property name="phlIsnew" type="java.lang.Integer">
            <column name="phl_isnew" />
        </property>
        <property name="phlRel" type="java.lang.String">
            <column name="phl_rel" length="50" />
        </property>
    </class>
</hibernate-mapping>
