<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>录音管理</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="stylesheet" type="text/css" href="css/style.css">
    <link rel="stylesheet" type="text/css" href="css/guide.css">
    <script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
    	function menuForward(index){
			switch(index){
				case 0:
					self.location.href="../soundRecordAction.do?op=toListLocalSoundRec";			
					break;
				case 1:
					self.location.href="../soundRecordAction.do?op=toStatRecByChannel";
					break;
				case 2:
					self.location.href="../ctiServerAction.do?op=toListCtis";			
					break;
				case 3:
					self.location.href="../ctiServerAction.do?op=toListChannel";			
					break;
					
			}
		}
	
		createProgressBar();
		window.onload=function(){
			loadGuideStyle();
			closeProgressBar();
		}
  </script>
  </head>
  
  <body> 
  <div id="deskBox">
  	<div id="title">录音管理 > 导航</div>
    <div id="topWords">
    	您要使用哪个功能？
    </div>
    <div class="guideDiv">
        <span class="guideLayer" style="background:url(images/icon/bfmod/rec.png) no-repeat;" onClick="menuForward(0)">
            <div class="guideTitle">录音查询</div>
            <ul>
                <li></li>
            </ul>
        </span>
        <span class="guideLayer" style="background:url(images/icon/bfmod/chart.png) no-repeat;" onClick="menuForward(1)">
            <div class="guideTitle">通话统计</div>
            <ul>
                <li></li>
            </ul>
        </span>
    </div>
    <div class="guideDiv">
        <span class="guideLayer" style="background:url(images/icon/bfmod/server.png) no-repeat;" onClick="menuForward(2)">
            <div class="guideTitle">服务器设置</div>
            <ul>
                <li></li>
            </ul>
        </span>
        <span class="guideLayer" style="background:url(images/icon/bfmod/telephone.gif) no-repeat;" onClick="menuForward(3)">
            <div class="guideTitle">通道设置</div>
            <ul>
                <li></li>
            </ul>
        </span>
    </div>
 </div>
</body>
</html>
