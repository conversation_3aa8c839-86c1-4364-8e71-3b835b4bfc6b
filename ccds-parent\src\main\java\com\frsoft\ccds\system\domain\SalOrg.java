package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 销售组织实体类
 * 对应数据库表: sal_org
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class SalOrg {
    
    /** 组织编码 */
    private String soCode;
    
    /** 组织名称 */
    private String soName;
    
    /** 管辖区域 */
    private String soConArea;
    
    /** 位置 */
    private String soLoc;
    
    /** 用户编码 */
    private String soUserCode;
    
    /** 员工数量 */
    private String soEmpNum;
    
    /** 职责 */
    private String soResp;
    
    /** 组织编码 */
    private String soOrgCode;
    
    /** 备注 */
    private String soRemark;
    
    /** 是否启用 */
    private String soIsenabled;
    
    /** 上级编码 */
    private String soUpCode;
    
    /** 成本中心 */
    private String soCostCenter;
    
    /** 组织性质 */
    private String soOrgNature;
}
