<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
  	<base href="<%=basePath%>"></base>
    <title>案件详情</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <link rel="stylesheet" type="text/css" href="case/caseDesc.css"/>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/md5.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/cti.js"></script>
    <script type="text/javascript" src="js/mathUtils.js"></script>
    <c:choose>
	    <c:when test="${HAS_BF_MOD}">
	    	<script type="text/javascript" src="js/bfcti-connect.js"></script>
	    </c:when>
	    <c:otherwise>
	    	<script type="text/javascript" src="js/cti-connect${CUS_VER_ID}.js"></script>
	    </c:otherwise>
    </c:choose>
    <script type="text/javascript" src="js/case.js"></script>
    <script type="text/javascript" src="case/caseDesc.js"></script>
    <script type="text/javascript" src="case/descCustomView.js"></script>
    <script type="text/javascript" src="case/descTabData0.js"></script>
    <script type="text/javascript" src="case/descTabData${CUS_VER_ID}.js"></script>
    <c:if test="${!empty fieldType}">
    <script type="text/javascript" src="case/descTabData_${fieldType}.js"></script>
    </c:if>
	<script type="text/javascript">
		/**
		 * 初始化页面
		 */
		function initPage(){
			initDescTable();//$("jsonData").innerHTML.evalJSON());
			initLinkDataMenu("${caseId}");
			if("${noticeType}"!=""){
				loadDefaultTab("${noticeType}");
			}
			document.onkeydown = hotkey; //快捷键
			loadPhoneType("phoneType");
			$("descLayer").show();
		}
		
		
		function batNewHur(){
			window.open('caseAction.do?op=toBatNewPR&caseId=${caseId}',"","width=590,directories=no,location=no,menubar=no,resizable=yes,scrollbars=yes,status=no,toolbar=no");
		}
		function setIsSelfAnsweredN(){
			$("isSelfAnsweredN").checked=true;
		}
		function cleanIsSelfAnswered(){
			$("isSelfAnsweredY").checked=false;
			$("isSelfAnsweredN").checked=false;
		} 
		/*
		function showAllPr(){
			$("isListAllPr").value="1";
			loadList();
			$("showAllPrBtn").hide();
		}
		*/
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			if(obj.bankCase.casId!="${caseId}"){
				className = 'brown';
			}
			var phoneNum = obj.prCat==0?"<a href=\"javascript:void(0)\" onClick=\"parent.showPRInputDiv('"+encodeString(obj.prContact)+"','"+encodeString(obj.prName)+"','"+obj.prConType+"','"+encodeString(obj.prRel)+"');return false;\">"+encodeString(obj.prContact)+"</a>&nbsp;":encodeString(obj.prContact);
			datas = [obj.prTime, obj.prName, obj.prRel, phoneNum, obj.prContent];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars = [];
			pars.op = "showPhoRed";
			pars.casId="${caseId}";
			pars.isShowOth=$("isShowOthOfPr").value;//是否加载同批次共债催记
			//pars.isShowAll=$("isListAllPr").value;
			var loadFunc = "loadList";
			var cols=[
				{name:"时间",width:'20%'},
				{name:"对象姓名",width:'10%'},
				{name:"关系",width:'10%'},
				{name:"电话/地址",align:"left",width:'20%'},
				{name:"催收记录",align:'left',width:'40%'}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("casePRListTab","prDataList");
    	gridEl.config.sortable=false;
		gridEl.config.isResize=false;
		gridEl.config.isShort=false;
		createProgressBar();
		window.onload=function(){
			initVer("${CUS_VER_ID}");
			initPage();
      	}
		<c:if test="${HAS_BF_MOD}">
		window.onbeforeunload = function (e) {
			if($("isTalk").value === "1") {
				var message = "话机正在录音，如果关闭页面将无法停止录音";
		        e = e || window.event;
		        if (e) {
		            e.returnValue = message;
		        }
		        return message;
		    }
		};
		</c:if>
  	</script>
  </head>
  
  <body>
  <div id="mainbox">
    	<div id="contentbox">
            <div id="descLayer" class="descInf" style="display:none">
                <div id="warnLayer" style="display:none">
                    <a href="javascript:void(0)" onClick="$('warnLayer').hide();return false;" style="float:right;top:0px;">[关闭]</a>
                    <div id="warnTil">警告：</div>
                    <div id="warnSpan"></div>
               </div>
               <div id="caseDesc"></div>
               <div id="linkDataLayer">
               	<div id="linkDataMenu" class="xpTab"></div>
				<div id="ifrContent" class="tabContent" style="display:none">
                    <iframe id="ifrList" style="height:200px;" src="" scrolling="no" frameborder="0"></iframe>
                </div>
               </div>
               <div id="noteInputLayer" style="display:none">
               <div id="noteInput">
               		<div id="noteTop"><span style="float:left">自定义信息：</span><button id="saveNoteBth" onClick="saveNote('${caseId}')">保存</button></div>
                    <textarea id="noteValue"></textarea>
               </div></div>
               <div class="HackBox"></div>
               	<!-- 新增电催记录 -->
                <div id="recordForm" style="display:none">
                    <div id="recordTab">
                    <form id="recForm" style="padding:0; margin:0">
                    <input type="hidden" id="opMethod" name="op"/>
                    <input type="hidden" name="casId" value="${caseId}"/>
                    <input type="hidden" id="phlId" name="phlId"/>
                    <input type="hidden" id="callId" name="callId"/>
                    <input type="hidden" id="tmpName" name="tmpName"/>
                    <input type="hidden" id="hurStateStr" name="hurStateStr"/>
                    <!--<input type="hidden" id="testType" name="testType"/>
                    <input type="hidden" id="phoneName" name="phoneName" />
                    <input type="hidden" id="phoneType" name="phoneType" />-->
                    <table class="dashTab" cellpadding="0" cellspacing="0" style="width:100%">
                        <tbody>
                        	<!-- 
                        	<tr>
                            	<td colspan="4" class="gray" style="font-size:14px; color:#bbb; padding-top:5px; padding-bottom:5px;">新增电催记录</td>
                            </tr>
                             -->
                        	<tr>
                            	<th class="required">电话号码：<span class="red">*</span></th>
                                <td>
                                <!--<span id='phnTxt' style="display:none"></span><span id="phnDiv" style="display:none">-->
								
								<input class="inputSize2 inputBoxAlign" type="text" id="phoneNum" name="phoneNum" onBlur="autoShort(this,50)"/><!--</span>-->&nbsp;
								<span id="callBtnLayer" style="display:none;"></span>
                        		</td>
                                <th id="prNameTh">姓名/类型：</th>
                                <td><input class="inputSize2 inputBoxAlign" style="width:72px" type="text" id="phoneName" name="phoneName" onBlur="autoShort(this,50)"/>&nbsp;<select id="phoneType" name="phoneType" class="inputSize2 inputBoxAlign" style="width:75px"></select></td>
                            </tr>
                            <tr>
                                <th id="prRelTh">关系：</th>
                                <td><input class="inputSize2 inputBoxAlign" style="width:72px" type="text" id="prRel" name="prRel" onBlur="autoShort(this,50)"/>&nbsp;<select id="relType" style="width:75px" class="inputSize2 inputBoxAlign" onChange="setValueFromSel(this,'prRel')" >
                                     <option value="">请选择</option>
                                     <c:if test="${!empty relType}">
                                     <c:forEach var="relType" items="${relType}">
                                     <option value="${relType.typName}">${relType.typName}</option>
                                     </c:forEach>
                                     </c:if>
                                 </select></td> 
                               <th id="negTypeTh">谈判方式：</th>
                               <td><input class="inputSize2 inputBoxAlign" style="width:72px" type="text" id="prNeg" name="prNeg" onBlur="autoShort(this,50)"/>&nbsp;<select id="negType" style="width:75px" class="inputSize2 inputBoxAlign" onChange="setValueFromSel(this,'prNeg')" >
                                     <option value="">请选择</option>
                                     <c:if test="${!empty negType}">
                                     <c:forEach var="negType" items="${negType}">
                                     <option value="${negType.typName}">${negType.typName}</option>
                                     </c:forEach>
                                     </c:if>
                                 </select>	           
                               </td>
                            </tr>
                            <tr>
                            	<th>催收模板：</th>
                                <td>
                                	<select id="tmpSel" class="inputSize2 inputBoxAlign" onchange="setTmpTxt()">
                                		<option value="">请选择</option>
                                		<c:if test="${!empty clTmps}">
                                     	<c:forEach var="clTmp" items="${clTmps}">
                                    	<option value="${clTmp.tmpHtml}">${clTmp.tmpName}</option>
                                     	</c:forEach>
                                     </c:if>
                                	</select>
                                </td>
                                <th id="contactCatTh">催收结果：</th>
                               	<td>
                                 <select id="contactCat" name="contactCat" class="inputSize2">
                                     <option value="">请选择</option>
                                     <c:if test="${!empty cpType}">
                                     <c:forEach var="cpType" items="${cpType}">
                                     <option value="${cpType.typId}">${cpType.typName}</option>
                                     </c:forEach>
                                     </c:if>
                                 </select>            
                               </td>
                            </tr>        
                            <tr>
                            	<th>PTP金额：</th>
                                <td><input class="inputSize2" type="text" id="ptpNum" name="ptpNum" onBlur="checkPrice(this)"/>
                                </td>
                            	<th>PTP时间：</th>
                                <td><input name="ptpTim" id="endTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand" readonly ondblClick="clearInput(this)" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                                </td>
                            </tr>
                            <tr>
                                <th class="required">通话记录：<span class="red">*</span></th>
                                <td colspan="3"><textarea class="inputSize2L" style="width:405px" rows="5" id="phoneRec" name="phoneRec" onBlur="autoShort(this,1000)"></textarea></td>
                               
                           </tr> 
                           <tr id="isAnsweredTR" style="display:none">
                               <th>是否接听：</th>
                               <td>
                              		<input type="radio" id="isAnsweredY" name="isAnswered" class="inputBoxAlign" value="1" onclick="cleanIsSelfAnswered()" /><label for="isAnsweredY" >是&nbsp;&nbsp;</label>&nbsp;&nbsp;&nbsp;
                              		<input type="radio" id="isAnsweredN" name="isAnswered" class="inputBoxAlign" value="0" onclick="setIsSelfAnsweredN()" /><label for="isAnsweredN" >否&nbsp;&nbsp;</label>     
                               </td>
                               <th>是否本人接听：</th>
                               <td>
                                 	<input type="radio" id="isSelfAnsweredY" name="isSelfAnswered" class="inputBoxAlign" value="1" /><label for="isSelfAnsweredY" >是&nbsp;&nbsp;</label>&nbsp;&nbsp;&nbsp;
                              		<input type="radio" id="isSelfAnsweredN" name="isSelfAnswered" class="inputBoxAlign" value="0" /><label for="isSelfAnsweredN" >否&nbsp;&nbsp;</label>     
                               </td>
                           </tr>
                           <tr id="clSTAndTypeTR" style="display:none">
                           		<th>案件状态：</th>
                               	<td>
                                 <select name="caseConState" class="inputSize2 inputBoxAlign" >
                                     <option value="">请选择</option>
                                     <option value="可联">可联</option>
                                     <option value="失联">失联</option>
                                     <option value="承诺还款">承诺还款</option>
                                 </select>	 
                               	</td>
                               	<th>催收类型：</th>
                               	<td>
                                 <select name="clType" class="inputSize2 inputBoxAlign" >
                                     <option value="电话催收">电话催收</option>
                                     <option value="短信催收">短信催收</option>
                                     <option value="QQ催收">QQ催收</option>
                                 	<option value="微信催收">微信催收</option>
                                 	<option value="信函催收">信函催收</option>
                                 	<option value="外访催收">外访催收</option></select>	 
                               	</td>
                           </tr>
                            <tr>
                               <th class="required">催收状态：<span class="red">*</span></th>
                               <td colspan=3>
                                 <select  id="hurState" name="hurState" class="inputSize2 inputBoxAlign" onChange="setNxtDateStyle(this,'${CUS_VER_ID}')" >
                                     <option value="">请选择</option>
                                     <c:if test="${!empty csType}">
                                     <c:forEach var="csType" items="${csType}">
                                     <option value="${csType.typId}">${csType.typName}</option>
                                     </c:forEach>
                                     </c:if>
                                 </select>	 
                                 <c:if test="${!empty csType}">
                                     <c:forEach var="csType" items="${csType}">
                                  <input type="hidden" id="csTypeDesc${csType.typId}" value="${csType.typDesc}" />     </c:forEach>
                                     </c:if>  
                                  &nbsp;&nbsp;
                               	<input type="checkbox" id="isUpdStateOfOth" name="isUpdStateOfOth" class="inputBoxAlign" value="1" /><label for="isUpdStateOfOth" >更新批次共债案件催收状态</label>     
                               </td>
                           </tr>
                           <tr>
                            	<th>减免金额：</th>
                                <td><input class="inputSize2" type="text" id="reduceM" name="reduceM" onBlur="checkPrice(this)"/>
                                </td>
                               <th>减免状态：</th>
                               <td colspan=3>
                                 <select  id="reduceState" name="reduceState" class="inputSize2 inputBoxAlign" >
                                     <option value="">请选择</option>
                                     <c:if test="${!empty reduceStates}">
                                     <c:forEach var="rState" items="${reduceStates}">
                                     <option value="${rState.typName}">${rState.typName}</option>
                                     </c:forEach>
                                     </c:if>
                                 </select>	 
                               </td>
                           </tr>
                           <tr class="noBorderBot">
                                <th id="nxtTipsDateTh"></th>
                               	<td id="nxtTipsDateTd">
                                	<input name="nxtTipsDate" id="nxtTipsDate" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand" readonly ondblClick="clearInput(this)" />
                                </td>
                                <th id="nxtClTypeTh" style="display:none">下次催收类型：</th>
                               	<td id="nxtClTypeTd" style="display:none">
                                 <select name="nxtClType" class="inputSize2 inputBoxAlign" >
                                     <option value="">请选择</option>
                                     <option value="电话催收">电话催收</option>
                                     <option value="短信催收">短信催收</option>
                                     <option value="QQ催收">QQ催收</option>
                                 	<option value="微信催收">微信催收</option>
                                 	<option value="信函催收">信函催收</option>
                                 	<option value="外访催收">外访催收</option>
                                 </select>	 
                               	</td>
                            </tr>
                            <tr class="submitTr">
                                <td colspan="6">
                                <input type="button" class="butSize1" id="save" value="保存" onClick="check()">
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="button" class="butSize1" id="doCancel"  value="清空" onClick="clearForm()"></td>
                            </tr>
                        </tbody>
                    </table>  
                    </form>
                    </div>
                    <div id="prListLayer">
                    	<!-- <input type="hidden" id="isListAllPr" value="0"/>&nbsp;&nbsp;<a id="showAllPrBtn" href="javascript:void(0)" onclick="showAllPr()">[点击显示全部]</a></div> -->
                    	<div id="prListTop" style="display:none">同批次共债催记</div>
                    	<div id="prDataList"></div>
                    </div>
                    
                    <div class="HackBox"></div>
               	</div>
                <c:if test="${HAS_BF_MOD}">
                	<div id="dialLayer">本机号码：<span id="localPhone">${CUR_USER.userCtiPhone}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;拨打号码：<input type="text" class="inputSize2 inputBoxAlign" id="phoneToDialOfBot" />&nbsp;&nbsp;<button  class="butSize1 inputBoxAlign" onclick="toDial($('phoneToDialOfBot').value)">拨号</button>&nbsp;&nbsp;&nbsp;&nbsp;<button  class="butSize1 inputBoxAlign" onclick="toHangUp()">停止录音</button><span id="dialInfo" class="inputBoxAlign"></span></div>
                	<div class="tipsLayer">
                		<ul><li>通话结束后请点击停止录音</li></ul>
                	</div>
                	<c:if test="${empty CUR_USER.userCtiPhone}">
                	<script type="text/javascript">getLocalPhoneByIP('localPhone');</script>
                	</c:if>
                </c:if>
       			<div id="savePrRs" class="grayBack blue" style="padding:10px; margin-top:5px;text-align:center; display:none"></div>
                <div id="descStamp" class="descStamp"></div>
            </div>
        </div>
	</div>
    <!--<input type="hidden" id="caseState" />-->
    <input type="hidden" id="casePtpC"/>
    <input type="hidden" id="caseId" value="${caseId}"/>
    <input type="hidden" id="ccId" value="${ccId}"/>
    <input type="hidden" id="cbatId" value="${cbatId}"/>
    <input type="hidden" id="idno" value="<c:out value="${idno}" />"/>
    <input type="hidden" id="vrCount" value="${vrCount}" />
    <input type="hidden" id="mailCount" value="${mailCount}" />
    <input type="hidden" id="editable" value="${editable}"/>
    <input type="hidden" id="operational" value="${operational}"/>
    <input type="hidden" id="view" value="${view}"/>
    <input type="hidden" id="fieldType" value="${fieldType}"/>
    <input type="hidden" id="verId" value="${CUS_VER_ID}" />
    <input type="hidden" id="today" value="${TODAY}" />
    <input type="hidden" id="regType" value="${REG_TYPE}" />
    <input type="hidden" id="regFunctions" value="${REG_FUNCTIONS}" />
    <input type="hidden" id="isHideNo" value="${SYS_PREF.sypClNoHide}"/>
	<input type="hidden" id="hasCTI" value="${HAS_CTI}" />
    <input type="hidden" id="ctiUser" value="${CUR_USER.userCtiLogin}" />
    <input type="hidden" id="ctiPwd" value="${CUR_USER.userCtiPwd}" />
    <input type="hidden" id="isShowOthOfPr" /><!-- 默认催记列表类别 -->
    <input type="hidden" id="isTalk" value="0" /><!-- BF 是否正在通话 -->
    <!-- 同批次共债合计 -->
    <input type="hidden" id="totalNumOfBat" value="0" />
	<input type="hidden" id="totalAmtOfBat" value="0" />
	<input type="hidden" id="totalPtpOfBat" value="0" />
	<input type="hidden" id="totalCpOfBat" value="0" />
	<input type="hidden" id="totalPaidOfBat" value="0" />
	<input type="hidden" id="totalNumOfBatNoBack" value="0" />
	<input type="hidden" id="totalAmtOfBatNoBack" value="0" />
	<input type="hidden" id="totalPtpOfBatNoBack" value="0" />
	<input type="hidden" id="totalCpOfBatNoBack" value="0" />
	<input type="hidden" id="totalPaidOfBatNoBack" value="0" />
  </body>
  
</html>
