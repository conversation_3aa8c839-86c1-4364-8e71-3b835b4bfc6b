<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt"  prefix="fmt"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>更新案件关联卡信息</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>  
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript">
		function check(){
			var errStr = "";
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				waitSubmit("doSave","保存中...");
				waitSubmit("doCancel");
				return $("saveForm").submit();
			}
		}
		
		function initForm(){
		}
		
		window.onload=function(){
			initForm();
		}
  	</script>
  </head>
  <body>
  	<div class="inputDiv">
        <form id="create" action="caseAction.do" method="post">
            <input type="hidden" name="op" value="uptCaseInfo" />
            <input type="hidden" id="isRepeat" value=""/>
            <input type="hidden" name="casId" value="${bankCase.casId}"/>
            <div id="errDiv" class="errorDiv redWarn" style="display:none">&nbsp;<img class="imgAlign" src="images/content/fail.gif" alt="警告"/>&nbsp;&nbsp;此案件个案序列号名称在系统中已存在</div>
            <table class="dashTab inputForm" cellpadding="0" cellspacing="0">
                <tbody>
                	<tr>
                        <th class="required">个案序列号：<span class='red'>*</span></th>
                        <td><input type="text" id="casCode" class="inputSize2" name="bankCase.casCode" value="<c:out value="${bankCase.casCode}"/>"  onblur="checkCasCd(this)"/></td>
                        <th class="required"><span id="casNameTxt"></span>：<span class='red'>*</span></th>
                        <td><input class="inputSize2" type="text" id="casName" name="bankCase.casName" value="<c:out value="${bankCase.casName}"/>"  onBlur="autoShort(this,20)" /></td>
                    </tr>
                    <tr>
                        <th>性别：</th>
                        <td>
                        	<input type="radio" id="casSex1" name="bankCase.casSex" value="男"/><label for="casSex1">男&nbsp;&nbsp;&nbsp;&nbsp;</label>
                            <input type="radio" id="casSex2" name="bankCase.casSex" value="女"/><label for="casSex2">女&nbsp;&nbsp;</label>
                        </td>
                        <th class="required"><span id="bankTxt"></span>：<span class='red'>*</span></th>
                        <td>
                            <c:if test="${!empty typeList}">
                            <select id="typId" name="typId" class="inputSize2 inputBoxAlign">
                                <option value="">请选择</option>
                           		<c:forEach items="${typeList}" var="t">
                           		<option value="${t.typId}">${t.typName}</option>
                           		</c:forEach>
                            </select>
                            </c:if>
                            <c:if test="${empty typeList}">
                                <select id="typeList" name="typId" class="inputSize2 inputBoxAlign" disabled="disabled">
                                    <option>未添加</option>
                                </select>
                            </c:if>
                            <img src="images/content/plugin.gif" alt="该字段可在类别设置中自定义" style="vertical-align:middle"/>
                        </td>
                    </tr>
                    <tr>
                       <th class="required">证件号：<span class='red'>*</span></th>
                        <td><input class="inputSize2" type="text" id="casNum" name="bankCase.casNum" value="<c:out value="${bankCase.casNum}"/>"  onBlur="autoShort(this,25)" /></td>   
                       <th class="required"><span id="casCaCdTxt"></span>：<span class='red'>*</span></th>
                        <td><input class="inputSize2" type="text" id="casCaCd" name="bankCase.casCaCd" value="<c:out value="${bankCase.casCaCd}"/>"  onBlur="autoShort(this,50)" /></td>
                    </tr>
                    <tr>
                        <th class="required">委案日期：<span class='red'>*</span></th>
                        <td>
                            <input type="text" id="casDate" name="casDate" readonly class="inputSize2 Wdate" style="cursor:hand" value="" ondblClick="clearInput(this)" onClick="WdatePicker({skin:'default',dateFmt:'yyyy-MM-dd'})"/></td>
                        <th class="required">委案金额：<span class='red'>*</span></th>
                        <td><input class="inputSize2" type="text" id="casM" name="bankCase.casM" value="${bankCase.casM}" onBlur="checkPrice(this)"/></td>
                    </tr>
                    <tr>
                        <th>预计退案日期：</th>
                        <td><input type="text" id="casBackdateP" name="casBackdateP" readonly class="inputSize2 Wdate" style="cursor:hand" value="" ondblClick="clearInput(this)" onClick="WdatePicker({skin:'default',dateFmt:'yyyy-MM-dd'})"/></td>
                        <th>是否结清：</th>
                        <td>
                        	<input type="radio" id="paidOver1" name="bankCase.casIsPaidover" value="1"/><label for="paidOver1">是&nbsp;&nbsp;&nbsp;&nbsp;</label>
                            <input type="radio" id="paidOver2" name="bankCase.casIsPaidover" value="0"/><label for="paidOver2">否&nbsp;&nbsp;</label>
                        </td>
                    </tr>
                    <tr>
                        <th>逾期账龄：</th>
                        <td><input style="width:80px;" class="inputSize2 inputBoxAlign" type="text" id="excLim" name="bankCase.casExcLim" onBlur="autoShort(this,200)" value="<c:out value="${bankCase.casExcLim}"/>"/>&nbsp;<c:if test="${!empty excLimList}"><select class="inputSize2 inputBoxAlign" style="width:70px" onChange="setValueFromSel(this,'excLim')"><option value="">请选择</option><c:forEach items="${excLimList}" var="eLim"><option value="${eLim.typName}">${eLim.typName}</option></c:forEach></select></c:if><c:if test="${empty excLimList}"><select class="inputSize2 inputBoxAlign" style="width:70px" disabled="disabled"><option>未添加</option></select></c:if>
                        <img src="images/content/plugin.gif" alt="该字段可在类别设置中自定义" style="vertical-align:middle"/></td>
                       	<th>逾期天数：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casOverdueDays" value="<c:out value="${bankCase.casOverdueDays}"/>"  onBlur="autoShort(this,200)" /></td>
                    </tr>
                    <tr>
                        <th>所在地区：</th>
                        <td colspan="3" class="longTd">
                            省份&nbsp;<select class="inputBoxAlign inputSize4" id="country"  name="provId" onChange="getCityInfo('cou')">
                            <c:if test="${!empty provList}">
                               <c:forEach var="provList" items="${provList}">
                               <option value="${provList.areId}">${provList.areName}</option>
                               </c:forEach>
                            </c:if>
                        </select>	 
                            &nbsp;城市&nbsp;
                            <select class="inputBoxAlign inputSize4" id="pro" name="cityId" onChange="getCityInfo('province')">
                            </select>	
                            &nbsp;区县&nbsp;
                            <select class="inputBoxAlign inputSize4" id="city" name="areaId">
                            </select>
                            <img src="images/content/plugin.gif" alt="该字段可在类别设置中自定义" style="vertical-align:middle"/>
                        </td>
                    </tr>
                    <tr>
                        <th>M值系数：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casMP" value="${bankCase.casMP}" onBlur="checkIsNum(this)"/></td>
                    	<th>公司佣金比率：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casBackP" value="${bankCase.casBackP}" onBlur="checkIsNum(this)"/></td>
                    </tr>
                    <tr>
                    	<th>公司佣金金额：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casBackM" value="${bankCase.casBackM}" onBlur="checkPrice(this)"/></td>
                        <th>委案期数：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casCount" value="<c:out value="${bankCase.casCount}"/>" onBlur="autoShort(this,100)"/></td>
                    </tr>
                    <c:if test="${SYS_CODE=='S'}">
                    <tr>
                    	<th>营业员佣金比率：</th>
                        <td colspan="3"><input class="inputSize2" type="text" name="bankCase.casPbackP" value="${bankCase.casPbackP}" onBlur="checkIsNum(this)"/></td>
                    </tr>
                    </c:if>
                    <tr>
                        <th>人民币：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casRmb" value="${bankCase.casRmb}" onBlur="checkPrice(this)"/></td>
                        <th>港币：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casGb" value="${bankCase.casGb}" onBlur="checkPrice(this)"/></td>
                    </tr>
                    <tr>
                        <th>外币：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casMy" value="${bankCase.casMy}" onBlur="checkPrice(this)"/></td>
                        <th>卡类：</th>
                        <td><input class="inputSize2" type="text" id="casMCat" name="bankCase.casCardCat" value="<c:out value="${bankCase.casCardCat}"/>" onBlur="autoShort(this,200)" /></td>
                    </tr>
                    <tr>
                        <th>开户行：</th>
                        <td><input class="inputSize2" type="text" id="cardBank" name="bankCase.casCardBank" value="<c:out value="${bankCase.casCardBank}"/>" onBlur="autoShort(this,200)"></td>
                        <th>账号：</th>
                       	<td><input class="inputSize2" type="text" id="casAccNum" name="bankCase.casAccNum" value="<c:out value="${bankCase.casAccNum}"/>"  onBlur="autoShort(this,50)" /></td>
                    </tr>
                    <tr>
                        <th>本金：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casPrincipal" value="<c:out value="${bankCase.casPrincipal}"/>" onBlur="autoShort(this,200)"/></td>
                        <th>剩余本金：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casLeftPri" value="<c:out value="${bankCase.casLeftPri}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                        <th>逾期利息：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casInt" value="<c:out value="${bankCase.casInt}"/>" onBlur="autoShort(this,200)"/></td>
                    	<th>滞纳金：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casOverduePaid" value="<c:out value="${bankCase.casOverduePaid}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                        <th>币种：</th>
                        <td><input class="inputSize2" type="text" id="casMCat" name="bankCase.casMCat" value="<c:out value="${bankCase.casMCat}"/>" onBlur="autoShort(this,200)" /></td>
                    	<th>信用额度：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casCredLim" value="<c:out value="${bankCase.casCredLim}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                        <th>最低还款额：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casMinPaid" value="<c:out value="${bankCase.casMinPaid}"/>" onBlur="autoShort(this,200)"/></td>
                        <th>保证金：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casGuaM" value="<c:out value="${bankCase.casGuaM}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                        <th>还款期限：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casPaidLim" value="<c:out value="${bankCase.casPaidLim}"/>" onBlur="autoShort(this,200)"/></td>
                        <th>已还期数：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casPaidCount" value="<c:out value="${bankCase.casPaidCount}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                        <th>开卡日：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casCreDate" value="<c:out value="${bankCase.casCreDate}"/>" onBlur="autoShort(this,200)"/></td>
                        <th>逾期日期：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casOverdueDate" value="<c:out value="${bankCase.casOverdueDate}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                        <th>账单日：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casBillDate" value="<c:out value="${bankCase.casBillDate}"/>" onBlur="autoShort(this,200)"/></td>
                        <th>主副卡：</th>
                        <td id="isHostTd"></td>
                    </tr>
                    <tr>
                        <th>最后还款金额：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casLastPaid" value="<c:out value="${bankCase.casLastPaid}"/>" onBlur="autoShort(this,200)"/></td>
                    	<th>最后还款日：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casPaidDate" value="<c:out value="${bankCase.casPaidDate}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                    	<th>最后消费日：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConDate" value="<c:out value="${bankCase.casConDate}"/>" onBlur="autoShort(this,200)"/></td>
                    	<th>最后提现日：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casRaiDate" value="<c:out value="${bankCase.casRaiDate}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                        <th>停卡日：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casStopDate" value="<c:out value="${bankCase.casStopDate}"/>" onBlur="autoShort(this,200)"/></td>
                    	<th>档案号：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casFileNo" value="<c:out value="${bankCase.casFileNo}"/>" onBlur="autoShort(this,100)"/></td>
                    </tr>
                    <tr>
                        <th>申请单号：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casAppNo" value="<c:out value="${bankCase.casAppNo}"/>" onBlur="autoShort(this,200)"/></td>
                    	 <th>拖欠级别：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casDelayLv" value="<c:out value="${bankCase.casDelayLv}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                    	<th>催收分类：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casCollType" value="<c:out value="${bankCase.casCollType}"/>" onBlur="autoShort(this,200)"/></td>
                    	<th>信贷分类：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casLoanType" value="<c:out value="${bankCase.casLoanType}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                    	 <th>贷款日期：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casLoanDate" value="<c:out value="${bankCase.casLoanDate}"/>" onBlur="autoShort(this,200)"/></td>
                    	<th>社保电脑号：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casSoPcno" value="<c:out value="${bankCase.casSoPcno}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                    	 <th>社保卡号：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casSoNo" value="<c:out value="${bankCase.casSoNo}"/>" onBlur="autoShort(this,200)"/></td>
                        <th>邮箱：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casEmail" value="<c:out value="${bankCase.casEmail}"/>" onBlur="autoShort(this,100)"/></td>
                    </tr>
                    <tr>
                    	<th>手机：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casMobPho" value="<c:out value="${bankCase.casMobPho}"/>" onBlur="autoShort(this,50)"/></td>
                    	<th>户籍地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casRegAdd" value="<c:out value="${bankCase.casRegAdd}"/>" onBlur="autoShort(this,200)"/></td>
                   	</tr>
                    <tr>  
                        <th>家庭地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casHomAdd" value="<c:out value="${bankCase.casHomAdd}"/>" onBlur="autoShort(this,4000)"/></td>
                        <th>家庭邮编：</th>
                        <td><input class="inputSize2" type="text" id="casPostCode" name="bankCase.casPostCode" value="<c:out value="${bankCase.casPostCode}"/>"  onBlur="autoShort(this,50)" /></td>
                    </tr>
                    <tr>
                    	<th>家庭号码：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casHomPho" value="<c:out value="${bankCase.casHomPho}"/>" onBlur="autoShort(this,50)"/></td> 
                    	<th>对账单地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casMailAdd" value="<c:out value="${bankCase.casMailAdd}"/>" onBlur="autoShort(this,4000)"/></td>
                    	
                    </tr>
                    <tr>
                        <th>单位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casUnitName" value="<c:out value="${bankCase.casUnitName}"/>" onBlur="autoShort(this,200)"/></td>
                        <th>单位邮编：</th>
                        <td><input class="inputSize2" type="text" id="casWpostCode" name="bankCase.casWpostCode" value="<c:out value="${bankCase.casWpostCode}"/>"  onBlur="autoShort(this,50)" /></td>
                    </tr>
                    <tr> 
                    	<th>职位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casPos" value="<c:out value="${bankCase.casPos}"/>" onBlur="autoShort(this,200)"/></td> 
                        <th>部门：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casPart" value="<c:out value="${bankCase.casPart}"/>" onBlur="autoShort(this,200)"/></td>
                    </tr>
                    <tr>
                        <th>单位地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casWorkAdd" value="<c:out value="${bankCase.casWorkAdd}"/>" onBlur="autoShort(this,4000)"/></td>
                        <th>单位号码：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casWorkPho" value="<c:out value="${bankCase.casWorkPho}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <c:if test="${SYS_CODE=='S'}">
                    <tr>                   
                        <th>联合贷款人&nbsp;&nbsp;&nbsp;&nbsp;<br/>姓名：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNameU" value="<c:out value="${bankCase.casNameU}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联合贷款人&nbsp;&nbsp;&nbsp;&nbsp;<br/>关系：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casReU" value="<c:out value="${bankCase.casReU}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>                   
                        <th>联合贷款人&nbsp;&nbsp;&nbsp;&nbsp;<br/>证件：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNumU" value="<c:out value="${bankCase.casNumU}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联合贷款人&nbsp;&nbsp;&nbsp;&nbsp;<br/>家庭电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUPho" value="<c:out value="${bankCase.casConUPho}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联合贷款人&nbsp;&nbsp;&nbsp;&nbsp;<br/>单位电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUWpho" value="<c:out value="${bankCase.casConUWpho}"/>" onBlur="autoShort(this,50)"/></td>               
                        <th>联合贷款人&nbsp;&nbsp;&nbsp;&nbsp;<br/>手机：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUMob" value="<c:out value="${bankCase.casConUMob}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>   
                    	<th>联合贷款人&nbsp;&nbsp;&nbsp;&nbsp;<br/>单位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUCom" value="<c:out value="${bankCase.casConUCom}"/>" onBlur="autoShort(this,200)"/></td>                
                        <th>联合贷款人&nbsp;&nbsp;&nbsp;&nbsp;<br/>地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUAdd" value="<c:out value="${bankCase.casConUAdd}"/>" onBlur="autoShort(this,4000)"/></td>
                    </tr>
                    </c:if>
                    <tr>                   
                        <th>联系人1姓名：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casName1" value="<c:out value="${bankCase.casName1}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人1关系：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casRe1" value="<c:out value="${bankCase.casRe1}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人1证件：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNum1" value="<c:out value="${bankCase.casNum1}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人1&nbsp;&nbsp;&nbsp;&nbsp;<br/>家庭电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConPho1" value="<c:out value="${bankCase.casConPho1}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联系人1&nbsp;&nbsp;&nbsp;&nbsp;<br/>单位电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConWpho1" value="<c:out value="${bankCase.casConWpho1}"/>" onBlur="autoShort(this,50)"/></td>                
                        <th>联系人1手机：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConMob1" value="<c:out value="${bankCase.casConMob1}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联系人1单位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConCom1" value="<c:out value="${bankCase.casConCom1}"/>" onBlur="autoShort(this,200)"/></td>                
                        <th>联系人1地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConAdd1" value="<c:out value="${bankCase.casConAdd1}"/>" onBlur="autoShort(this,4000)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人2姓名：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casName2" value="<c:out value="${bankCase.casName2}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人2关系：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casRe2" value="<c:out value="${bankCase.casRe2}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人2证件：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNum2" value="<c:out value="${bankCase.casNum2}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人2&nbsp;&nbsp;&nbsp;&nbsp;<br/>家庭电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConPho2" value="<c:out value="${bankCase.casConPho2}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>     
                    	<th>联系人2&nbsp;&nbsp;&nbsp;&nbsp;<br/>单位电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConWpho2" value="<c:out value="${bankCase.casConWpho2}"/>" onBlur="autoShort(this,50)"/></td>              
                        <th>联系人2手机：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConMob2" value="<c:out value="${bankCase.casConMob2}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联系人2单位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConCom2" value="<c:out value="${bankCase.casConCom2}"/>" onBlur="autoShort(this,200)"/></td>                
                        <th>联系人2地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConAdd2" value="<c:out value="${bankCase.casConAdd2}"/>" onBlur="autoShort(this,4000)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人3姓名：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casName3" value="<c:out value="${bankCase.casName3}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人3关系：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casRe3" value="<c:out value="${bankCase.casRe3}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人3证件：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNum3" value="<c:out value="${bankCase.casNum3}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人3&nbsp;&nbsp;&nbsp;&nbsp;<br/>家庭电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConPho3" value="<c:out value="${bankCase.casConPho3}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联系人3&nbsp;&nbsp;&nbsp;&nbsp;<br/>单位电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConWpho3" value="<c:out value="${bankCase.casConWpho3}"/>" onBlur="autoShort(this,50)"/></td>                
                        <th>联系人3手机：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConMob3" value="<c:out value="${bankCase.casConMob3}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联系人3单位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConCom3" value="<c:out value="${bankCase.casConCom3}"/>" onBlur="autoShort(this,200)"/></td>                
                        <th>联系人3地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConAdd3" value="<c:out value="${bankCase.casConAdd3}"/>" onBlur="autoShort(this,4000)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人4姓名：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casName4" value="<c:out value="${bankCase.casName4}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人4关系：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casRe4" value="<c:out value="${bankCase.casRe4}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人4证件：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNum4" value="<c:out value="${bankCase.casNum4}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人4&nbsp;&nbsp;&nbsp;&nbsp;<br/>家庭电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConPho4" value="<c:out value="${bankCase.casConPho4}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联系人4&nbsp;&nbsp;&nbsp;&nbsp;<br/>单位电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConWpho4" value="<c:out value="${bankCase.casConWpho4}"/>" onBlur="autoShort(this,50)"/></td>               
                        <th>联系人4手机：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConMob4" value="<c:out value="${bankCase.casConMob4}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>   
                    	<th>联系人4单位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConCom4" value="<c:out value="${bankCase.casConCom4}"/>" onBlur="autoShort(this,200)"/></td>                
                        <th>联系人4地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConAdd4" value="<c:out value="${bankCase.casConAdd4}"/>" onBlur="autoShort(this,4000)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人5姓名：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casName5" value="<c:out value="${bankCase.casName5}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人5关系：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casRe5" value="<c:out value="${bankCase.casRe5}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人5证件：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNum5" value="<c:out value="${bankCase.casNum5}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人5&nbsp;&nbsp;&nbsp;&nbsp;<br/>家庭电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConPho5" value="<c:out value="${bankCase.casConPho5}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联系人5&nbsp;&nbsp;&nbsp;&nbsp;<br/>单位电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConWpho5" value="<c:out value="${bankCase.casConWpho5}"/>" onBlur="autoShort(this,50)"/></td>               
                        <th>联系人5手机：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConMob5" value="<c:out value="${bankCase.casConMob5}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>   
                    	<th>联系人5单位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConCom5" value="<c:out value="${bankCase.casConCom5}"/>" onBlur="autoShort(this,200)"/></td>                
                        <th>联系人5地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConAdd5" value="<c:out value="${bankCase.casConAdd5}"/>" onBlur="autoShort(this,4000)"/></td>
                    </tr>
                    <c:if test="${SYS_CODE!='S'}">
                    <tr>                   
                        <th>联系人6姓名：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNameU" value="<c:out value="${bankCase.casNameU}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人6关系：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casReU" value="<c:out value="${bankCase.casReU}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>                   
                        <th>联系人6证件：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casNumU" value="<c:out value="${bankCase.casNumU}"/>" onBlur="autoShort(this,50)"/></td>  
                       	<th>联系人6&nbsp;&nbsp;&nbsp;&nbsp;<br/>家庭电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUPho" value="<c:out value="${bankCase.casConUPho}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>
                    	<th>联系人6&nbsp;&nbsp;&nbsp;&nbsp;<br/>单位电话：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUWpho" value="<c:out value="${bankCase.casConUWpho}"/>" onBlur="autoShort(this,50)"/></td>               
                        <th>联系人6手机：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUMob" value="<c:out value="${bankCase.casConUMob}"/>" onBlur="autoShort(this,50)"/></td>
                    </tr>
                    <tr>   
                    	<th>联系人6单位：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUCom" value="<c:out value="${bankCase.casConUCom}"/>" onBlur="autoShort(this,200)"/></td>                
                        <th>联系人6地址：</th>
                        <td><input class="inputSize2" type="text" name="bankCase.casConUAdd" value="<c:out value="${bankCase.casConUAdd}"/>" onBlur="autoShort(this,4000)"/></td>
                    </tr>
                    </c:if>
                    <tr>
                        <th>备注1：</th>
                        <td><textarea class="inputSize2" id="remark" name="bankCase.casRemark" onBlur="autoShort(this,4000)">${bankCase.casRemark}</textarea></td>
                        <th>备注2：</th>
                        <td><textarea class="inputSize2" id="remark2" name="bankCase.casRemark2" onBlur="autoShort(this,4000)">${bankCase.casRemark2}</textarea></td>
                    </tr>
                    <tr>
                        <th>备注3：</th>
                        <td><textarea class="inputSize2" id="remark3" name="bankCase.casRemark3" onBlur="autoShort(this,4000)">${bankCase.casRemark3}</textarea></td>
                        <th>备注4：</th>
                        <td><textarea class="inputSize2" id="remark4" name="bankCase.casRemark4" onBlur="autoShort(this,4000)">${bankCase.casRemark4}</textarea></td>
                    </tr>
                    <tr>
                        <th>备注5：</th>
                        <td><textarea class="inputSize2" id="remark5" name="bankCase.casRemark5" onBlur="autoShort(this,4000)">${bankCase.casRemark5}</textarea></td>
                        <th>备注6：</th>
                        <td><textarea class="inputSize2" id="remark6" name="bankCase.casRemark6" onBlur="autoShort(this,4000)">${bankCase.casRemark6}</textarea></td>
                    </tr>
                    <tr>
                        <th>商品：</th>
                        <td><textarea class="inputSize2" id="remark7" name="bankCase.casRemark7" onBlur="autoShort(this,4000)">${bankCase.casRemark7}</textarea></td>
                        <th><span id="rem8Txt"></span>：</th>
                        <td><textarea class="inputSize2" id="remark8" name="bankCase.casRemark8" onBlur="autoShort(this,4000)">${bankCase.casRemark8}</textarea></td>
                    </tr>
                    <tr class="noBorderBot">
                        <th>原催收记录：</th>
                        <td colspan="3"><textarea class="inputSize2L" name="bankCase.casPreRec" onBlur="autoShort(this,4000)">${bankCase.casPreRec}</textarea></td>
                    </tr>
                    <tr class="submitTr">
                        <td colspan="4">
                        <input id="caseSave" class="butSize1" type="button" value="保存" onClick="checkCasCd()" />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()"/>
                        </td>
                    </tr>   
                </tbody>
            </table>
        </form>
    </div>
  </body>
</html>
