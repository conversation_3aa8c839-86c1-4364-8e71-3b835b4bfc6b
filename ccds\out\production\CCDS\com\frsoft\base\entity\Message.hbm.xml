<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.Message" table="message" schema="dbo">
        <id name="meCode" type="java.lang.Long">
            <column name="me_code"/>
            <generator class="identity" />
        </id>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select"  not-null="false">
            <column name="me_se_no"/>
        </many-to-one>
        <property name="meTitle" type="java.lang.String">
            <column name="me_title" length="100" />
        </property>
        <property name="meContent" type="java.lang.String">
            <column name="me_content" length="1073741823" />
        </property>
        <property name="meIssend" type="java.lang.String">
            <column name="me_issend" length="1" />
        </property>
         <property name="meIsdel" type="java.lang.String">
            <column name="me_isdel" length="1" />
        </property>
         <property name="meInsUser" type="java.lang.String">
            <column name="me_ins_user" length="50" />
        </property>
        <property name="meDate" type="java.util.Date">
            <column name="me_date" length="23" />
        </property>
        <property name="meRecName" type="java.lang.String">
            <column name="me_rec_name" length="1073741823" />
        </property>
        <set name="RMessLims" inverse="true"  cascade="all">
            <key>
                <column name="rml_me_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.base.entity.RMessLim" />
        </set>
        <set name="attachments" inverse="true"  cascade="all" where="att_type='mes'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
    </class>
</hibernate-mapping>
