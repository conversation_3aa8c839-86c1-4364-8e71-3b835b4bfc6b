<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.Account" table="account" schema="dbo" >
        <id name="acoId" type="java.lang.Long">
            <column name="aco_id" />
            <generator class="identity" />
        </id>
        <property name="acoType" type="java.lang.String">
            <column name="aco_type" length="50" />
        </property>
        <property name="acoName" type="java.lang.String">
            <column name="aco_name" length="100" />
        </property>
        <property name="acoBankNum" type="java.lang.String">
            <column name="aco_bank_num" length="50" />
        </property>
        <property name="acoBank" type="java.lang.String">
            <column name="aco_bank" length="100" />
        </property>
        <property name="acoBankName" type="java.lang.String">
            <column name="aco_bank_name" length="50" />
        </property>
        <property name="acoCreDate" type="java.util.Date">
            <column name="aco_cre_date" length="23" />
        </property>
        <property name="acoOrgMon" type="java.lang.Double">
            <column name="aco_org_mon" precision="18" />
        </property>
        <property name="acoCurMon" type="java.lang.Double">
            <column name="aco_cur_mon" precision="18" />
        </property>
        <property name="acoRemark" type="java.lang.String">
            <column name="aco_remark" length="**********" />
        </property>
        <property name="acoInpUser" type="java.lang.String">
            <column name="aco_inp_user" length="50" />
        </property>
        <property name="acoInpDate" type="java.util.Date">
            <column name="aco_inp_date" length="23" />
        </property>
        <property name="acoAltDate" type="java.util.Date">
            <column name="aco_alt_date" length="23" />
        </property>
        <property name="acoAltUser" type="java.lang.String">
            <column name="aco_alt_user" length="50" />
        </property>
        <set name="accLines" inverse="true" order-by="acl_cre_date desc" cascade="all">
        	<key>
        		<column name="acl_aco_id" />
        	</key>
        	<one-to-many class="com.psit.struts.entity.AccLine"/>
        </set>
        <!--   <set name="inAccTranss" inverse="true" order-by="atr_date desc" cascade="all">
        	<key>
        		<column name="atr_in_aco" />
        	</key>
        	<one-to-many class="com.psit.struts.entity.AccTrans"/>
        </set>
        <set name="outAccTranss" inverse="true" order-by="atr_date desc" cascade="all">
        	<key>
        		<column name="atr_out_aco" />
        	</key>
        	<one-to-many class="com.psit.struts.entity.AccTrans"/>
        </set> -->
    </class>
</hibernate-mapping>
