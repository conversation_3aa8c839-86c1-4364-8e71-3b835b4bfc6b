package com.frsoft.ccds.web.controller;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.common.core.domain.model.LoginUser;
import com.frsoft.ccds.common.utils.StringUtils;
import com.frsoft.ccds.system.domain.SysUser;
import com.frsoft.ccds.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 登录控制器
 */
@RestController
public class LoginController extends BaseController {

    @Autowired
    private ISysUserService userService;

    /**
     * 登录方法
     *
     * @param loginName 登录名
     * @param password  密码
     * @param request   请求
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestParam String loginName, 
                           @RequestParam String password, 
                           HttpServletRequest request) {
        
        if (StringUtils.isEmpty(loginName) || StringUtils.isEmpty(password)) {
            return error("用户名或密码不能为空");
        }

        // 查询用户
        SysUser user = userService.selectUserByLoginName(loginName);
        if (user == null) {
            return error("用户不存在");
        }

        // 检查用户状态
        if (!"1".equals(user.getEnabled())) {
            return error("用户已被禁用");
        }

        // 验证密码
        String encryptedPassword = md5(password);
        if (!encryptedPassword.equals(user.getPassword())) {
            return error("密码错误");
        }

        // 创建登录用户信息
        LoginUser loginUser = new LoginUser();
        loginUser.setUserCode(user.getUserCode());
        loginUser.setUsername(user.getLoginName());

        // 保存到session
        HttpSession session = request.getSession();
        session.setAttribute("loginUser", loginUser);

        return success("登录成功");
    }

    /**
     * 退出登录
     *
     * @param request 请求
     * @return 结果
     */
    @PostMapping("/logout")
    public AjaxResult logout(HttpServletRequest request) {
        HttpSession session = request.getSession();
        session.removeAttribute("loginUser");
        session.invalidate();
        return success("退出成功");
    }

    /**
     * 获取用户信息
     *
     * @param request 请求
     * @return 结果
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(HttpServletRequest request) {
        LoginUser loginUser = getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        return success(loginUser);
    }

    /**
     * MD5加密
     *
     * @param input 输入字符串
     * @return 加密后的字符串
     */
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
}
