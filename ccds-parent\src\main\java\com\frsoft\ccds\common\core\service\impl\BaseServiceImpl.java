package com.frsoft.ccds.common.core.service.impl;

import com.frsoft.ccds.common.core.mapper.BaseMapper;
import com.frsoft.ccds.common.core.service.BaseService;
import com.frsoft.ccds.common.exception.BusinessException;
import com.frsoft.ccds.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;

/**
 * 通用Service实现基类
 * 
 * @param <M> Mapper类型
 * @param <T> 实体类型
 * <AUTHOR>
 */
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T> implements BaseService<T> {
    
    @Autowired
    protected M baseMapper;
    
    @Override
    public T selectById(Long id) {
        if (id == null) {
            return null;
        }
        return baseMapper.selectById(id);
    }
    
    @Override
    public List<T> selectAll() {
        return baseMapper.selectAll();
    }
    
    @Override
    public List<T> selectList(T entity) {
        return baseMapper.selectList(entity);
    }
    
    @Override
    public T selectOne(T entity) {
        return baseMapper.selectOne(entity);
    }
    
    @Override
    public int selectCount(T entity) {
        return baseMapper.selectCount(entity);
    }
    
    @Override
    public int insert(T entity) {
        if (entity == null) {
            throw new BusinessException("插入对象不能为空");
        }
        
        // 数据验证
        validate(entity, false);
        
        // 设置创建信息
        setCreateInfo(entity);
        
        return baseMapper.insertSelective(entity);
    }
    
    @Override
    public int insertBatch(List<T> list) {
        if (list == null || list.isEmpty()) {
            throw new BusinessException("批量插入列表不能为空");
        }
        
        // 批量验证和设置创建信息
        for (T entity : list) {
            validate(entity, false);
            setCreateInfo(entity);
        }
        
        return baseMapper.insertBatch(list);
    }
    
    @Override
    public int update(T entity) {
        if (entity == null) {
            throw new BusinessException("更新对象不能为空");
        }
        
        // 数据验证
        validate(entity, true);
        
        // 设置更新信息
        setUpdateInfo(entity);
        
        return baseMapper.updateByIdSelective(entity);
    }
    
    @Override
    public int updateBatch(List<T> list) {
        if (list == null || list.isEmpty()) {
            throw new BusinessException("批量更新列表不能为空");
        }
        
        int result = 0;
        for (T entity : list) {
            result += update(entity);
        }
        return result;
    }
    
    @Override
    public int saveOrUpdate(T entity) {
        if (entity == null) {
            throw new BusinessException("保存对象不能为空");
        }
        
        // 通过反射获取ID字段
        Object id = getIdValue(entity);
        if (id == null) {
            return insert(entity);
        } else {
            return update(entity);
        }
    }
    
    @Override
    public int deleteById(Long id) {
        if (id == null) {
            throw new BusinessException("删除ID不能为空");
        }
        return baseMapper.deleteById(id);
    }
    
    @Override
    public int deleteByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            throw new BusinessException("删除ID列表不能为空");
        }
        return baseMapper.deleteByIds(ids);
    }
    
    @Override
    public int deleteByCondition(T entity) {
        if (entity == null) {
            throw new BusinessException("删除条件不能为空");
        }
        return baseMapper.deleteByCondition(entity);
    }
    
    @Override
    public int logicDeleteById(Long id) {
        if (id == null) {
            throw new BusinessException("逻辑删除ID不能为空");
        }
        return baseMapper.logicDeleteById(id);
    }
    
    @Override
    public int logicDeleteByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            throw new BusinessException("逻辑删除ID列表不能为空");
        }
        return baseMapper.logicDeleteByIds(ids);
    }
    
    @Override
    public void validate(T entity, boolean isUpdate) {
        // 子类可以重写此方法进行具体的数据验证
        if (entity == null) {
            throw new BusinessException("验证对象不能为空");
        }
    }
    
    @Override
    public void setCreateInfo(T entity) {
        if (entity == null) {
            return;
        }
        
        String currentUser = SecurityUtils.getCurrentUserCode();
        Date currentTime = new Date();
        
        // 通过反射设置创建信息
        setFieldValue(entity, "createTime", currentTime);
        setFieldValue(entity, "createUser", currentUser);
        setFieldValue(entity, "casInsTime", currentTime);
        setFieldValue(entity, "casInsUser", currentUser);
    }
    
    @Override
    public void setUpdateInfo(T entity) {
        if (entity == null) {
            return;
        }
        
        String currentUser = SecurityUtils.getCurrentUserCode();
        Date currentTime = new Date();
        
        // 通过反射设置更新信息
        setFieldValue(entity, "updateTime", currentTime);
        setFieldValue(entity, "updateUser", currentUser);
        setFieldValue(entity, "casAltTime", currentTime);
        setFieldValue(entity, "casAltUser", currentUser);
    }
    
    /**
     * 获取实体ID值
     * 
     * @param entity 实体对象
     * @return ID值
     */
    private Object getIdValue(T entity) {
        try {
            // 尝试常见的ID字段名
            String[] idFields = {"id", "casId", "seNo", "userCode"};
            
            for (String fieldName : idFields) {
                try {
                    Field field = entity.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    if (value != null) {
                        return value;
                    }
                } catch (NoSuchFieldException e) {
                    // 继续尝试下一个字段
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }
    
    /**
     * 设置字段值
     * 
     * @param entity 实体对象
     * @param fieldName 字段名
     * @param value 字段值
     */
    private void setFieldValue(T entity, String fieldName, Object value) {
        try {
            Field field = entity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(entity, value);
        } catch (Exception e) {
            // 字段不存在或设置失败，忽略
        }
    }
}
