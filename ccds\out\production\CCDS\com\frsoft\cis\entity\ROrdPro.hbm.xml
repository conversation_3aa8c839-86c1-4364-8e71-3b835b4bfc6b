<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.ROrdPro" table="r_ord_pro" schema="dbo" >
        <id name="ropId" type="java.lang.Long">
            <column name="rop_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rop_pro_id"/>
        </many-to-one>
        <many-to-one name="salOrdCon" class="com.frsoft.cis.entity.SalOrdCon" fetch="select" not-null="false">
            <column name="rop_ord_code" length="50" />
        </many-to-one>
        <property name="ropNum" type="java.lang.Double">
            <column name="rop_num" precision="18" />
        </property>
        <property name="ropRealPrice" type="java.lang.Double">
            <column name="rop_real_price" precision="18" />
        </property>
        <property name="ropRemark" type="java.lang.String">
            <column name="rop_remark" length="1073741823" />
        </property>
        <property name="ropPrice" type="java.lang.Double">
            <column name="rop_price" length="1073741823" />
        </property>
        <property name="ropZk" type="java.lang.String">
            <column name="rop_zk" length="50" />
        </property>
        <property name="ropOutNum" type="java.lang.Double">
            <column name="rop_out_num" />
        </property>
        <property name="ropRealNum" type="java.lang.Double">
            <column name="rop_real_num"/>
        </property>
    </class>
</hibernate-mapping>
