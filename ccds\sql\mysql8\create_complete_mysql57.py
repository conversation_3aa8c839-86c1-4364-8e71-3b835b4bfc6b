#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整的MySQL 5.7兼容数据库文件
基于原始MSSQL文件完整转换所有表
"""

import re
import os

def extract_table_definitions(sql_content):
    """从MSSQL文件中提取所有表定义"""
    
    # 匹配CREATE TABLE语句的正则表达式
    table_pattern = r'CREATE TABLE \[dbo\]\.\[(\w+)\]\((.*?)\)(?:\s*CONSTRAINT.*?)?(?=\s*END\s*GO|\s*GO|$)'
    
    tables = []
    matches = re.finditer(table_pattern, sql_content, re.DOTALL | re.IGNORECASE)
    
    for match in matches:
        table_name = match.group(1)
        table_content = match.group(2)
        
        # 提取主键信息
        pk_pattern = r'CONSTRAINT \[PK_[^\]]+\] PRIMARY KEY CLUSTERED\s*\(\s*\[(\w+)\] ASC\s*\)'
        pk_match = re.search(pk_pattern, sql_content[match.end():match.end()+500], re.IGNORECASE)
        primary_key = pk_match.group(1) if pk_match else None
        
        tables.append({
            'name': table_name,
            'content': table_content,
            'primary_key': primary_key
        })
    
    return tables

def convert_field_definition(field_def):
    """转换字段定义从MSSQL到MySQL"""
    
    # 移除COLLATE子句
    field_def = re.sub(r'\s+COLLATE\s+[\w_]+', '', field_def)
    
    # 转换数据类型
    conversions = [
        (r'\[(\w+)\]\s+\[bigint\]\s+IDENTITY\(1,1\)\s+NOT\s+NULL', r'`\1` bigint NOT NULL AUTO_INCREMENT'),
        (r'\[(\w+)\]\s+\[int\]\s+IDENTITY\(1,1\)\s+NOT\s+NULL', r'`\1` int NOT NULL AUTO_INCREMENT'),
        (r'\[(\w+)\]\s+\[bigint\]', r'`\1` bigint'),
        (r'\[(\w+)\]\s+\[int\]', r'`\1` int'),
        (r'\[(\w+)\]\s+\[varchar\]\((\d+)\)', r'`\1` varchar(\2)'),
        (r'\[(\w+)\]\s+\[nvarchar\]\((\d+)\)', r'`\1` varchar(\2)'),
        (r'\[(\w+)\]\s+\[nvarchar\]\(max\)', r'`\1` longtext'),
        (r'\[(\w+)\]\s+\[varchar\]\(max\)', r'`\1` longtext'),
        (r'\[(\w+)\]\s+\[char\]\((\d+)\)', r'`\1` char(\2)'),
        (r'\[(\w+)\]\s+\[nchar\]\((\d+)\)', r'`\1` char(\2)'),
        (r'\[(\w+)\]\s+\[text\]', r'`\1` text'),
        (r'\[(\w+)\]\s+\[ntext\]', r'`\1` longtext'),
        (r'\[(\w+)\]\s+\[datetime\]', r'`\1` datetime'),
        (r'\[(\w+)\]\s+\[date\]', r'`\1` date'),
        (r'\[(\w+)\]\s+\[time\]', r'`\1` time'),
        (r'\[(\w+)\]\s+\[decimal\]\((\d+),\s*(\d+)\)', r'`\1` decimal(\2,\3)'),
        (r'\[(\w+)\]\s+\[numeric\]\((\d+),\s*(\d+)\)', r'`\1` decimal(\2,\3)'),
        (r'\[(\w+)\]\s+\[money\]', r'`\1` decimal(19,4)'),
        (r'\[(\w+)\]\s+\[smallmoney\]', r'`\1` decimal(10,4)'),
        (r'\[(\w+)\]\s+\[float\]', r'`\1` double'),
        (r'\[(\w+)\]\s+\[real\]', r'`\1` float'),
        (r'\[(\w+)\]\s+\[bit\]', r'`\1` tinyint(1)'),
        (r'\[(\w+)\]\s+\[image\]', r'`\1` longblob'),
        (r'\[(\w+)\]\s+\[varbinary\]\(max\)', r'`\1` longblob'),
        (r'\[(\w+)\]\s+\[varbinary\]\((\d+)\)', r'`\1` varbinary(\2)'),
        (r'\[(\w+)\]\s+\[binary\]\((\d+)\)', r'`\1` binary(\2)'),
        (r'\[(\w+)\]\s+\[uniqueidentifier\]', r'`\1` varchar(36)'),
        (r'\[(\w+)\]\s+\[timestamp\]', r'`\1` timestamp'),
    ]
    
    for pattern, replacement in conversions:
        field_def = re.sub(pattern, replacement, field_def, flags=re.IGNORECASE)
    
    return field_def

def create_mysql_table(table_info):
    """创建MySQL表定义"""
    
    table_name = table_info['name']
    content = table_info['content']
    primary_key = table_info['primary_key']
    
    # 分割字段定义
    fields = []
    field_lines = content.split(',')
    
    for line in field_lines:
        line = line.strip()
        if line and not line.startswith('CONSTRAINT'):
            converted_field = convert_field_definition(line)
            if converted_field.strip():
                fields.append('\t' + converted_field)
    
    # 添加主键
    if primary_key:
        fields.append(f'\tPRIMARY KEY (`{primary_key}`)')
    
    # 构建完整的CREATE TABLE语句
    mysql_table = f"""DROP TABLE IF EXISTS `{table_name}`;
CREATE TABLE `{table_name}` (
{',\n'.join(fields)}
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

"""
    
    return mysql_table

def create_complete_mysql57():
    """创建完整的MySQL 5.7数据库文件"""
    
    print("开始创建完整的MySQL 5.7数据库文件...")
    
    # 读取原始MSSQL文件
    sql_files = ['../CCDS.sql', '../CCDS_data.sql', '../CCDS_in_cashBus.sql']
    all_tables = []
    
    for sql_file in sql_files:
        if os.path.exists(sql_file):
            print(f"处理文件: {sql_file}")
            # 尝试不同的编码
            encodings = ['utf-16', 'utf-8', 'gbk', 'gb2312']
            content = None

            for encoding in encodings:
                try:
                    with open(sql_file, 'r', encoding=encoding) as f:
                        content = f.read()
                        print(f"成功使用编码 {encoding} 读取 {sql_file}")
                        break
                except (UnicodeDecodeError, UnicodeError):
                    continue

            if content:
                tables = extract_table_definitions(content)
                all_tables.extend(tables)
                print(f"从 {sql_file} 提取了 {len(tables)} 个表")
            else:
                print(f"无法读取文件 {sql_file}")
    
    # 去重（基于表名）
    unique_tables = {}
    for table in all_tables:
        if table['name'] not in unique_tables:
            unique_tables[table['name']] = table
    
    print(f"总共找到 {len(unique_tables)} 个唯一表")
    
    # 创建MySQL头部
    header = """-- MySQL 5.7 版本的CCDS完整数据库结构
-- 从MSSQL完整转换而来
-- 生成时间: 2024年

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `ccds`;

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 表结构定义开始
-- ========================================

"""
    
    # 创建所有表的MySQL定义
    mysql_tables = []
    for table_name, table_info in unique_tables.items():
        try:
            mysql_table = create_mysql_table(table_info)
            mysql_tables.append(mysql_table)
        except Exception as e:
            print(f"转换表 {table_name} 时出错: {e}")
    
    # 创建尾部
    footer = """
-- ========================================
-- 表结构定义结束
-- ========================================

-- 创建一些常用索引
CREATE INDEX idx_sal_emp_name ON `sal_emp` (`se_name`);
CREATE INDEX idx_cus_cor_cus_user ON `cus_cor_cus` (`cor_user_code`);
CREATE INDEX idx_lim_user_enabled ON `lim_user` (`user_isenabled`);

SET FOREIGN_KEY_CHECKS = 1;

-- 数据库结构创建完成
SELECT 'CCDS完整数据库结构创建完成' as message;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ccds';
"""
    
    # 组合最终SQL
    final_sql = header + '\n'.join(mysql_tables) + footer
    
    return final_sql, len(unique_tables)

def main():
    """主函数"""
    try:
        sql_content, table_count = create_complete_mysql57()
        
        output_file = "mysql57_CCDS_complete.sql"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        print(f"完整的MySQL 5.7数据库文件已创建: {output_file}")
        print(f"包含 {table_count} 个表")
        
    except Exception as e:
        print(f"创建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
