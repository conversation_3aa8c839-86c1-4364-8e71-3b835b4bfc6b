<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>PTP转为CP</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/config.js"></script>

	<script type="text/javascript" >
    	function check(){
			var errStr = "";
			if(isEmpty("cpNum")){
				errStr+="- 未填写CP金额！\n";
			 }
			if(isEmpty("cpTime")){
				errStr+="- 未选择CP时间！\n";
			 }
			 if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			 else{
				waitSubmit("save","保存中...");
				waitSubmit("doCancel");
				return $("create").submit();
			 }
		}

		window.onload=function(){
			resetCpTimeFormat("${CUS_VER_ID}","cpTime");
		}
  </script></head>
  
  <body>

  <div class="inputDiv">
  	<form id="create" action="caseAction.do" method="post">
  		<input type="hidden" name="op" value="saveCP" />
  		<input type="hidden" name="paId"  value="${paId}"/>
        <table class="dashTab inputForm single" cellpadding="0" cellspacing="0">
            <tbody>
                <tr>
                    <th class="required">CP金额：<span class="red">*</span></th>
                    <td><input class="inputSize2" type="text"  name="cpNum"  value="${ptpNum}" onBlur="checkPrice(this)"/></td>
                </tr>
                <tr> 
                    <th class="required">CP时间：<span class="red">*</span></th>
                    <td><input name="cpTime" id="cpTime" value="${ptpTime}" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly="readonly" ondblClick="clearInput(this)" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/></td>
                </tr>	
                <tr>
                    <th>还款人：</th>
                  	<td><input class="inputSize2" type="text" name="payMan" onBlur="autoShort(this,50)"/></td>
                </tr>
                <tr>
                    <th>还款方式：</th>
                  	<td><select name="payMethod" class="inputSize2 inputBoxAlign"><option value="">请选择</option>
                  	<c:if test="${!empty payMethodList}"><c:forEach items="${payMethodList}" var="payMethod"><option value="${payMethod.typName}">${payMethod.typName}</option></c:forEach></c:if>
                  	</select></td>
                </tr>
                <tr class="noBorderBot">
                    <th>备注：</th>
                    <td><textarea class="inputSize2" rows="3" name="paidRem" onBlur="autoShort(this,4000)"></textarea></td>
                </tr>
                <tr class="submitTr">
                    <td colspan="2">
                    <input id="save" class="butSize1" type="button" value="保存" onClick="check()" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                    </td>
                </tr>
            </tbody>
        </table>
	</form>
    </div>
  </body>
</html>
