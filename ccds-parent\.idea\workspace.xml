<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a871f935-76df-4ffa-9808-6d809a7370d6" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/constant/Constants.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/core/controller/BaseController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/core/domain/AjaxResult.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/core/domain/model/LoginUser.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/utils/Convert.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/utils/DateUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/utils/PageDomain.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/utils/PageUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/utils/ServletUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/utils/StringUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/utils/TableSupport.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-common/src/main/java/com/frsoft/ccds/common/utils/sql/SqlUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-system/src/main/java/com/frsoft/ccds/system/controller/SysUserController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-system/src/main/java/com/frsoft/ccds/system/service/impl/SysUserServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-web/logs/ccds.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-web/src/main/java/com/frsoft/ccds/web/controller/IndexController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-web/src/main/java/com/frsoft/ccds/web/controller/LoginController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-web/src/main/resources/application-dev.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ccds-web/src/main/resources/mybatis/mybatis-config.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ccds-common/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ccds-common/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ccds-core/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ccds-core/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ccds-framework/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ccds-framework/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="PUSH_TAGS">
      <GitPushTagMode>
        <option name="argument" value="--tags" />
        <option name="title" value="All" />
      </GitPushTagMode>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\devops\repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../devops/apache-maven-3.9.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="D:\devops\apache-maven-3.9.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="1.8" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="delegateBuildToMaven" value="true" />
    <option name="jreName" value="1.8" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectId" id="2ylGokXoYsKe4p5tTokO7DB2TWd" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "com.codeium.enabled": "true",
    "jdk.selected.JAVA_MODULE": "17",
    "last_opened_file_path": "D:/cuishou/ccds-parent/ccds-system/pom.xml",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.keymap",
    "spring.configuration.checksum": "e143e72419001a9e52dd5b18b4aac576",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.CCDSApplication">
    <configuration name="CcdsApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.frsoft.ccds.CcdsApplication" />
      <module name="ccds-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.frsoft.ccds.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="CCDS-maven" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="CCDS-maven" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CCDSApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ccds-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.frsoft.ccds.CCDSApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.CcdsApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a871f935-76df-4ffa-9808-6d809a7370d6" name="更改" comment="" />
      <created>1744435260925</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744435260925</updated>
      <workItem from="1750401926177" duration="865000" />
      <workItem from="1750403743107" duration="3475000" />
      <workItem from="1750407539717" duration="1866000" />
      <workItem from="1750735980102" duration="585000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <option name="closed" value="true" />
      <created>1750402234272</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750402234272</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="init" />
    <option name="LAST_COMMIT_MESSAGE" value="init" />
  </component>
</project>