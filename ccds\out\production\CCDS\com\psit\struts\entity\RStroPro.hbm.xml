<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RStroPro" table="r_stro_pro" schema="dbo" >
        <id name="rspId" type="java.lang.Long">
            <column name="rsp_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rsp_pro_id"/>
        </many-to-one>
        <many-to-one name="wmsStro" class="com.psit.struts.entity.WmsStro" fetch="select" not-null="false">
            <column name="rsp_stro_code" length="50" />
        </many-to-one>
        <property name="rspProNum" type="java.lang.Double">
            <column name="rsp_pro_num" />
        </property>
    </class>
</hibernate-mapping>
