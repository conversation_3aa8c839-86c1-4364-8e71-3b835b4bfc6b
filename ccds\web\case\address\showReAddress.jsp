 <%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>联系地址列表</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
	<style type="text/css">
    .newAddr {
		color:#FF9900;
		font-weight:bold;
	}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/case.js"></script>
    <script type="text/javascript">

	 if('${msg}'!=null&&'${msg}'!=""){
	    alert('${msg}');
		parent.history.go(0);
	   }
	
      function batchUpdate(updState,obj){
		var priKey = $N("priKey");
		//var updState = $('updState').value;
		var priKeys='';
		for(var i=0;i<priKey.length;i++){ 
			if(priKey[i].checked==true){ 
				flag=true; 
				priKeys +=priKey[i].value+',';
				
			}
	    } 
	   /*if(isEmpty("updState")){
	   		alert("未选择状态!");
			return false;
	   }*/

	   if(checkBoxIsEmpty("priKey")){
	    	var url = "caseAction.do";
			var pars = "op=batchUpdate&casId=${casId}&updState="+updState+"&ids="+priKeys;
			new Ajax.Request(url, {
				method: 'post',
				parameters: pars,
				onSuccess: function(transport) {
					 var rs = transport.responseText;
					 if(rs=="1"){
						alert("更新成功！");
						loadList();
					 }
					 else{
						alert("更新失败！");
					 }
					 obj.blur();
				},
				onFailure: function(response){
					if (response.status == 404)
						alert("您访问的地址不存在！");
					else
						alert("Error: status code is " + response.status);
				}
			});
     	}
     	/*else{
     		$("updState").value="";
     	}*/
  	}   
    /*  function batchDel(){
		var priKey=$N("priKey");
		var priKey1;
		var flag=false;
		for(var i=0;i<priKey.length;i++){ 
			if(priKey[i].checked==true){ 
				flag=true; 
				priKey1 +=priKey[i].value+',';
			   }
		    } 
		if(!flag){
			alert("请选要批量删除的联系地址!");
			return false;	
		   }else{
		     if(!confirm("确定要删除这些联系地址吗？"))
	 		    return false;
	         else
		        window.location.href="caseAction.do?op=batchDelAdd&codes="+priKey1+"&casId=${casId}&state=2";
		   }
	    }
	   function check(count,arg){
       	var errStr = "";
       	var sts = $('staSel'+count);
		if(isEmpty(sts)){
			errStr+="- 未选择地址状态！\n";
		 }
		if(errStr!=""){
			errStr+="\n请返回选择...";
			alert(errStr);
			return false;
		 }else{	   
		        var state = sts.value;
		        var url = "caseAction.do";
				var pars = "op=updateState&adrId="+arg+"&state="+state+"&count="+count+"&ran=" + Math.random();
				new Ajax.Request(url, {
					method: 'post',
					parameters: pars,
					onSuccess: function(transport) {
						 var str = transport.responseText;
						 var str1 = str.split(',');
						 var state = str1[0];
						 var count = str1[1];
					     switch(state){
							  case '0':
							  		$('staSpa'+count).hide();
							  		$('staImg'+count).hide();
									$('staTxt'+count).show();
							        $('staTxt'+count).innerHTML='未知';
							        break; 
							  case '1':
							  		$('staSpa'+count).hide();
							  		$('staImg'+count).hide();
									$('staTxt'+count).show();
							        $('staTxt'+count).innerHTML='有效';
							        break; 
							  case '2':
							  		$('staSpa'+count).hide();
							  		$('staImg'+count).hide();
									$('staTxt'+count).show();
							        $('staTxt'+count).innerHTML='无效';
							        break; 
						}
					},
			       	onFailure: function(response){
						if (response.status == 404)
							alert("您访问的地址不存在！");
						else
							alert("Error: status code is " + response.status);
					}
			});
		 }
      }
   function showImg(count){
    	var selEl = $('staImg'+count);
   		if(selEl.style.display=='none'){
      		selEl.show();
       }
    }
    function hideImg(count){
     	var selEl = $('staImg'+count);
   		if(selEl.style.display==''){
      		selEl.hide();
      }
    }
   function change(count){
        var selEl = $('staSpa'+count);
        var selImg = $('staImg'+count);
        selEl.show();
        selImg.hide();

   }*/

   function initPage(){
   		/*var state ='${state}';
   		if(state!=''){
   			setCurItemStyle(new Array("${state}","${state}","${state}","${state}"),new Array("1","0","2","3"));//设置判断选中按钮条件
   		}*/
   		//可操作案件
   		if('${operational}'!='0'){
   			$("newAddress").show();
			if($("toolsBarTop")!=null){
				$("toolsBarTop").show();
			}
   		}
   }
  /*  function getAdd(){
		self.location.href="assitanceAction.do?op=toListAssByCaseId&assType=2&caseId=${casId}";
	} */
   	function getMails(){
   		self.location.href="assitanceAction.do?op=toListAssByCaseId&assType=1&caseId=${casId}";
   	}
   	/*function getVisRec(){
   		self.location.href="visRecordAction.do?op=toListVisRec&casId=${casId}";
   	}*/
		
	//列表筛选按钮链接
	function filterList(state){
		$("state").value=state;
		setCurItemStyle(state,["1","0","2",""]);
		loadList();
	}
	
	function dataMapper(obj){
		var datas,className,dblFunc,dataId;
		dataId = obj.adrId;
		var addrStr = obj.adrAdd;
		if(obj.adrIsnew==1){
			addrStr = "<span class='newAddr'>"+obj.adrAdd+"</span>";
		}
		var state = "";
		switch(obj.adrState){
			case 0:
				state = "未知";break;
			case 1:
				state = "有效";break;
			case 2:
				state = "无效";break;
		}
		var re = "";
		if(obj.adrRemark != undefined){
			re = obj.adrRemark+"&nbsp;";
		}
		var remark = "<div style='width:100%; cursor:pointer;' onClick=\"parent.casePopDiv(27,['"+obj.adrId+"','"+re+"']);\">"+re+"&nbsp;</div>";
		//var funcCol1 = "<a href=\"javascript:void(0)\" onClick=\"parent.casePopDiv(22,'"+obj.adrId+"');return false;\">[操作历史]</a>";
		
		var funcCol2 ="";
		if(obj.adrState==1){
			if(obj.adrMailApp==0){
				funcCol2 += "<a href=\"javascript:void(0)\" onClick=\"parent.casePopDiv(19,'"+obj.adrId+"');return false;\">[信函]</a>&nbsp;";
			}
			else if(obj.adrMailApp==1){
				funcCol2 += "<span>[已申请信函]</span>&nbsp;";
			}
			
			if(obj.adrVisApp==0){
				funcCol2 += "<a href=\"javascript:void(0)\" onClick=\"parent.casePopDiv(17,'"+obj.adrId+"');return false;\">[外访]</a>&nbsp;"
			}
			else if(obj.adrVisApp==1){
				funcCol2 += "<span>[已申请外访]</a>&nbsp;";
			}
		}
		else{
			funcCol2 = "<span class='gray'>状态标为有效后才可申请信函和外访</span>";
		}
             /* "<a href=\"javascript：void(0)\"  style=\"display:"+ (((obj.adrState!=0) || (obj.adrCheckApp==1))?("none"):("")) +"\"  onClick=\"parent.casePopDiv(16,'"+obj.adrId+"');return false;\">[地址核准]</a>&nbsp;"
	+"<span style=\"display:"+ ((obj.adrCheckApp==1)?(""):("none")) +"\">[已申请地址核准]</span>&nbsp;" */
		
		var funcCol3 ="<img onClick=\"parent.casePopDiv(22,'"+obj.adrId+"')\" class=\"hand\" src=\"images/content/detail.gif\" alt=\"操作记录\"/>&nbsp;&nbsp;<img onClick=\"parent.casePopDiv(34,'"+obj.adrId+"')\" class=\"hand\" src=\"images/content/edit.gif\" alt=\"编辑\"/>&nbsp;&nbsp;<img onClick=\"parent.caseDelDiv(2,'"+obj.adrId+"','1')\" class='hand' src='images/content/del.gif' alt='删除'/>";
		if('${operational}'=='0'){
			datas = [state, obj.adrName, obj.adrRel, addrStr, obj.adrCat, remark, obj.adrMailCount?obj.adrMailCount:0, obj.adrNum];
		}
		else{
			datas = [state, obj.adrName, obj.adrRel, addrStr, obj.adrCat, remark, obj.adrMailCount?obj.adrMailCount:0, obj.adrNum,funcCol2,funcCol3];
		}
		return [datas,className,dblFunc,dataId];
	}
	function loadList(sortCol,isDe,pageSize,curP){
		var url = "caseAction.do";
		var pars = [];
		pars.state = $('state').value;
		pars.op = "showReAddress";
		pars.casId="${casId}";
		var loadFunc = "loadList";
		var cols;
		if('${operational}'=='0'){
			cols =[
				{name:"状态"},
				{name:"姓名"},
				{name:"关系"},
				{name:"地址",align:"left"},
				{name:"类别"},
				{name:"备注",align:"left",isSort:false},
				{name:"信函次数",isSort:false},
				{name:"外访次数",isSort:false}
			];
		}
		else{
			cols = [
				{name:"状态"},
				{name:"姓名"},
				{name:"关系"},
				{name:"地址",align:"left"},
				{name:"类别"},
				{name:"备注",align:"left",isSort:false},
				{name:"信函次数",isSort:false},
				{name:"外访次数",isSort:false},
				{name:"申请协助", hasTil:false,isSort:false},
				{name:"操作", hasTil:false,isSort:false}
			];
		}
		gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
		gridEl.loadData(dataMapper);
	}
	
   	var gridEl = new MGrid("reAddrTab${operational}","dataList");
	if('${operational}'!='0'){
		gridEl.config.hasCheckBox = true;
	}
	//gridEl.config.sortable=false;
	createProgressBar();
   	window.onload=function(){
   		initPage();
   		loadList();
   }
	
    </script>
</head>
    
  <body>
  	<div class="divWithScroll2 innerIfm">
     	<table class="normal ifmTopTab" cellpadding="0" cellspacing="0">
            <tr>
            	<!--<th id="toolsBarTop" style="font-weight:normal; vertical-align:top; padding:4px 0 0 0; width:80px; display:none;">
                	<select id="updState" class="inputBoxAlign" onChange="batchUpdate()">
	    				<option value="">标记为...</option>
	   					<option value="1">有效</option>
	    				<option value="2">无效</option>
	    				<option value="0">未知</option>
	   				</select>
                </th>-->
                <th style="font-weight:normal;">
                    <div id="topChoose" class="listTopBox">
                        <!--<a href="javascript:void(0)" onClick="filterList('1')">&nbsp;有效地址&nbsp;</a>&nbsp;
                        <a href="javascript:void(0)" onClick="filterList('0')">&nbsp;未知地址&nbsp;</a>&nbsp;
                        <a href="javascript:void(0)" onClick="filterList('2')">&nbsp;无效地址&nbsp;</a>&nbsp;-->
                        <a href="javascript:void(0)" onClick="batchUpdate('1',this)">&nbsp;标记为有效&nbsp;</a>&nbsp;
                        <a href="javascript:void(0)" onClick="batchUpdate('0',this)">&nbsp;标记为未知&nbsp;</a>&nbsp;
                        <a href="javascript:void(0)" onClick="batchUpdate('2',this)">&nbsp;标记为无效&nbsp;</a>&nbsp;
                        <a href="javascript:void(0)" onClick="filterList('')">&nbsp;显示全部地址&nbsp;</a>&nbsp;
                        <!-- <a href="javascript:void(0)" onClick="getAdd();return false;">&nbsp;查看核准记录&nbsp;</a>&nbsp; -->
                        <a href="javascript:void(0)" onClick="getMails();return false;">&nbsp;查看信函记录&nbsp;</a>&nbsp;
                    </div>
                </th>
                    <!--<a href="javascript:void(0)" onClick="getVisRec();return false;">[查看外访记录]</a>-->
                <td style="vertical-align:top;">
                <a id="newAddress" style="display:none" href="javascript:void(0)" onClick="parent.casePopDiv(7,'${casId}');return false;" class="newBlueButton">新建联系地址</a></td>
            </tr>
        </table>
		<div id="dataList" class="dataList"></div>
		<input type='hidden' name='casId' value='${casId}'></input>
        <input type="hidden" id="state" value="df"/>
  	</div>	  
  </body>
</html>