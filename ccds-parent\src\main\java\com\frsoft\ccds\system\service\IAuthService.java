package com.frsoft.ccds.system.service;

import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.common.core.domain.model.LoginUser;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 */
public interface IAuthService {
    
    /**
     * 用户登录
     * 
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    AjaxResult login(String username, String password);
    
    /**
     * 用户登出
     * 
     * @param token 用户令牌
     * @return 登出结果
     */
    AjaxResult logout(String token);
    
    /**
     * 获取当前登录用户信息
     * 
     * @return 用户信息
     */
    LoginUser getCurrentUser();
    
    /**
     * 刷新令牌
     * 
     * @param token 原令牌
     * @return 新令牌
     */
    AjaxResult refreshToken(String token);
    
    /**
     * 验证令牌
     * 
     * @param token 令牌
     * @return 是否有效
     */
    boolean validateToken(String token);
    
    /**
     * 修改密码
     * 
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 修改结果
     */
    AjaxResult changePassword(String oldPassword, String newPassword);
}
