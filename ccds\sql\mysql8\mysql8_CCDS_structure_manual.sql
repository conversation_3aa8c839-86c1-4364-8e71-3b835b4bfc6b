-- MySQL 8.0 版本的CCDS数据库结构
-- 从MSSQL自动转换生成
CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ccds`;
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `spo_paid_past`;
CREATE TABLE `spo_paid_past`(
`spa_id` bigint NOT NULL AUTO_INCREMENT,
`spa_code` varchar(300) NULL,
`spa_spo_id` bigint NULL,
`spa_aco_id` bigint NULL,
`spa_ssu_id` bigint NULL,
`spa_fct_date` datetime NULL,
`spa_type_id` bigint NULL,
`spa_pay_type` varchar(50) NULL,
`spa_pay_mon` decimal(18,2) NULL,
`spa_in_name` varchar(100) NULL,
`spa_inp_user` varchar(50) NULL,
`spa_se_no` bigint NULL,
`spa_isinv` char(1) NULL,
`spa_remark` longtext NULL,
`spa_cre_date` datetime NULL,
`spa_isdel` char(1) NULL,
`spa_content` varchar(100) NULL,
`spa_acc_type_id` bigint NULL,
`spa_alt_date` datetime NULL,
`spa_alt_user` varchar(50) NULL,
`spa_undo_date` datetime NULL,
`spa_undo_user` varchar(50) NULL,
PRIMARY KEY (`spa_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `sal_emp`(
`se_no` bigint NOT NULL AUTO_INCREMENT,
`se_so_code` varchar(50) NULL,
`se_name` varchar(100) NULL,
`se_ide_code` varchar(50) NULL,
`se_pos` varchar(50) NULL,
`se_sex` varchar(50) NULL,
`se_prob` varchar(50) NULL,
`se_bir_place` varchar(50) NULL,
`se_acc_place` varchar(100) NULL,
`se_birth` varchar(50) NULL,
`se_marry` varchar(10) NULL,
`se_type` varchar(50) NULL,
`se_job_lev` bigint NULL,
`se_job_cate` varchar(50) NULL,
`se_job_title` varchar(50) NULL,
`se_start_day` datetime NULL,
`se_year_pay` varchar(50) NULL,
`se_cost_center` varchar(50) NULL,
`se_email` varchar(50) NULL,
`se_nation` varchar(50) NULL,
`se_poli_status` varchar(50) NULL,
`se_edu` varchar(50) NULL,
`se_tel` varchar(50) NULL,
`se_phone` varchar(50) NULL,
`se_qq` varchar(50) NULL,
`se_msn` varchar(50) NULL,
`se_rec_source` varchar(100) NULL,
`se_prov_fund` varchar(50) NULL,
`se_job_date` datetime NULL,
`se_hou_reg` varchar(50) NULL,
`se_social_code` varchar(50) NULL,
`se_rap` varchar(50) NULL,
`se_address` varchar(500) NULL,
`se_remark` longtext NULL,
`se_bank_name` varchar(50) NULL,
`se_bank_card` varchar(50) NULL,
`se_weal_address` varchar(50) NULL,
`se_weal_pos` varchar(50) NULL,
`se_isovertime` varchar(50) NULL,
`se_attendance` varchar(50) NULL,
`se_card_num` varchar(50) NULL,
`se_pic` longtext NULL,
`se_isenabled` char(1) NULL,
`se_inser_date` datetime NULL,
`se_code` varchar(50) NULL,
`se_log` longtext NULL,
`se_alt_date` datetime NULL,
`se_inser_user` varchar(50) NULL,
`se_alt_user` varchar(50) NULL,
`se_end_date` datetime NULL,
`se_edc_bac` longtext NULL,
`se_work_ex` longtext NULL,
`se_user_code` varchar(50) NULL,
`se_per_tel` varchar(50) NULL,
`se_plan_sign_date` datetime NULL,
`se_sign_date` datetime NULL,
`se_credit_date` datetime NULL,
`se_college` varchar(200) NULL,
`se_transfer` text NULL,
PRIMARY KEY (`se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
;
DROP TABLE IF EXISTS `r_rep_lim`;
CREATE TABLE `r_rep_lim`(
`rrl_id` bigint NOT NULL AUTO_INCREMENT,
`rrl_rep_code` bigint NULL,
`rrl_se_no` bigint NULL,
`rrl_date` datetime NULL,
`rrl_content` longtext NULL,
`rrl_isappro` char(1) NULL,
`rrl_oppro_date` datetime NULL,
`rrl_isdel` char(1) NULL,
`rrl_app_order` int NULL,
`rrl_isview` char(1) NULL,
`rrl_is_all_appro` char(1) NULL,
`rrl_rec_user` varchar(50) NULL,
PRIMARY KEY (`rrl_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `sal_paid_plan`(
`spd_id` bigint NOT NULL AUTO_INCREMENT,
`spd_ord_code` bigint NULL,
`spd_prm_date` datetime NULL,
`spd_count` int NULL,
`spd_pay_mon` decimal(18,2) NULL,
`spd_mon_type` varchar(50) NULL,
`spd_user_code` varchar(50) NULL,
`spd_isp` char(1) NULL,
`spd_resp` varchar(50) NULL,
`spd_cre_date` datetime NULL,
`spd_alt_date` datetime NULL,
`spd_alt_user` varchar(50) NULL,
`spd_isdel` char(1) NULL,
`spd_content` varchar(100) NULL,
`spd_cor_code` bigint NULL,
PRIMARY KEY (`spd_id`)
)
CREATE TABLE `r_new_lim`(
`rnl_id` bigint NOT NULL AUTO_INCREMENT,
`rnl_new_code` bigint NULL,
`rnl_se_no` bigint NULL,
`rnl_date` datetime NULL,
PRIMARY KEY (`rnl_id`)
)
CREATE TABLE `account`(
`aco_id` bigint NOT NULL AUTO_INCREMENT,
`aco_type` varchar(50) NULL,
`aco_name` varchar(100) NULL,
`aco_bank_num` varchar(50) NULL,
`aco_bank` varchar(100) NULL,
`aco_bank_name` varchar(50) NULL,
`aco_cre_date` datetime NULL,
`aco_org_mon` decimal(18,2) NULL,
`aco_cur_mon` decimal(18,2) NULL,
`aco_remark` longtext NULL,
`aco_inp_user` varchar(50) NULL,
`aco_inp_date` datetime NULL,
`aco_alt_date` datetime NULL,
`aco_alt_user` varchar(50) NULL,
PRIMARY KEY (`aco_id`)
)
CREATE TABLE `lim_user`(
`user_code` varchar(50) NOT NULL,
`user_loginName` varchar(50) NULL,
`user_pwd` varchar(50) NULL,
`user_up_code` varchar(50) NULL,
`user_lev` char(1) NULL,
`user_so_code` varchar(50) NULL,
`user_se_id` bigint NULL,
`user_se_name` varchar(100) NULL,
`user_desc` longtext NULL,
`user_isenabled` char(1) NULL,
`user_num` varchar(200) NULL,
`user_role_id` bigint NULL,
`user_islogin` char(1) NULL,
`user_ip` varchar(50) NULL,
`user_fail` int NULL,
`user_pwd_upd_date` datetime NULL,
`user_cti_login` varchar(255) NULL,
`user_cti_pwd` varchar(255) NULL,
`user_cti_server` varchar(50) NULL,
`user_cti_phone` varchar(50) NULL,
`user_grp_id` bigint NULL,
`user_sms_max_num` int NULL,
PRIMARY KEY (`user_code`)
)
CREATE TABLE `inquiry`(
`inq_id` bigint NOT NULL AUTO_INCREMENT,
`inq_ssu_id` bigint NULL,
`inq_pro_id` bigint NULL,
`inq_title` varchar(100) NULL,
`inq_price` decimal(18,2) NULL,
`inq_se_no` bigint NULL,
`inq_date` datetime NULL,
`inq_inp_user` varchar(50) NULL,
`inq_upd_user` varchar(50) NULL,
`inq_ins_date` datetime NULL,
`inq_upd_date` datetime NULL,
`inq_remark` longtext NULL,
`inq_isdel` char(1) NULL,
PRIMARY KEY (`inq_id`)
)
CREATE TABLE `quote`(
`quo_id` bigint NOT NULL AUTO_INCREMENT,
`quo_opp_id` bigint NULL,
`quo_title` varchar(100) NULL,
`quo_price` decimal(18,2) NULL,
`quo_se_no` bigint NULL,
`quo_remark` longtext NULL,
`quo_date` datetime NULL,
`quo_desc` longtext NULL,
`quo_ins_date` datetime NULL,
`quo_upd_date` datetime NULL,
`quo_inp_user` varchar(50) NULL,
`quo_upd_user` varchar(50) NULL,
`quo_isdel` char(1) NULL,
`quo_pro_id` bigint NULL,
PRIMARY KEY (`quo_id`)
)
CREATE TABLE `cus_cor_cus`(
`cor_code` bigint NOT NULL AUTO_INCREMENT,
`cor_num` varchar(50) NULL,
`cor_user_code` varchar(50) NULL,
`cor_name` varchar(100) NULL,
`cor_hot` varchar(50) NULL,
`cor_mne` varchar(50) NULL,
`cor_lic_code` varchar(50) NULL,
`cor_org_code` varchar(50) NULL,
`cor_star` varchar(50) NULL,
`cor_cre_lev` varchar(50) NULL,
`cor_cre_lim` varchar(50) NULL,
`cor_ind_id` bigint NULL,
`cor_per_size` varchar(50) NULL,
`cor_acc_bank` varchar(100) NULL,
`cor_bank_num` varchar(50) NULL,
`cor_sou_id` bigint NULL,
`cor_com_inf` longtext NULL,
`cor_country` bigint NULL,
`cor_province` bigint NULL,
`cor_city` bigint NULL,
`cor_phone` varchar(50) NULL,
`cor_fex` varchar(50) NULL,
`cor_net` varchar(500) NULL,
`cor_zip_code` varchar(50) NULL,
`cor_address` longtext NULL,
`cor_remark` longtext NULL,
`cor_creat_date` datetime NULL,
`cor_upd_date` datetime NULL,
`cor_issuc` char(1) NULL,
`cor_last_date` datetime NULL,
`cor_temp_tag` varchar(50) NULL,
`cor_isdelete` char(1) NULL,
`cor_spe_write` longtext NULL,
`cor_upd_user` varchar(50) NULL,
`cor_typ_id` bigint NULL,
`cor_ins_user` varchar(50) NULL,
PRIMARY KEY (`cor_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
;
DROP TABLE IF EXISTS `sal_opp`;
CREATE TABLE `sal_opp`(
`opp_id` bigint NOT NULL AUTO_INCREMENT,
`opp_cor_code` bigint NULL,
`opp_title` varchar(300) NULL,
`opp_lev` varchar(50) NULL,
`opp_exe_date` datetime NULL,
`opp_des` longtext NULL,
`opp_remark` longtext NULL,
`opp_ins_date` datetime NULL,
`opp_isexe` varchar(10) NULL,
`opp_state` varchar(10) NULL,
`opp_upd_date` datetime NULL,
`opp_inp_user` varchar(50) NULL,
`opp_upd_user` varchar(50) NULL,
`opp_isdel` char(1) NULL,
`opp_sign_date` datetime NULL,
`opp_money` decimal(18,2) NULL,
`opp_stage` bigint NULL,
`opp_possible` varchar(50) NULL,
`opp_sta_remark` varchar(100) NULL,
`opp_sta_update` datetime NULL,
`opp_sta_log` longtext NULL,
`opp_find_date` datetime NULL,
`opp_user_code` varchar(50) NULL,
`opp_se_no` bigint NULL,
PRIMARY KEY (`opp_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `sal_pra`(
`pra_id` bigint NOT NULL AUTO_INCREMENT,
`pra_cor_code` bigint NULL,
`pra_title` varchar(300) NULL,
`pra_content` longtext NULL,
`pra_ins_date` datetime NULL,
`pra_type` varchar(100) NULL,
`pra_state` varchar(100) NULL,
`pra_isPrice` varchar(10) NULL,
`pra_exe_date` datetime NULL,
`pra_cost_time` varchar(20) NULL,
`pra_cus_link` varchar(50) NULL,
`pra_se_no` bigint NULL,
`pra_back` longtext NULL,
`pra_remark` longtext NULL,
`pra_upd_date` datetime NULL,
`pra_opp_id` bigint NULL,
`pra_inp_user` varchar(50) NULL,
`pra_upd_user` varchar(50) NULL,
`pra_isdel` char(1) NULL,
`pra_user_code` varchar(50) NULL,
PRIMARY KEY (`pra_id`)
)
CREATE TABLE `pro_task`(
`prta_id` bigint NOT NULL AUTO_INCREMENT,
`prta_se_no` bigint NULL,
`prta_pro_id` bigint NULL,
`prta_sta_name` varchar(300) NULL,
`prta_name` varchar(50) NULL,
`prta_title` varchar(300) NULL,
`prta_rel_date` datetime NULL,
`prta_change_date` datetime NULL,
`prta_fin_date` datetime NULL,
`prta_lev` varchar(50) NULL,
`prta_state` char(1) NULL,
`prta_cyc` varchar(50) NULL,
`prta_tag` longtext NULL,
`prta_desc` longtext NULL,
`prta_log` longtext NULL,
`prta_remark` longtext NULL,
`prta_isdel` char(1) NULL,
`prta_fct_date` datetime NULL,
`prta_upd_user` varchar(50) NULL,
PRIMARY KEY (`prta_id`)
)
CREATE TABLE `cus_serv`(
`ser_code` bigint NOT NULL AUTO_INCREMENT,
`ser_cor_code` bigint NULL,
`ser_title` varchar(300) NULL,
`ser_cus_link` varchar(50) NULL,
`ser_method` varchar(100) NULL,
`ser_content` longtext NULL,
`ser_exe_date` datetime NULL,
`ser_cos_time` varchar(50) NULL,
`ser_state` varchar(10) NULL,
`ser_se_no` bigint NULL,
`ser_feedback` longtext NULL,
`ser_remark` longtext NULL,
`ser_ins_date` datetime NULL,
`ser_upd_date` datetime NULL,
`ser_inp_user` varchar(50) NULL,
`ser_upd_user` varchar(50) NULL,
`ser_isdel` char(1) NULL,
`ser_user_code` varchar(50) NULL,
PRIMARY KEY (`ser_code`)
)
CREATE TABLE `sal_ord_con`(
`sod_code` bigint NOT NULL AUTO_INCREMENT,
`sod_num` varchar(300) NULL,
`sod_til` varchar(300) NULL,
`sod_type_id` bigint NULL,
`sod_cus_code` bigint NULL,
`sod_pro_id` bigint NULL,
`sod_sum_mon` decimal(18,2) NULL,
`sod_paid_mon` decimal(18,2) NULL,
`sod_mon_type` varchar(50) NULL,
`sod_state` varchar(10) NULL,
`sod_ship_state` varchar(10) NULL,
`sod_own_code` varchar(50) NULL,
`sod_deadline` datetime NULL,
`sod_end_date` datetime NULL,
`sod_ord_date` datetime NULL,
`sod_inp_date` datetime NULL,
`sod_isfail` char(1) NULL,
`sod_remark` longtext NULL,
`sod_change_date` datetime NULL,
`sod_paid_method` varchar(20) NULL,
`sod_inp_code` varchar(50) NULL,
`sod_cus_con` varchar(100) NULL,
`sod_se_no` bigint NULL,
`sod_con_date` datetime NULL,
`sod_change_user` varchar(50) NULL,
`sod_app_date` datetime NULL,
`sod_app_man` varchar(50) NULL,
`sod_app_desc` longtext NULL,
`sod_app_isok` char(1) NULL,
`sod_content` longtext NULL,
PRIMARY KEY (`sod_code`)
)
CREATE TABLE `wms_line`(
`wli_id` bigint NOT NULL AUTO_INCREMENT,
`wli_type_code` varchar(50) NULL,
`wli_type` varchar(50) NULL,
`wli_stro_code` varchar(50) NULL,
`wli_wpr_id` bigint NULL,
`wli_in_num` decimal(18,2) NULL,
`wli_out_num` decimal(18,2) NULL,
`wli_date` datetime NULL,
`wli_state` char(1) NULL,
`wli_man` varchar(50) NULL,
`wli_wms_id` bigint NULL,
`wli_isdel` char(1) NULL,
`wli_num` decimal(18,2) NULL,
PRIMARY KEY (`wli_id`)
)
CREATE TABLE `r_ship_pro`(
`rshp_id` bigint NOT NULL AUTO_INCREMENT,
`rshp_ship_code` varchar(50) NULL,
`rshp_pro_id` bigint NULL,
PRIMARY KEY (`rshp_id`)
)
CREATE TABLE `doc_template`(
`tmp_id` bigint NOT NULL AUTO_INCREMENT,
`tmp_name` varchar(50) NULL,
`tmp_html` longtext NULL,
`tmp_type` varchar(50) NULL,
`tmp_mark` varchar(100) NULL,
PRIMARY KEY (`tmp_id`)
)
CREATE TABLE `r_stro_pro`(
`rsp_id` bigint NOT NULL AUTO_INCREMENT,
`rsp_stro_code` varchar(50) NULL,
`rsp_pro_id` bigint NULL,
`rsp_pro_num` decimal(18,2) NULL,
PRIMARY KEY (`rsp_id`)
)
CREATE TABLE `address`(
`adr_id` bigint NOT NULL AUTO_INCREMENT,
`adr_state` int NULL,
`adr_name` varchar(50) NULL,
`adr_add` varchar(200) NULL,
`adr_cas_id` bigint NULL,
`adr_cat` varchar(50) NULL,
`adr_remark` longtext NULL,
`adr_isdel` char(1) NULL,
`adr_num` int NULL,
`adr_check_app` int NULL,
`adr_mail_app` int NULL,
`adr_vis_app` int NULL,
`adr_rel` varchar(50) NULL,
`adr_mail_count` int NULL,
`adr_isnew` int NULL,
PRIMARY KEY (`adr_id`)
)
CREATE TABLE `wms_product`(
`wpr_id` bigint NOT NULL AUTO_INCREMENT,
`wpr_name` varchar(100) NULL,
`wpr_type_id` bigint NULL,
`wpr_model` varchar(100) NULL,
`wpr_unit` bigint NULL,
`wpr_color` varchar(50) NULL,
`wpr_size` varchar(50) NULL,
`wpr_provider` varchar(100) NULL,
`wpr_up_lim` int NULL,
`wpr_low_lim` int NULL,
`wpr_cost_prc` decimal(18,2) NULL,
`wpr_sale_prc` decimal(18,2) NULL,
`wpr_pic` longtext NULL,
`wpr_cuser_code` varchar(50) NULL,
`wpr_cre_date` datetime NULL,
`wpr_euser_code` varchar(50) NULL,
`wpr_edit_date` datetime NULL,
`wpr_desc` longtext NULL,
`wpr_remark` longtext NULL,
`wpr_states` char(1) NULL,
`wpr_range` longtext NULL,
`wpr_technology` longtext NULL,
`wpr_problem` longtext NULL,
`wpr_isdel` char(1) NULL,
`wpr_code` varchar(50) NULL,
`wpr_iscount` char(1) NULL,
PRIMARY KEY (`wpr_id`)
)
CREATE TABLE `r_spo_pro`(
`rpp_id` bigint NOT NULL AUTO_INCREMENT,
`rpp_spo_id` bigint NULL,
`rpp_pro_id` bigint NULL,
`rpp_num` decimal(18,2) NULL,
`rpp_price` decimal(18,2) NULL,
`rpp_sum_mon` decimal(18,2) NULL,
`rpp_remark` longtext NULL,
`rpp_out_num` decimal(18,2) NULL,
`rpp_real_num` decimal(18,2) NULL,
PRIMARY KEY (`rpp_id`)
)
CREATE TABLE `cus_province`(
`prv_id` bigint NOT NULL AUTO_INCREMENT,
`prv_area_id` bigint NULL,
`prv_name` varchar(100) NULL,
`prv_isenabled` varchar(10) NULL,
PRIMARY KEY (`prv_id`)
)
CREATE TABLE `sal_supplier`(
`ssu_id` bigint NOT NULL AUTO_INCREMENT,
`ssu_code` varchar(300) NULL,
`ssu_name` varchar(100) NULL,
`ssu_phone` varchar(50) NULL,
`ssu_fex` varchar(50) NULL,
`ssu_email` varchar(50) NULL,
`ssu_net` varchar(200) NULL,
`ssu_add` longtext NULL,
`ssu_prd` longtext NULL,
`ssu_county` bigint NULL,
`ssu_pro` bigint NULL,
`ssu_city` bigint NULL,
`ssu_zip_code` varchar(50) NULL,
`ssu_bank` varchar(50) NULL,
`ssu_bank_code` varchar(50) NULL,
`ssu_isdel` char(1) NULL,
`ssu_remark` longtext NULL,
`ssu_inp_user` varchar(50) NULL,
`ssu_cre_date` datetime NULL,
`ssu_alt_date` datetime NULL,
`ssu_alt_user` varchar(50) NULL,
`ssu_bank_name` varchar(50) NULL,
`ssu_type_id` bigint NULL,
PRIMARY KEY (`ssu_id`)
)
CREATE TABLE `sal_all_task`(
`sat_id` bigint NOT NULL AUTO_INCREMENT,
`sat_date` varchar(50) NULL,
`sat_se_no` bigint NULL,
`sat_inp_date` datetime NULL,
`sat_alt_date` datetime NULL,
`sat_inp_name` varchar(50) NULL,
`sat_alt_name` varchar(50) NULL,
`sat_ht_mon` decimal(18,2) NULL,
`sat_paid_mon` decimal(18,2) NULL,
`sat_cus_num` int NULL,
PRIMARY KEY (`sat_id`)
)
CREATE TABLE `type_list`(
`typ_id` bigint NOT NULL AUTO_INCREMENT,
`typ_name` varchar(50) NULL,
`typ_desc` longtext NULL,
`typ_type` varchar(50) NULL,
`typ_isenabled` char(1) NULL,
PRIMARY KEY (`typ_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
;
DROP TABLE IF EXISTS `pho_red`;
CREATE TABLE `pho_red`(
`pr_id` bigint NOT NULL AUTO_INCREMENT,
`pr_typ_id` bigint NULL,
`pr_contact` varchar(200) NULL,
`pr_cas_id` bigint NULL,
`pr_content` longtext NULL,
`pr_time` datetime NULL,
`pr_se_no` bigint NULL,
`pr_con_type` varchar(50) NULL,
`pr_pa_id` bigint NULL,
`pr_name` varchar(50) NULL,
`pr_cat` int NULL,
`pr_ptp_date` datetime NULL,
`pr_ptp_num` decimal(18,2) NULL,
`pr_rel` varchar(50) NULL,
`pr_state_id` bigint NULL,
`pr_negotiation` varchar(50) NULL,
`pr_cc_id` bigint NULL,
`pr_call_id` longtext NULL,
`pr_sms_id` longtext NULL,
PRIMARY KEY (`pr_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `spo_paid_plan`(
`spp_id` bigint NOT NULL AUTO_INCREMENT,
`spp_spo_id` bigint NULL,
`spp_prm_date` datetime NULL,
`spp_pay_mon` decimal(18,2) NULL,
`spp_inp_user` varchar(50) NULL,
`spp_resp` varchar(50) NULL,
`spp_isp` char(1) NULL,
`spp_cre_date` datetime NULL,
`spp_alt_date` datetime NULL,
`spp_alt_user` varchar(50) NULL,
`spp_isdel` char(1) NULL,
`spp_content` varchar(100) NULL,
PRIMARY KEY (`spp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
;
DROP TABLE IF EXISTS `pro_actor`;
CREATE TABLE `pro_actor`(
`act_id` bigint NOT NULL AUTO_INCREMENT,
`act_pro_id` bigint NULL,
`act_se_no` bigint NULL,
`act_isdel` char(1) NULL,
`act_duty` varchar(100) NULL,
PRIMARY KEY (`act_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `sup_contact`(
`scn_id` bigint NOT NULL AUTO_INCREMENT,
`scn_ssu_id` bigint NULL,
`scn_name` varchar(50) NULL,
`scn_sex` varchar(50) NULL,
`scn_dep` longtext NULL,
`scn_service` varchar(100) NULL,
`scn_phone` varchar(50) NULL,
`scn_work_pho` varchar(50) NULL,
`scn_home_pho` varchar(50) NULL,
`scn_fex` varchar(50) NULL,
`scn_zip_code` varchar(50) NULL,
`scn_email` varchar(100) NULL,
`scn_qq` varchar(50) NULL,
`scn_msn` varchar(100) NULL,
`scn_add` longtext NULL,
`scn_oth_link` longtext NULL,
`scn_remark` longtext NULL,
`scn_inp_user` varchar(50) NULL,
`scn_upd_user` varchar(50) NULL,
`scn_cre_date` datetime NULL,
`scn_mod_date` datetime NULL,
`scn_isdel` char(1) NULL,
PRIMARY KEY (`scn_id`)
)
CREATE TABLE `pro_task_lim`(
`ptl_id` bigint NOT NULL AUTO_INCREMENT,
`ptl_prta_id` bigint NULL,
`ptl_se_no` bigint NULL,
`ptl_name` varchar(50) NULL,
`ptl_isfin` char(1) NULL,
`ptl_fin_date` datetime NULL,
`ptl_isdel` char(1) NULL,
`ptl_desc` longtext NULL,
PRIMARY KEY (`ptl_id`)
)
CREATE TABLE `cus_contact`(
`con_id` bigint NOT NULL AUTO_INCREMENT,
`con_cor_code` bigint NULL,
`con_name` varchar(50) NULL,
`con_sex` varchar(10) NULL,
`con_dep` longtext NULL,
`con_service` varchar(100) NULL,
`con_lev` varchar(50) NULL,
`con_phone` varchar(50) NULL,
`con_work_pho` varchar(50) NULL,
`con_home_pho` varchar(50) NULL,
`con_fex` varchar(50) NULL,
`con_zip_code` varchar(50) NULL,
`con_email` varchar(100) NULL,
`con_qq` varchar(50) NULL,
`con_msn` varchar(100) NULL,
`con_add` longtext NULL,
`con_oth_link` longtext NULL,
`con_bir` datetime NULL,
`con_hob` varchar(100) NULL,
`con_taboo` varchar(100) NULL,
`con_edu` varchar(100) NULL,
`con_photo` longtext NULL,
`con_remark` longtext NULL,
`con_cre_date` datetime NULL,
`con_mod_date` datetime NULL,
`con_inp_user` varchar(50) NULL,
`con_upd_user` varchar(50) NULL,
`con_isdel` char(1) NULL,
`con_type` varchar(50) NULL,
`con_user_code` varchar(50) NULL,
PRIMARY KEY (`con_id`)
)
CREATE TABLE `wms_shipment`(
`wsh_code` varchar(50) NOT NULL,
`wsh_wout_code` varchar(50) NULL,
`wsh_ord_code` bigint NULL,
`wsh_state` char(1) NULL,
`wsh_out_date` datetime NULL,
`wsh_inp_date` datetime NULL,
`wsh_user_code` varchar(50) NULL,
`wsh_rec_man` varchar(50) NULL,
`wsh_type` varchar(50) NULL,
`wsh_cost` decimal(18,2) NULL,
`wsh_remark` longtext NULL,
PRIMARY KEY (`wsh_code`)
)
CREATE TABLE `r_wms_wms`(
`rww_id` bigint NOT NULL AUTO_INCREMENT,
`wch_id` bigint NULL,
`rww_pro_id` bigint NULL,
`rww_num` decimal(18,2) NULL,
`rww_remark` longtext NULL,
PRIMARY KEY (`rww_id`)
)
CREATE TABLE `cus_area`(
`are_id` bigint NOT NULL AUTO_INCREMENT,
`are_name` varchar(100) NULL,
`are_isenabled` varchar(10) NULL,
PRIMARY KEY (`are_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
;
DROP TABLE IF EXISTS `r_wout_pro`;
CREATE TABLE `r_wout_pro`(
`rwo_id` bigint NOT NULL AUTO_INCREMENT,
`rwo_wout_id` bigint NULL,
`rwo_pro_id` bigint NULL,
`rwo_wout_num` decimal(18,2) NULL,
`rwo_remark` longtext NULL,
PRIMARY KEY (`rwo_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `case_int`(
`cin_id` bigint NOT NULL AUTO_INCREMENT,
`cin_cas_id` bigint NULL,
`cin_name` varchar(50) NULL,
`cin_m_cat` varchar(200) NULL,
`cin_m` decimal(18,2) NULL,
`cin_principal` varchar(200) NULL,
`cin_int` varchar(200) NULL,
`cin_overdue_paid` varchar(200) NULL,
`cin_over_limit` varchar(200) NULL,
`cin_service` varchar(200) NULL,
`cin_year` varchar(200) NULL,
`cin_other` varchar(200) NULL,
`cin_out` varchar(200) NULL,
`cin_ins_time` datetime NULL,
`cin_end_date` varchar(100) NULL,
`cin_damages_amt` varchar(200) NULL,
PRIMARY KEY (`cin_id`)
)
CREATE TABLE `wms_war_in`(
`wwi_id` bigint NOT NULL AUTO_INCREMENT,
`wwi_code` varchar(50) NULL,
`wwi_title` longtext NULL,
`wwi_stro_code` varchar(50) NULL,
`wwi_user_code` varchar(50) NULL,
`wwi_state` char(1) NULL,
`wwi_remark` longtext NULL,
`wwi_isdel` char(1) NULL,
`wwi_inp_name` varchar(50) NULL,
`wwi_alt_name` varchar(50) NULL,
`wwi_inp_time` datetime NULL,
`wwi_alt_time` datetime NULL,
`wwi_in_date` datetime NULL,
`wwi_app_date` datetime NULL,
`wwi_app_man` varchar(50) NULL,
`wwi_app_desc` longtext NULL,
`wwi_app_isok` char(1) NULL,
`wwi_spo_code` bigint NULL,
`wwi_can_date` datetime NULL,
`wwi_can_man` varchar(50) NULL,
PRIMARY KEY (`wwi_id`)
)
CREATE TABLE `r_win_pro`(
`rwi_id` bigint NOT NULL AUTO_INCREMENT,
`rwi_win_id` bigint NULL,
`rwi_pro_id` bigint NULL,
`rwi_win_num` decimal(18,2) NULL,
`rwi_remark` longtext NULL,
PRIMARY KEY (`rwi_id`)
)
CREATE TABLE `wms_stro`(
`wms_code` varchar(50) NOT NULL,
`wms_name` varchar(300) NULL,
`wms_type_id` bigint NULL,
`wms_loc` longtext NULL,
`wms_cre_date` datetime NULL,
`wms_user_code` varchar(50) NULL,
`wms_remark` longtext NULL,
`wms_isenabled` char(1) NULL,
PRIMARY KEY (`wms_code`)
)
CREATE TABLE `case_grp`(
`cg_id` bigint NOT NULL AUTO_INCREMENT,
`cg_name` varchar(30) NULL,
`cg_user_code` varchar(50) NULL,
PRIMARY KEY (`cg_id`)
)
CREATE TABLE `lim_right`(
`rig_code` varchar(50) NOT NULL,
`rig_fun_code` varchar(50) NULL,
`rig_ope_code` varchar(50) NULL,
`rig_wms_name` varchar(300) NULL,
PRIMARY KEY (`rig_code`)
)
CREATE TABLE `cus_city`(
`city_id` bigint NOT NULL AUTO_INCREMENT,
`city_prv_id` bigint NULL,
`city_name` varchar(100) NULL,
`city_isenabled` varchar(10) NULL,
PRIMARY KEY (`city_id`)
)
CREATE TABLE `wms_change`(
`wch_id` bigint NOT NULL AUTO_INCREMENT,
`wch_code` varchar(50) NULL,
`wch_title` longtext NULL,
`wch_state` char(1) NULL,
`wch_in_date` datetime NULL,
`wch_out_wms` varchar(50) NULL,
`wch_in_wms` varchar(50) NULL,
`wch_rec_man` varchar(50) NULL,
`wch_remark` longtext NULL,
`wch_checkIn` varchar(50) NULL,
`wch_checkOut` varchar(50) NULL,
`wch_out_date` datetime NULL,
`wch_isdel` char(1) NULL,
`wch_in_time` datetime NULL,
`wch_out_time` datetime NULL,
`wch_inp_name` varchar(50) NULL,
`wch_inp_date` datetime NULL,
`wch_alt_name` varchar(50) NULL,
`wch_alt_date` datetime NULL,
`wch_app_date` datetime NULL,
`wch_app_man` varchar(50) NULL,
`wch_app_desc` longtext NULL,
`wch_app_isok` char(1) NULL,
`wch_mat_name` varchar(50) NULL,
`wch_can_date` datetime NULL,
`wch_can_man` varchar(50) NULL,
PRIMARY KEY (`wch_id`)
)
CREATE TABLE `attachment`(
`att_id` bigint NOT NULL AUTO_INCREMENT,
`att_name` longtext NULL,
`att_size` bigint NULL,
`att_path` longtext NULL,
`att_isJunk` char(1) NULL,
`att_date` datetime NULL,
`att_type` varchar(100) NULL,
`att_fk_id` bigint NULL,
`att_file_type` varchar(100) NULL,
PRIMARY KEY (`att_id`)
)
CREATE TABLE `lim_function`(
`fun_code` varchar(50) NOT NULL,
`fun_desc` longtext NULL,
`fun_type` varchar(50) NULL,
PRIMARY KEY (`fun_code`)
)
CREATE TABLE `bank_case`(
`cas_id` bigint NOT NULL AUTO_INCREMENT,
`cas_code` varchar(100) NULL,
`cas_group` varchar(50) NULL,
`cas_state` int NULL,
`cas_typ_hid` bigint NULL,
`cas_out_state` int NULL,
`cas_cbat_id` bigint NULL,
`cas_m` decimal(18,2) NULL,
`cas_ptp_m` decimal(18,2) NULL,
`cas_cp_m` decimal(18,2) NULL,
`cas_paid_m` decimal(18,2) NULL,
`cas_date` datetime NULL,
`cas_typ_bid` bigint NULL,
`cas_name` varchar(50) NULL,
`cas_sex` varchar(1) NULL,
`cas_ca_cd` varchar(50) NULL,
`cas_num` varchar(50) NULL,
`cas_area_1`varchar(50) NULL,
`cas_area_2`varchar(50) NULL,
`cas_area_3`varchar(50) NULL,
`cas_post_code` varchar(50) NULL,
`cas_se_no` bigint NULL,
`cas_ins_user` varchar(25) NULL,
`cas_ins_time` datetime NULL,
`cas_alt_user` varchar(25) NULL,
`cas_alt_time` datetime NULL,
`cas_tremark` varchar(300) NULL,
`cas_warn` varchar(300) NULL,
`cas_acc_num` varchar(100) NULL,
`cas_card_cat` varchar(200) NULL,
`cas_principal` varchar(200) NULL,
`cas_min_paid` varchar(200) NULL,
`cas_cred_lim` varchar(200) NULL,
`cas_delay_lv` varchar(200) NULL,
`cas_gua_m` varchar(200) NULL,
`cas_m_cat` varchar(200) NULL,
`cas_pre_rec` longtext NULL,
`cas_exc_lim` varchar(200) NULL,
`cas_unit_name` varchar(200) NULL,
`cas_m_p` `double` NULL,
`cas_name_1` varchar(50) NULL,
`cas_name_2` varchar(50) NULL,
`cas_name_3` varchar(50) NULL,
`cas_num_1` varchar(200) NULL,
`cas_num_2` varchar(200) NULL,
`cas_num_3` varchar(200) NULL,
`cas_re_1` varchar(200) NULL,
`cas_re_2` varchar(200) NULL,
`cas_re_3` varchar(200) NULL,
`cas_con_com1` varchar(200) NULL,
`cas_pr_time` datetime NULL,
`cas_remark` longtext NULL,
`cas_con_com2` varchar(200) NULL,
`cas_con_com3` varchar(200) NULL,
`cas_app_1` int NULL,
`cas_app_2` int NULL,
`cas_app_3` int NULL,
`cas_app_4` int NULL,
`cas_app_5` int NULL,
`cas_app_6` int NULL,
`cas_app_7` int NULL,
`cas_app_8` int NULL,
`cas_app_9` int NULL,
`cas_app_10` int NULL,
`cas_app_11` int NULL,
`cas_app_12` int NULL,
`cas_app_13` int NULL,
`cas_app_14` int NULL,
`cas_remark2` longtext NULL,
`cas_remark3` longtext NULL,
`cas_remark4` longtext NULL,
`cas_ptp_c` int NULL,
`cas_remark5` longtext NULL,
`cas_card_bank` varchar(200) NULL,
`cas_tip_time` datetime NULL,
`cas_hom_pho` varchar(50) NULL,
`cas_work_pho` varchar(50) NULL,
`cas_mob_pho` varchar(50) NULL,
`cas_hom_add` longtext NULL,
`cas_work_add` longtext NULL,
`cas_mail_add` longtext NULL,
`cas_reg_add` longtext NULL,
`cas_con_pho1` varchar(50) NULL,
`cas_con_mob1` varchar(50) NULL,
`cas_con_add1` longtext NULL,
`cas_con_pho2` varchar(50) NULL,
`cas_con_mob2` varchar(50) NULL,
`cas_con_add2` longtext NULL,
`cas_loan_type` varchar(200) NULL,
`cas_coll_type` varchar(200) NULL,
`cas_int` varchar(200) NULL,
`cas_overdue_paid` varchar(200) NULL,
`cas_cre_paid` varchar(200) NULL,
`cas_paid_lim` varchar(200) NULL,
`cas_paid_date` varchar(200) NULL,
`cas_con_date` varchar(200) NULL,
`cas_rai_date` varchar(200) NULL,
`cas_stop_date` varchar(200) NULL,
`cas_cre_date` varchar(200) NULL,
`cas_remark6` longtext NULL,
`cas_note` longtext NULL,
`cas_con_pho3` varchar(50) NULL,
`cas_con_mob3` varchar(50) NULL,
`cas_con_add3` longtext NULL,
`cas_con_pho4` varchar(50) NULL,
`cas_con_mob4` varchar(50) NULL,
`cas_con_add4` longtext NULL,
`cas_name_4` varchar(50) NULL,
`cas_num_4` varchar(200) NULL,
`cas_re_4` varchar(200) NULL,
`cas_con_com4` varchar(200) NULL,
`cas_file_no` varchar(100) NULL,
`cas_remark7` longtext NULL,
`cas_remark8` longtext NULL,
`cas_email` varchar(100) NULL,
`cas_is_oth` int NULL,
`cas_is_newpr` int NULL,
`cas_is_newpaid` int NULL,
`cas_is_paidover` int NULL,
`cas_is_updint` int NULL,
`cas_rmb` varchar(100) NULL,
`cas_gb` varchar(100) NULL,
`cas_my` varchar(100) NULL,
`cas_pos` varchar(200) NULL,
`cas_part` varchar(200) NULL,
`cas_backdate_p` datetime NULL,
`cas_backdate` datetime NULL,
`cas_back_p` `double` NULL,
`cas_con_wpho1` varchar(50) NULL,
`cas_con_wpho2` varchar(50) NULL,
`cas_con_wpho3` varchar(50) NULL,
`cas_con_wpho4` varchar(50) NULL,
`cas_name_u` varchar(50) NULL,
`cas_num_u` varchar(200) NULL,
`cas_re_u` varchar(200) NULL,
`cas_con_u_com` varchar(200) NULL,
`cas_con_u_wpho` varchar(50) NULL,
`cas_con_u_pho` varchar(50) NULL,
`cas_con_u_mob` varchar(50) NULL,
`cas_con_u_add` longtext NULL,
`cas_back_m` decimal(18,2) NULL,
`cas_name_5` varchar(50) NULL,
`cas_num_5` varchar(200) NULL,
`cas_re_5` varchar(200) NULL,
`cas_con_com_5` varchar(200) NULL,
`cas_con_wpho_5` varchar(50) NULL,
`cas_con_pho_5` varchar(50) NULL,
`cas_con_mob_5` varchar(50) NULL,
`cas_con_add_5` longtext NULL,
`cas_loan_date` varchar(200) NULL,
`cas_app_no` varchar(100) NULL,
`cas_paid_count` varchar(100) NULL,
`cas_so_pcno` varchar(100) NULL,
`cas_so_no` varchar(100) NULL,
`cas_overdue_date` varchar(200) NULL,
`cas_pback_p` `double` NULL,
`cas_wpost_code` varchar(50) NULL,
`cas_deadline` varchar(200) NULL,
`cas_is_host` varchar(50) NULL,
`cas_bill_date` varchar(200) NULL,
`cas_last_paid` varchar(200) NULL,
`cas_count` varchar(100) NULL,
`cas_left_pri` varchar(100) NULL,
`cas_assign_ids` longtext NULL,
`cas_assign_names` longtext NULL,
`cas_last_assign_time` datetime NULL,
`cas_overdue_days` int NULL,
`cas_overdue_days_str` varchar(200) NULL,
`cas_bir` varchar(50) NULL,
`cas_mpost_code` varchar(50) NULL,
`cas_perm_crline` varchar(50) NULL,
`cas_alt_hold` varchar(50) NULL,
`cas_cycle` varchar(50) NULL,
`cas_noout` varchar(50) NULL,
`cas_field_type` varchar(50) NULL,
`cas_cl_area_id` bigint NULL,
`cas_pr_count` int NULL,
`cas_overdue_m` varchar(200) NULL,
`cas_overdue_num` varchar(200) NULL,
`cas_overdue_once` int NULL,
`cas_loan_rate` varchar(200) NULL,
`cas_month_paid` varchar(200) NULL,
`cas_last_vis` datetime NULL,
`cas_fst_cl_paid_date` datetime NULL,
`cas_last_cl_paid_date` datetime NULL,
`cas_color` int NULL,
`cas_cc_id` bigint NULL,
`cas_is_newass` int NULL,
`cas_reg_post_code` varchar(50) NULL,
`cas_last_m` decimal(18,2) NULL,
`cas_last_int_date` datetime NULL,
`cas_loan_end_date` varchar(200) NULL,
`cas_over_limit` varchar(200) NULL,
`cas_num_type` varchar(50) NULL,
`cas_last_end_date` varchar(100) NULL,
`cas_assign_times` longtext NULL,
`cas_cl_count` varchar(100) NULL,
PRIMARY KEY (`cas_id`)
)
create index IX_cnum on bank_case(cas_num) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
create index IX_cacd on bank_case(cas_ca_cd);
create index IX_bank on bank_case(cas_typ_bid);
create index IX_cl_area on bank_case(cas_cl_area_id);
create index IX_bat on bank_case(cas_cbat_id);
create index IX_file_no on bank_case(cas_file_no);
create index IX_acc_date on bank_case(cas_acc_num, cas_date);
create index IX_cacd_date on bank_case(cas_ca_cd, cas_date);
DROP TABLE IF EXISTS `oth_card`;
CREATE TABLE `oth_card`(
`ocd_id` bigint NOT NULL AUTO_INCREMENT,
`ocd_cas_id` bigint NULL,
`ocd_area` varchar(50) NULL,
`ocd_acct` varchar(50) NULL,
`ocd_id_no` varchar(50) NULL,
`ocd_card` varchar(50) NULL,
`ocd_name` varchar(50) NULL,
`ocd_h_pho` varchar(50) NULL,
`ocd_o_pho` varchar(50) NULL,
`ocd_addr` varchar(200) NULL,
`ocd_employer` varchar(100) NULL,
`ocd_alt_name` varchar(50) NULL,
`ocd_alt_h_pho` varchar(50) NULL,
`ocd_alt_o_pho` varchar(50) NULL,
`ocd_con_name` varchar(50) NULL,
`ocd_con_pho` varchar(50) NULL,
`ocd_bir` varchar(50) NULL,
`ocd_r_addr` varchar(200) NULL,
`ocd_cyc` varchar(50) NULL,
`ocd_blk` varchar(50) NULL,
`ocd_m_post` varchar(50) NULL,
`ocd_msg` varchar(200) NULL,
`ocd_cre_man` varchar(50) NULL,
`ocd_cre_time` datetime NULL,
`ocd_upd_man` varchar(50) NULL,
`ocd_upd_time` datetime NULL,
PRIMARY KEY (`ocd_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
DROP TABLE IF EXISTS `pa_bank_case`;
CREATE TABLE `pa_bank_case`(
`cas_id` bigint NOT NULL,
`cas_policy_man` varchar(50) NULL,
`cas_day_paid` varchar(100) NULL,
`cas_cla_date` varchar(100) NULL,
`cas_cla_m` varchar(100) NULL,
`cas_eff_date` varchar(100) NULL,
`cas_premiums` varchar(100) NULL,
`cas_manage_cost` varchar(100) NULL,
`cas_penalty` varchar(100) NULL,
`cas_fail_cost` varchar(100) NULL,
`cas_is_wd` varchar(100) NULL,
`cas_acc_name` varchar(100) NULL,
`cas_house_type1` varchar(100) NULL,
`cas_house_own1` varchar(100) NULL,
`cas_hfax1` varchar(100) NULL,
`cas_addr2` varchar(500) NULL,
`cas_house_type2` varchar(100) NULL,
`cas_house_own2` varchar(100) NULL,
`cas_pho2` varchar(100) NULL,
`cas_hfax2` varchar(100) NULL,
`cas_com2` varchar(200) NULL,
`cas_com2_addr` varchar(500) NULL,
`cas_wpho2` varchar(100) NULL,
`cas_fax1` varchar(100) NULL,
`cas_fax2` varchar(100) NULL,
`cas_com_type` varchar(50) NULL,
`cas_com_date` varchar(100) NULL,
`cas_tax_no` varchar(100) NULL,
`cas_com_no` varchar(100) NULL,
`cas_con_mob4b` varchar(100) NULL,
`cas_con_wpho4` varchar(100) NULL,
`cas_con_mob1b` varchar(100) NULL,
`cas_con_mob2b` varchar(100) NULL,
`cas_con_mob3b` varchar(100) NULL,
PRIMARY KEY (`cas_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
DROP TABLE IF EXISTS `js_bank_case`;
CREATE TABLE `js_bank_case`(
`cas_id` bigint NOT NULL,
`jsc_overdue_p` varchar(200) NULL,
`jsc_un_overdue_p` varchar(200) NULL,
`jsc_man` varchar(100) NULL,
`jsc_cd` varchar(200) NULL,
`jsc_is_married` varchar(50) NULL,
`jsc_oth_pho` varchar(200) NULL,
`jsc_ide` varchar(100) NULL,
`jsc_home_prop` varchar(100) NULL,
`jsc_live_with` varchar(100) NULL,
`jsc_haddr_able` varchar(200) NULL,
`jsc_waddr_able` varchar(200) NULL,
`jsc_com_prop` varchar(100) NULL,
`jsc_age1` varchar(50) NULL,
`jsc_pos1` varchar(200) NULL,
`jsc_age2` varchar(50) NULL,
`jsc_pos2` varchar(200) NULL,
`jsc_age3` varchar(50) NULL,
`jsc_pos3` varchar(200) NULL,
`jsc_cost` varchar(200) NULL,
`jsc_posu` varchar(200) NULL,
PRIMARY KEY (`cas_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `project`(
`pro_id` bigint NOT NULL AUTO_INCREMENT,
`pro_user_code` varchar(50) NULL,
`pro_typ_id` bigint NULL,
`pro_title` varchar(300) NULL,
`pro_state` varchar(50) NULL,
`pro_cre_date` datetime NULL,
`pro_fin_date` datetime NULL,
`pro_desc` longtext NULL,
`pro_remark` longtext NULL,
`pro_inp_user` varchar(50) NULL,
`pro_upd_user` varchar(50) NULL,
`pro_ins_date` datetime NULL,
`pro_mod_date` datetime NULL,
`pro_isdel` char(1) NULL,
`pro_cor_code` bigint NULL,
`pro_period` varchar(50) NULL,
`pro_pro` varchar(300) NULL,
`pro_pro_log` longtext NULL,
PRIMARY KEY (`pro_id`)
)
CREATE TABLE `wms_war_out`(
`wwo_id` bigint NOT NULL AUTO_INCREMENT,
`wwo_ord_code` bigint NULL,
`wwo_title` longtext NULL,
`wwo_stro_code` varchar(50) NULL,
`wwo_user_code` varchar(50) NULL,
`wwo_inp_date` datetime NULL,
`wwo_out_date` datetime NULL,
`wwo_state` char(1) NULL,
`wwo_remark` longtext NULL,
`wwo_isdel` char(1) NULL,
`wwo_inp_name` varchar(50) NULL,
`wwo_alt_name` varchar(50) NULL,
`wwo_user_name` varchar(50) NULL,
`wwo_res_name` varchar(50) NULL,
`wwo_alt_date` datetime NULL,
`wwo_code` varchar(50) NULL,
`wwo_app_isok` char(1) NULL,
`wwo_app_date` datetime NULL,
`wwo_app_man` varchar(50) NULL,
`wwo_app_desc` longtext NULL,
`wwo_can_date` datetime NULL,
`wwo_can_man` varchar(50) NULL,
PRIMARY KEY (`wwo_id`)
)
CREATE TABLE `r_wms_change`(
`rwc_id` bigint NOT NULL AUTO_INCREMENT,
`rwc_pro_id` bigint NULL,
`rwc_different` decimal(18,2) NULL,
`rmc_type` varchar(50) NULL,
`rmc_remark` longtext NULL,
`rwc_wmc_code` bigint NULL,
`rmc_wms_count` decimal(18,2) NULL,
`rmc_real_num` decimal(18,2) NULL,
PRIMARY KEY (`rwc_id`)
)
CREATE TABLE `r_user_rig`(
`rur_id` bigint NOT NULL AUTO_INCREMENT,
`rur_user_code` varchar(50) NULL,
`rur_rig_code` varchar(50) NULL,
`rur_type` varchar(50) NULL,
PRIMARY KEY (`rur_id`)
)
CREATE TABLE `case_bat`(
`cbat_id` bigint NOT NULL AUTO_INCREMENT,
`cbat_code` varchar(50) NULL,
`cbat_typ_bid` bigint NULL,
`cbat_date` datetime NULL,
`cbat_type_id` bigint NULL,
`cbat_backdate_p` datetime NULL,
`cbat_backdate` datetime NULL,
`cbat_ins_user` varchar(25) NULL,
`cbat_ins_date` datetime NULL,
`cbat_state` int NULL,
`cbat_num` int NULL,
`cbat_mon` decimal(18,2) NULL,
`cbat_log` longtext NULL,
`cbat_xls` longtext NULL,
`cbat_up_date` datetime NULL,
`cbat_remark` longtext NULL,
`cbat_tips` varchar(200) NULL,
`cbat_area_id` bigint NULL,
`cbat_target` `double` NULL,
PRIMARY KEY (`cbat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
;
DROP TABLE IF EXISTS `extra_inf`;
CREATE TABLE `extra_inf`(
`exi_id` bigint NOT NULL AUTO_INCREMENT,
`exi_id_number` varchar(50) NULL,
`exi_type` varchar(50) NULL,
`exi_content` text NULL,
`exi_cre_time` datetime NULL,
`exi_cre_man` varchar(25) NULL,
`exi_upd_time` datetime NULL,
`exi_upd_man` varchar(25) NULL,
`exi_name` varchar(50) NULL,
`exi_remark` text NULL,
PRIMARY KEY (`exi_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

TEXTIMAGE_ON `PRIMARY`
;
DROP TABLE IF EXISTS `case_collection`;
CREATE TABLE `case_collection`(
`cc_id` bigint NOT NULL AUTO_INCREMENT,
`cc_cas_ids` longtext NULL,
`cc_cbat_id` bigint NULL,
`cc_id_no` varchar(50) NULL,
PRIMARY KEY (`cc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
DROP TABLE IF EXISTS `user_area`;
CREATE TABLE `user_area`(
`uar_id` bigint NOT NULL AUTO_INCREMENT,
`uar_user_code` varchar(50) NULL,
`uar_area_id` bigint NULL,
PRIMARY KEY (`uar_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `pro_stage`(
`sta_id` bigint NOT NULL AUTO_INCREMENT,
`sta_pro_id` bigint NULL,
`sta_title` varchar(300) NULL,
`sta_aim` varchar(300) NULL,
`sta_start_date` datetime NULL,
`sta_end_date` datetime NULL,
`sta_remark` longtext NULL,
`sta_ins_date` datetime NULL,
`sta_mod_date` datetime NULL,
`sta_isdel` char(1) NULL,
`sta_inp_user` varchar(50) NULL,
`sta_upd_user` varchar(50) NULL,
PRIMARY KEY (`sta_id`)
)
CREATE TABLE `sal_org`(
`so_code` varchar(50) NOT NULL,
`so_name` varchar(50) NULL,
`so_con_area` longtext NULL,
`so_loc` longtext NULL,
`so_user_code` varchar(50) NULL,
`so_emp_num` varchar(50) NULL,
`so_resp` longtext NULL,
`so_org_code` varchar(50) NULL,
`so_remark` longtext NULL,
`so_isenabled` char(1) NULL,
`so_up_code` varchar(50) NULL,
`so_cost_center` varchar(100) NULL,
`so_org_nature` varchar(100) NULL,
PRIMARY KEY (`so_code`)
)
CREATE TABLE `r_ord_pro`(
`rop_id` bigint NOT NULL AUTO_INCREMENT,
`rop_ord_code` bigint NULL,
`rop_pro_id` bigint NULL,
`rop_num` decimal(18,2) NULL,
`rop_real_price` decimal(18,2) NULL,
`rop_remark` longtext NULL,
`rop_price` decimal(18,2) NULL,
`rop_zk` varchar(50) NULL,
`rop_out_num` decimal(18,2) NULL,
`rop_real_num` decimal(18,2) NULL,
PRIMARY KEY (`rop_id`)
)
CREATE TABLE `message`(
`me_code` bigint NOT NULL AUTO_INCREMENT,
`me_title` varchar(100) NULL,
`me_content` longtext NULL,
`me_se_no` bigint NULL,
`me_date` datetime NULL,
`me_issend` char(1) NULL,
`me_isdel` char(1) NULL,
`me_ins_user` varchar(50) NULL,
`me_rec_name` longtext NULL,
PRIMARY KEY (`me_code`)
)
CREATE TABLE `lock_table`(
`table_name` varchar(50) NOT NULL,
`table_max` bigint NULL,
PRIMARY KEY (`table_name`)
)
CREATE TABLE `r_mess_lim`(
`rml_id` bigint NOT NULL AUTO_INCREMENT,
`rml_me_code` bigint NULL,
`rml_se_no` bigint NULL,
`rml_date` datetime NULL,
`rml_isdel` char(1) NULL,
`rml_isread` char(1) NULL,
`rml_isreply` char(1) NULL,
`rml_rec_user` varchar(50) NULL,
`rml_state` char(1) NULL,
PRIMARY KEY (`rml_id`)
)
CREATE TABLE `wms_pro_type`(
`wpt_id` bigint NOT NULL AUTO_INCREMENT,
`wpt_name` varchar(50) NULL,
`wpt_desc` longtext NULL,
`wpt_isenabled` varchar(10) NULL,
`wpt_up_id` bigint NULL,
PRIMARY KEY (`wpt_id`)
)
CREATE TABLE `news`(
`new_code` bigint NOT NULL AUTO_INCREMENT,
`new_title` varchar(100) NULL,
`new_type` varchar(100) NULL,
`new_se_no` bigint NULL,
`new_content` longtext NULL,
`new_istop` char(1) NULL,
`new_date` datetime NULL,
`new_ins_user` varchar(50) NULL,
`new_upd_user` varchar(50) NULL,
`new_upd_date` datetime NULL,
PRIMARY KEY (`new_code`)
)
CREATE TABLE `lim_operate`(
`ope_code` varchar(50) NOT NULL,
`ope_desc` longtext NULL,
PRIMARY KEY (`ope_code`)
)
CREATE TABLE `schedule`(
`sch_id` bigint NOT NULL AUTO_INCREMENT,
`sch_type` bigint NULL,
`sch_title` varchar(100) NULL,
`sch_start_date` datetime NULL,
`sch_se_no` bigint NULL,
`sch_start_time` varchar(50) NULL,
`sch_date` datetime NULL,
`sch_state` varchar(50) NULL,
`sch_end_time` varchar(50) NULL,
`sch_ins_user` varchar(50) NULL,
`sch_upd_user` varchar(50) NULL,
`sch_upd_date` datetime NULL,
PRIMARY KEY (`sch_id`)
)
CREATE TABLE `sal_invoice`(
`sin_id` bigint NOT NULL AUTO_INCREMENT,
`sin_ord_code` bigint NULL,
`sin_con` longtext NULL,
`sin_type` bigint NULL,
`sin_mon` decimal(18,2) NULL,
`sin_date` datetime NULL,
`sin_remark` longtext NULL,
`sin_code` varchar(100) NULL,
`sin_isPaid` char(1) NULL,
`sin_isPlaned` varchar(50) NULL,
`sin_user_code` varchar(50) NULL,
`sin_resp` varchar(50) NULL,
`sin_mon_type` varchar(50) NULL,
`sin_alt_user` varchar(50) NULL,
`sin_cre_date` datetime NULL,
`sin_alt_date` datetime NULL,
`sin_isdel` char(1) NULL,
`sin_spo_id` bigint NULL,
`sin_isrecieve` char(1) NULL,
PRIMARY KEY (`sin_id`)
)
CREATE TABLE `report`(
`rep_code` bigint NOT NULL AUTO_INCREMENT,
`rep_title` varchar(100) NULL,
`rep_content` longtext NULL,
`rep_se_no` bigint NULL,
`rep_appro_content` longtext NULL,
`rep_isappro` char(1) NULL,
`rep_date` datetime NULL,
`rep_type` bigint NULL,
`rep_isdel` char(1) NULL,
`rep_issend` char(1) NULL,
`rep_send_title` varchar(100) NULL,
`rep_ins_user` varchar(50) NULL,
`rep_rec_name` longtext NULL,
PRIMARY KEY (`rep_code`)
)
CREATE TABLE `acc_trans`(
`atr_id` bigint NOT NULL AUTO_INCREMENT,
`atr_code` varchar(50) NULL,
`atr_date` datetime NULL,
`atr_mon` decimal(18,2) NULL,
`atr_type_id` bigint NULL,
`atr_in_aco` bigint NULL,
`atr_out_aco` bigint NULL,
`atr_remark` longtext NULL,
`atr_isdel` char(1) NULL,
`atr_inp_user` varchar(50) NULL,
`atr_cre_date` datetime NULL,
`atr_undo_user` varchar(50) NULL,
`atr_undo_date` datetime NULL,
`atr_content` varchar(100) NULL,
PRIMARY KEY (`atr_id`)
)
CREATE TABLE `acc_line`(
`acl_id` bigint NOT NULL AUTO_INCREMENT,
`acl_aco_id` bigint NULL,
`acl_type` varchar(100) NULL,
`acl_note_id` varchar(300) NULL,
`acl_mon` decimal(18,2) NULL,
`acl_cur_mon` decimal(18,2) NULL,
`acl_cre_date` datetime NULL,
`acl_isInv` char(1) NULL,
`acl_content` varchar(100) NULL,
`acl_user` varchar(50) NULL,
`acl_other` varchar(100) NULL,
PRIMARY KEY (`acl_id`)
)
CREATE TABLE `vis_record`(
`vr_id` bigint NOT NULL AUTO_INCREMENT,
`vr_state` int NULL,
`vr_adr_id` bigint NULL,
`vr_cas_id` bigint NULL,
`vr_num` int NULL,
`vr_typ_id1` bigint NULL,
`vr_typ_id2` bigint NULL,
`vr_typ_id3` bigint NULL,
`vr_typ_id` bigint NULL,
`vr_name` varchar(50) NULL,
`vr_sex` varchar(1) NULL,
`vr_age` int NULL,
`vr_req` longtext NULL,
`vr_remark` longtext NULL,
`vr_report` longtext NULL,
`vr_est_date` datetime NULL,
`vr_rel_date` datetime NULL,
`vr_app_user` varchar(25) NULL,
`vr_app_time` datetime NULL,
`vr_bk_time` datetime NULL,
`vr_rec_user` longtext NULL,
`vr_adr` varchar(200) NULL,
`vr_rs` varchar(50) NULL,
`vr_is_prt` char(1) NULL,
PRIMARY KEY (`vr_id`)
)
CREATE TABLE `sal_task`(
`st_id` bigint NOT NULL AUTO_INCREMENT,
`st_title` varchar(200) NULL,
`st_se_no` bigint NULL,
`st_name` varchar(50) NULL,
`st_rel_date` datetime NULL,
`st_fin_date` datetime NULL,
`st_lev` varchar(50) NULL,
`st_cyc` varchar(50) NULL,
`st_type_id` bigint NULL,
`st_stu` char(1) NULL,
`st_mon` decimal(18,2) NULL,
`st_tag` longtext NULL,
`st_remark` longtext NULL,
`st_change_date` datetime NULL,
`st_log` longtext NULL,
`st_isdel` char(1) NULL,
`st_fct_date` datetime NULL,
`st_upd_user` varchar(50) NULL,
`st_start_date` datetime NULL,
PRIMARY KEY (`st_id`)
)
CREATE TABLE `vis_rec_ass`(
`vra_id` bigint NOT NULL AUTO_INCREMENT,
`vra_vr_id` bigint NULL,
`vra_user_code` varchar(50) NULL,
PRIMARY KEY (`vra_id`)
)
CREATE TABLE `acc_lock`(
`table_name` varchar(50) NOT NULL,
`table_max` bigint NULL,
PRIMARY KEY (`table_name`)
)
CREATE TABLE `comment`(
`cot_id` bigint NOT NULL AUTO_INCREMENT,
`cot_content` longtext NULL,
`cot_cas_id` bigint NULL,
`cot_user` varchar(25) NULL,
`cot_time` datetime NULL,
`cot_state` int NULL,
PRIMARY KEY (`cot_id`)
)
CREATE TABLE `ta_lim`(
`ta_lim_id` bigint NOT NULL AUTO_INCREMENT,
`ta_se_no` bigint NULL,
`ta_isdel` char(1) NULL,
`ta_task_id` bigint NULL,
`ta_fin_date` datetime NULL,
`ta_isfin` char(1) NULL,
`ta_desc` longtext NULL,
`ta_name` varchar(50) NULL,
PRIMARY KEY (`ta_lim_id`)
)
CREATE TABLE `phone_list`(
`phl_id` bigint NOT NULL AUTO_INCREMENT,
`phl_state` int NULL,
`phl_name` varchar(50) NULL,
`phl_num` varchar(50) NULL,
`phl_cas_id` bigint NULL,
`phl_cat` varchar(50) NULL,
`phl_count` int NULL,
`phl_remark` longtext NULL,
`phl_isdel` char(1) NULL,
`phl_isnew` int NULL,
`phl_upd_time` datetime NULL,
`phl_rel` varchar(50) NULL,
PRIMARY KEY (`phl_id`)
)
CREATE TABLE `r_inq_pro`(
`rqp_id` bigint NOT NULL AUTO_INCREMENT,
`rqp_inq_id` bigint NULL,
`rqp_wpr_id` bigint NULL,
`rqp_num` decimal(18,2) NULL,
`rqp_price` decimal(18,2) NULL,
`rqp_all_price` decimal(18,2) NULL,
`rqp_remark` longtext NULL,
PRIMARY KEY (`rqp_id`)
)
CREATE TABLE `case_hp`(
`ch_id` bigint NOT NULL AUTO_INCREMENT,
`ch_chk_state` int NULL,
`ch_typ` int NULL,
`ch_text` longtext NULL,
`ch_cat_1` varchar(20) NULL,
`ch_cat_2` varchar(20) NULL,
`ch_adr_id` bigint NULL,
`ch_msg_state` int NULL,
`ch_cas_id` bigint NULL,
`ch_res` longtext NULL,
`ch_app_user` varchar(20) NULL,
`ch_sur_user` varchar(20) NULL,
`ch_app_time` datetime NULL,
`ch_sur_time` datetime NULL,
`ch_remark` longtext NULL,
`ch_cont_user` varchar(20) NULL,
`ch_adr` varchar(1000) NULL,
`ch_count` int NULL,
`ch_upd_time` datetime NULL,
`ch_upd_man` varchar(25) NULL,
PRIMARY KEY (`ch_id`)
)
CREATE TABLE `lim_role`(
`rol_id` bigint NOT NULL AUTO_INCREMENT,
`rol_name` varchar(50) NULL,
`rol_lev` int NULL,
`rol_desc` longtext NULL,
`rol_grp_id` bigint,
PRIMARY KEY (`rol_id`)
)
CREATE TABLE `r_quo_pro`(
`rup_id` bigint NOT NULL AUTO_INCREMENT,
`rup_quo_id` bigint NULL,
`rup_wpr_id` bigint NULL,
`rup_num` decimal(18,2) NULL,
`rup_price` decimal(18,2) NULL,
`rup_all_price` decimal(18,2) NULL,
`rup_remark` longtext NULL,
PRIMARY KEY (`rup_id`)
)
CREATE TABLE `sal_paid_past`(
`sps_id` bigint NOT NULL AUTO_INCREMENT,
`sps_ord_code` bigint NULL,
`sps_fct_date` datetime NULL,
`sps_count` int NULL,
`sps_type_id` bigint NULL,
`sps_pay_type` varchar(50) NULL,
`sps_pay_mon` decimal(18,2) NULL,
`sps_mon_type` varchar(50) NULL,
`sps_user_code` varchar(50) NULL,
`sps_se_no` bigint NULL,
`sps_isinv` char(1) NULL,
`sps_remark` longtext NULL,
`sps_alt_user` varchar(50) NULL,
`sps_cre_date` datetime NULL,
`sps_alt_date` datetime NULL,
`sps_isdel` char(1) NULL,
`sps_code` varchar(300) NULL,
`sps_aco_id` bigint NULL,
`sps_out_name` varchar(100) NULL,
`sps_content` varchar(100) NULL,
`sps_acc_type_id` bigint NULL,
`sps_undo_date` datetime NULL,
`sps_undo_user` varchar(50) NULL,
`sps_cus_id` bigint NULL,
PRIMARY KEY (`sps_id`)
)
CREATE TABLE `case_paid`(
`pa_id` bigint NOT NULL AUTO_INCREMENT,
`pa_state` int NULL,
`pa_cas_id` bigint NULL,
`pa_ptp_d` datetime NULL,
`pa_ptp_num` decimal(18,2) NULL,
`pa_cp_time` datetime NULL,
`pa_cp_num` decimal(18,2) NULL,
`pa_comt_user` varchar(25) NULL,
`pa_comt_time` datetime NULL,
`pa_paid_time` datetime NULL,
`pa_paid_num` decimal(18,2) NULL,
`pa_sur_user` varchar(25) NULL,
`pa_sur_time` datetime NULL,
`pa_sur_remark` longtext NULL,
`pa_writer` varchar(25) NULL,
`pa_wri_time` datetime NULL,
`pa_alt_user` varchar(25) NULL,
`pa_alt_time` datetime NULL,
`pa_del_user` varchar(25) NULL,
`pa_del_time` datetime NULL,
`pa_m_paid` decimal(18,2) NULL,
`pa_cpm_paid` decimal(18,2) NULL,
`pa_se_no` bigint NULL,
`pa_cm_paid` decimal(18,2) NULL,
`pa_back_paid` decimal(18,2) NULL,
`pa_pback_paid` decimal(18,2) NULL,
`pa_last_debt_m` decimal(18,2) NULL,
`pa_left_amt` decimal(18,2) NULL,
PRIMARY KEY (`pa_id`)
)
CREATE TABLE `sal_pur_ord`(
`spo_id` bigint NOT NULL AUTO_INCREMENT,
`spo_til` varchar(300) NULL,
`spo_code` varchar(300) NULL,
`spo_con_date` datetime NULL,
`spo_sup_id` bigint NULL,
`spo_type_id` bigint NULL,
`spo_proj_id` bigint NULL,
`spo_sum_mon` decimal(18,2) NULL,
`spo_paid_mon` decimal(18,2) NULL,
`spo_user_code` varchar(50) NULL,
`spo_content` longtext NULL,
`spo_isend` char(1) NULL,
`spo_isdel` char(1) NULL,
`spo_remark` longtext NULL,
`spo_inp_user` varchar(50) NULL,
`spo_cre_date` datetime NULL,
`spo_alt_date` datetime NULL,
`spo_alt_user` varchar(50) NULL,
`spo_app_date` datetime NULL,
`spo_app_man` varchar(50) NULL,
`spo_app_desc` longtext NULL,
`spo_app_isok` char(1) NULL,
`spo_se_no` bigint NULL,
PRIMARY KEY (`spo_id`)
)
CREATE TABLE `hurr_rec`(
`hur_id` bigint NOT NULL AUTO_INCREMENT,
`hur_cat` varchar(50) NULL,
`hur_cas_id` bigint NULL,
`hur_oper` varchar(25) NULL,
`hur_op_time` datetime NULL,
`hur_op_cont` longtext NULL,
`hur_re_id` bigint NULL,
PRIMARY KEY (`hur_id`)
)
CREATE TABLE `wms_check`(
`wmc_id` bigint NOT NULL AUTO_INCREMENT,
`wmc_code` varchar(50) NULL,
`wmc_title` longtext NULL,
`wmc_stro_code` varchar(50) NULL,
`wmc_user_code` varchar(50) NULL,
`wmc_date` datetime NULL,
`wmc_state` char(1) NULL,
`wmc_remark` longtext NULL,
`wmc_isdel` char(1) NULL,
`wmc_inp_name` varchar(50) NULL,
`wmc_alt_name` varchar(50) NULL,
`wmc_inp_date` datetime NULL,
`wmc_alt_date` datetime NULL,
`wmc_app_date` datetime NULL,
`wmc_app_man` varchar(50) NULL,
`wmc_app_isok` char(1) NULL,
`wmc_app_desc` longtext NULL,
`wmc_can_date` datetime NULL,
`wmc_can_man` varchar(50) NULL,
PRIMARY KEY (`wmc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
DROP TABLE IF EXISTS `loan_season`;
CREATE TABLE `loan_season`(
`lse_id` bigint NOT NULL AUTO_INCREMENT,
`lse_name` varchar(50) NULL,
PRIMARY KEY (`lse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `loan_cus`;
CREATE TABLE `loan_cus`(
`lc_id` bigint NOT NULL AUTO_INCREMENT,
`lc_name` varchar(50) NULL,
`lc_card_num` varchar(50) NULL,
`lc_card_type` varchar(20) NULL,
`lc_bank` varchar(100) NULL,
`lc_due_m` decimal(18,2) NULL CONSTRAINT `DF__bank_cus__bc_m__084C046C`  DEFAULT ((0.00)),
`lc_principal` decimal(18,2) NULL CONSTRAINT `DF__bank_cus__bc_pri__094028A5`  DEFAULT ((0.00)),
`lc_time_lim` varchar(50) NULL,
`lc_quality` varchar(20) NULL,
`lc_due_num` varchar(50) NULL,
`lc_overdue` varchar(50) NULL,
`lc_due_date` datetime NULL,
`lc_end_date` datetime NULL,
`lc_company` varchar(100) NULL,
`lc_com_addr` varchar(300) NULL,
`lc_com_pho` varchar(50) NULL,
`lc_home_addr` varchar(300) NULL,
`lc_home_pho` varchar(50) NULL,
`lc_manager` varchar(50) NULL,
`lc_num` varchar(50) NULL,
`lc_lse_id` bigint NULL,
`lc_remark` varchar(200) NULL,
`lc_ins_user` varchar(25) NULL,
`lc_ins_time` datetime NULL,
`lc_alt_user` varchar(25) NULL,
`lc_alt_time` datetime NULL,
`lc_risk_adv` varchar(500) NULL,
`lc_risk_per` varchar(500) NULL,
`lc_risk_com` varchar(500) NULL,
`lc_risk_biz` varchar(500) NULL,
`lc_risk_que` varchar(50) NULL,
`lc_op_state` varchar(200) NULL,
`lc_risk_rs` varchar(500) NULL,
PRIMARY KEY (`lc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
ALTER TABLE `loan_cus`  WITH CHECK ADD  CONSTRAINT `FK_loan_cus_loan_season` FOREIGN KEY(`lc_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
;
DROP TABLE IF EXISTS `loan_com`;
CREATE TABLE `loan_com`(
`lcm_id` bigint NOT NULL AUTO_INCREMENT,
`lcm_lse_id` bigint NULL,
`lcm_company` varchar(100) NULL,
`lcm_content` longtext NULL,
`lcm_ins_user` varchar(25) NULL,
`lcm_ins_time` datetime NULL,
`lcm_alt_user` varchar(25) NULL,
`lcm_alt_time` datetime NULL,
PRIMARY KEY (`lcm_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE `loan_com`  WITH CHECK ADD  CONSTRAINT `FK_loan_com_loan_season` FOREIGN KEY(`lcm_lse_id`)
REFERENCES `loan_season` (`lse_id`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
ON DELETE CASCADE
DROP TABLE IF EXISTS `loan_per`;
CREATE TABLE `loan_per`(
`lp_id` bigint NOT NULL AUTO_INCREMENT,
`lp_lse_id` bigint NULL,
`lp_card_num` varchar(50) NULL,
`lp_content` longtext NULL,
`lp_ins_user` varchar(25) NULL,
`lp_ins_time` datetime NULL,
`lp_alt_user` varchar(25) NULL,
`lp_alt_time` datetime NULL,
`lp_name` varchar(50) NULL,
PRIMARY KEY (`lp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
ALTER TABLE `loan_per`  WITH CHECK ADD  CONSTRAINT `FK_loan_per_loan_season` FOREIGN KEY(`lp_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
DROP TABLE IF EXISTS `loan_police`;
CREATE TABLE `loan_police`(
`lpol_id` bigint NOT NULL AUTO_INCREMENT,
`lpol_lse_id` bigint NULL,
`lpol_name` varchar(50) NULL,
`lpol_card_num` varchar(50) NULL,
`lpol_content` longtext NULL,
`lpol_ins_user` varchar(25) NULL,
`lpol_ins_time` datetime NULL,
`lpol_alt_user` varchar(25) NULL,
`lpol_alt_time` datetime NULL,
`lpol_card_type` varchar(20) NULL,
PRIMARY KEY (`lpol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
ALTER TABLE `loan_police`  WITH CHECK ADD  CONSTRAINT `FK_loan_police_loan_season` FOREIGN KEY(`lpol_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
DROP TABLE IF EXISTS `loan_reg_inf`;
CREATE TABLE `loan_reg_inf`(
`lreg_id` bigint NOT NULL AUTO_INCREMENT,
`lreg_company` varchar(100) NULL,
`lreg_content` longtext NULL,
`lreg_ins_user` varchar(25) NULL,
`lreg_ins_time` datetime NULL,
`lreg_alt_user` varchar(25) NULL,
`lreg_alt_time` datetime NULL,
`lreg_lse_id` bigint NULL,
`lreg_state` varchar(50) NULL,
`lreg_last_year` varchar(50) NULL,
`lreg_boss_name` varchar(500) NULL,
`lreg_ord_code` varchar(50) NULL,
`lreg_law_man` varchar(50) NULL,
PRIMARY KEY (`lreg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
ALTER TABLE `loan_reg_inf`  WITH CHECK ADD  CONSTRAINT `FK_loan_reg_inf_loan_season` FOREIGN KEY(`lreg_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
DROP TABLE IF EXISTS `loan_sd_rec`;
CREATE TABLE `loan_sd_rec`(
`lsd_id` bigint NOT NULL AUTO_INCREMENT,
`lsd_lse_id` bigint NULL,
`lsd_name` varchar(50) NULL,
`lsd_card_num` varchar(50) NULL,
`lsd_sear_num` varchar(50) NULL,
`lsd_date` varchar(50) NULL,
`lsd_m` varchar(50) NULL,
`lsd_ins_user` varchar(25) NULL,
`lsd_ins_time` datetime NULL,
`lsd_alt_user` varchar(25) NULL,
`lsd_alt_time` datetime NULL,
PRIMARY KEY (`lsd_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
ALTER TABLE `loan_sd_rec`  WITH CHECK ADD  CONSTRAINT `FK_loan_sd_rec_loan_season` FOREIGN KEY(`lsd_lse_id`)
REFERENCES `loan_season` (`lse_id`)
ON DELETE CASCADE
DROP TABLE IF EXISTS `house_inf`;
CREATE TABLE `house_inf`(
`hoi_id` bigint NOT NULL AUTO_INCREMENT,
`hoi_lse_id` bigint NULL,
`hoi_name` varchar(50) NULL,
`hoi_id_no` varchar(50) NULL,
`hoi_house_no` varchar(500) NULL,
`hoi_type` varchar(100) NULL,
`hoi_com` varchar(200) NULL,
`hoi_ins_user` varchar(50) NULL,
`hoi_ins_time` datetime NULL,
`hoi_alt_user` varchar(50) NULL,
`hoi_alt_time` datetime NULL,
PRIMARY KEY (`hoi_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `neg_inf`;
CREATE TABLE `neg_inf`(
`nei_id` bigint NOT NULL AUTO_INCREMENT,
`nei_lse_id` bigint NULL,
`nei_name` varchar(100) NULL,
`nei_inf` varchar(500) NULL,
`nei_ins_user` varchar(50) NULL,
`nei_ins_time` datetime NULL,
`nei_alt_user` varchar(50) NULL,
`nei_alt_time` datetime NULL,
PRIMARY KEY (`nei_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `car_loan`;
CREATE TABLE `car_loan`(
`cal_cas_id` bigint NULL,
`cal_price` varchar(200) NULL,
`cal_lice` varchar(200) NULL,
`cal_make` varchar(200) NULL,
`cal_vin` varchar(200) NULL,
`cal_engine_no` varchar(200) NULL,

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

create table sys_pref(
syp_id bigint primary key identity(1,1) NOT NULL,
syp_name	varchar(50),
syp_is_def	int,
syp_is_app	int,
syp_pwd_len	int,
syp_pwd_rule	varchar(50),
syp_pwd_upd_days	int,
syp_login_fail	int,
syp_offline_days	int,
syp_has_captcha int,
syp_hide_back	int,
syp_global_org	int,
syp_cl_no_hide int
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
DROP TABLE IF EXISTS `law_act`;
CREATE TABLE `law_act`(
`lwa_id` bigint NOT NULL AUTO_INCREMENT,
`lwa_lwc_id` bigint NULL,
`lwa_emp_id` bigint NULL,
`lwa_content` longtext NULL,
`lwa_time` datetime NULL,
`lwa_cre_man` varchar(50) NULL,
`lwa_cre_time` datetime NULL,
`lwa_upd_man` varchar(50) NULL,
`lwa_upd_time` datetime NULL,
`lwa_remark` longtext NULL,
`lwa_proc` varchar(50) NULL,
PRIMARY KEY (`lwa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `law_case`;
CREATE TABLE `law_case`(
`lwc_id` bigint NOT NULL AUTO_INCREMENT,
`lwc_state` varchar(50) NULL,
`lwc_name` varchar(100) NULL,
`lwc_id_code` varchar(50) NULL,
`lwc_consigner` varchar(100) NULL,
`lwc_defendant` varchar(100) NULL,
`lwc_cost` decimal(18,2) NULL,
`lwc_target` varchar(50) NULL,
`lwc_emp_id` bigint NULL,
`lwc_type_id` bigint NULL,
`lwc_date` datetime NULL,
`lwc_contact` varchar(200) NULL,
`lwc_court_id` bigint NULL,
`lwc_filing_date` datetime NULL,
`lwc_no` varchar(100) NULL,
`lwc_judge` varchar(50) NULL,
`lwc_judge_contact` varchar(200) NULL,
`lwc_fst_hearing_date` datetime NULL,
`lwc_end_date` datetime NULL,
`lwc_act_date` datetime NULL,
`lwc_act_no` varchar(100) NULL,
`lwc_act_end_date` datetime NULL,
`lwc_remark` varchar(500) NULL,
`lwc_cre_man` varchar(50) NULL,
`lwc_cre_time` datetime NULL,
`lwc_upd_man` varchar(50) NULL,
`lwc_upd_time` datetime NULL,
`lwc_paid` decimal(18,2) NULL,
`lwc_proc_st_id`	bigint NULL,
`lwc_cas_id` bigint NULL,
`lwc_legal_pay_date` datetime NULL,
`lwc_presv_pay_date` datetime NULL,
`lwc_asset_presv` longtext NULL,
`lwc_serv_rs` varchar(100) NULL,
`lwc_judgment` longtext NULL,
`lwc_proc` varchar(50) NULL,
`lwc_approve_time` datetime NULL,
`lwc_approve_man` varchar(25) NULL,
PRIMARY KEY (`lwc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
DROP TABLE IF EXISTS `law_paid`;
CREATE TABLE `law_paid`(
`lwpa_id` bigint NOT NULL AUTO_INCREMENT,
`lwpa_amt` decimal(18,2) NULL,
`lwpa_type_id` bigint NULL,
`lwpa_date` datetime NULL,
`lwpa_name` varchar(100) NULL,
`lwpa_man` varchar(50) NULL,
`lwpa_pay_med` int NULL,
`lwpa_file_code` varchar(100) NULL,
`lwpa_remark` varchar(500) NULL,
`lwpa_cre_man` varchar(50) NULL,
`lwpa_cre_time` datetime NULL,
`lwpa_upd_man` varchar(50) NULL,
`lwpa_upd_time` datetime NULL,
`lwpa_lwc_id` bigint NULL,
PRIMARY KEY (`lwpa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `lim_group`;
CREATE TABLE `lim_group`(
`grp_id` bigint NOT NULL AUTO_INCREMENT,
`grp_name` varchar(50) NULL,
`grp_desc` varchar(200) NULL,
`grp_cre_time` datetime NULL,
`grp_cre_man` varchar(50) NULL,
`grp_upd_time` datetime NULL,
`grp_upd_man` varchar(50) NULL,
PRIMARY KEY (`grp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `r_group_rig`;
CREATE TABLE `r_group_rig`(
`rgr_id` bigint NOT NULL AUTO_INCREMENT,
`rgr_grp_id` bigint NULL,
`rgr_rig_code` varchar(50) NULL,
PRIMARY KEY (`rgr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 现金巴士批次表
DROP TABLE IF EXISTS `cashbus_batch`;
CREATE TABLE `cashbus_batch`(
`id` bigint NOT NULL,
`batchname` varchar(255) NOT NULL,
`description` text NULL,
`withdraw` `tinyint(1)` NULL,
`type` varchar(255) NULL,
`withdrawTime` datetime NULL,
`ext1` varchar(100) NULL,
`ext2` varchar(100) NULL,
`ext3` varchar(100) NULL,
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

TEXTIMAGE_ON `PRIMARY`
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'batchname'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'description'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否撤案' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'withdraw'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类型 weiwai, yuzhengxin, zhengshizhengxin' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'type'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'撤案日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch', @level2type=N'COLUMN',@level2name=N'withdrawTime'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'现金巴士批次' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_batch'
-- 现金巴士批次表结束
DROP TABLE IF EXISTS `user_log`;
CREATE TABLE `user_log`(
`ulg_id` bigint NOT NULL AUTO_INCREMENT,
`ulg_type`	varchar(50),
`ulg_oper`	varchar(50),
`ulg_op_time`	datetime,
`ulg_op_content`	longtext,
`ulg_user`	varchar(50),
PRIMARY KEY (`ulg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
DROP TABLE IF EXISTS `type_list_connect`;
CREATE TABLE `type_list_connect`(
`tlc_id` bigint NOT NULL AUTO_INCREMENT,
`parent_typ_id` bigint NOT NULL,
`child_typ_id` bigint NOT NULL,
PRIMARY KEY (`tlc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

;
CREATE PROCEDURE `empExcLim`(
@excLimIds  varchar(8000),
@sqlAppend  varchar(8000)
)
AS
--申明字符串变量，以供动态拼装
declare @sql longtext,@sql1 varchar(8000)
set @sql1='select exclims.typ_name as exc_lim into ##tempExcLim from type_list as exclims where exclims.typ_type=''excLimType'' '+ @excLimIds +' '
if object_id('tempdb..##tempExcLim') is not null
drop table ##tempExcLim
exec(@sql1)
--拼装SQL命令
set @sql = 'select se_no,max(se_name) as head'
select @sql = @sql + ',
sum(case cas_exc_lim when '''+exc_lim+''' then 1 else 0 ) ['+exc_lim+'],
sum(case cas_exc_lim when '''+exc_lim+''' then cas_m else 0 ) ['+exc_lim+'],
sum(case cas_exc_lim when '''+exc_lim+''' then cas_paid_m else 0 ) ['+exc_lim+']' from (select exc_lim from ##tempExcLim)as a 
--加上合计
select @sql = @sql+' 
from bank_case inner join sal_emp on cas_se_no=se_no inner join type_list on cas_typ_bid=typ_id 
inner join case_bat on cas_cbat_id = cbat_id 
where cas_state!=3 ' + @sqlAppend
+ ' group by se_no with rollup'
--print(@sql)
exec(@sql)
CREATE PROCEDURE `empState` 
@sqlAppend  varchar(8000)
AS
declare @sql longtext,@sql1 varchar(8000)
set @sql1='select typ_name as state_name into ##tempHurState 
from type_list where typ_type=''caseState'' '
if object_id('tempdb..##tempHurState') is not null
drop table ##tempHurState
exec(@sql1)
set @sql = 'select se_no,max(se_code),max(se_name) as head'
select @sql = @sql + ',
sum(case hstate.typ_name when '''+state_name+''' then 1 else 0 ) ['+state_name+'] ' from (select state_name from ##tempHurState)as a 
select @sql = @sql+',
count(cas_id)as 合计 
from bank_case left join sal_emp on cas_se_no=se_no inner join type_list as hstate on cas_typ_hid=hstate.typ_id '+ 
@sqlAppend+
' group by se_no '
--print(@sql)
exec(@sql)
;
-- =============================================
-- Author:		GM
-- Create date: 2015-8-13
-- Description:	批次账龄催收员分组催收状态统计
-- =============================================
CREATE PROCEDURE `batExcLimEmpByStateStat`(
@sqlAppend  varchar(8000)
)
AS
if object_id('tempdb..##tempClState') is not null
drop table ##tempClState
declare @stateSql longtext,@sql longtext
set @stateSql = 'select clstate.typ_id as state_id, max(clstate.typ_name) as state_name
into ##tempClState
from type_list as clstate
inner join bank_case as b on clstate.typ_id=b.cas_typ_hid 
inner join case_bat on b.cas_cbat_id = cbat_id
left join type_list as batBank on cas_typ_bid = batBank.typ_id 
'+@sqlAppend+'
group by clstate.typ_id'
--print(@stateSql)
exec(@stateSql)
--拼装SQL命令
set @sql = 'select b.cas_cbat_id , b.cas_se_no, 
case when grouping(b.cas_cbat_id)=1 then ''合计'' else max(cbat_code)  as ''head'', 
case when grouping(b.cas_cbat_id)=1 then ''-'' else (case when grouping(b.cas_exc_lim)=1 then ''小计'' else b.cas_exc_lim )  as ''逾期账龄'', 
case when grouping(b.cas_exc_lim)=1 or grouping(b.cas_cbat_id)=1 then ''-'' else (case when grouping(b.cas_se_no)=1 then ''小计'' else max(se_name) )  as ''催收员'', 
case when grouping(b.cas_se_no)=1 then ''-'' else max(so_name)  as ''部门'',
case when grouping(b.cas_cbat_id)=1 then null else datediff(day,max(cbat_date),getdate())  as ''委案天数'',
case when grouping(b.cas_cbat_id)=1 then null else max(cbat_date)  as ''委案日期'',
case when grouping(b.cas_cbat_id)=1 then null else max(cbat_backdate_p)  as ''预计退案日'',
case when grouping(b.cas_cbat_id)=1 then null else max(cbat_backdate)  as ''实际退案日'', 
case when grouping(b.cas_cbat_id)=1 then null else max(carea.typ_name)  as ''催收区域'', 
case when grouping(b.cas_cbat_id)=1 then null else max(bType.typ_name)  as ''案件类型'',
sum(b.cas_m) as ''委案金额'',count(b.cas_id) as ''委案户数'',
case when sum(b.cas_paid_m)>0 then sum(b.cas_m)-sum(b.cas_paid_m) else sum(b.cas_m)  as ''余额'',
sum(b.cas_paid_m) as ''还款案件'',
case when sum(b.cas_m)>0 then sum(b.cas_paid_m)/sum(b.cas_m) else 0  as ''还款案件'',
sum(case when b.cas_paid_m > 0 then 1 else 0 ) as ''还款案件'', 
case when count(b.cas_id)>0 then sum(case when b.cas_paid_m is not null and b.cas_paid_m > 0 then 1 else 0 )/count(b.cas_id) else 0  as ''还款案件'',
sum(case when b.cas_paid_m>= b.cas_m then b.cas_m else 0 ) as ''还清案件'',
case when sum(b.cas_m)>0 then sum(case when b.cas_paid_m>= b.cas_m then b.cas_m else 0 )/sum(b.cas_m) else 0  as ''还清案件'',
sum(case when b.cas_paid_m>= b.cas_m then 1 else 0 ) as ''还清案件'', 
case when count(b.cas_id)>0 then sum(case when b.cas_paid_m>= b.cas_m then 1 else 0 )/count(b.cas_id) else 0  as ''还清案件'',
sum(case when b.cas_paid_m>0 and b.cas_paid_m< b.cas_m then b.cas_m else 0 ) as ''部分还款案件'',
case when sum(b.cas_m)>0 then sum(case when b.cas_paid_m>0 and b.cas_paid_m< b.cas_m then b.cas_m else 0 )/sum(b.cas_m) else 0  as ''部分还款案件'',
sum(case when b.cas_paid_m>0 and b.cas_paid_m< b.cas_m then 1 else 0 ) as ''部分还款案件'', 
case when count(b.cas_id)>0 then sum(case when b.cas_paid_m>0 and b.cas_paid_m< b.cas_m then 1 else 0 )/count(b.cas_id) else 0  as ''部分还款案件'',
sum(case when b.cas_paid_m=0 or b.cas_paid_m is null then b.cas_m else 0 ) as ''未还款案件'',
case when sum(b.cas_m)>0 then sum(case when b.cas_paid_m=0 or b.cas_paid_m is null then b.cas_m else 0 )/sum(b.cas_m) else 0  as ''未还款案件'',
sum(case when b.cas_paid_m=0 or b.cas_paid_m is null then 1 else 0 ) as ''未还款案件'', 
case when count(b.cas_id)>0 then sum(case when b.cas_paid_m=0 or b.cas_paid_m is null then 1 else 0 )/count(b.cas_id) else 0  as ''未还款案件'' '
select @sql = @sql + ',
sum(case b.cas_typ_hid when '+convert(varchar(100),temp.state_id)+' then b.cas_m else 0 ) as '''+temp.state_name+''',
case when sum(b.cas_m)>0 then sum(case b.cas_typ_hid when '+convert(varchar(100),temp.state_id)+' then b.cas_m else 0 )/sum(b.cas_m) else 0  as '''+temp.state_name+''',
sum(case b.cas_typ_hid when '+convert(varchar(100),temp.state_id)+' then 1 else 0 ) as '''+temp.state_name+''',  
case when count(b.cas_id)>0 then sum(case b.cas_typ_hid when '+convert(varchar(100),temp.state_id)+' then 1 else 0 )/count(b.cas_id) else 0  as '''+temp.state_name+''' '  from (select state_id,state_name from ##tempClState)as temp 
select @sql = @sql+'
from bank_case as b
inner join case_bat on b.cas_cbat_id = cbat_id
left join sal_emp on b.cas_se_no = se_no
left join sal_org on se_so_code = so_code
left join type_list as carea on cbat_area_id = carea.typ_id
left join type_list as batBank on cbat_typ_bid = batBank.typ_id
left join type_list as bType on cbat_type_id = bType.typ_id 
'+@sqlAppend+'
group by b.cas_cbat_id,b.cas_exc_lim,b.cas_se_no with rollup'
--print(@sql)
exec(@sql)
SET FOREIGN_KEY_CHECKS = 1;