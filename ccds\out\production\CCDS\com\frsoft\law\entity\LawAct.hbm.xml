<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.law.entity.LawAct" table="law_act" schema="dbo" >
        <id name="lwaId" type="java.lang.Long">
            <column name="lwa_id" />
            <generator class="identity" />
        </id>
        <property name="lwaTime" type="java.util.Date">
            <column name="lwa_time" length="23" />
        </property>
        <property name="lwaContent" type="java.lang.String">
            <column name="lwa_content" />
        </property>
        <property name="lwaCreMan" type="java.lang.String">
            <column name="lwa_cre_man" length="50" />
        </property>
        <property name="lwaCreTime" type="java.util.Date">
            <column name="lwa_cre_time" length="23" />
        </property>
        <property name="lwaUpdMan" type="java.lang.String">
            <column name="lwa_upd_man" length="50" />
        </property>
        <property name="lwaUpdTime" type="java.util.Date">
            <column name="lwa_upd_time" length="23" />
        </property>
        <property name="lwaRemark" type="java.lang.String">
            <column name="lwa_remark" />
        </property>
        <property name="lwaProc" type="java.lang.String">
            <column name="lwa_proc" length="50" />
        </property>
        <many-to-one name="lwaLwc" class="com.frsoft.law.entity.LawCase">
            <column name="lwa_lwc_id" />
        </many-to-one>
        <many-to-one name="lwaEmp" class="com.frsoft.base.entity.SalEmp">
            <column name="lwa_emp_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
