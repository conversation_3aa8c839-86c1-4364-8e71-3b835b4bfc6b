function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','C_DT'],
		['ACC','ID_NO','AREA','STATE'],
		[['AMT',1,'合计欠款'],['OV_INT',1,'表外息'],['C_CODE',1,'账户下任一卡号'],'ALT_HOLD'],
		['LAST_M','M_CAT','AGE','NOOUT'],
		[['RMB',1,'RMB总余额'],['MY',1,'USD总余额'],['GB',1,'目前总余额'],'CYCLE'],
		['EMP','ASS_TM','ASS_HIS','BACK_DT'],
		['MOB','PHO','W_PHO','EMAIL'],
		['C1_NAME','C1_HM_PHO','C2_NAME','C2_HM_PHO'],
		['C1_MOB','C2_MOB','LAST_P_DT','LAST_P'],
		['PAID','PTP','CP','BACK_AMT'],
		['TIPS_DT','DEAD_L',['TREMARK',3]]
	);
}
function getLayout2(){
	return  new Array(
		['HOM','H_POST','M_ADDR','M_POST'],
		['COM','W_ADDR','W_POST','REG'],
		['START_DT','STOP_DT','DAYS','COUNT'],
		['BIR','F_BANK','HOST','BILL_DT'],
		['LAST_C_DT','LAST_R_DT','OV_DT','APP_NO'],
		['OV_P','C_L','P_CRLINE','G_AMT'],
		['LOAN_T','DELAY_LV','MIN_P','P_L'],
		['P_COUNT','CL_T','PROD','BIZ'],
		['LOAN_DT','PRI','LEFT_PRI','FILE_NO'],
		['SC_PC_NO','SC_NO','POS','PART'],
		['C1_ID_NO','C1_W_PHO','C1_COM','C1_ADR'],
		['C2_ID_NO','C2_W_PHO','C2_COM','C2_ADR'],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		['C4_NAME','C4_ID_NO','C4_HM_PHO','C4_W_PHO'],
		['C4_MOB','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		['C6_NAME','C6_ID_NO','C6_HM_PHO','C6_W_PHO'],
		['C6_MOB','C6_COM',['C6_ADR',3]],
		[['O_REC',3,'拖欠历史'],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}

function cardInfMapper(obj){
	var datas,className,dblFunc,dataId;
	datas = [obj.ocdArea,obj.ocdIdNo,obj.ocdCard,obj.ocdName,obj.ocdHPho?"<a href='javascript:void(0)' onClick=\"showPRInputDiv('"+obj.ocdHPho+"','"+obj.ocdName+"','家庭电话','');return false;\">"+obj.ocdHPho+"</a>":"&nbsp;",obj.ocdOPho?"<a href='javascript:void(0)' onClick=\"showPRInputDiv('"+obj.ocdOPho+"','"+obj.ocdName+"','单位电话','');return false;\">"+obj.ocdOPho+"</a>":"&nbsp;",obj.ocdAddr,obj.ocdEmployer,obj.ocdAltName,obj.ocdAltHPho?"<a href='javascript:void(0)' onClick=\"showPRInputDiv('"+obj.ocdAltHPho+"','"+obj.ocdAltName+"','家庭电话','');return false;\">"+obj.ocdAltHPho+"</a>":"&nbsp;",obj.ocdAltOPho?"<a href='javascript:void(0)' onClick=\"showPRInputDiv('"+obj.ocdAltOPho+"','"+obj.ocdAltName+"','单位电话','');return false;\">"+obj.ocdAltOPho+"</a>":"&nbsp;",obj.ocdConName,obj.ocdConPho?"<a href='javascript:void(0)' onClick=\"showPRInputDiv('"+obj.ocdConPho+"','"+obj.ocdConName+"','单位电话','');return false;\">"+obj.ocdConPho+"</a>":"&nbsp;",obj.ocdBir,obj.ocdRAddr,obj.ocdCyc,obj.ocdBlk,obj.ocdMPost,obj.ocdMsg];
	return [datas,className,dblFunc,dataId];
}
function loadCardList(sortCol,isDe,pageSize,curP){
	var url = "caseAction.do";
	var pars = [];
	pars.op = "listOthCard";
	pars.caseId=$("caseId").value;
	var loadFunc = "loadCardList";
	var cols=[
		{name:"地区",width:'1%'},
		{name:"证件号",width:'5%'},
		{name:"卡号",width:'5%'},
		{name:"姓名",width:'5%'},
		{name:"电话(家)",width:'5%'},
		{name:"电话(公)",width:'5%'},
		{name:"通讯地址",width:'10%'},
		{name:"公司名称",width:'5%'},
		{name:"副卡人姓名",width:'5%'},
		{name:"副卡人电A",width:'5%'},
		{name:"副卡人电B",width:'5%'},
		{name:"联络人姓名",width:'5%'},
		{name:"联络人电话",width:'5%'},
		{name:"生日",width:'4%'},
		{name:"户籍地址",width:'10%'},
		{name:"周期",width:'2%'},
		{name:"账户BLK",width:'2%'},
		{name:"账户邮编",width:'4%'},
		{name:"永久讯息",width:'12%'}
		/*{name:"操作",width:'5%'}*/
	];
	cardGridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
	cardGridEl.loadData(cardInfMapper);
}
var cardGridEl = new MGrid("cardListTab","cardInfList");
cardGridEl.config.sortable=false;
cardGridEl.config.isResize=false;
cardGridEl.config.isShort=false;