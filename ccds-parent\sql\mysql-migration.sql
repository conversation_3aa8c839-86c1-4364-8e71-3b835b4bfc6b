-- 催收系统MySQL数据迁移脚本
-- 从SQL Server迁移到MySQL
-- 版本: 1.0.0

USE `ccds`;

-- 系统偏好设置表
CREATE TABLE IF NOT EXISTS `sys_pref` (
  `syp_id` bigint NOT NULL AUTO_INCREMENT COMMENT '偏好ID',
  `syp_name` varchar(100) NOT NULL COMMENT '偏好名称',
  `syp_is_def` tinyint DEFAULT '0' COMMENT '是否默认',
  `syp_is_app` tinyint DEFAULT '0' COMMENT '是否应用',
  `syp_pwd_len` int DEFAULT '6' COMMENT '密码长度',
  `syp_pwd_rule` varchar(10) DEFAULT '0' COMMENT '密码规则',
  `syp_pwd_upd_days` int DEFAULT '0' COMMENT '密码更新天数',
  `syp_login_fail` int DEFAULT '5' COMMENT '登录失败次数',
  `syp_offline_days` int DEFAULT '0' COMMENT '离线天数',
  `syp_has_captcha` tinyint DEFAULT '1' COMMENT '是否有验证码',
  PRIMARY KEY (`syp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统偏好设置表';

-- 操作权限表
CREATE TABLE IF NOT EXISTS `lim_operate` (
  `ope_id` bigint NOT NULL AUTO_INCREMENT COMMENT '操作ID',
  `ope_code` varchar(50) NOT NULL COMMENT '操作编码',
  `ope_desc` varchar(200) DEFAULT NULL COMMENT '操作描述',
  PRIMARY KEY (`ope_id`),
  UNIQUE KEY `uk_ope_code` (`ope_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作权限表';

-- 功能模块表
CREATE TABLE IF NOT EXISTS `lim_function` (
  `fun_id` bigint NOT NULL AUTO_INCREMENT COMMENT '功能ID',
  `fun_code` varchar(50) NOT NULL COMMENT '功能编码',
  `fun_desc` varchar(200) DEFAULT NULL COMMENT '功能描述',
  `fun_type` varchar(50) DEFAULT NULL COMMENT '功能类型',
  PRIMARY KEY (`fun_id`),
  UNIQUE KEY `uk_fun_code` (`fun_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能模块表';

-- 权限表
CREATE TABLE IF NOT EXISTS `lim_right` (
  `rig_id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `rig_code` varchar(50) NOT NULL COMMENT '权限编码',
  `rig_fun_code` varchar(50) DEFAULT NULL COMMENT '功能编码',
  `rig_ope_code` varchar(50) DEFAULT NULL COMMENT '操作编码',
  `rig_wms_name` varchar(100) DEFAULT NULL COMMENT 'WMS名称',
  PRIMARY KEY (`rig_id`),
  UNIQUE KEY `uk_rig_code` (`rig_code`),
  KEY `idx_rig_fun_code` (`rig_fun_code`),
  KEY `idx_rig_ope_code` (`rig_ope_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 省份表
CREATE TABLE IF NOT EXISTS `cus_province` (
  `prv_id` bigint NOT NULL AUTO_INCREMENT COMMENT '省份ID',
  `prv_area_id` bigint DEFAULT NULL COMMENT '区域ID',
  `prv_name` varchar(50) NOT NULL COMMENT '省份名称',
  `prv_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='省份表';

-- 城市表
CREATE TABLE IF NOT EXISTS `cus_city` (
  `city_id` bigint NOT NULL AUTO_INCREMENT COMMENT '城市ID',
  `city_prv_id` bigint DEFAULT NULL COMMENT '省份ID',
  `city_name` varchar(50) NOT NULL COMMENT '城市名称',
  `city_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`city_id`),
  KEY `idx_city_prv_id` (`city_prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='城市表';

-- 区域表
CREATE TABLE IF NOT EXISTS `cus_area` (
  `are_id` bigint NOT NULL AUTO_INCREMENT COMMENT '区域ID',
  `are_name` varchar(50) NOT NULL COMMENT '区域名称',
  `are_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`are_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='区域表';

-- 锁表
CREATE TABLE IF NOT EXISTS `lock_table` (
  `lock_id` bigint NOT NULL AUTO_INCREMENT COMMENT '锁ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_max` bigint DEFAULT '0' COMMENT '最大值',
  PRIMARY KEY (`lock_id`),
  UNIQUE KEY `uk_table_name` (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='锁表';

-- 插入系统偏好设置
INSERT INTO `sys_pref` (`syp_name`, `syp_is_def`, `syp_is_app`, `syp_pwd_len`, `syp_pwd_rule`, `syp_pwd_upd_days`, `syp_login_fail`, `syp_offline_days`, `syp_has_captcha`) 
VALUES ('系统默认', 1, 1, 6, '0', 0, 5, 0, 1)
ON DUPLICATE KEY UPDATE `syp_name` = '系统默认';

-- 插入操作权限数据
INSERT INTO `lim_operate` (`ope_code`, `ope_desc`) VALUES
('case001', '导入案件'),
('case002', '删除批次'),
('case003', '编辑批次'),
('case004', '编辑案件'),
('case005', '退案'),
('case006', '分配案件'),
('case007', '暂停案件'),
('case008', '关闭案件'),
('case009', '恢复案件'),
('case010', '批量评语'),
('case011', '导出案件'),
('case012', '导出催收记录'),
('casehp001', '确认核查'),
('casehp002', '确认登帐'),
('casehp003', '修改CP金额'),
('casehp004', '新建登帐'),
('fun001', '添加权限'),
('fun002', '删除权限'),
('fun003', '修改权限'),
('fun004', '查看详情权限'),
('fun005', '访问权限'),
('fun006', '查看全部'),
('fun007', '管理权限'),
('fun008', '审核权限'),
('fun009', '导入权限'),
('fun010', '添加修改权限'),
('hurr001', '添加评语'),
('hurr002', '添加警告'),
('hurr003', '修改催收小结'),
('hurr004', '修改案件地区'),
('hurr016', '作废CP'),
('hurr017', '查看部门案件'),
('hurr022', '添加电催记录'),
('hurr023', '修改电话'),
('hurr024', '删除电话'),
('hurr025', '修改地址'),
('hurr026', '删除地址'),
('hurr027', '访问操作记录'),
('hurr028', '修改操作记录'),
('hurr029', '删除操作记录'),
('hurr030', '修改预计退案日'),
('hurr031', '添加电话'),
('hurr032', '添加地址'),
('hurr033', '添加辅助催记'),
('hurr034', '添加协催记录'),
('hurr035', '添加案人数据'),
('hurr036', '删除案件附件'),
('hurr037', '访问共债案件'),
('hurr038', '批量标色'),
('hurr039', '发送短信'),
('visR001', '外访审核、撤销、排程'),
('visR003', '修改排程时间'),
('visR005', '完成外访'),
('law001', '访问案件办理记录'),
('law002', '添加修改办理记录'),
('law003', '删除办理记录'),
('law004', '访问案件收费记录'),
('law005', '添加修改收费记录'),
('law006', '删除收费记录'),
('law007', '访问关联催收案件'),
('sys001', '锁定账号'),
('sys002', '查看账号日志'),
('sys003', '删除账号日志'),
('sys004', '设置短信额度'),
('file001', '上传附件'),
('file002', '查看附件')
ON DUPLICATE KEY UPDATE `ope_desc` = VALUES(`ope_desc`);

-- 插入省份数据
INSERT INTO `cus_province` (`prv_id`, `prv_area_id`, `prv_name`, `prv_isenabled`) VALUES
(1, 1, '请选择', '1')
ON DUPLICATE KEY UPDATE `prv_name` = VALUES(`prv_name`);

-- 插入城市数据
INSERT INTO `cus_city` (`city_id`, `city_prv_id`, `city_name`, `city_isenabled`) VALUES
(1, 1, '请选择', '1')
ON DUPLICATE KEY UPDATE `city_name` = VALUES(`city_name`);

-- 插入区域数据
INSERT INTO `cus_area` (`are_id`, `are_name`, `are_isenabled`) VALUES
(1, '请选择', '1'),
(3, '江苏', '1'),
(4, '安徽', '1'),
(5, '浙江', '1'),
(6, '上海', '1'),
(7, '北京', '1')
ON DUPLICATE KEY UPDATE `are_name` = VALUES(`are_name`);

-- 插入锁表数据
INSERT INTO `lock_table` (`table_name`, `table_max`) VALUES
('acc_trans', 0),
('cus_cor_cus', 0),
('sal_org', 0),
('sal_paid_past', 0),
('sal_supplier', 0),
('spo_paid_past', 0),
('wms_change', 0),
('wms_check', 0),
('wms_stro', 0),
('wms_war_in', 0),
('wms_war_out', 0)
ON DUPLICATE KEY UPDATE `table_max` = VALUES(`table_max`);

-- 插入功能模块数据
INSERT INTO `lim_function` (`fun_code`, `fun_desc`, `fun_type`) VALUES
('c000', '访问权限', 'case'),
('c001', '批次管理', 'case'),
('c002', '案件管理', 'case'),
('c003', '催记管理', 'case'),
('c004', '导出', 'case'),
('c005', '案件详情', 'case'),
('casehelp000', '访问权限', 'cp'),
('casehelp001', '案件协助', 'cp'),
('casehelp002', '银行登帐', 'cp'),
('casehelp004', '案人数据库', 'cp'),
('cop000', '访问权限', 'oa'),
('cop001', '新闻公告', 'oa'),
('cop002', '日程', 'oa'),
('cop003', '工作任务', 'oa'),
('cop004', '报告', 'oa'),
('cop005', '邮件', 'oa'),
('cus000', '访问权限', 'cus'),
('cus001', '客户资料', 'cus'),
('cus002', '客户联系人', 'cus'),
('cus004', '联系记录', 'cus'),
('cus005', '客户关怀', 'cus'),
('fin001', '财务管理', 'acc'),
('fin002', '账户管理', 'acc'),
('fin003', '入账记录', 'acc'),
('fin004', '出账记录', 'acc'),
('fin005', '内部转账', 'acc'),
('fin006', '票据管理', 'acc'),
('hr000', '访问权限', 'hr'),
('hr001', '员工档案', 'hr'),
('hurry000', '访问权限', 'hurry'),
('hurry001', '我的案件', 'hurry'),
('hurry002', '来电查询', 'hurry'),
('hurry003', '主管协催', 'hurry'),
('law001', '访问权限', 'law'),
('law002', '我的诉讼案件', 'law'),
('law003', '部门诉讼案件', 'law'),
('law004', '收费记录', 'law'),
('loan001', '访问权限', 'loan'),
('m001', '我的外访', 'mob'),
('m002', '案件查询', 'mob'),
('pro001', '项目', 'proj'),
('pro002', '子项目', 'proj'),
('pro003', '项目任务', 'proj'),
('pro004', '项目统计', 'proj'),
('pro005', '项目管理', 'proj'),
('pro006', '项目日志', 'proj'),
('pro007', '查看特定项目', 'proj'),
('pur001', '采购管理', 'pur'),
('pur002', '采购单', 'pur'),
('pur003', '付款计划', 'pur'),
('pur004', '付款记录', 'pur'),
('pur005', '供应商资料', 'pur'),
('pur006', '供应商联系人', 'pur'),
('pur007', '询价管理', 'pur'),
('sal000', '访问权限', 'sal'),
('sal001', '合同', 'sal'),
('sal004', '回款记录', 'sal'),
('sta000', '访问权限', 'sta'),
('sys000', '访问权限', 'sys'),
('sys001', '帐号设置', 'sys'),
('sys002', '职位设置', 'sys'),
('sys003', '部门设置', 'sys'),
('sys004', '类别管理', 'sys'),
('sys006', '安全设置', 'sys'),
('sys007', '权限组设置', 'sys'),
('vis000', '访问权限', 'vis'),
('vis001', '外访管理', 'vis')
ON DUPLICATE KEY UPDATE `fun_desc` = VALUES(`fun_desc`);

COMMIT;
