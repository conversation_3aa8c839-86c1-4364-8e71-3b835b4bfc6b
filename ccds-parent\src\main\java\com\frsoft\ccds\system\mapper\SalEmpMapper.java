package com.frsoft.ccds.system.mapper;

import com.frsoft.ccds.system.domain.SalEmp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface SalEmpMapper {
    
    /**
     * 查询员工
     * 
     * @param seNo 员工主键
     * @return 员工
     */
    SalEmp selectSalEmpBySeNo(Long seNo);
    
    /**
     * 查询员工列表
     * 
     * @param salEmp 员工
     * @return 员工集合
     */
    List<SalEmp> selectSalEmpList(SalEmp salEmp);
    
    /**
     * 新增员工
     * 
     * @param salEmp 员工
     * @return 结果
     */
    int insertSalEmp(SalEmp salEmp);
    
    /**
     * 修改员工
     * 
     * @param salEmp 员工
     * @return 结果
     */
    int updateSalEmp(SalEmp salEmp);
    
    /**
     * 删除员工
     * 
     * @param seNo 员工主键
     * @return 结果
     */
    int deleteSalEmpBySeNo(Long seNo);
    
    /**
     * 批量删除员工
     * 
     * @param seNos 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSalEmpBySeNos(Long[] seNos);
    
    /**
     * 根据用户编码查询员工
     * 
     * @param userCode 用户编码
     * @return 员工信息
     */
    SalEmp selectSalEmpByUserCode(@Param("userCode") String userCode);
    
    /**
     * 根据员工姓名模糊查询员工列表
     * 
     * @param seName 员工姓名
     * @return 员工列表
     */
    List<SalEmp> selectSalEmpListByName(@Param("seName") String seName);
    
    /**
     * 根据组织编码查询员工列表
     * 
     * @param soCode 组织编码
     * @return 员工列表
     */
    List<SalEmp> selectSalEmpListBySoCode(@Param("soCode") String soCode);
    
    /**
     * 查询启用的员工列表
     * 
     * @return 员工列表
     */
    List<SalEmp> selectEnabledSalEmpList();
}
