import os
import re
import sys

def remove_sql_comments(content):
    """移除SQL注释"""
    # 移除单行注释
    content = re.sub(r'--.*$', '', content, flags=re.MULTILINE)
    
    # 移除多行注释
    content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
    
    return content

def extract_tables(content):
    """提取所有CREATE TABLE语句"""
    tables = []
    
    # 匹配所有CREATE TABLE语句
    pattern = r'CREATE\s+TABLE\s+(?:\[?dbo\]?\.)?\[?(\w+)\]?\s*\((.*?)\)'
    matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
    
    for match in matches:
        table_name = match.group(1)
        columns_text = match.group(2)
        
        # 分割列定义
        columns = []
        current_column = []
        parentheses_count = 0
        
        for line in columns_text.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            parentheses_count += line.count('(') - line.count(')')
            current_column.append(line)
            
            if parentheses_count == 0 and line.endswith(','):
                columns.append(' '.join(current_column))
                current_column = []
            elif parentheses_count == 0 and not line.endswith(','):
                columns.append(' '.join(current_column))
                current_column = []
        
        if current_column:
            columns.append(' '.join(current_column))
        
        # 处理每个列定义
        processed_columns = []
        for column in columns:
            if column.strip():
                # 移除多余的反引号
                column = re.sub(r'`(\w+)`\s+`([^`]+)`', r'`\1` \2', column)
                column = re.sub(r'\[(\w+)\]', r'`\1`', column)
                
                # 转换数据类型
                column = re.sub(r'(?i)nvarchar', 'varchar', column)
                column = re.sub(r'(?i)text', 'longtext', column)
                column = re.sub(r'(?i)varchar\s*\(\s*max\s*\)', 'longtext', column)
                column = re.sub(r'(?i)datetime2', 'datetime', column)
                column = re.sub(r'(?i)ntext', 'longtext', column)
                column = re.sub(r'(?i)bigint', 'bigint', column)
                column = re.sub(r'(?i)int', 'int', column)
                column = re.sub(r'(?i)decimal\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)', r'decimal(\1,\2)', column)
                
                # 处理IDENTITY
                column = re.sub(r'(?i)IDENTITY\s*\(\s*1\s*,\s*1\s*\)', 'AUTO_INCREMENT', column)
                column = re.sub(r'(?i)IDENTITY\s*\(\s*\d+\s*,\s*\d+\s*\)', 'AUTO_INCREMENT', column)
                
                # 移除COLLATE语句
                column = re.sub(r'(?i)COLLATE\s+\w+(?:_\w+)*\s*', '', column)
                
                # 修复IDENTITY语法
                column = re.sub(r'`(\w+)`\s+`([^`]+)`', r'`\1` \2', column)
                column = re.sub(r'\[(\w+)\]\s+\[([^\]]+)\]', r'`\1` \2', column)
                
                # 移除ASC/DESC关键字
                column = re.sub(r'(?i)\s+(ASC|DESC)\s*(?=[\),])', '', column)
                
                # 清理多余的空格
                column = re.sub(r'\s+', ' ', column.strip())
                
                # 移除末尾的逗号
                if column.endswith(','):
                    column = column[:-1]
                
                processed_columns.append(column)
        
        # 生成新的CREATE TABLE语句
        create_table = (
            f"CREATE TABLE IF NOT EXISTS `{table_name}` (\n  " +
            ",\n  ".join(processed_columns) +
            "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;"
        )
        tables.append(create_table)
    
    return tables

def extract_indexes(content):
    """提取所有CREATE INDEX语句"""
    indexes = []
    
    # 匹配所有CREATE INDEX语句
    pattern = r'CREATE\s+(?:NONCLUSTERED\s+)?INDEX\s+\[?(\w+)\]?\s+ON\s+\[?dbo\]?\.\[?(\w+)\]?\s*\(\s*([^)]+)\)'
    matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
    
    for match in matches:
        index_name = match.group(1)
        table_name = match.group(2)
        columns = match.group(3)
        
        # 移除ASC/DESC关键字
        columns = re.sub(r'(?i)\s+(ASC|DESC)\s*(?=[\),])', '', columns)
        
        # 清理列名中的反引号和方括号
        columns = re.sub(r'[`\[\]]', '', columns)
        
        # 清理多余的空格
        columns = re.sub(r'\s+', ' ', columns.strip())
        
        # 生成新的CREATE INDEX语句
        create_index = f"CREATE INDEX `{index_name}` ON `{table_name}` ({columns});"
        indexes.append(create_index)
    
    return indexes

def cleanup_sql(content):
    """清理和格式化SQL内容"""
    # 统一换行符
    content = content.replace('\r\n', '\n').replace('\r', '\n')
    
    # 移除注释
    content = remove_sql_comments(content)
    
    # 移除 GO 语句
    content = re.sub(r'(?m)^GO\s*$', '', content)
    
    # 移除 SET ANSI_NULLS ON 和 SET QUOTED_IDENTIFIER ON
    content = re.sub(r'(?i)SET\s+ANSI_NULLS\s+ON', '', content)
    content = re.sub(r'(?i)SET\s+QUOTED_IDENTIFIER\s+ON', '', content)
    
    # 移除IF NOT EXISTS和BEGIN/END块
    content = re.sub(r'IF\s+NOT\s+EXISTS\s*\([^)]+\)\s*BEGIN', '', content)
    content = re.sub(r'\s*END\s*$', '', content, flags=re.MULTILINE)
    
    # 添加头部语句
    header = [
        "USE ccds;",
        "SET NAMES utf8mb4;",
        "SET FOREIGN_KEY_CHECKS = 0;"
    ]
    
    # 提取CREATE TABLE和CREATE INDEX语句
    tables = extract_tables(content)
    indexes = extract_indexes(content)
    
    # 组合所有语句
    result = []
    result.extend(header)
    result.append('')  # 添加一个空行
    result.extend(tables)
    result.append('')  # 添加一个空行
    result.extend(indexes)
    result.append('')  # 确保文件以换行符结束
    
    # 移除多余的空行
    result = [line for line in result if line.strip() or line == '']
    while len(result) > 1 and not result[-2].strip() and not result[-1].strip():
        result.pop()
    
    return '\n'.join(result)

def process_file(input_file, output_file):
    """处理单个文件"""
    print(f"正在转换 {os.path.basename(input_file)}...")
    
    try:
        # 先以二进制模式读取文件
        with open(input_file, 'rb') as f:
            raw_content = f.read()
            
            # 检测并移除 BOM
            if raw_content.startswith(b'\xef\xbb\xbf'):  # UTF-8 BOM
                raw_content = raw_content[3:]
            elif raw_content.startswith(b'\xff\xfe'):  # UTF-16 LE BOM
                raw_content = raw_content[2:]
            elif raw_content.startswith(b'\xfe\xff'):  # UTF-16 BE BOM
                raw_content = raw_content[2:]
            
            # 尝试不同的编码
            encodings = ['utf-16le', 'utf-8', 'gbk', 'gb2312']
            content = None
            
            for encoding in encodings:
                try:
                    content = raw_content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                raise UnicodeDecodeError("无法使用任何已知编码解码文件")
        
        # 清理和转换SQL
        content = cleanup_sql(content)
        
        # 写入转换后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"成功将 {input_file} 转换为 MySQL 8.0 格式并保存为 {output_file}")
        print(f"转换完成: {os.path.basename(output_file)}")
        
    except Exception as e:
        print(f"处理文件 {input_file} 时出错: {str(e)}")
        raise

def main():
    """主函数"""
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    
    # 定义要处理的文件
    files = [
        ('CCDS.sql', 'mysql8_CCDS.sql'),
        ('CCDS_data.sql', 'mysql8_CCDS_data.sql'),
        ('CCDS_in_cashBus.sql', 'mysql8_CCDS_in_cashBus.sql')
    ]
    
    # 处理每个文件
    for input_name, output_name in files:
        input_file = os.path.join(parent_dir, input_name)
        output_file = os.path.join(current_dir, output_name)
        process_file(input_file, output_file)

if __name__ == '__main__':
    main()