-- 添加剩余的25个表，确保达到完整的106个表
-- 继续添加缺失的表

USE `ccds`;

-- 11. r_wms_wms (仓库关系表)
DROP TABLE IF EXISTS `r_wms_wms`;
CREATE TABLE `r_wms_wms` (
  `rww_id` bigint NOT NULL AUTO_INCREMENT,
  `wch_id` bigint DEFAULT NULL,
  `rww_pro_id` bigint DEFAULT NULL,
  `rww_num` decimal(18,2) DEFAULT NULL,
  `rww_remark` longtext,
  PRIMARY KEY (`rww_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 12. r_wout_pro (出库产品关系表)
DROP TABLE IF EXISTS `r_wout_pro`;
CREATE TABLE `r_wout_pro` (
  `rwo_id` bigint NOT NULL AUTO_INCREMENT,
  `rwo_wout_id` bigint DEFAULT NULL,
  `rwo_pro_id` bigint DEFAULT NULL,
  `rwo_wout_num` decimal(18,2) DEFAULT NULL,
  `rwo_remark` longtext,
  PRIMARY KEY (`rwo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 13. case_int (案件利息表)
DROP TABLE IF EXISTS `case_int`;
CREATE TABLE `case_int` (
  `cin_id` bigint NOT NULL AUTO_INCREMENT,
  `cin_cas_id` bigint DEFAULT NULL,
  `cin_name` varchar(50) DEFAULT NULL,
  `cin_m_cat` varchar(200) DEFAULT NULL,
  `cin_m` decimal(18,2) DEFAULT NULL,
  `cin_principal` varchar(200) DEFAULT NULL,
  `cin_int` varchar(200) DEFAULT NULL,
  `cin_overdue_paid` varchar(200) DEFAULT NULL,
  `cin_over_limit` varchar(200) DEFAULT NULL,
  `cin_service` varchar(200) DEFAULT NULL,
  `cin_year` varchar(200) DEFAULT NULL,
  `cin_other` varchar(200) DEFAULT NULL,
  `cin_out` varchar(200) DEFAULT NULL,
  `cin_ins_time` datetime DEFAULT NULL,
  `cin_end_date` varchar(100) DEFAULT NULL,
  `cin_damages_amt` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`cin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 14. wms_war_in (仓库入库表)
DROP TABLE IF EXISTS `wms_war_in`;
CREATE TABLE `wms_war_in` (
  `wwi_id` bigint NOT NULL AUTO_INCREMENT,
  `wwi_code` varchar(50) DEFAULT NULL,
  `wwi_title` longtext,
  `wwi_stro_code` varchar(50) DEFAULT NULL,
  `wwi_user_code` varchar(50) DEFAULT NULL,
  `wwi_state` char(1) DEFAULT NULL,
  `wwi_remark` longtext,
  `wwi_isdel` char(1) DEFAULT NULL,
  `wwi_inp_name` varchar(50) DEFAULT NULL,
  `wwi_alt_name` varchar(50) DEFAULT NULL,
  `wwi_inp_time` datetime DEFAULT NULL,
  `wwi_alt_time` datetime DEFAULT NULL,
  `wwi_in_date` datetime DEFAULT NULL,
  `wwi_app_date` datetime DEFAULT NULL,
  `wwi_app_man` varchar(50) DEFAULT NULL,
  `wwi_app_desc` longtext,
  `wwi_app_isok` char(1) DEFAULT NULL,
  `wwi_spo_code` bigint DEFAULT NULL,
  `wwi_can_date` datetime DEFAULT NULL,
  `wwi_can_man` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`wwi_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 15. r_win_pro (入库产品关系表)
DROP TABLE IF EXISTS `r_win_pro`;
CREATE TABLE `r_win_pro` (
  `rwi_id` bigint NOT NULL AUTO_INCREMENT,
  `rwi_win_id` bigint DEFAULT NULL,
  `rwi_pro_id` bigint DEFAULT NULL,
  `rwi_win_num` decimal(18,2) DEFAULT NULL,
  `rwi_remark` longtext,
  PRIMARY KEY (`rwi_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 16. wms_stro (仓库存储表)
DROP TABLE IF EXISTS `wms_stro`;
CREATE TABLE `wms_stro` (
  `wms_code` varchar(50) NOT NULL,
  `wms_name` varchar(300) DEFAULT NULL,
  `wms_type_id` bigint DEFAULT NULL,
  `wms_loc` longtext,
  `wms_cre_date` datetime DEFAULT NULL,
  `wms_user_code` varchar(50) DEFAULT NULL,
  `wms_remark` longtext,
  `wms_isenabled` char(1) DEFAULT NULL,
  PRIMARY KEY (`wms_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 17. case_grp (案件组表)
DROP TABLE IF EXISTS `case_grp`;
CREATE TABLE `case_grp` (
  `cg_id` bigint NOT NULL AUTO_INCREMENT,
  `cg_name` varchar(30) DEFAULT NULL,
  `cg_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 18. wms_change (仓库变更表)
DROP TABLE IF EXISTS `wms_change`;
CREATE TABLE `wms_change` (
  `wch_id` bigint NOT NULL AUTO_INCREMENT,
  `wch_code` varchar(50) DEFAULT NULL,
  `wch_title` longtext,
  `wch_state` char(1) DEFAULT NULL,
  `wch_in_date` datetime DEFAULT NULL,
  `wch_out_wms` varchar(50) DEFAULT NULL,
  `wch_in_wms` varchar(50) DEFAULT NULL,
  `wch_rec_man` varchar(50) DEFAULT NULL,
  `wch_remark` longtext,
  `wch_checkIn` varchar(50) DEFAULT NULL,
  `wch_checkOut` varchar(50) DEFAULT NULL,
  `wch_out_date` datetime DEFAULT NULL,
  `wch_isdel` char(1) DEFAULT NULL,
  `wch_in_time` datetime DEFAULT NULL,
  `wch_out_time` datetime DEFAULT NULL,
  `wch_inp_name` varchar(50) DEFAULT NULL,
  `wch_inp_date` datetime DEFAULT NULL,
  `wch_alt_name` varchar(50) DEFAULT NULL,
  `wch_alt_date` datetime DEFAULT NULL,
  `wch_app_date` datetime DEFAULT NULL,
  `wch_app_man` varchar(50) DEFAULT NULL,
  `wch_app_desc` longtext,
  `wch_app_isok` char(1) DEFAULT NULL,
  `wch_mat_name` varchar(50) DEFAULT NULL,
  `wch_can_date` datetime DEFAULT NULL,
  `wch_can_man` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`wch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 19. oth_card (其他卡片表)
DROP TABLE IF EXISTS `oth_card`;
CREATE TABLE `oth_card` (
  `oc_id` bigint NOT NULL AUTO_INCREMENT,
  `oc_cas_id` bigint DEFAULT NULL,
  `oc_name` varchar(50) DEFAULT NULL,
  `oc_card_no` varchar(50) DEFAULT NULL,
  `oc_bank` varchar(100) DEFAULT NULL,
  `oc_limit` varchar(200) DEFAULT NULL,
  `oc_balance` varchar(200) DEFAULT NULL,
  `oc_overdue` varchar(200) DEFAULT NULL,
  `oc_min_pay` varchar(200) DEFAULT NULL,
  `oc_bill_date` varchar(50) DEFAULT NULL,
  `oc_due_date` varchar(50) DEFAULT NULL,
  `oc_last_pay_date` varchar(50) DEFAULT NULL,
  `oc_last_pay_amt` varchar(200) DEFAULT NULL,
  `oc_status` varchar(50) DEFAULT NULL,
  `oc_remark` longtext,
  `oc_ins_time` datetime DEFAULT NULL,
  `oc_ins_user` varchar(25) DEFAULT NULL,
  `oc_upd_time` datetime DEFAULT NULL,
  `oc_upd_user` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`oc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 20. pa_bank_case (PA银行案件表)
DROP TABLE IF EXISTS `pa_bank_case`;
CREATE TABLE `pa_bank_case` (
  `pbc_id` bigint NOT NULL AUTO_INCREMENT,
  `pbc_cas_id` bigint DEFAULT NULL,
  `pbc_name` varchar(50) DEFAULT NULL,
  `pbc_id_no` varchar(50) DEFAULT NULL,
  `pbc_phone` varchar(50) DEFAULT NULL,
  `pbc_address` varchar(500) DEFAULT NULL,
  `pbc_work_address` varchar(500) DEFAULT NULL,
  `pbc_work_phone` varchar(50) DEFAULT NULL,
  `pbc_email` varchar(100) DEFAULT NULL,
  `pbc_bank` varchar(100) DEFAULT NULL,
  `pbc_card_no` varchar(50) DEFAULT NULL,
  `pbc_m` decimal(18,2) DEFAULT NULL,
  `pbc_paid_m` decimal(18,2) DEFAULT NULL,
  `pbc_overdue_date` varchar(200) DEFAULT NULL,
  `pbc_pback_p` float DEFAULT NULL,
  `pbc_wpost_code` varchar(50) DEFAULT NULL,
  `pbc_deadline` varchar(200) DEFAULT NULL,
  `pbc_is_host` varchar(50) DEFAULT NULL,
  `pbc_bill_date` varchar(200) DEFAULT NULL,
  `pbc_last_paid` varchar(200) DEFAULT NULL,
  `pbc_count` varchar(100) DEFAULT NULL,
  `pbc_left_pri` varchar(100) DEFAULT NULL,
  `pbc_overdue_days` int DEFAULT NULL,
  `pbc_overdue_days_str` varchar(200) DEFAULT NULL,
  `pbc_bir` varchar(50) DEFAULT NULL,
  `pbc_mpost_code` varchar(50) DEFAULT NULL,
  `pbc_perm_crline` varchar(50) DEFAULT NULL,
  `pbc_alt_hold` varchar(50) DEFAULT NULL,
  `pbc_ins_time` datetime DEFAULT NULL,
  `pbc_ins_user` varchar(25) DEFAULT NULL,
  `pbc_alt_time` datetime DEFAULT NULL,
  `pbc_alt_user` varchar(25) DEFAULT NULL,
  `pbc_remark` longtext,
  PRIMARY KEY (`pbc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 21. js_bank_case (JS银行案件表)
DROP TABLE IF EXISTS `js_bank_case`;
CREATE TABLE `js_bank_case` (
  `jbc_id` bigint NOT NULL AUTO_INCREMENT,
  `jbc_cas_id` bigint DEFAULT NULL,
  `jbc_name` varchar(50) DEFAULT NULL,
  `jbc_id_no` varchar(50) DEFAULT NULL,
  `jbc_phone` varchar(50) DEFAULT NULL,
  `jbc_address` varchar(500) DEFAULT NULL,
  `jbc_work_address` varchar(500) DEFAULT NULL,
  `jbc_work_phone` varchar(50) DEFAULT NULL,
  `jbc_email` varchar(100) DEFAULT NULL,
  `jbc_bank` varchar(100) DEFAULT NULL,
  `jbc_card_no` varchar(50) DEFAULT NULL,
  `jbc_m` decimal(18,2) DEFAULT NULL,
  `jbc_paid_m` decimal(18,2) DEFAULT NULL,
  `jbc_overdue_date` varchar(200) DEFAULT NULL,
  `jbc_pback_p` float DEFAULT NULL,
  `jbc_wpost_code` varchar(50) DEFAULT NULL,
  `jbc_deadline` varchar(200) DEFAULT NULL,
  `jbc_is_host` varchar(50) DEFAULT NULL,
  `jbc_bill_date` varchar(200) DEFAULT NULL,
  `jbc_last_paid` varchar(200) DEFAULT NULL,
  `jbc_count` varchar(100) DEFAULT NULL,
  `jbc_left_pri` varchar(100) DEFAULT NULL,
  `jbc_overdue_days` int DEFAULT NULL,
  `jbc_overdue_days_str` varchar(200) DEFAULT NULL,
  `jbc_bir` varchar(50) DEFAULT NULL,
  `jbc_mpost_code` varchar(50) DEFAULT NULL,
  `jbc_perm_crline` varchar(50) DEFAULT NULL,
  `jbc_alt_hold` varchar(50) DEFAULT NULL,
  `jbc_ins_time` datetime DEFAULT NULL,
  `jbc_ins_user` varchar(25) DEFAULT NULL,
  `jbc_alt_time` datetime DEFAULT NULL,
  `jbc_alt_user` varchar(25) DEFAULT NULL,
  `jbc_remark` longtext,
  PRIMARY KEY (`jbc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

SELECT 'CCDS数据库 - 第二批缺失表添加完成' as message;
