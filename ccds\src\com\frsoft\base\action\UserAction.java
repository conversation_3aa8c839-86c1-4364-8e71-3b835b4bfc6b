//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.frsoft.base.action;

import com.frsoft.base.action.EmpAction;
import com.frsoft.base.biz.EmpBIZ;
import com.frsoft.base.biz.MessageBIZ;
import com.frsoft.base.biz.SysPrefBIZ;
import com.frsoft.base.biz.UserBIZ;
import com.frsoft.base.biz.UserLogBIZ;
import com.frsoft.base.entity.LimGroup;
import com.frsoft.base.entity.LimRole;
import com.frsoft.base.entity.LimUser;
import com.frsoft.base.entity.SalEmp;
import com.frsoft.base.entity.SalOrg;
import com.frsoft.base.entity.SysPref;
import com.frsoft.ccds.biz.CaseBIZ;
import com.frsoft.ccds.biz.CtiServerBIZ;
import com.frsoft.ccds.biz.VisRecAssBIZ;
import com.frsoft.ccds.entity.CtiServer;
import com.frsoft.ccds.form.ListForm;
import com.frsoft.util.OperateDate;
import com.frsoft.util.Page;
import com.frsoft.util.format.GetDate;
import com.frsoft.util.format.StringFormat;
import com.frsoft.util.format.TransStr;
import com.frsoft.util.register.Register;
import com.frsoft.util.web.AjaxUtils;
import com.frsoft.util.web.JSONConvert;
import com.frsoft.util.xml.ConfigXml;
import com.octo.captcha.service.CaptchaServiceException;
import com.octo.captcha.service.image.ImageCaptchaService;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.action.DynaActionForm;
import org.apache.struts.actions.DispatchAction;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;

public class UserAction extends DispatchAction {
    private static final Logger logger = Logger.getLogger(UserAction.class);
    UserLogBIZ userLogBIZ = null;
    UserBIZ userBiz = null;
    MessageBIZ messageBiz = null;
    EmpBIZ empBiz = null;
    VisRecAssBIZ vraBiz = null;
    CaseBIZ caseBIZ = null;
    SysPrefBIZ sysPrefBIZ = null;
    ImageCaptchaService captchaService = null;
    CtiServerBIZ ctiServerBIZ = null;
    private String ORGTOPCODE = EmpAction.getORGTOPCODE();
    private long init = 0L;

    public UserAction() {
    }

    public void setCtiServerBIZ(CtiServerBIZ ctiServerBIZ) {
        this.ctiServerBIZ = ctiServerBIZ;
    }

    public void setCaptchaService(ImageCaptchaService captchaService) {
        this.captchaService = captchaService;
    }

    public void setSysPrefBIZ(SysPrefBIZ sysPrefBIZ) {
        this.sysPrefBIZ = sysPrefBIZ;
    }

    public ActionForward execute(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) throws Exception {
        return this.isLimitAllow(request)?super.execute(mapping, form, request, response):mapping.findForward("limError");
    }

    protected boolean isLimitAllow(HttpServletRequest request) {
        String methodName = request.getParameter("op");
        String[][] methLim = new String[][]{{"userManage", "asy001"}, {"searchLimRole", "asy006"}, {"toSaveRole", "sy002"}, {"saveRole", "sy002"}, {"updateRole", "sy003"}, {"goUpdateRole", "sy003"}, {"delRole", "sy004"}, {"toListLimGroup", "asy017"}, {"lockUser", "sy018"}, {"toListUserLog", "sy019"}, {"listUserLog", "sy019"}, {"toDeleteUserLog", "sy020"}, {"deleteUserLog", "sy020"}, {"toSetSmsMaxNum", "sy021"}};
        return this.userBiz.getLimit(request, methodName, methLim);
    }

    public void hasLimByAjax(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String rig = request.getParameter("rig");
        String[] rigArr = rig.split("\\|");
        response.setContentType("text/xml;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        PrintWriter out = null;

        try {
            out = response.getWriter();
            boolean[] e = this.userBiz.isInUserLims(request, rigArr);

            for(int i = 0; i < e.length; ++i) {
                if(i == 0) {
                    out.print(e[i]);
                } else {
                    out.print("|" + e[i]);
                }
            }

            out.flush();
        } catch (IOException var13) {
            var13.printStackTrace();
        } finally {
            out.close();
        }

    }

    public ActionForward toSetSmsMaxNum(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String userCodes = request.getParameter("userCodes");
        request.setAttribute("userCodes", userCodes);
        return mapping.findForward("setSmsMaxNum");
    }

    public ActionForward setSmsMaxNum(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        LimUser curUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        String userCodes = request.getParameter("userCodes");
        String maxNum = request.getParameter("maxNum");
        if(StringFormat.isEmpty(maxNum)) {
            maxNum = "0";
        }

        String[] userCodesArr = userCodes.split(",");
        this.userBiz.updateSmsMaxNum(userCodesArr, maxNum, curUser);
        this.userLogBIZ.saveLogOfUpdSms(maxNum, userCodesArr, curUser);
        request.setAttribute("msg", "设置短信额度");
        return mapping.findForward("popDivSuc");
    }

    public ActionForward toListSmsPrByEmp(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String seId = request.getParameter("seId");
        request.setAttribute("seId", seId);
        return mapping.findForward("listSmsPr");
    }

    public void listSmsPrByEmp(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String seId = request.getParameter("seId");
        String orderCol = request.getParameter("orderCol");
        String isDe = request.getParameter("isDe");
        String p = request.getParameter("p");
        String pageSize = request.getParameter("pageSize");
        String orderItem = "";
        String[] items = new String[]{"casCode", "time", "name", "rel", "contact"};
        if(orderCol != null && !orderCol.equals("")) {
            orderItem = items[Integer.parseInt(orderCol)];
        }

        ListForm listForm = new ListForm();
        LinkedList awareCollect = new LinkedList();
        if(p == null || p.trim().length() < 1) {
            p = "1";
        }

        if(pageSize == null || pageSize.equals("")) {
            pageSize = "50";
        }

        Page page = new Page(this.empBiz.listSmsPrByEmpCount(seId), Integer.parseInt(pageSize));
        page.setCurrentPageNo(Integer.parseInt(p));
        List list = this.empBiz.listSmsPrByEmp(seId, orderItem, isDe, page.getCurrentPageNo(), page.getPageSize());
        listForm.setList(list);
        listForm.setPage(page);
        awareCollect.add("bankCase");
        AjaxUtils.outPrintJSON(response, (new JSONConvert()).model2JSON(listForm, awareCollect));
    }

    public ActionForward toOCXFrame(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        SalEmp curEmp = (SalEmp)request.getSession().getAttribute("CUR_EMP");
        String orgCodes = request.getParameter("orgCodes");
        String servIps = request.getParameter("servIps");
        String servIp = this.empBiz.getOCXServIp(curEmp, orgCodes, servIps);
        request.setAttribute("servIP", servIp);
        return mapping.findForward("connectOCXWeb");
    }

    public ActionForward toIndex(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        return mapping.findForward("deskTop");
    }

    public ActionForward start(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String verId = (String)request.getSession().getAttribute("CUS_VER_ID");
        Date currDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        int userNum = this.userBiz.getCountAllUse();

        try {
            Register e = new Register();
            if(e.getUserSize() != null && e.getStartDate() != null && e.getEndDate() != null) {
                if(dateFormat.parse(e.getStartDate()).after(currDate)) {
                    request.setAttribute("msg", "您的授权已失效，请联系我们购买新的授权！");
                    request.setAttribute("isReg", "1");
                    request.setAttribute("isUpdSys", "1");
                } else if(dateFormat.parse(e.getEndDate()).before(currDate)) {
                    request.setAttribute("msg", "您的授权已过期，请联系我们购买新的授权！");
                    request.setAttribute("isReg", "1");
                    request.setAttribute("isUpdSys", "1");
                } else if(userNum > Integer.parseInt(e.getUserSize())) {
                    request.setAttribute("msg", "您当前系统的账户数超过授权数量，系统已锁定！");
                    request.setAttribute("isReg", "1");
                    request.setAttribute("isUpdSys", "1");
                } else {
                    if(StringFormat.isEmpty(verId)) {
                        verId = ConfigXml.getCustomerId(request);
                        if(request.getSession().getAttribute("SYS_PREF") == null) {
                            SysPref sysPref = this.sysPrefBIZ.findCurPref();
                            request.getSession().setAttribute("SYS_PREF", sysPref);
                        }

                        request.getSession().setAttribute("CUS_VER_ID", verId);
                        ConfigXml.setHasBFMod(e.getHasBFMod());
                        request.getSession().setAttribute("HAS_BF_MOD", Boolean.valueOf(ConfigXml.hasBFMod(request)));
                        if(e.getRegFunctions() != null && !e.getRegFunctions().equals("null")) {
                            ConfigXml.setRegFunctions(e.getRegFunctions());
                            request.getSession().setAttribute("REG_FUNCTIONS", e.getRegFunctions());
                        }

                        request.getSession().setAttribute("HAS_CTI", ConfigXml.getHasCTI(request));
                        request.getSession().setAttribute("HAS_SMS", ConfigXml.getHasSMS(request));
                        request.getSession().setAttribute("SMS_NEED_AUD", ConfigXml.getSMSNeedAud(request));
                        request.getSession().setAttribute("SYS_CODE", ConfigXml.getSysCode(request));
                    }

                    ConfigXml.setRegType(e.getRegType());
                    request.getSession().setAttribute("REG_TYPE", StringFormat.removeAllBlank(e.getRegType()));
                }
            } else {
                request.setAttribute("isReg", "1");
            }

            return this.toLoginPage(mapping, request);
        } catch (Exception var11) {
            var11.printStackTrace();
            request.setAttribute("errorMsg", "操作失败[" + var11.toString() + "]");
            return mapping.findForward("error");
        }
    }

    public ActionForward doLogin(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        SysPref sysPref = (SysPref)request.getSession().getAttribute("SYS_PREF");
        String loginName = request.getParameter("loginName");
        String pwd = request.getParameter("pwd");
        String macAddr = request.getParameter("macAddr");
        if(this.init == 0L) {
            this.userBiz.recInit();
            ++this.init;
        }

        LimUser limUser = this.userBiz.getUserByName(loginName);
        boolean isPass = false;
        boolean isForceUpd = false;
        boolean isMACValid = true;

        try {
            isMACValid = ConfigXml.isMACValid(macAddr, request);
        } catch (DocumentException var29) {
            var29.printStackTrace();
            request.setAttribute("msg", "MAC地址配置文件不存在!");
            return this.toLoginPage(mapping, request);
        }

        if(!isMACValid) {
            request.setAttribute("msg", "无权限访问系统!");
            return this.toLoginPage(mapping, request);
        } else {
            String captchaResp = request.getParameter("j_captcha_response");
            Boolean isResponseCorrect = Boolean.FALSE;
            String e;
            if(sysPref.getSypHasCaptcha() != null && sysPref.getSypHasCaptcha().intValue() == 0) {
                isResponseCorrect = Boolean.TRUE;
            } else {
                e = request.getSession().getId();

                try {
                    isResponseCorrect = this.captchaService.validateResponseForID(e, captchaResp);
                } catch (CaptchaServiceException var28) {
                    var28.printStackTrace();
                }
            }

            if(isResponseCorrect.booleanValue()) {
                if(limUser != null && limUser.getUserIsenabled() != null && limUser.getUserIsenabled().equals("2")) {
                    request.setAttribute("msg", "该账号已被锁定暂时不能登录!");
                } else if(this.userBiz.checkLogin(limUser, pwd, sysPref)) {
                    e = "";
                    if(request.getHeader("x-forwarded-for") == null) {
                        e = request.getRemoteAddr();
                    } else {
                        e = request.getHeader("x-forwarded-for");
                    }

                    List rightsList = this.userBiz.getLimRightsOfUser(limUser);
                    request.getSession().setAttribute("limUser", limUser);
                    request.getSession().setAttribute("userCode", limUser.getUserCode());
                    request.getSession().setAttribute("userId", limUser.getSalEmp().getSeNo().toString());
                    request.getSession().setAttribute("userName", limUser.getSalEmp().getSeName());
                    request.getSession().setAttribute("userNum", limUser.getUserNum());
                    request.getSession().setAttribute("CUR_EMP", limUser.getSalEmp());
                    request.getSession().setAttribute("CUR_USER", limUser);
                    if(ConfigXml.hasBFMod(request)) {
                        String dateformat = null;
                        if(limUser.getUserCtiServerObj() != null) {
                            dateformat = limUser.getUserCtiServerObj().getCtisIp();
                        } else {
                            dateformat = this.getDefaultCtiServer();
                        }

                        if(!StringUtils.isBlank(dateformat)) {
                            request.getSession().setAttribute("CUR_CTI_SERVER_IP", dateformat);
                        }
                    }

                    request.getSession().setAttribute("USER_RIGHTS", rightsList);
                    SimpleDateFormat var30 = new SimpleDateFormat("yyyy-MM-dd");
                    SimpleDateFormat dateformat1 = new SimpleDateFormat("yyyy年MM月dd日  E");
                    String time = dateformat1.format(new Date());
                    String time1 = var30.format(new Date());
                    String time2 = var30.format(OperateDate.getDate(new Date(), 1));
                    String time3 = var30.format(OperateDate.getDate(new Date(), 2));
                    String time4 = var30.format(OperateDate.getDate(new Date(), -1));
                    String time5 = var30.format(OperateDate.getDate(new Date(), -2));
                    String time6 = var30.format(OperateDate.getDate(new Date(), -3));
                    String time7 = var30.format(OperateDate.getDate(new Date(), -4));
                    request.getSession().setAttribute("time", time);
                    request.getSession().setAttribute("time1", time1);
                    request.getSession().setAttribute("time2", time2);
                    request.getSession().setAttribute("time3", time3);
                    request.getSession().setAttribute("time4", time4);
                    request.getSession().setAttribute("time5", time5);
                    request.getSession().setAttribute("TODAY_E", time);
                    request.getSession().setAttribute("TODAY", time1);
                    request.getSession().setAttribute("TMR", time2);
                    request.getSession().setAttribute("AF_TMR", time3);
                    request.getSession().setAttribute("YDAY", time4);
                    request.getSession().setAttribute("BF_YDAY", time5);
                    request.getSession().setAttribute("BF_YDAY_1", time6);
                    request.getSession().setAttribute("BF_YDAY_2", time7);
                    limUser.setUserIsLogin("1");
                    limUser.setUserIp(e);
                    this.userBiz.updateUser(limUser);
                    this.userLogBIZ.saveLogOfLogin(e, limUser);
                    logger.info("用户" + limUser.getUserCode() + "[" + limUser.getUserSeName() + "]登录系统,IP:" + e + ".");
                    if(sysPref.getSypPwdUpdDays() != null && this.userBiz.isForceUpdPwd(limUser, sysPref.getSypPwdUpdDays().intValue())) {
                        request.getSession().setAttribute("INVALID_USER_CODE", limUser.getUserCode());
                        isForceUpd = true;
                    } else {
                        request.getSession().removeAttribute("INVALID_USER_CODE");
                    }

                    isPass = true;
                } else {
                    request.setAttribute("msg", "登录名或密码错误,请重新登录!");
                    request.setAttribute("lName", loginName);
                }
            } else {
                request.setAttribute("msg", "验证码输入错误!");
                request.setAttribute("lName", loginName);
            }

            if(isPass) {
                if(isForceUpd) {
                    return mapping.findForward("forceUpdPwd");
                } else {
                    try {
                        response.sendRedirect("index.jsp");
                    } catch (IOException var27) {
                        var27.printStackTrace();
                    }

                    return null;
                }
            } else {
                return this.toLoginPage(mapping, request);
            }
        }
    }

    private ActionForward toLoginPage(ActionMapping mapping, HttpServletRequest request) {
        request.setAttribute("isHavLogin", "1");
        return mapping.findForward("login");
    }

    private String getDefaultCtiServer() {
        String defaultIP = null;
        List serverList = this.ctiServerBIZ.listCtis();
        if(serverList != null && serverList.size() > 0) {
            defaultIP = ((CtiServer)serverList.get(0)).getCtisIp();
        }

        return defaultIP;
    }

    public ActionForward checkLoginName(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String loginName = TransStr.transStr(request.getParameter("loginName"));
        String divCount = request.getParameter("divCount");
        List list = this.userBiz.getLoginName(loginName);
        PrintWriter out = null;
        String isRep = "";
        response.setContentType("text/xml;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");

        try {
            out = response.getWriter();
            if(list.size() > 0) {
                isRep = "1";
            } else {
                isRep = "0";
            }

            if(divCount != null && !divCount.equals("")) {
                isRep = isRep + "," + divCount;
            }

            out.print(isRep);
            out.flush();
        } catch (IOException var14) {
            var14.printStackTrace();
        } finally {
            out.close();
        }

        return null;
    }

    public ActionForward checkRoleName(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String rolName = TransStr.transStr(request.getParameter("rolName"));
        List list = this.userBiz.getRoleByName(rolName);
        response.setContentType("text/xml;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        PrintWriter out = null;

        try {
            out = response.getWriter();
            if(list.size() > 0) {
                out.print("1");
            }

            out.flush();
        } catch (IOException var12) {
            var12.printStackTrace();
        } finally {
            out.close();
        }

        return null;
    }

    public ActionForward userManage(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String userCode = (String)request.getSession().getAttribute("userCode");
        int allUse = this.userBiz.getCountAllUse();
        int notUse = this.userBiz.getCountNotUse();
        int isUse = allUse - notUse;
        LimUser limUser = this.userBiz.findById(userCode);
        SalOrg comOrg = this.empBiz.salOrgDesc(this.ORGTOPCODE);

        try {
            Register e1 = new Register();
            request.setAttribute("startDate", e1.getStartDate());
            request.setAttribute("endDate", e1.getEndDate());
        } catch (Exception var12) {
            var12.printStackTrace();
        }

        request.setAttribute("allUse", Integer.valueOf(allUse));
        request.setAttribute("isUse", Integer.valueOf(isUse));
        request.setAttribute("notUse", Integer.valueOf(notUse));
        request.setAttribute("limUser", limUser);
        request.setAttribute("comOrg", comOrg);
        request.setAttribute("isDefault", "1");
        return mapping.findForward("limUser");
    }

    public ActionForward goAddUser(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String curUserCode = request.getParameter("curUserCode");
        if(curUserCode != null && !curUserCode.equals("")) {
            LimUser ctisList = this.userBiz.findById(curUserCode);
            String groupList = ctisList.getSalEmp().getSeName() + "[" + (ctisList.getLimRole() != null?ctisList.getLimRole().getRolName():"") + "]";
            request.setAttribute("upUserCode", curUserCode);
            request.setAttribute("upUserName", groupList);
            request.setAttribute("upUserRolLev", ctisList.getLimRole() != null?Integer.valueOf(ctisList.getLimRole().getRolLev()):"");
            request.setAttribute("isAddLowLev", "1");
        } else {
            request.setAttribute("isAddLowLev", "0");
        }

        List ctisList1 = this.ctiServerBIZ.listCtis();
        List groupList1 = this.userBiz.listAllLimGroup();
        request.setAttribute("groupList", groupList1);
        request.setAttribute("ctisList", ctisList1);
        return mapping.findForward("addUser");
    }

    public ActionForward addEmp(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        DynaActionForm form1 = (DynaActionForm)form;
        LimUser curUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        LimUser limUser = (LimUser)form1.get("limUser");
        String upCode = request.getParameter("upCode");
        String seNo = request.getParameter("seNo");
        String grpId = request.getParameter("grpId");
        String serverId = request.getParameter("serverId");
        String channelNo = request.getParameter("channelNo");
        String ctiPhone = request.getParameter("ctiPhone");
        String userCode = this.userBiz.getMinCode();
        if(StringFormat.isEmpty(userCode)) {
            request.setAttribute("errorMsg", "账号数已达上限，无法新建账号！");
            return mapping.findForward("error");
        } else {
            String grpName = null;
            String ctiServerName = null;
            String upUserName = null;
            LimUser limUser1 = this.userBiz.findById(userCode);
            String seName;
            String log;
            if(upCode != null && !upCode.equals("")) {
                LimUser salEmp = this.userBiz.findById(upCode);
                limUser1.setLimUser(salEmp);
                seName = salEmp.getUserNum();
                log = userCode + seName;
                limUser1.setUserNum(log);
                upUserName = upCode + "-" + salEmp.getUserSeName();
            } else {
                limUser1.setLimUser((LimUser)null);
                limUser1.setUserNum(userCode);
            }

            SalEmp salEmp1 = this.empBiz.salEmpDesc(Long.valueOf(seNo));
            seName = salEmp1.getSeName();
            limUser1.setSalEmp(salEmp1);
            limUser1.setSalOrg(salEmp1.getSalOrg());
            limUser1.setLimRole(salEmp1.getLimRole());
            limUser1.setUserSeName(seName);
            if(!StringFormat.isEmpty(grpId)) {
                LimGroup log1 = this.userBiz.findLimGroupById(Long.valueOf(Long.parseLong(grpId)));
                limUser1.setUserGroup(log1);
                grpName = log1.getGrpName();
            } else {
                limUser1.setUserGroup((LimGroup)null);
            }

            if(!StringFormat.isEmpty(serverId)) {
                CtiServer log2 = this.ctiServerBIZ.findCtisById(serverId);
                limUser1.setUserCtiServerObj(log2);
                ctiServerName = log2.getCtisName() + "-" + log2.getCtisIp();
            } else {
                limUser1.setUserCtiServerObj((CtiServer)null);
            }

            if(!StringFormat.isEmpty(channelNo)) {
                limUser1.setUserChannel(Integer.valueOf(Integer.parseInt(channelNo)));
            } else {
                limUser1.setUserChannel((Integer)null);
            }

            if(!StringFormat.isEmpty(ctiPhone)) {
                limUser1.setUserCtiPhone(ctiPhone);
            } else {
                limUser1.setUserCtiPhone((String)null);
            }

            log = salEmp1.getSeLog();
            String seLog = "";
            if(log != null && !log.equals("")) {
                seLog = log + "<br>" + seName + "于" + GetDate.parseStrTime(GetDate.getCurTime()) + "开始使用" + userCode + "账号[操作人" + curUser.getUserSeName() + "]。";
            } else {
                seLog = seName + "于" + GetDate.parseStrTime(GetDate.getCurTime()) + "开始使用" + userCode + "账号[操作人" + curUser.getUserSeName() + "]。";
            }

            salEmp1.setSeLog(seLog);
            salEmp1.setSeUserCode(userCode);
            limUser1.setUserIsenabled("1");
            limUser1.setUserLoginName(limUser.getUserLoginName());
            limUser1.setUserDesc(limUser.getUserDesc());
            limUser1.setUserPwd(limUser.getUserPwd());
            limUser1.setUserPwdUpdDate(GetDate.getCurDate());
            this.userBiz.updateUser(limUser1);
            this.empBiz.updateSalEmp(salEmp1);
            this.userLogBIZ.saveLogOfAddUser(userCode, seName, grpName, upUserName, ctiServerName, channelNo, curUser);
            request.setAttribute("msg", "新建账号");
            return mapping.findForward("popDivSuc");
        }
    }

    public ActionForward goUpdate(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String curUserCode = request.getParameter("curUserCode");
        LimUser limUser = this.userBiz.findById(curUserCode);
        List groupList = this.userBiz.listAllLimGroup();
        List ctisList = this.ctiServerBIZ.listCtis();
        request.setAttribute("groupList", groupList);
        request.setAttribute("ctisList", ctisList);
        request.setAttribute("limUser", limUser);
        return mapping.findForward("updateAddEmp");
    }

    public ActionForward updateAddEmp(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        DynaActionForm form1 = (DynaActionForm)form;
        LimUser curUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        LimUser limUser = (LimUser)form1.get("limUser");
        String curUserCode = request.getParameter("curUserCode");
        String seNo = request.getParameter("seNo");
        String oldSeNo = request.getParameter("oldSeNo");
        String upCode = request.getParameter("userCode");
        String grpId = request.getParameter("grpId");
        String serverId = request.getParameter("serverId");
        String channelNo = request.getParameter("channelNo");
        String ctiPhone = request.getParameter("ctiPhone");
        String userNum = "";
        LimUser limUser1 = this.userBiz.findById(curUserCode);
        String origEmp = limUser1.getSalEmp() != null?limUser1.getSalEmp().getSeName():"无";
        String origGroup = limUser1.getUserGroup() != null?limUser1.getUserGroup().getGrpName():"无";
        String origUpUser = limUser1.getLimUser() != null?limUser1.getLimUser().getUserCode() + "-" + limUser1.getLimUser().getUserSeName():"无";
        String origLoginName = limUser1.getUserLoginName();
        String origPwd = limUser1.getUserPwd();
        String origCtiServer = limUser1.getUserCtiServerObj() != null?limUser1.getUserCtiServerObj().getCtisName():"无";
        String origChannel = limUser1.getUserChannel() != null?limUser1.getUserChannel().toString():"无";
        String updatedGroup;
        String updatedUpUser;
        String updatedLoginName;
        String updatedPwd;
        if(!seNo.equals(oldSeNo)) {
            SalEmp updatedEmp = limUser1.getSalEmp();
            if(updatedEmp != null) {
                updatedGroup = updatedEmp.getSeLog();
                if(updatedGroup != null && !updatedGroup.equals("")) {
                    updatedGroup = updatedGroup + "<br>" + updatedEmp.getSeName() + "于" + GetDate.parseStrTime(GetDate.getCurTime()) + "停用" + curUserCode + "账号[操作人" + curUser.getUserSeName() + "]。";
                } else {
                    updatedGroup = updatedEmp.getSeName() + "于" + GetDate.parseStrTime(GetDate.getCurTime()) + "停用" + curUserCode + "账号[操作人" + curUser.getUserSeName() + "]。";
                }

                updatedEmp.setSeLog(updatedGroup);
                updatedEmp.setSeUserCode((String)null);
                this.empBiz.updateSalEmp(updatedEmp);
            }

            SalEmp updatedGroup1 = this.empBiz.salEmpDesc(Long.valueOf(seNo));
            updatedUpUser = updatedGroup1.getSeLog();
            updatedLoginName = updatedGroup1.getSeName();
            limUser1.setSalEmp(updatedGroup1);
            limUser1.setSalOrg(updatedGroup1.getSalOrg());
            limUser1.setLimRole(updatedGroup1.getLimRole());
            limUser1.setUserSeName(updatedLoginName);
            updatedPwd = "";
            if(updatedUpUser != null && !updatedUpUser.equals("")) {
                updatedPwd = updatedUpUser + "<br>" + updatedLoginName + "于" + GetDate.parseStrTime(GetDate.getCurTime()) + "开始使用" + curUserCode + "账号[操作人" + curUser.getUserSeName() + "]。";
            } else {
                updatedPwd = updatedLoginName + "于" + GetDate.parseStrTime(GetDate.getCurTime()) + "开始使用" + curUserCode + "账号[操作人" + curUser.getUserSeName() + "]。";
            }

            updatedGroup1.setSeLog(updatedPwd);
            updatedGroup1.setSeUserCode(curUserCode);
            this.empBiz.updateSalEmp(updatedGroup1);
        }

        limUser1.setUserLoginName(limUser.getUserLoginName());
        if(!limUser.getUserPwd().equals(limUser1.getUserPwd())) {
            limUser1.setUserPwd(limUser.getUserPwd());
            limUser1.setUserPwdUpdDate(GetDate.getCurDate());
        }

        if(!StringFormat.isEmpty(grpId)) {
            LimGroup updatedEmp1 = this.userBiz.findLimGroupById(Long.valueOf(Long.parseLong(grpId)));
            limUser1.setUserGroup(updatedEmp1);
        } else {
            limUser1.setUserGroup((LimGroup)null);
        }

        if(!StringFormat.isEmpty(serverId)) {
            CtiServer updatedEmp2 = this.ctiServerBIZ.findCtisById(serverId);
            limUser1.setUserCtiServerObj(updatedEmp2);
        } else {
            limUser1.setUserCtiServerObj((CtiServer)null);
        }

        if(!StringFormat.isEmpty(channelNo)) {
            limUser1.setUserChannel(Integer.valueOf(Integer.parseInt(channelNo)));
        } else {
            limUser1.setUserChannel((Integer)null);
        }

        if(!StringFormat.isEmpty(ctiPhone)) {
            limUser1.setUserCtiPhone(ctiPhone);
        } else {
            limUser1.setUserCtiPhone((String)null);
        }

        limUser1.setUserDesc(limUser.getUserDesc());
        if(upCode != null && !upCode.equals("")) {
            LimUser updatedEmp3 = this.userBiz.findById(upCode);
            userNum = limUser1.getUserCode() + updatedEmp3.getUserNum();
            limUser1.setLimUser(updatedEmp3);
            limUser1.setUserNum(userNum);
        } else {
            userNum = limUser1.getUserCode();
            limUser1.setLimUser((LimUser)null);
            limUser1.setUserNum(userNum);
        }

        this.userBiz.UpdateLowUserNum(limUser1, userNum);
        this.userBiz.updateUser(limUser1);
        String updatedEmp4 = limUser1.getSalEmp() != null?limUser1.getSalEmp().getSeName():"无";
        updatedGroup = limUser1.getUserGroup() != null?limUser1.getUserGroup().getGrpName():"无";
        updatedUpUser = limUser1.getLimUser() != null?limUser1.getLimUser().getUserCode() + "-" + limUser1.getLimUser().getUserSeName():"无";
        updatedLoginName = limUser1.getUserLoginName();
        updatedPwd = limUser1.getUserPwd();
        String updatedCtiServer = limUser1.getUserCtiServerObj() != null?limUser1.getUserCtiServerObj().getCtisName():"无";
        String updatedChannel = limUser1.getUserChannel() != null?limUser1.getUserChannel().toString():"无";
        this.userLogBIZ.saveLogOfUpdUser(curUserCode, origEmp, updatedEmp4, origGroup, updatedGroup, origUpUser, updatedUpUser, origLoginName, updatedLoginName, origPwd, updatedPwd, origCtiServer, updatedCtiServer, origChannel, updatedChannel, curUser);
        request.setAttribute("msg", "编辑账号");
        return mapping.findForward("popDivSuc");
    }

    public ActionForward lockUser(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        LimUser curUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        String userCode = request.getParameter("userCode");
        String islock = request.getParameter("islock");
        LimUser limUser = this.userBiz.findById(userCode);
        if(islock != null && !islock.equals("")) {
            if(islock.equals("1")) {
                limUser.setUserIsenabled("2");
                this.userBiz.updateUser(limUser);
                this.userLogBIZ.saveLogOfLockUser(false, userCode, curUser);
            } else {
                limUser.setUserIsenabled("1");
                this.userBiz.updateUser(limUser);
                this.userLogBIZ.saveLogOfUnLockUser(userCode, curUser);
            }
        }

        String url = "userAction.do?op=getUser&userCode=" + userCode;

        try {
            response.sendRedirect(url);
        } catch (IOException var11) {
            var11.printStackTrace();
        }

        return null;
    }

    public ActionForward delConfirm(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String code = request.getParameter("code");
        String delType = request.getParameter("delType");
        request.setAttribute("code", code);
        request.setAttribute("delType", delType);
        return mapping.findForward("delConfirm");
    }

    public ActionForward delUser(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        LimUser curUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        String userCode = request.getParameter("userCode");
        LimUser limUser = this.userBiz.findById(userCode);
        List list = this.userBiz.checkDownCode(userCode);
        if(list.size() > 0) {
            request.setAttribute("ms", "请确认该账号没有下级账号！");
            return mapping.findForward("faileDel");
        } else {
            SalEmp salEmp = limUser.getSalEmp();
            if(salEmp != null) {
                String oldLog = salEmp.getSeLog();
                if(oldLog != null && !oldLog.equals("")) {
                    oldLog = oldLog + "<br>" + salEmp.getSeName() + "于" + GetDate.parseStrTime(GetDate.getCurTime()) + "停用" + userCode + "账号[操作人" + curUser.getUserSeName() + "]。";
                } else {
                    oldLog = salEmp.getSeName() + "于" + GetDate.parseStrTime(GetDate.getCurTime()) + "停用" + userCode + "账号[操作人" + curUser.getUserSeName() + "]。";
                }

                salEmp.setSeLog(oldLog);
                salEmp.setSeUserCode((String)null);
                this.empBiz.updateSalEmp(salEmp);
            }

            this.userBiz.delRUserRig(userCode, (String)null);
            this.vraBiz.clsVRA(userCode);
            limUser.setUserNum((String)null);
            limUser.setLimUser((LimUser)null);
            limUser.setUserSeName((String)null);
            limUser.setSalOrg((SalOrg)null);
            limUser.setLimRole((LimRole)null);
            limUser.setSalEmp((SalEmp)null);
            limUser.setUserDesc((String)null);
            limUser.setUserIsenabled((String)null);
            limUser.setUserLoginName((String)null);
            limUser.setUserPwd((String)null);
            limUser.setUserIsLogin((String)null);
            limUser.setUserIp((String)null);
            limUser.setUserCtiLogin((String)null);
            limUser.setUserCtiPwd((String)null);
            limUser.setUserCtiServer((String)null);
            limUser.setUserCtiPhone((String)null);
            limUser.setUserPwdUpdDate((Date)null);
            limUser.setUserGroup((LimGroup)null);
            limUser.setUserSmsMaxNum((Integer)null);
            limUser.setUserCtiServerObj((CtiServer)null);
            this.userBiz.updateUser(limUser);
            this.userLogBIZ.saveLogOfCloseUser(limUser.getUserCode(), salEmp.getSeName(), curUser);
            request.setAttribute("msg", "关闭账号");
            return mapping.findForward("popDivSuc");
        }
    }

    public void cleanUserRight(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        LimUser limUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        String userCode = request.getParameter("userCode");
        if(!StringFormat.isEmpty(userCode)) {
            this.userBiz.delRUserRig(userCode, (String)null);
            this.userLogBIZ.saveLogOfCleanUserRight(userCode, limUser);
            AjaxUtils.outPrintText(response, "1");
        } else {
            AjaxUtils.outPrintText(response, "0");
        }

    }

    public ActionForward goUpdatePwd(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        LimUser limUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        limUser = this.userBiz.findById(limUser.getUserCode());
        request.setAttribute("limUser", limUser);
        return mapping.findForward("updatePwd");
    }

    public ActionForward updatePwd(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String userCode = request.getParameter("userCode");
        LimUser limUser = this.userBiz.findById(userCode);
        String userPwd = request.getParameter("userPwd");
        String loginName = request.getParameter("loginName");
        String isForceUpd = request.getParameter("isForceUpd");
        if(!userPwd.equals(limUser.getUserPwd())) {
            limUser.setUserPwd(userPwd);
            limUser.setUserPwdUpdDate(GetDate.getCurDate());
        }

        limUser.setUserLoginName(loginName);
        this.userBiz.updateUser(limUser);
        if("1".equals(isForceUpd)) {
            request.getSession().invalidate();
            return this.start(mapping, form, request, response);
        } else {
            request.setAttribute("limUser", limUser);
            request.setAttribute("msg", "信息修改成功！");
            return mapping.findForward("updatePwd");
        }
    }

    public void updateCtiInf(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String userCode = request.getParameter("userCode");
        String ctiPhone = request.getParameter("ctiPhone");
        String ctiServer = request.getParameter("ctiServer");
        String ctiLogin = request.getParameter("ctiLogin");
        String ctiPwd = request.getParameter("ctiPwd");
        LimUser limUser = this.userBiz.findById(userCode);
        limUser.setUserCtiPhone(ctiPhone);
        limUser.setUserCtiServer(ctiServer);
        limUser.setUserCtiLogin(ctiLogin);
        limUser.setUserCtiPwd(ctiPwd);
        this.userBiz.updateUser(limUser);
        AjaxUtils.outPrintText(response, "1");
    }

    public void checkPwdValid(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String pwd = request.getParameter("pwd");
        SysPref sysPref = (SysPref)request.getSession().getAttribute("SYS_PREF");
        int pwdRule = sysPref.getSypPwdRule() != null?Integer.parseInt(sysPref.getSypPwdRule()):0;
        int pwdLen = sysPref.getSypPwdLen() != null?sysPref.getSypPwdLen().intValue():0;
        String rs;
        if(this.userBiz.checkPwdValid(pwd, pwdRule, pwdLen)) {
            rs = "suc";
        } else {
            rs = pwdRule + "," + pwdLen;
        }

        AjaxUtils.outPrintText(response, rs);
    }

    public ActionForward getUser(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String userCode = request.getParameter("userCode");
        LimUser limUser = this.userBiz.getUser(userCode);
        if(limUser == null) {
            return this.userManage(mapping, form, request, response);
        } else {
            List userAreaList = this.caseBIZ.listUserArea(limUser.getUserCode());
            int allUse = this.userBiz.getCountAllUse();
            int notUse = this.userBiz.getCountNotUse();
            int isUse = allUse - notUse;
            request.setAttribute("userAreaList", userAreaList);
            request.setAttribute("allUse", Integer.valueOf(allUse));
            request.setAttribute("isUse", Integer.valueOf(isUse));
            request.setAttribute("notUse", Integer.valueOf(notUse));
            request.setAttribute("limUser", limUser);
            return mapping.findForward("limUser");
        }
    }

    public ActionForward searchLimRole(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        List list = this.userBiz.roleSerach();
        request.setAttribute("limRole", list);
        return mapping.findForward("userRole");
    }

    public ActionForward toSaveRole(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        List groupList = this.userBiz.listAllLimGroup();
        request.setAttribute("groupList", groupList);
        return mapping.findForward("newRole");
    }

    public ActionForward saveRole(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        DynaActionForm form1 = (DynaActionForm)form;
        LimRole limRole = (LimRole)form1.get("limRole");
        String grpId = request.getParameter("grpId");
        if(!StringFormat.isEmpty(grpId)) {
            limRole.setRolGroup(new LimGroup(Long.valueOf(Long.parseLong(grpId))));
        }

        this.userBiz.saveRole(limRole);
        request.setAttribute("msg", "添加职位");
        return mapping.findForward("popDivSuc");
    }

    public ActionForward goUpdateRole(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        Long rolId = Long.valueOf(Long.parseLong(request.getParameter("rolId")));
        LimRole limRole = this.userBiz.getRole(rolId);
        List groupList = this.userBiz.listAllLimGroup();
        request.setAttribute("limRole", limRole);
        request.setAttribute("groupList", groupList);
        return mapping.findForward("updateRole");
    }

    public ActionForward updateRole(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        Long rolId = Long.valueOf(Long.parseLong(request.getParameter("rolId")));
        String grpId = request.getParameter("grpId");
        LimRole limRole = this.userBiz.getRole(rolId);
        if(!StringFormat.isEmpty(grpId)) {
            limRole.setRolGroup(new LimGroup(Long.valueOf(Long.parseLong(grpId))));
        } else {
            limRole.setRolGroup((LimGroup)null);
        }

        limRole.setRolName(request.getParameter("rolName"));
        limRole.setRolLev(Integer.parseInt(request.getParameter("rolLev")));
        limRole.setRoleDesc(request.getParameter("roleDesc"));
        this.userBiz.updateRole(limRole);
        request.setAttribute("msg", "修改职位");
        return mapping.findForward("popDivSuc");
    }

    public ActionForward delRole(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String rolId = request.getParameter("rolId");
        LimRole limRole = this.userBiz.getRole(Long.valueOf(Long.parseLong(rolId)));
        boolean isDel = true;
        StringBuilder eMsg = new StringBuilder();
        int count;
        if(limRole.getSalEmps() != null) {
            count = 0;
            Set list = limRole.getSalEmps();
            if(list != null) {
                count = list.size();
            }

            if(count > 0) {
                isDel = false;
                StringFormat.getDelEMsg(eMsg, count, 0, "员工");
            }
        }

        if(this.userBiz.checkRole(rolId) != null) {
            count = 0;
            List list1 = this.userBiz.checkRole(rolId);
            if(list1 != null) {
                count = list1.size();
            }

            if(count > 0) {
                isDel = false;
                StringFormat.getDelEMsg(eMsg, count, 0, "用户");
            }
        }

        if(isDel) {
            this.userBiz.delRole(limRole);
            request.setAttribute("msg", "删除职位");
            return mapping.findForward("popDivSuc");
        } else {
            request.setAttribute("errorMsg", "请先删除该职位下的关联数据：" + eMsg.toString());
            return mapping.findForward("error");
        }
    }

    public ActionForward roleXml(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) throws DocumentException, IOException {
        response.setContentType("application/xml;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        SAXReader reader = new SAXReader();
        String url = request.getSession().getServletContext().getRealPath("/") + "system\\role.xml";
        Document document = reader.read(new File(url));
        Element root = document.getRootElement();
        List rolenums = root.elements("rolenum");
        String xlnum = request.getParameter("xmlNum");
        String maxLev = this.userBiz.maxRole();
        if(!StringFormat.isEmpty(maxLev) && Integer.parseInt(maxLev) > Integer.parseInt(xlnum)) {
            request.setAttribute("msg", "您设置的最大职级必须大于正在使用中的最大职级！");
            return mapping.findForward("roleLev");
        } else {
            Iterator e = rolenums.iterator();

            while(e.hasNext()) {
                Element xmlWriter = (Element)e.next();
                Element num = xmlWriter.element("num");
                num.setText(xlnum);
            }

            try {
                XMLWriter xmlWriter1 = new XMLWriter(new FileWriter(url));
                xmlWriter1.write(document);
                xmlWriter1.flush();
                xmlWriter1.close();
            } catch (IOException var15) {
                var15.printStackTrace();
            }

            request.setAttribute("ms", xlnum);
            request.setAttribute("msg", "修改职级");
            return mapping.findForward("popDivSuc");
        }
    }

    public ActionForward limUserTree(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String type = request.getParameter("type");
        String removeUser = request.getParameter("removeUser");
        List limUsers = null;
        if(removeUser != null && !removeUser.equals("")) {
            limUsers = this.userBiz.getUserWithOut(removeUser);
        } else {
            limUsers = this.userBiz.listValidUser();
        }

        request.setAttribute("limUsers", limUsers);
        request.setAttribute("type", type);
        return mapping.findForward("limUserTree");
    }

    public ActionForward setFunLim(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        LimUser curUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        String userCode = request.getParameter("userCode");
        String funType = request.getParameter("funType");
        String[] userLim2 = request.getParameterValues("chkbLim");
        List origNamesList = this.userBiz.getRUserRigNamesByUser(userCode, funType);
        List newNamesList = null;
        this.userBiz.delRUserRig(userCode, funType);
        this.userBiz.addRUserRig(userLim2, userCode, funType);
        String url;
        if(userLim2 != null && userLim2.length > 0) {
            ArrayList diffs = new ArrayList();
            String[] var15 = userLim2;
            int var14 = userLim2.length;

            for(int e = 0; e < var14; ++e) {
                url = var15[e];
                diffs.add(url);
            }

            newNamesList = this.userBiz.getRightNamesByIds(diffs);
        }

        List[] var17 = StringFormat.getDiffs(origNamesList, newNamesList);
        if(var17 != null) {
            this.userLogBIZ.saveLogOfUpdUserRight(var17, userCode, curUser);
        }

        url = "userAction.do?op=getFunOperate&funType=" + funType + "&userCode=" + userCode;

        try {
            response.sendRedirect(url);
        } catch (IOException var16) {
            var16.printStackTrace();
        }

        return null;
    }

    public ActionForward getFunOperate(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String funType = request.getParameter("funType");
        String userCode = request.getParameter("userCode");
        LimUser limUser = this.userBiz.findById(userCode);
        List rigList = this.userBiz.getLimRight(userCode, funType);
        List funcList = this.userBiz.getLimFuncByType(funType);
        request.setAttribute("user", limUser);
        request.setAttribute("funType", funType);
        request.setAttribute("rigList", rigList);
        request.setAttribute("funcList", funcList);
        return mapping.findForward("setOpRight");
    }

    public ActionForward iniSystem(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String cusName = request.getParameter("cusName");
        String passWord = request.getParameter("password");

        try {
            Register ex = new Register();
            if(!ex.writeRegInfo(cusName, passWord)) {
                request.setAttribute("msg", "注册名称或授权码填写不正确！");
                request.setAttribute("isReg", "1");
                return this.toLoginPage(mapping, request);
            } else {
                SalOrg salOrg1 = this.empBiz.salOrgDesc(this.ORGTOPCODE);
                if(salOrg1 == null) {
                    SalOrg salOrg = new SalOrg();
                    salOrg.setSoCode(this.ORGTOPCODE);
                    salOrg.setSoName(cusName);
                    salOrg.setSoIsenabled("1");
                    this.empBiz.save(salOrg);
                    SalEmp salEmp = new SalEmp();
                    salEmp.setSeIsenabled("2");
                    salEmp.setSeName("超级管理员");
                    this.empBiz.save(salEmp);
                    String userCode = "u000";
                    LimUser limUser = new LimUser();
                    limUser.setUserCode(userCode);
                    limUser.setUserIsenabled("3");
                    limUser.setUserLoginName("admin");
                    limUser.setUserPwd(LimUser.encryptPwd("admin"));
                    limUser.setSalEmp(salEmp);
                    limUser.setUserSeName(salEmp.getSeName());
                    limUser.setUserPwdUpdDate(GetDate.getCurDate());
                    this.userBiz.saveLim(limUser);

                    for(int i = 1; i <= Integer.parseInt(ex.getUserSize()); ++i) {
                        String uCode = "";
                        if(i < 10) {
                            uCode = "u00" + i;
                        } else if(i >= 10 && i < 100) {
                            uCode = "u0" + i;
                        } else {
                            uCode = "u" + i;
                        }

                        LimUser limUser1 = new LimUser();
                        limUser1.setUserCode(uCode);
                        this.userBiz.saveLim(limUser1);
                    }
                }

                return mapping.findForward("iniSuc");
            }
        } catch (ClassNotFoundException var16) {
            var16.printStackTrace();
            request.setAttribute("msg", "SQL驱动文件丢失，无法执行查询！");
            request.setAttribute("isReg", "1");
            return this.toLoginPage(mapping, request);
        } catch (SQLException var17) {
            var17.printStackTrace();
            request.setAttribute("msg", "无法连接远程服务器，请确保已正确连接网络！");
            request.setAttribute("isReg", "1");
            return this.toLoginPage(mapping, request);
        } catch (Exception var18) {
            var18.printStackTrace();
            request.setAttribute("errorMsg", var18.toString());
            return mapping.findForward("error");
        }
    }

    public ActionForward updateSystem(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String cusName = request.getParameter("cusName");
        String passWord = request.getParameter("password");
        String isLoginPage = request.getParameter("isLoginPage");

        try {
            Register ex = new Register();
            if(!ex.writeRegInfo(cusName, passWord)) {
                if(isLoginPage != null && isLoginPage.equals("1")) {
                    request.setAttribute("msg", "注册名称或授权码填写不正确！");
                    request.setAttribute("isReg", "1");
                    return this.toLoginPage(mapping, request);
                } else {
                    request.setAttribute("errorMsg", "授权信息更新失败！");
                    return mapping.findForward("error");
                }
            } else {
                int userNum1 = this.userBiz.getCountAllUse();
                if(userNum1 > Integer.parseInt(ex.getUserSize())) {
                    request.setAttribute("errorMsg", "导入授权信息失败");
                    return mapping.findForward("error");
                } else {
                    int len = Integer.parseInt(ex.getUserSize());

                    for(int i = 1; i <= len - userNum1; ++i) {
                        String uCode = "";
                        int addCode = userNum1 + i;
                        if(addCode < 10) {
                            uCode = "u00" + addCode;
                        } else if(addCode >= 10 && addCode < 100) {
                            uCode = "u0" + addCode;
                        } else {
                            uCode = "u" + addCode;
                        }

                        LimUser limUser1 = new LimUser();
                        limUser1.setUserCode(uCode);
                        this.userBiz.saveLim(limUser1);
                    }

                    if(isLoginPage != null && isLoginPage.equals("1")) {
                        return mapping.findForward("iniSuc");
                    } else {
                        request.setAttribute("msg", "导入授权信息");
                        return mapping.findForward("popDivSuc");
                    }
                }
            }
        } catch (ClassNotFoundException var15) {
            var15.printStackTrace();
            request.setAttribute("errorMsg", "SQL驱动文件丢失，无法执行查询！");
            return mapping.findForward("error");
        } catch (SQLException var16) {
            var16.printStackTrace();
            request.setAttribute("errorMsg", "无法连接远程服务器，请确保已正确连接网络！");
            return mapping.findForward("error");
        } catch (Exception var17) {
            var17.printStackTrace();
            request.setAttribute("errorMsg", var17.toString());
            return mapping.findForward("error");
        }
    }

    public ActionForward toListLimGroup(ActionMapping mapping, ActionForm form, HttpServletRequest requrest, HttpServletResponse response) {
        return mapping.findForward("listLimGroup");
    }

    public void listLimGroup(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String grpName = request.getParameter("grpName");
        String p = request.getParameter("p");
        String orderCol = request.getParameter("orderCol");
        String isDe = request.getParameter("isDe");
        String pageSize = request.getParameter("pageSize");
        String orderItem = "";
        String[] items = new String[]{"grpName", "grpDesc", "creTime", "creMan", "updTime", "updMan"};
        if(p == null || p.trim().length() < 1) {
            p = "1";
        }

        if(pageSize == null || pageSize.equals("")) {
            pageSize = "30";
        }

        if(orderCol != null && !orderCol.equals("")) {
            orderItem = items[Integer.parseInt(orderCol)];
        }

        Page page = new Page(this.userBiz.listLimGroupCount(grpName), Integer.parseInt(pageSize));
        page.setCurrentPageNo(Integer.parseInt(p));
        List list = this.userBiz.listLimGroup(grpName, orderItem, isDe, page.getCurrentPageNo(), page.getPageSize());
        ListForm listForm = new ListForm();
        listForm.setList(list);
        listForm.setPage(page);
        LinkedList awareCollect = new LinkedList();
        AjaxUtils.outPrintJSON(response, (new JSONConvert()).model2JSON(listForm, awareCollect));
    }

    public ActionForward toSaveLimGroup(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String grpId = request.getParameter("grpId");
        List rightList = this.userBiz.getAllLimRights();
        List funcList = this.userBiz.getAllLimFunc();
        if(!StringFormat.isEmpty(grpId)) {
            LimGroup limGroup = this.userBiz.findLimGroupById(Long.valueOf(Long.parseLong(grpId)));
            request.setAttribute("limRightList", this.userBiz.setEnabledRight(rightList, limGroup));
            request.setAttribute("limGroup", limGroup);
        } else {
            request.setAttribute("limRightList", rightList);
        }

        request.setAttribute("limFuncList", funcList);
        return mapping.findForward("saveLimGroup");
    }

    public ActionForward saveLimGroup(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        DynaActionForm form1 = (DynaActionForm)form;
        LimUser curUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        LimGroup limGroup = (LimGroup)form1.get("limGroup");
        String[] rightCodes = request.getParameterValues("grpRight");
        String grpId = request.getParameter("grpId");
        if(!StringFormat.isEmpty(grpId)) {
            limGroup.setGrpId(Long.valueOf(Long.parseLong(grpId)));
            limGroup.setGrpUpdMan(curUser.getUserSeName());
            limGroup.setGrpUpdTime(GetDate.getCurTime());
        } else {
            limGroup.setGrpCreMan(curUser.getUserSeName());
            limGroup.setGrpCreTime(GetDate.getCurTime());
        }

        this.userBiz.saveLimGroup(limGroup, rightCodes, curUser);
        request.setAttribute("msg", "保存权限组");
        return mapping.findForward("popDivSuc");
    }

    public void checkIsRepeatGrpName(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String grpName = request.getParameter("grpName");
        String rs = "0";
        if(!StringFormat.isEmpty(grpName)) {
            grpName = TransStr.transStr(grpName);
            if(this.userBiz.getCountByGrpName(grpName) > 0) {
                rs = "1";
            }
        }

        AjaxUtils.outPrintText(response, rs);
    }

    public ActionForward delLimGroup(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        LimUser curUser = (LimUser)request.getSession().getAttribute("CUR_USER");
        String grpId = request.getParameter("grpId");
        String errMsg = "";
        List userList = this.userBiz.getUserCodesByGrpId(grpId);
        StringBuilder userCodes = new StringBuilder();
        Iterator userIt = userList.iterator();

        while(userIt.hasNext()) {
            userCodes.append((String)userIt.next()).append(",");
        }

        if(userCodes.length() > 0) {
            errMsg = errMsg + "账号【" + StringFormat.removeLastSplitWord(userCodes.toString()) + "】已分配此权限组，";
        }

        List roleList = this.userBiz.getRoleNamesByGrpId(grpId);
        StringBuilder roleNames = new StringBuilder();
        Iterator it = roleList.iterator();

        while(it.hasNext()) {
            roleNames.append((String)it.next()).append(",");
        }

        if(roleNames.length() > 0) {
            errMsg = errMsg + "职位【" + StringFormat.removeLastSplitWord(roleNames.toString()) + "】已设定为初始权限组，";
        }

        if(!StringFormat.isEmpty(errMsg)) {
            request.setAttribute("errorMsg", errMsg + "请先清除或者重选这些关联数据下的权限组");
            return mapping.findForward("error");
        } else {
            LimGroup grp = this.userBiz.findLimGroupById(Long.valueOf(Long.parseLong(grpId)));
            String name = grp.getGrpName();
            this.userBiz.deleteLimGroup(grpId);
            this.userLogBIZ.saveLogOfDeleteLimGroup(name, curUser);
            request.setAttribute("msg", "删除权限组");
            return mapping.findForward("popDivSuc");
        }
    }

    public ActionForward toListUserLog(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        return mapping.findForward("listUserLog");
    }

    public void listUserLog(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String userCode = request.getParameter("userCode");
        String logType = request.getParameter("logType");
        String userName = request.getParameter("userName");
        String logTimeStart = request.getParameter("logTimeStart");
        String logTimeEnd = request.getParameter("logTimeEnd");
        String orderCol = request.getParameter("orderCol");
        String isDe = request.getParameter("isDe");
        String p = request.getParameter("p");
        String pageSize = request.getParameter("pageSize");
        String orderItem = "";
        String[] items = new String[]{"time", "type", "oper"};
        String[] args = new String[]{userCode, logType, userName, logTimeStart, logTimeEnd};
        if(p == null || p.trim().length() < 1) {
            p = "1";
        }

        if(pageSize == null || pageSize.equals("")) {
            pageSize = "50";
        }

        if(orderCol != null && !orderCol.equals("")) {
            orderItem = items[Integer.parseInt(orderCol)];
        }

        Page page = new Page(this.userLogBIZ.listUserLogCount(args), Integer.parseInt(pageSize));
        page.setCurrentPageNo(Integer.parseInt(p));
        List userLogList = this.userLogBIZ.listUserLog(args, orderItem, isDe, page.getCurrentPageNo(), page.getPageSize());
        ListForm listForm = new ListForm();
        listForm.setList(userLogList);
        listForm.setPage(page);
        AjaxUtils.outPrintJSON(response, (new JSONConvert()).model2JSON(listForm, (Collection)null));
    }

    public ActionForward toDeleteUserLog(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        request.setAttribute("opType", "delUserLog");
        return mapping.findForward("batOperate");
    }

    public ActionForward deleteUserLog(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String ids = request.getParameter("pars");
        this.userLogBIZ.deleteUserLogByIds(ids);
        request.setAttribute("msg", "删除账号日志");
        return mapping.findForward("popDivSuc");
    }

    public void setUserBiz(UserBIZ userBiz) {
        this.userBiz = userBiz;
    }

    public void setEmpBiz(EmpBIZ empBiz) {
        this.empBiz = empBiz;
    }

    public void setMessageBiz(MessageBIZ messageBiz) {
        this.messageBiz = messageBiz;
    }

    public void setVraBiz(VisRecAssBIZ vraBiz) {
        this.vraBiz = vraBiz;
    }

    public void setCaseBIZ(CaseBIZ caseBIZ) {
        this.caseBIZ = caseBIZ;
    }

    public void setUserLogBIZ(UserLogBIZ userLogBIZ) {
        this.userLogBIZ = userLogBIZ;
    }
}
