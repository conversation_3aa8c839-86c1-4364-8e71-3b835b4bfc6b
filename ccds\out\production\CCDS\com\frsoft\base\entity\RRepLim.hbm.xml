<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.RRepLim" table="r_rep_lim" schema="dbo" >
        <id name="rrlId" type="java.lang.Long">
            <column name="rrl_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="rrl_se_no"/>
        </many-to-one>
        <many-to-one name="report" class="com.frsoft.base.entity.Report" fetch="select" not-null="false">
            <column name="rrl_rep_code" length="50" />
        </many-to-one>
         <property name="rrlContent" type="java.lang.String">
            <column name="rrl_content" length="1073741823" />
         </property>
         <property name="rrlIsappro" type="java.lang.String">
            <column name="rrl_isappro" length="1" />
         </property>
         <property name="rrlIsdel" type="java.lang.String">
            <column name="rrl_isdel" length="1" />
         </property>
         <property name="rrlAppOrder" type="java.lang.Integer">
            <column name="rrl_app_order" />
         </property>
         <property name="rrlIsView" type="java.lang.String">
            <column name="rrl_isview" length="1" />
         </property>
          <property name="rrlRecUser" type="java.lang.String">
            <column name="rrl_rec_user" length="50" />
         </property>
         <property name="rrlIsAllAppro" type="java.lang.String">
            <column name="rrl_is_all_appro" length="1" />
         </property>
         <property name="rrlOpproDate" type="java.util.Date">
            <column name="rrl_oppro_date" length="23" />
        </property>
        <property name="rrlDate" type="java.util.Date">
            <column name="rrl_date" length="23" />
        </property>
    </class>
</hibernate-mapping>
