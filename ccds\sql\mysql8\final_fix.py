#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复脚本 - 修复MySQL文件格式问题
"""

import re

def final_fix(sql_content):
    """最终修复MySQL SQL文件"""
    
    print("开始最终修复...")
    
    # 1. 修复CREATE DATABASE语句
    sql_content = re.sub(r'CREATE DATABASE\s*\n', 'CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n', sql_content)
    
    # 2. 修复CREATE TABLE语句 - 确保每个都有正确的结尾
    def fix_table_statement(match):
        table_content = match.group(0)
        
        # 如果没有ENGINE，添加ENGINE
        if 'ENGINE=' not in table_content.upper():
            # 找到最后的)
            table_content = re.sub(r'\)\s*$', ') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;', table_content, flags=re.MULTILINE)
        
        # 确保以分号结尾
        if not table_content.strip().endswith(';'):
            table_content = table_content.rstrip() + ';'
        
        return table_content
    
    # 匹配CREATE TABLE语句（包括多行）
    pattern = r'CREATE TABLE `\w+`\s*\([^;]*\)(?:\s*ENGINE[^;]*)?'
    sql_content = re.sub(pattern, fix_table_statement, sql_content, flags=re.IGNORECASE | re.DOTALL)
    
    # 3. 修复索引语句
    sql_content = re.sub(
        r'CREATE INDEX `([^`]+)` ON `([^`]+)` \(`([^`]+)`\);[^;]*',
        r'CREATE INDEX `\1` ON `\2` (`\3`);',
        sql_content, flags=re.IGNORECASE
    )
    
    # 4. 清理多余的分号和空行
    sql_content = re.sub(r';\s*;\s*', ';\n', sql_content)
    sql_content = re.sub(r'\n\s*\n\s*\n', '\n\n', sql_content)
    
    # 5. 确保每个DROP TABLE后面跟着CREATE TABLE
    lines = sql_content.split('\n')
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        if line.startswith('DROP TABLE IF EXISTS'):
            fixed_lines.append(lines[i])
            i += 1
            
            # 查找对应的CREATE TABLE
            while i < len(lines) and not lines[i].strip().startswith('CREATE TABLE'):
                if lines[i].strip():  # 跳过空行
                    break
                i += 1
            
            if i < len(lines) and lines[i].strip().startswith('CREATE TABLE'):
                # 收集整个CREATE TABLE语句
                create_table_lines = []
                paren_count = 0
                
                while i < len(lines):
                    create_table_lines.append(lines[i])
                    
                    # 计算括号
                    paren_count += lines[i].count('(') - lines[i].count(')')
                    
                    # 如果括号平衡且以分号结尾，表示CREATE TABLE结束
                    if paren_count == 0 and lines[i].strip().endswith(';'):
                        break
                    
                    i += 1
                
                # 添加CREATE TABLE语句
                fixed_lines.extend(create_table_lines)
                fixed_lines.append('')  # 添加空行分隔
        else:
            if line:  # 只添加非空行
                fixed_lines.append(lines[i])
        
        i += 1
    
    return '\n'.join(fixed_lines)

def process_file(input_file, output_file):
    """处理单个文件"""
    try:
        print(f"正在修复文件: {input_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixed_content = final_fix(content)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"修复完成: {output_file}")
        return True
        
    except Exception as e:
        print(f"修复失败 {input_file}: {e}")
        return False

def main():
    """主函数"""
    files_to_fix = [
        ("mysql8_CCDS_structure_clean.sql", "mysql8_CCDS_structure_final.sql"),
        ("mysql8_CCDS_data_clean.sql", "mysql8_CCDS_data_final.sql"),
        ("mysql8_CCDS_cashbus_clean.sql", "mysql8_CCDS_cashbus_final.sql")
    ]
    
    success_count = 0
    
    for input_name, output_name in files_to_fix:
        if process_file(input_name, output_name):
            success_count += 1
    
    print(f"\n修复完成! 成功: {success_count}/{len(files_to_fix)}")
    print("最终文件:")
    for _, output_name in files_to_fix:
        print(f"  - {output_name}")

if __name__ == "__main__":
    main()
