#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整转换所有106个表的脚本
确保每一个表都被正确转换并迁移
"""

import re
import os

def extract_table_definition(sql_content, start_pos):
    """提取单个表的完整定义"""
    
    # 找到CREATE TABLE开始位置
    create_match = re.search(r'CREATE TABLE \[dbo\]\.\[(\w+)\]', sql_content[start_pos:])
    if not create_match:
        return None, None, 0
    
    table_name = create_match.group(1)
    table_start = start_pos + create_match.start()
    
    # 找到表定义结束位置（下一个GO或文件结尾）
    remaining_content = sql_content[table_start:]
    
    # 查找结束标记
    end_patterns = [
        r'\nGO\n',
        r'\nGO$',
        r'SET ANSI_PADDING OFF\nGO',
        r'ALTER TABLE.*?GO',
    ]
    
    table_end = len(remaining_content)
    for pattern in end_patterns:
        match = re.search(pattern, remaining_content, re.MULTILINE | re.DOTALL)
        if match:
            table_end = min(table_end, match.end())
    
    table_definition = remaining_content[:table_end]
    
    return table_name, table_definition, table_start + table_end

def convert_mssql_field_to_mysql(field_text):
    """转换MSSQL字段定义为MySQL"""
    
    # 移除COLLATE子句
    field_text = re.sub(r'\s+COLLATE\s+[\w_]+', '', field_text)
    
    # 转换字段定义
    conversions = [
        # IDENTITY字段
        (r'\[(\w+)\]\s+\[bigint\]\s+IDENTITY\(1,1\)\s+NOT\s+NULL', r'`\1` bigint NOT NULL AUTO_INCREMENT'),
        (r'\[(\w+)\]\s+\[int\]\s+IDENTITY\(1,1\)\s+NOT\s+NULL', r'`\1` int NOT NULL AUTO_INCREMENT'),
        
        # 基本数据类型
        (r'\[(\w+)\]\s+\[bigint\]\s+NOT\s+NULL', r'`\1` bigint NOT NULL'),
        (r'\[(\w+)\]\s+\[bigint\]\s+NULL', r'`\1` bigint DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[bigint\]', r'`\1` bigint DEFAULT NULL'),
        
        (r'\[(\w+)\]\s+\[int\]\s+NOT\s+NULL', r'`\1` int NOT NULL'),
        (r'\[(\w+)\]\s+\[int\]\s+NULL', r'`\1` int DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[int\]', r'`\1` int DEFAULT NULL'),
        
        # 字符串类型
        (r'\[(\w+)\]\s+\[varchar\]\((\d+)\)\s+NOT\s+NULL', r'`\1` varchar(\2) NOT NULL'),
        (r'\[(\w+)\]\s+\[varchar\]\((\d+)\)\s+NULL', r'`\1` varchar(\2) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[varchar\]\((\d+)\)', r'`\1` varchar(\2) DEFAULT NULL'),
        
        (r'\[(\w+)\]\s+\[nvarchar\]\((\d+)\)\s+NOT\s+NULL', r'`\1` varchar(\2) NOT NULL'),
        (r'\[(\w+)\]\s+\[nvarchar\]\((\d+)\)\s+NULL', r'`\1` varchar(\2) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[nvarchar\]\((\d+)\)', r'`\1` varchar(\2) DEFAULT NULL'),
        
        (r'\[(\w+)\]\s+\[nvarchar\]\(max\)', r'`\1` longtext'),
        (r'\[(\w+)\]\s+\[varchar\]\(max\)', r'`\1` longtext'),
        
        (r'\[(\w+)\]\s+\[char\]\((\d+)\)\s+NOT\s+NULL', r'`\1` char(\2) NOT NULL'),
        (r'\[(\w+)\]\s+\[char\]\((\d+)\)\s+NULL', r'`\1` char(\2) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[char\]\((\d+)\)', r'`\1` char(\2) DEFAULT NULL'),
        
        (r'\[(\w+)\]\s+\[nchar\]\((\d+)\)', r'`\1` char(\2) DEFAULT NULL'),
        
        (r'\[(\w+)\]\s+\[text\]', r'`\1` text'),
        (r'\[(\w+)\]\s+\[ntext\]', r'`\1` longtext'),
        
        # 日期时间类型
        (r'\[(\w+)\]\s+\[datetime\]\s+NOT\s+NULL', r'`\1` datetime NOT NULL'),
        (r'\[(\w+)\]\s+\[datetime\]\s+NULL', r'`\1` datetime DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[datetime\]', r'`\1` datetime DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[date\]', r'`\1` date DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[time\]', r'`\1` time DEFAULT NULL'),
        
        # 数值类型
        (r'\[(\w+)\]\s+\[decimal\]\((\d+),\s*(\d+)\)', r'`\1` decimal(\2,\3) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[numeric\]\((\d+),\s*(\d+)\)', r'`\1` decimal(\2,\3) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[money\]', r'`\1` decimal(19,4) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[smallmoney\]', r'`\1` decimal(10,4) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[float\]', r'`\1` double DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[real\]', r'`\1` float DEFAULT NULL'),
        
        # 其他类型
        (r'\[(\w+)\]\s+\[bit\]', r'`\1` tinyint(1) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[image\]', r'`\1` longblob'),
        (r'\[(\w+)\]\s+\[varbinary\]\(max\)', r'`\1` longblob'),
        (r'\[(\w+)\]\s+\[varbinary\]\((\d+)\)', r'`\1` varbinary(\2) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[binary\]\((\d+)\)', r'`\1` binary(\2) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[uniqueidentifier\]', r'`\1` varchar(36) DEFAULT NULL'),
        (r'\[(\w+)\]\s+\[timestamp\]', r'`\1` timestamp DEFAULT CURRENT_TIMESTAMP'),
    ]
    
    for pattern, replacement in conversions:
        field_text = re.sub(pattern, replacement, field_text, flags=re.IGNORECASE)
    
    return field_text

def convert_table_to_mysql(table_name, table_definition):
    """转换单个表定义为MySQL格式"""
    
    # 提取字段定义部分
    create_match = re.search(r'CREATE TABLE.*?\((.*?)\)(?:\s*CONSTRAINT.*?)?(?=\s*GO|$)', 
                           table_definition, re.DOTALL | re.IGNORECASE)
    
    if not create_match:
        print(f"警告: 无法解析表 {table_name} 的字段定义")
        return None
    
    fields_text = create_match.group(1)
    
    # 分割字段
    field_lines = []
    current_field = ""
    paren_count = 0
    
    for line in fields_text.split('\n'):
        line = line.strip()
        if not line:
            continue
            
        current_field += " " + line if current_field else line
        
        # 计算括号
        paren_count += line.count('(') - line.count(')')
        
        # 如果括号平衡且以逗号结尾，或者是最后一行
        if paren_count == 0 and (line.endswith(',') or not line.endswith(',')):
            if current_field.strip() and not current_field.strip().startswith('CONSTRAINT'):
                field_lines.append(current_field.strip().rstrip(','))
            current_field = ""
    
    # 转换字段
    mysql_fields = []
    primary_key = None
    
    for field_line in field_lines:
        if field_line.strip():
            converted = convert_mssql_field_to_mysql(field_line)
            if converted and '`' in converted:
                mysql_fields.append('  ' + converted)
                
                # 检查是否是AUTO_INCREMENT字段（作为主键）
                if 'AUTO_INCREMENT' in converted:
                    pk_match = re.search(r'`(\w+)`', converted)
                    if pk_match:
                        primary_key = pk_match.group(1)
    
    # 如果没有找到AUTO_INCREMENT主键，尝试从CONSTRAINT中找
    if not primary_key:
        constraint_match = re.search(r'CONSTRAINT.*?PRIMARY KEY.*?\(\s*\[(\w+)\]', 
                                   table_definition, re.IGNORECASE | re.DOTALL)
        if constraint_match:
            primary_key = constraint_match.group(1)
    
    # 添加主键
    if primary_key:
        mysql_fields.append(f'  PRIMARY KEY (`{primary_key}`)')
    
    # 构建MySQL CREATE TABLE语句
    if mysql_fields:
        fields_sql = ',\n'.join(mysql_fields)
        mysql_sql = f"""DROP TABLE IF EXISTS `{table_name}`;
CREATE TABLE `{table_name}` (
{fields_sql}
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

"""
        return mysql_sql
    
    return None

def main():
    """主函数 - 转换所有106个表"""
    
    print("开始转换所有106个表...")
    
    # 读取原始MSSQL文件
    sql_file = "../CCDS.sql"
    
    try:
        # 尝试不同编码
        encodings = ['utf-16', 'utf-8', 'gbk']
        content = None
        
        for encoding in encodings:
            try:
                with open(sql_file, 'r', encoding=encoding) as f:
                    content = f.read()
                    print(f"成功使用编码 {encoding} 读取文件")
                    break
            except (UnicodeDecodeError, UnicodeError):
                continue
        
        if not content:
            raise Exception("无法读取MSSQL文件")
        
        # 查找所有CREATE TABLE位置
        table_positions = []
        for match in re.finditer(r'CREATE TABLE \[dbo\]\.\[(\w+)\]', content):
            table_positions.append((match.group(1), match.start()))
        
        print(f"找到 {len(table_positions)} 个表")
        
        # 生成MySQL头部
        mysql_header = """-- MySQL 5.7 完整CCDS数据库 - 所有106个表
-- 从MSSQL完整转换，确保功能100%兼容

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `ccds`;

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 所有106个表的完整定义
-- ========================================

"""
        
        # 转换所有表
        mysql_tables = []
        success_count = 0
        
        for i, (table_name, start_pos) in enumerate(table_positions):
            try:
                # 确定表定义结束位置
                if i + 1 < len(table_positions):
                    end_pos = table_positions[i + 1][1]
                else:
                    end_pos = len(content)
                
                # 提取表定义
                table_def = content[start_pos:end_pos]
                
                # 转换为MySQL
                mysql_sql = convert_table_to_mysql(table_name, table_def)
                
                if mysql_sql:
                    mysql_tables.append(mysql_sql)
                    success_count += 1
                    print(f"✅ 成功转换表: {table_name}")
                else:
                    print(f"❌ 转换失败: {table_name}")
                    
            except Exception as e:
                print(f"❌ 转换表 {table_name} 时出错: {e}")
        
        # 生成MySQL尾部
        mysql_footer = """
-- ========================================
-- 创建索引优化查询性能
-- ========================================

SET FOREIGN_KEY_CHECKS = 1;

-- 数据库创建完成
SELECT 'CCDS完整数据库创建完成 - 包含所有106个表' as message;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ccds';
"""
        
        # 组合最终SQL
        final_sql = mysql_header + '\n'.join(mysql_tables) + mysql_footer
        
        # 写入文件
        output_file = "mysql57_ALL_106_TABLES.sql"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_sql)
        
        print(f"\n🎉 完整转换完成!")
        print(f"✅ 成功转换: {success_count}/{len(table_positions)} 个表")
        print(f"📁 输出文件: {output_file}")
        print(f"🚀 现在可以导入到MySQL数据库了!")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
