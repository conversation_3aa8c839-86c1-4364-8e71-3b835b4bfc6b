<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>  
    <title>批量导出录音</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript">
		function startExport(){
			var url = "soundRecordAction.do";
  			var pars =  parent.getFormArgs();
  			var exportPars = $("exportForm").serialize(true);
			pars.op = "getFileLocOfRec";
			pars.groupType = exportPars.groupType;
			$("submitTd").innerHTML = "<img class='imgAlign' src='images/gif/uploading.gif' alt='正在导出' /><span style='padding:10px 0 0 0'>正在准备数据，请耐心等待</span>";
			new Ajax.Request(url,{
				method : 'post',
				parameters : pars,
				onSuccess : function(response){
						/* if(response.responseText!=""){
	  						var rs = response.responseText;
	  						if(rs.indexOf("error")==0){
	  	  						errInfoCallBack(rs);
	  						}
	  						else if(rs.indexOf("empty")==0){
	  	  						errInfoCallBack("error|未查询到案件");
	  						}
	  						else if(rs.indexOf("0")==0){
	  							alert(rs.substring(2));
	  						}
	  					}
	  					else{
	  						alert("导出失败，请重新操作！");
	  					} */
						
					if(response.responseText!=""){
						var rs = response.responseText;
						if(rs.indexOf("error")==0){
							$("submitTd").innerHTML = "<span>导出失败&nbsp;[原因:"+rs.substring(6)+"]</span>";
  						}
  						else if(rs.indexOf("empty")==0){
  							$("submitTd").innerHTML = "<span>导出失败&nbsp;[原因:未查询到案件]</span>";
  						}
  						else if(rs.indexOf("0")==0){
  							$("submitTd").innerHTML = "<span>导出完成，导出位置在录音服务器 "+rs.substring(2)+"</span>";
  						}
					}
				},
				onfailure : function(response){
					$("submitTd").innerHTML = "<span>导出失败，请关闭窗口重试&nbsp;[原因:"+transport.status+"]</span>";
				}
			});
		}
	</script>
</head>
  <body>
  <div class="inputDiv">
  	<form id="exportForm" method="post" >
		<table class="dashTab inputForm" cellpadding="0" cellspacing="0">
		    <tr class="noBorderBot">
		        <th>分组类型：</th>
		        <td colspan="3" class="longTd">
                      <input type="radio" id="idNumberGroup" name="groupType" value="1" checked><label for="idNumberGroup">证件号&nbsp;&nbsp;</label>
                      <input type="radio" id="nameGroup" name="groupType" value="0"><label for="nameGroup">姓名&nbsp;&nbsp;</label>
                      <input type="radio" id="cardNumberGroup" name="groupType" value="2"><label for="cardNumberGroup">卡号&nbsp;&nbsp;</label>
		              <input type="radio" id="noGroup" name="groupType" value=""><label for="noGroup">不分组&nbsp;&nbsp;</label>
                </td>
		    </tr>
            <tr class="submitTr">	
                <td colspan="4" id="submitTd" class="exportSubmitTd">
                    <input type="button" class="butSize1" id="doOut" value="确认导出" onClick="startExport()" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()" />
                </td>
            </tr>
            <tr>
                <td class="tipsTd" colspan="4">
                    <div class="tipsLayer">
                        <ul>
                            <li>导出大量数据时等待时间较长，请在导出成功后再关闭此窗口</li>
                        </ul>
                    </div>
                </td>
            </tr>				
	  </table>
	</form>
  </div>
  </body>
</html>
