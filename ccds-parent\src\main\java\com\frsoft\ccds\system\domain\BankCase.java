package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 银行案件实体类
 * 对应数据库表: bank_case
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class BankCase {
    
    /** 案件ID */
    private Long casId;
    
    /** 案件编码 */
    private String casCode;
    
    /** 案件组别 */
    private String casGroup;
    
    /** 案件状态 */
    private Integer casState;
    
    /** 案件类型ID */
    private Long casTypHid;
    
    /** 客户姓名 */
    private String casName;
    
    /** 客户电话 */
    private String casPhone;
    
    /** 案件金额 */
    private BigDecimal casM;
    
    /** 已还金额 */
    private BigDecimal casPaidM;
    
    /** 分配员工编号 */
    private Long casSeNo;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date casInsTime;
    
    /** 创建用户 */
    private String casInsUser;
    
    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date casAltTime;
    
    /** 修改用户 */
    private String casAltUser;
    
    /** 备注 */
    private String casRemark;
    
    /** 身份证号 */
    private String casIdNo;
    
    /** 家庭地址 */
    private String casAddress;
    
    /** 工作地址 */
    private String casWorkAddress;
    
    /** 工作电话 */
    private String casWorkPhone;
    
    /** 邮箱 */
    private String casEmail;
    
    /** 银行 */
    private String casBank;
    
    /** 卡号 */
    private String casCardNo;
    
    /** 逾期日期 */
    private String casOverdueDate;
    
    /** 回款比例 */
    private Float casPbackP;
    
    /** 工作邮编 */
    private String casWpostCode;
    
    /** 截止日期 */
    private String casDeadline;
    
    /** 是否主卡 */
    private String casIsHost;
    
    /** 账单日期 */
    private String casBillDate;
    
    /** 最后还款日期 */
    private String casLastPaid;
    
    /** 计数 */
    private String casCount;
    
    /** 剩余本金 */
    private String casLeftPri;
    
    /** 分配员工IDs */
    private String casAssignIds;
    
    /** 分配员工姓名 */
    private String casAssignNames;
    
    /** 最后分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date casLastAssignTime;
    
    /** 逾期天数 */
    private Integer casOverdueDays;
    
    /** 逾期天数字符串 */
    private String casOverdueDaysStr;
    
    /** 生日 */
    private String casBir;
    
    /** 家庭邮编 */
    private String casMpostCode;
    
    /** 永久信用额度 */
    private String casPermCrline;
    
    /** 替代持有人 */
    private String casAltHold;
    
    // 关联对象
    /** 分配员工信息 */
    private SalEmp salEmp;
    
    /** 案件类型信息 */
    private TypeList typeList;
}
