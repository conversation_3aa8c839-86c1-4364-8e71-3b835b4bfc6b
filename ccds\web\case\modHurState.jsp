<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic" prefix="logic"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>修改案件催收状态</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="cache-control" content="no-cache"/>
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
    	body{
			background-color:#fff;
		}
		.inputForm tbody th{
			width:70px;
		}
		.inputForm tbody td{
			width:200px;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
    	window.onload=function(){
			if("${isBat}"!=""){
				//批量修改
				$("opMethod").value="batHurState";
				$("casId").value=getBacthIds("-")[0];
				$("hurState").value="";
			}
			else{
				$("opMethod").value="modCasHurState";
				$("casId").value="${bankCase.casId}";
				$("hurState").value="${bankCase.typeList.typId}";
			}
			
		}
    </script>
  </head>
  
  <body>
    <form action="caseAction.do" method="post">  
        <input type="hidden" id="opMethod" name="op">
    	<input type="hidden" id="casId" name="casId">
        <table class="dashTab inputForm single" cellpadding="0" cellspacing="0">
        	<tbody>
           	<tr class="noBorderBot">
               	<th>催收状态：</th>
               	<td>
               	<select class="inputBoxAlign inputSize2" id="hurState"  name="hurState">
                    <logic:notEmpty name="csType">
                       <logic:iterate id="csType" name="csType">
                       <option value="${csType.typId}">${csType.typName}</option>
                       </logic:iterate>
                    </logic:notEmpty>
                </select>	
           		</td>
            </tr>
           	<tr class="submitTr">
           		<td colspan="2">
                	<input id="caseSave" class="butSize1" type="submit" value="保存" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()"/>
                </td>
          	</tr>
           </tbody>
        </table>
	  	</form>
	</body>
</html>
