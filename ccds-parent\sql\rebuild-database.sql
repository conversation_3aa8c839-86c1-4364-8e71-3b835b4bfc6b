-- 完整的数据库重建脚本
-- 解决SQL Server到MySQL的迁移问题

-- 删除数据库（如果存在）
DROP DATABASE IF EXISTS `ccds`;

-- 创建数据库
CREATE DATABASE `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `ccds`;

-- 系统偏好设置表
CREATE TABLE `sys_pref` (
  `syp_id` bigint NOT NULL AUTO_INCREMENT COMMENT '偏好ID',
  `syp_name` varchar(100) NOT NULL COMMENT '偏好名称',
  `syp_is_def` tinyint DEFAULT '0' COMMENT '是否默认',
  `syp_is_app` tinyint DEFAULT '0' COMMENT '是否应用',
  `syp_pwd_len` int DEFAULT '6' COMMENT '密码长度',
  `syp_pwd_rule` varchar(10) DEFAULT '0' COMMENT '密码规则',
  `syp_pwd_upd_days` int DEFAULT '0' COMMENT '密码更新天数',
  `syp_login_fail` int DEFAULT '5' COMMENT '登录失败次数',
  `syp_offline_days` int DEFAULT '0' COMMENT '离线天数',
  `syp_has_captcha` tinyint DEFAULT '1' COMMENT '是否有验证码',
  PRIMARY KEY (`syp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统偏好设置表';

-- 系统用户表
CREATE TABLE `sys_user` (
  `user_code` varchar(50) NOT NULL COMMENT '用户编码',
  `user_login_name` varchar(50) NOT NULL COMMENT '登录名',
  `user_pwd` varchar(100) NOT NULL COMMENT '密码',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `user_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `user_cti_server` varchar(100) DEFAULT NULL COMMENT 'CTI服务器',
  `user_cti_phone` varchar(20) DEFAULT NULL COMMENT 'CTI电话',
  `user_sms_max_num` int DEFAULT NULL COMMENT '短信最大发送数',
  PRIMARY KEY (`user_code`),
  UNIQUE KEY `uk_user_login_name` (`user_login_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- 组织表
CREATE TABLE `sal_org` (
  `so_code` varchar(50) NOT NULL COMMENT '组织编码',
  `so_name` varchar(100) NOT NULL COMMENT '组织名称',
  `so_con_area` varchar(100) DEFAULT NULL COMMENT '管辖区域',
  `so_loc` varchar(100) DEFAULT NULL COMMENT '位置',
  `so_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `so_emp_num` varchar(20) DEFAULT NULL COMMENT '员工数量',
  `so_resp` varchar(200) DEFAULT NULL COMMENT '职责',
  `so_org_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `so_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `so_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `so_up_code` varchar(50) DEFAULT NULL COMMENT '上级编码',
  `so_cost_center` varchar(50) DEFAULT NULL COMMENT '成本中心',
  `so_org_nature` varchar(50) DEFAULT NULL COMMENT '组织性质',
  PRIMARY KEY (`so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织表';

-- 员工表
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT COMMENT '员工编号',
  `se_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `se_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `se_so_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `se_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `se_phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `se_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `se_position` varchar(50) DEFAULT NULL COMMENT '职位',
  `se_entry_date` date DEFAULT NULL COMMENT '入职日期',
  PRIMARY KEY (`se_no`),
  KEY `idx_se_user_code` (`se_user_code`),
  KEY `idx_se_so_code` (`se_so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 角色表
CREATE TABLE `lim_role` (
  `rol_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `rol_name` varchar(50) NOT NULL COMMENT '角色名称',
  `rol_lev` int DEFAULT NULL COMMENT '角色级别',
  `rol_desc` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `rol_grp_id` bigint DEFAULT NULL COMMENT '组ID',
  PRIMARY KEY (`rol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 组表
CREATE TABLE `lim_group` (
  `grp_id` bigint NOT NULL AUTO_INCREMENT COMMENT '组ID',
  `grp_name` varchar(50) NOT NULL COMMENT '组名称',
  `grp_desc` varchar(200) DEFAULT NULL COMMENT '组描述',
  `grp_cre_time` datetime DEFAULT NULL COMMENT '创建时间',
  `grp_cre_man` varchar(50) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`grp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组表';

-- 操作权限表
CREATE TABLE `lim_operate` (
  `ope_id` bigint NOT NULL AUTO_INCREMENT COMMENT '操作ID',
  `ope_code` varchar(50) NOT NULL COMMENT '操作编码',
  `ope_desc` varchar(200) DEFAULT NULL COMMENT '操作描述',
  PRIMARY KEY (`ope_id`),
  UNIQUE KEY `uk_ope_code` (`ope_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作权限表';

-- 功能模块表
CREATE TABLE `lim_function` (
  `fun_id` bigint NOT NULL AUTO_INCREMENT COMMENT '功能ID',
  `fun_code` varchar(50) NOT NULL COMMENT '功能编码',
  `fun_desc` varchar(200) DEFAULT NULL COMMENT '功能描述',
  `fun_type` varchar(50) DEFAULT NULL COMMENT '功能类型',
  PRIMARY KEY (`fun_id`),
  UNIQUE KEY `uk_fun_code` (`fun_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能模块表';

-- 权限表
CREATE TABLE `lim_right` (
  `rig_id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `rig_code` varchar(50) NOT NULL COMMENT '权限编码',
  `rig_fun_code` varchar(50) DEFAULT NULL COMMENT '功能编码',
  `rig_ope_code` varchar(50) DEFAULT NULL COMMENT '操作编码',
  `rig_wms_name` varchar(100) DEFAULT NULL COMMENT 'WMS名称',
  PRIMARY KEY (`rig_id`),
  UNIQUE KEY `uk_rig_code` (`rig_code`),
  KEY `idx_rig_fun_code` (`rig_fun_code`),
  KEY `idx_rig_ope_code` (`rig_ope_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 类型字典表
CREATE TABLE `type_list` (
  `typ_id` bigint NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `typ_name` varchar(50) NOT NULL COMMENT '类型名称',
  `typ_desc` varchar(200) DEFAULT NULL COMMENT '类型描述',
  `typ_type` varchar(50) DEFAULT NULL COMMENT '类型分类',
  `typ_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  PRIMARY KEY (`typ_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='类型字典表';

-- 省份表
CREATE TABLE `cus_province` (
  `prv_id` bigint NOT NULL AUTO_INCREMENT COMMENT '省份ID',
  `prv_area_id` bigint DEFAULT NULL COMMENT '区域ID',
  `prv_name` varchar(50) NOT NULL COMMENT '省份名称',
  `prv_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='省份表';

-- 城市表
CREATE TABLE `cus_city` (
  `city_id` bigint NOT NULL AUTO_INCREMENT COMMENT '城市ID',
  `city_prv_id` bigint DEFAULT NULL COMMENT '省份ID',
  `city_name` varchar(50) NOT NULL COMMENT '城市名称',
  `city_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`city_id`),
  KEY `idx_city_prv_id` (`city_prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='城市表';

-- 区域表
CREATE TABLE `cus_area` (
  `are_id` bigint NOT NULL AUTO_INCREMENT COMMENT '区域ID',
  `are_name` varchar(50) NOT NULL COMMENT '区域名称',
  `are_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`are_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='区域表';

-- 锁表
CREATE TABLE `lock_table` (
  `lock_id` bigint NOT NULL AUTO_INCREMENT COMMENT '锁ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_max` bigint DEFAULT '0' COMMENT '最大值',
  PRIMARY KEY (`lock_id`),
  UNIQUE KEY `uk_table_name` (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='锁表';

-- 银行案件表
CREATE TABLE `bank_case` (
  `cas_id` bigint NOT NULL AUTO_INCREMENT COMMENT '案件ID',
  `cas_code` varchar(50) DEFAULT NULL COMMENT '案件编码',
  `cas_group` varchar(50) DEFAULT NULL COMMENT '案件组',
  `cas_state` int DEFAULT '0' COMMENT '案件状态(0待分配 1催收中 2已结案 3暂停催收 4法务处理)',
  `cas_typ_hid` bigint DEFAULT NULL COMMENT '类型ID',
  `cas_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `cas_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `cas_m` decimal(15,2) DEFAULT NULL COMMENT '案件金额',
  `cas_paid_m` decimal(15,2) DEFAULT '0.00' COMMENT '已还金额',
  `cas_se_no` bigint DEFAULT NULL COMMENT '员工编号',
  `cas_ins_time` datetime DEFAULT NULL COMMENT '创建时间',
  `cas_ins_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `cas_alt_time` datetime DEFAULT NULL COMMENT '修改时间',
  `cas_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `cas_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `cas_id_no` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `cas_address` varchar(200) DEFAULT NULL COMMENT '地址',
  `cas_work_address` varchar(200) DEFAULT NULL COMMENT '工作地址',
  `cas_work_phone` varchar(20) DEFAULT NULL COMMENT '工作电话',
  `cas_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `cas_bank` varchar(50) DEFAULT NULL COMMENT '银行',
  `cas_card_no` varchar(30) DEFAULT NULL COMMENT '银行卡号',
  `cas_overdue_date` date DEFAULT NULL COMMENT '逾期日期',
  `cas_pback_p` decimal(5,2) DEFAULT NULL COMMENT '回收率',
  `cas_wpost_code` varchar(10) DEFAULT NULL COMMENT '工作邮编',
  `cas_deadline` date DEFAULT NULL COMMENT '截止日期',
  `cas_is_host` char(1) DEFAULT '0' COMMENT '是否主案件',
  `cas_bill_date` date DEFAULT NULL COMMENT '账单日期',
  `cas_last_paid` date DEFAULT NULL COMMENT '最后还款日期',
  `cas_count` int DEFAULT '0' COMMENT '催收次数',
  `cas_left_pri` decimal(15,2) DEFAULT NULL COMMENT '剩余本金',
  `cas_assign_ids` varchar(200) DEFAULT NULL COMMENT '分配ID列表',
  `cas_assign_names` varchar(200) DEFAULT NULL COMMENT '分配姓名列表',
  `cas_last_assign_time` datetime DEFAULT NULL COMMENT '最后分配时间',
  `cas_overdue_days` int DEFAULT '0' COMMENT '逾期天数',
  `cas_overdue_days_str` varchar(20) DEFAULT NULL COMMENT '逾期天数字符串',
  `cas_bir` date DEFAULT NULL COMMENT '生日',
  `cas_mpost_code` varchar(10) DEFAULT NULL COMMENT '邮编',
  `cas_perm_crline` decimal(15,2) DEFAULT NULL COMMENT '永久信用额度',
  `cas_alt_hold` char(1) DEFAULT '0' COMMENT '是否暂停',
  PRIMARY KEY (`cas_id`),
  KEY `idx_cas_code` (`cas_code`),
  KEY `idx_cas_name` (`cas_name`),
  KEY `idx_cas_phone` (`cas_phone`),
  KEY `idx_cas_se_no` (`cas_se_no`),
  KEY `idx_cas_state` (`cas_state`),
  KEY `idx_cas_ins_time` (`cas_ins_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='银行案件表';

-- 催收记录表
CREATE TABLE `pho_red` (
  `pr_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `pr_typ_id` bigint DEFAULT NULL COMMENT '类型ID',
  `pr_contact` varchar(50) DEFAULT NULL COMMENT '联系人',
  `pr_cas_id` bigint NOT NULL COMMENT '案件ID',
  `pr_content` text COMMENT '催收内容',
  `pr_time` datetime DEFAULT NULL COMMENT '催收时间',
  `pr_se_no` bigint DEFAULT NULL COMMENT '员工编号',
  `pr_con_type` varchar(20) DEFAULT NULL COMMENT '联系方式',
  `pr_result` varchar(50) DEFAULT NULL COMMENT '催收结果',
  `pr_next_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `pr_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `pr_isdel` char(1) DEFAULT '0' COMMENT '是否删除(0否 1是)',
  PRIMARY KEY (`pr_id`),
  KEY `idx_pr_cas_id` (`pr_cas_id`),
  KEY `idx_pr_se_no` (`pr_se_no`),
  KEY `idx_pr_time` (`pr_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='催收记录表';

-- 还款记录表
CREATE TABLE `case_paid` (
  `pa_id` bigint NOT NULL AUTO_INCREMENT COMMENT '还款ID',
  `pa_state` int DEFAULT '0' COMMENT '状态(0承诺中 1已还款 2逾期未还 3部分还款)',
  `pa_cas_id` bigint NOT NULL COMMENT '案件ID',
  `pa_ptp_d` datetime DEFAULT NULL COMMENT '承诺还款日期',
  `pa_ptp_num` decimal(15,2) DEFAULT NULL COMMENT '承诺还款金额',
  `pa_paid_date` datetime DEFAULT NULL COMMENT '实际还款日期',
  `pa_paid_num` decimal(15,2) DEFAULT NULL COMMENT '实际还款金额',
  `pa_se_no` bigint DEFAULT NULL COMMENT '员工编号',
  `pa_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `pa_type` varchar(20) DEFAULT NULL COMMENT '还款类型',
  `pa_method` varchar(20) DEFAULT NULL COMMENT '还款方式',
  `pa_ins_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pa_ins_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `pa_alt_time` datetime DEFAULT NULL COMMENT '修改时间',
  `pa_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  PRIMARY KEY (`pa_id`),
  KEY `idx_pa_cas_id` (`pa_cas_id`),
  KEY `idx_pa_se_no` (`pa_se_no`),
  KEY `idx_pa_paid_date` (`pa_paid_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='还款记录表';

-- 插入基础数据

-- 插入系统偏好设置
INSERT INTO `sys_pref` (`syp_name`, `syp_is_def`, `syp_is_app`, `syp_pwd_len`, `syp_pwd_rule`, `syp_pwd_upd_days`, `syp_login_fail`, `syp_offline_days`, `syp_has_captcha`)
VALUES ('系统默认', 1, 1, 6, '0', 0, 5, 0, 1);

-- 插入默认用户
INSERT INTO `sys_user` (`user_code`, `user_login_name`, `user_pwd`, `user_name`, `user_isenabled`)
VALUES ('admin', 'admin', 'e10adc3949ba59abbe56e057f20f883e', '系统管理员', '1');

-- 插入默认组织
INSERT INTO `sal_org` (`so_code`, `so_name`, `so_con_area`, `so_loc`, `so_user_code`, `so_isenabled`)
VALUES ('ORG001', '总公司', '全国', '总部', 'admin', '1');

-- 插入默认员工
INSERT INTO `sal_emp` (`se_name`, `se_user_code`, `se_so_code`, `se_isenabled`, `se_position`)
VALUES ('系统管理员', 'admin', 'ORG001', '1', '管理员');

-- 插入区域数据
INSERT INTO `cus_area` (`are_id`, `are_name`, `are_isenabled`) VALUES
(1, '请选择', '1'),
(3, '江苏', '1'),
(4, '安徽', '1'),
(5, '浙江', '1'),
(6, '上海', '1'),
(7, '北京', '1');

-- 插入省份数据
INSERT INTO `cus_province` (`prv_id`, `prv_area_id`, `prv_name`, `prv_isenabled`) VALUES
(1, 1, '请选择', '1');

-- 插入城市数据
INSERT INTO `cus_city` (`city_id`, `city_prv_id`, `city_name`, `city_isenabled`) VALUES
(1, 1, '请选择', '1');

-- 插入操作权限数据
INSERT INTO `lim_operate` (`ope_code`, `ope_desc`) VALUES
('case001', '导入案件'),
('case002', '删除批次'),
('case003', '编辑批次'),
('case004', '编辑案件'),
('case005', '退案'),
('case006', '分配案件'),
('case007', '暂停案件'),
('case008', '关闭案件'),
('case009', '恢复案件'),
('case010', '批量评语'),
('case011', '导出案件'),
('case012', '导出催收记录'),
('fun001', '添加权限'),
('fun002', '删除权限'),
('fun003', '修改权限'),
('fun004', '查看详情权限'),
('fun005', '访问权限'),
('fun006', '查看全部'),
('fun007', '管理权限'),
('fun008', '审核权限'),
('fun009', '导入权限'),
('fun010', '添加修改权限'),
('hurr001', '添加评语'),
('hurr002', '添加警告'),
('hurr003', '修改催收小结'),
('hurr004', '修改案件地区'),
('hurr022', '添加电催记录'),
('hurr023', '修改电话'),
('hurr024', '删除电话'),
('hurr025', '修改地址'),
('hurr026', '删除地址'),
('hurr027', '访问操作记录'),
('hurr028', '修改操作记录'),
('hurr029', '删除操作记录'),
('hurr030', '修改预计退案日'),
('hurr031', '添加电话'),
('hurr032', '添加地址'),
('hurr033', '添加辅助催记'),
('hurr034', '添加协催记录'),
('hurr035', '添加案人数据'),
('hurr036', '删除案件附件'),
('hurr037', '访问共债案件'),
('hurr038', '批量标色'),
('hurr039', '发送短信'),
('sys001', '锁定账号'),
('sys002', '查看账号日志'),
('sys003', '删除账号日志'),
('sys004', '设置短信额度'),
('file001', '上传附件'),
('file002', '查看附件');

-- 插入功能模块数据
INSERT INTO `lim_function` (`fun_code`, `fun_desc`, `fun_type`) VALUES
('c000', '访问权限', 'case'),
('c001', '批次管理', 'case'),
('c002', '案件管理', 'case'),
('c003', '催记管理', 'case'),
('c004', '导出', 'case'),
('c005', '案件详情', 'case'),
('hurry000', '访问权限', 'hurry'),
('hurry001', '我的案件', 'hurry'),
('hurry002', '来电查询', 'hurry'),
('hurry003', '主管协催', 'hurry'),
('sys000', '访问权限', 'sys'),
('sys001', '帐号设置', 'sys'),
('sys002', '职位设置', 'sys'),
('sys003', '部门设置', 'sys'),
('sys004', '类别管理', 'sys'),
('sys006', '安全设置', 'sys'),
('sys007', '权限组设置', 'sys');

-- 插入类型字典数据
INSERT INTO `type_list` (`typ_name`, `typ_desc`, `typ_type`, `typ_isenabled`) VALUES
('待分配', NULL, 'caseState', '1'),
('催收中', NULL, 'caseState', '1'),
('已结案', NULL, 'caseState', '1'),
('暂停催收', NULL, 'caseState', '1'),
('法务处理', NULL, 'caseState', '1'),
('电话', NULL, 'contactType', '1'),
('短信', NULL, 'contactType', '1'),
('邮件', NULL, 'contactType', '1'),
('上门', NULL, 'contactType', '1'),
('承诺还款', NULL, 'callResult', '1'),
('拒绝还款', NULL, 'callResult', '1'),
('无人接听', NULL, 'callResult', '1'),
('关机', NULL, 'callResult', '1'),
('空号', NULL, 'callResult', '1'),
('忙音', NULL, 'callResult', '1'),
('工商银行', NULL, 'caseBank', '1'),
('建设银行', NULL, 'caseBank', '1'),
('农业银行', NULL, 'caseBank', '1'),
('中国银行', NULL, 'caseBank', '1'),
('交通银行', NULL, 'caseBank', '1'),
('招商银行', NULL, 'caseBank', '1'),
('民生银行', NULL, 'caseBank', '1'),
('光大银行', NULL, 'caseBank', '1'),
('华夏银行', NULL, 'caseBank', '1'),
('平安银行', NULL, 'caseBank', '1');

-- 插入锁表数据
INSERT INTO `lock_table` (`table_name`, `table_max`) VALUES
('bank_case', 0),
('pho_red', 0),
('case_paid', 0),
('sal_emp', 0),
('sys_user', 0);

COMMIT;

-- 显示创建结果
SELECT '=== 数据库重建完成 ===' AS message;
SELECT '表数量' AS type, COUNT(*) AS count FROM information_schema.tables WHERE table_schema = 'ccds';
SELECT '用户数量' AS type, COUNT(*) AS count FROM sys_user;
SELECT '员工数量' AS type, COUNT(*) AS count FROM sal_emp;
SELECT '操作权限数量' AS type, COUNT(*) AS count FROM lim_operate;
SELECT '功能模块数量' AS type, COUNT(*) AS count FROM lim_function;
