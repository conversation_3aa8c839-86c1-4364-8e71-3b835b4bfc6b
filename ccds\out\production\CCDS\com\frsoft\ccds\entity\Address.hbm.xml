<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.Address" table="address" schema="dbo" >
        <id name="adrId" type="java.lang.Long">
            <column name="adr_id" />
            <generator class="identity" />
        </id>
        <property name="adrState" type="java.lang.Integer">
            <column name="adr_state" />
        </property>
        <property name="adrName" type="java.lang.String">
            <column name="adr_name" length="50" />
        </property>
        <property name="adrAdd" type="java.lang.String">
            <column name="adr_add" length="500" />
        </property>
        <many-to-one name="bankCase" class="com.frsoft.ccds.entity.BankCase" fetch="select" not-null="false">
            <column name="adr_cas_id" />
        </many-to-one>
        <property name="adrCat" type="java.lang.String">
            <column name="adr_cat" length="50" />
        </property>
        <property name="adrRemark" type="java.lang.String">
            <column name="adr_remark" length="**********" />
        </property>
        <property name="adrIsdel" type="java.lang.String">
            <column name="adr_isdel" length="1" />
        </property>
        <property name="adrNum" type="java.lang.Integer">
            <column name="adr_num" />
        </property>
        <property name="adrCheckApp" type="java.lang.Integer">
            <column name="adr_check_app" />
        </property>
        <property name="adrMailApp" type="java.lang.Integer">
            <column name="adr_mail_app" />
        </property>
        <property name="adrVisApp" type="java.lang.Integer">
            <column name="adr_vis_app" />
        </property>
        <property name="adrMailCount" type="java.lang.Integer">
            <column name="adr_mail_count" />
        </property>
        <property name="adrIsnew" type="java.lang.Integer">
            <column name="adr_isnew" />
        </property>
        <property name="adrRel" type="java.lang.String">
            <column name="adr_rel" length="50" />
        </property>
    </class>
</hibernate-mapping>
