<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.PhoRed" table="pho_red" schema="dbo" >
        <id name="prId" type="java.lang.Long">
            <column name="pr_id" />
            <generator class="identity" />
        </id>
        <property name="prCcId" type="java.lang.Long">
            <column name="pr_cc_id" />
        </property>
        <property name="prName" type="java.lang.String">
            <column name="pr_name" length="50" />
        </property>
        <property name="prCat" type="java.lang.Integer">
            <column name="pr_cat"/>
        </property>
        <property name="prContact" type="java.lang.String">
            <column name="pr_contact" length="200" />
        </property>
        <property name="prConType" type="java.lang.String">
            <column name="pr_con_type" length="50" />
        </property>
        <property name="prContent" type="java.lang.String">
            <column name="pr_content" length="1073741823" />
        </property>
        <property name="prTime" type="java.util.Date">
            <column name="pr_time" length="23" />
        </property>
        <property name="prPtpDate" type="java.util.Date">
            <column name="pr_ptp_date" length="23" />
        </property>
        <property name="prPtpNum" type="java.lang.Double">
            <column name="pr_ptp_num" precision="18" />
        </property>
         <property name="prRel" type="java.lang.String">
            <column name="pr_rel" length="50" />
        </property>
         <property name="prNegotiation" type="java.lang.String">
            <column name="pr_negotiation" length="50" />
        </property>
        <property name="prCallId" type="java.lang.String">
            <column name="pr_call_id" />
        </property>
        <property name="prSmsId" type="java.lang.String">
            <column name="pr_sms_id" />
        </property>
        <property name="prReduceM" type="java.lang.Double">
            <column name="pr_reduce_m" precision="18" />
        </property>
	 	<property name="prReduceState" type="java.lang.String">
            <column name="pr_reduce_state" length="50" />
        </property>
	 	<property name="prCaseConState" type="java.lang.String">
            <column name="pr_case_con_state" length="50" />
        </property>
	 	<property name="prIsAnswered" type="java.lang.String">
            <column name="pr_is_answered" length="1" />
        </property>
	 	<property name="prIsSelfAnswered" type="java.lang.String">
            <column name="pr_is_self_answered" length="1" />
        </property>
        <property name="prNxtClDate" type="java.util.Date">
            <column name="pr_nxt_cl_date" length="23" />
        </property>
        <property name="prNxtClType" type="java.lang.String">
            <column name="pr_nxt_cl_type" length="50" />
        </property>
        <property name="prClType" type="java.lang.String">
            <column name="pr_cl_type" length="50" />
        </property>
        <property name="prClTmp" type="java.lang.String">
            <column name="pr_cl_tmp" length="50" />
        </property>
        <many-to-one name="typeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="pr_typ_id" />
        </many-to-one>
        <many-to-one name="prStateType" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="pr_state_id" />
        </many-to-one>
        <many-to-one name="bankCase" class="com.frsoft.ccds.entity.BankCase" fetch="select" not-null="false">
            <column name="pr_cas_id" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="pr_se_no" />
        </many-to-one>
        <!-- <many-to-one name="casePaid" class="com.frsoft.ccds.entity.CasePaid" fetch="select" not-null="false" cascade="save-update">
            <column name="pr_pa_id" />
        </many-to-one> -->
    </class>
</hibernate-mapping>
