<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.SysPref" table="sys_pref" schema="dbo" >
        <id name="sypId" type="java.lang.Long">
            <column name="syp_id" />
            <generator class="identity" />
        </id>
        <property name="sypName" type="java.lang.String">
            <column name="syp_name" />
        </property>
        <property name="sypIsDef" type="java.lang.Integer">
            <column name="syp_is_def" />
        </property>
        <property name="sypIsApp" type="java.lang.Integer">
            <column name="syp_is_app" />
        </property>
        <property name="sypPwdLen" type="java.lang.Integer">
            <column name="syp_pwd_len" />
        </property>
        <property name="sypPwdRule" type="java.lang.String">
            <column name="syp_pwd_rule" length="50" />
        </property>
        <property name="sypPwdUpdDays" type="java.lang.Integer">
            <column name="syp_pwd_upd_days" />
        </property>
        <property name="sypLoginFail" type="java.lang.Integer">
            <column name="syp_login_fail" />
        </property>
        <property name="sypOfflineDays" type="java.lang.Integer">
            <column name="syp_offline_days" />
        </property>
        <property name="sypHasCaptcha" type="java.lang.Integer">
            <column name="syp_has_captcha" />
        </property>
        <property name="sypHideBack" type="java.lang.Integer">
            <column name="syp_hide_back" />
        </property>
        <property name="sypGlobalOrg" type="java.lang.Integer">
            <column name="syp_global_org" />
        </property>
        <property name="sypClNoHide" type="java.lang.Integer">
            <column name="syp_cl_no_hide" />
        </property>
    </class>
</hibernate-mapping>
