<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.TypeList" table="type_list" schema="dbo" >
        <id name="typId" type="java.lang.Long">
            <column name="typ_id" />
            <generator class="identity" />
        </id>
        <property name="typName" type="java.lang.String">
            <column name="typ_name" />
        </property>
        <property name="typDesc" type="java.lang.String">
            <column name="typ_desc" />
        </property>
        <property name="typType" type="java.lang.String">
            <column name="typ_type" length="50" />
        </property>
        <property name="typIsenabled" type="java.lang.String">
            <column name="typ_isenabled" length="1" />
        </property>
    </class>
</hibernate-mapping>
