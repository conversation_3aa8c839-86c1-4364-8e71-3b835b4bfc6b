<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.WmsShipment" table="wms_shipment" schema="dbo" >
        <id name="wshCode" type="java.lang.String">
            <column name="wsh_code" length="50" />
            <generator class="assigned" />
        </id>
        <many-to-one name="wmsWarOut" class="com.psit.struts.entity.WmsWarOut" fetch="select" not-null="false">
            <column name="wsh_wout_code" length="50" />
        </many-to-one>
        <many-to-one name="salOrdCon" class="com.frsoft.cis.entity.SalOrdCon" fetch="select" not-null="false">
            <column name="wsh_ord_code" length="50" />
        </many-to-one>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select" not-null="false">
            <column name="wsh_user_code" length="50" />
        </many-to-one>
        <property name="wshState" type="java.lang.String">
            <column name="wsh_state" length="1" />
        </property>
        <property name="wshOutDate" type="java.util.Date">
            <column name="wsh_out_date" length="23" />
        </property>
        <property name="wshInpDate" type="java.util.Date">
            <column name="wsh_inp_date" length="23" />
        </property>
        <property name="wshRecMan" type="java.lang.String">
            <column name="wsh_rec_man" length="50" />
        </property>
        <property name="wshType" type="java.lang.String">
            <column name="wsh_type" length="50" />
        </property>
        <property name="wshCost" type="java.lang.Double">
            <column name="wsh_cost" precision="18" />
        </property>
        <property name="wshRemark" type="java.lang.String">
            <column name="wsh_remark" length="1073741823" />
        </property>
        <set name="RShipPros" inverse="true" cascade="all">
            <key>
                <column name="rshp_ship_code" length="50" />
            </key>
            <one-to-many class="com.psit.struts.entity.RShipPro" />
        </set>
    </class>
</hibernate-mapping>
