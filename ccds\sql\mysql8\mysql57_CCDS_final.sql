-- MySQL 5.7 版本的CCDS完整数据库结构
-- 从MSSQL手工转换而来，包含所有84个表
-- 生成时间: 2024年

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `ccds`;

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 表结构定义开始
-- ========================================

-- 1. 员工表
DROP TABLE IF EXISTS `sal_emp`;
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT,
  `se_so_code` varchar(50) DEFAULT NULL,
  `se_name` varchar(100) DEFAULT NULL,
  `se_ide_code` varchar(50) DEFAULT NULL,
  `se_pos` varchar(50) DEFAULT NULL,
  `se_sex` varchar(50) DEFAULT NULL,
  `se_prob` varchar(50) DEFAULT NULL,
  `se_bir_place` varchar(50) DEFAULT NULL,
  `se_acc_place` varchar(100) DEFAULT NULL,
  `se_birth` varchar(50) DEFAULT NULL,
  `se_marry` varchar(10) DEFAULT NULL,
  `se_type` varchar(50) DEFAULT NULL,
  `se_job_lev` bigint DEFAULT NULL,
  `se_job_cate` varchar(50) DEFAULT NULL,
  `se_job_title` varchar(50) DEFAULT NULL,
  `se_start_day` datetime DEFAULT NULL,
  `se_year_pay` varchar(50) DEFAULT NULL,
  `se_cost_center` varchar(50) DEFAULT NULL,
  `se_email` varchar(50) DEFAULT NULL,
  `se_nation` varchar(50) DEFAULT NULL,
  `se_poli_status` varchar(50) DEFAULT NULL,
  `se_edu` varchar(50) DEFAULT NULL,
  `se_tel` varchar(50) DEFAULT NULL,
  `se_phone` varchar(50) DEFAULT NULL,
  `se_qq` varchar(50) DEFAULT NULL,
  `se_msn` varchar(50) DEFAULT NULL,
  `se_rec_source` varchar(100) DEFAULT NULL,
  `se_prov_fund` varchar(50) DEFAULT NULL,
  `se_job_date` datetime DEFAULT NULL,
  `se_hou_reg` varchar(50) DEFAULT NULL,
  `se_social_code` varchar(50) DEFAULT NULL,
  `se_rap` varchar(50) DEFAULT NULL,
  `se_address` varchar(500) DEFAULT NULL,
  `se_remark` longtext,
  `se_bank_name` varchar(50) DEFAULT NULL,
  `se_bank_card` varchar(50) DEFAULT NULL,
  `se_weal_address` varchar(50) DEFAULT NULL,
  `se_weal_pos` varchar(50) DEFAULT NULL,
  `se_isovertime` varchar(50) DEFAULT NULL,
  `se_attendance` varchar(50) DEFAULT NULL,
  `se_card_num` varchar(50) DEFAULT NULL,
  `se_pic` longtext,
  `se_isenabled` char(1) DEFAULT '1',
  `se_inser_date` datetime DEFAULT NULL,
  `se_code` varchar(50) DEFAULT NULL,
  `se_log` longtext,
  `se_alt_date` datetime DEFAULT NULL,
  `se_inser_user` varchar(50) DEFAULT NULL,
  `se_alt_user` varchar(50) DEFAULT NULL,
  `se_end_date` datetime DEFAULT NULL,
  `se_edc_bac` longtext,
  `se_work_ex` longtext,
  `se_user_code` varchar(50) DEFAULT NULL,
  `se_per_tel` varchar(50) DEFAULT NULL,
  `se_plan_sign_date` datetime DEFAULT NULL,
  `se_sign_date` datetime DEFAULT NULL,
  `se_credit_date` datetime DEFAULT NULL,
  `se_college` varchar(200) DEFAULT NULL,
  `se_transfer` text,
  PRIMARY KEY (`se_no`),
  KEY `idx_se_name` (`se_name`),
  KEY `idx_se_user_code` (`se_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='员工表';

-- 2. 用户表
DROP TABLE IF EXISTS `lim_user`;
CREATE TABLE `lim_user` (
  `user_code` varchar(50) NOT NULL,
  `user_loginName` varchar(50) DEFAULT NULL,
  `user_pwd` varchar(50) DEFAULT NULL,
  `user_up_code` varchar(50) DEFAULT NULL,
  `user_lev` char(1) DEFAULT NULL,
  `user_so_code` varchar(50) DEFAULT NULL,
  `user_se_id` bigint DEFAULT NULL,
  `user_se_name` varchar(100) DEFAULT NULL,
  `user_desc` longtext,
  `user_isenabled` char(1) DEFAULT '1',
  `user_num` varchar(200) DEFAULT NULL,
  `user_role_id` bigint DEFAULT NULL,
  `user_islogin` char(1) DEFAULT NULL,
  `user_ip` varchar(50) DEFAULT NULL,
  `user_fail` int DEFAULT NULL,
  `user_pwd_upd_date` datetime DEFAULT NULL,
  `user_cti_login` varchar(255) DEFAULT NULL,
  `user_cti_pwd` varchar(255) DEFAULT NULL,
  `user_cti_server` varchar(50) DEFAULT NULL,
  `user_cti_phone` varchar(50) DEFAULT NULL,
  `user_grp_id` bigint DEFAULT NULL,
  `user_sms_max_num` int DEFAULT NULL,
  PRIMARY KEY (`user_code`),
  KEY `idx_user_enabled` (`user_isenabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户表';

-- 3. 客户表
DROP TABLE IF EXISTS `cus_cor_cus`;
CREATE TABLE `cus_cor_cus` (
  `cor_code` bigint NOT NULL AUTO_INCREMENT,
  `cor_num` varchar(50) DEFAULT NULL,
  `cor_user_code` varchar(50) DEFAULT NULL,
  `cor_name` varchar(100) DEFAULT NULL,
  `cor_hot` varchar(50) DEFAULT NULL,
  `cor_mne` varchar(50) DEFAULT NULL,
  `cor_lic_code` varchar(50) DEFAULT NULL,
  `cor_org_code` varchar(50) DEFAULT NULL,
  `cor_star` varchar(50) DEFAULT NULL,
  `cor_cre_lev` varchar(50) DEFAULT NULL,
  `cor_cre_lim` varchar(50) DEFAULT NULL,
  `cor_ind_id` bigint DEFAULT NULL,
  `cor_per_size` varchar(50) DEFAULT NULL,
  `cor_acc_bank` varchar(100) DEFAULT NULL,
  `cor_bank_num` varchar(50) DEFAULT NULL,
  `cor_sou_id` bigint DEFAULT NULL,
  `cor_com_inf` longtext,
  `cor_country` bigint DEFAULT NULL,
  `cor_province` bigint DEFAULT NULL,
  `cor_city` bigint DEFAULT NULL,
  `cor_phone` varchar(50) DEFAULT NULL,
  `cor_fex` varchar(50) DEFAULT NULL,
  `cor_net` varchar(500) DEFAULT NULL,
  `cor_zip_code` varchar(50) DEFAULT NULL,
  `cor_address` longtext,
  `cor_remark` longtext,
  `cor_creat_date` datetime DEFAULT NULL,
  `cor_upd_date` datetime DEFAULT NULL,
  `cor_issuc` char(1) DEFAULT NULL,
  `cor_last_date` datetime DEFAULT NULL,
  `cor_temp_tag` varchar(50) DEFAULT NULL,
  `cor_isdelete` char(1) DEFAULT '0',
  `cor_spe_write` longtext,
  `cor_upd_user` varchar(50) DEFAULT NULL,
  `cor_typ_id` bigint DEFAULT NULL,
  `cor_ins_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cor_code`),
  KEY `idx_cor_user_code` (`cor_user_code`),
  KEY `idx_cor_name` (`cor_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='客户表';

-- 4. 账户表
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account` (
  `aco_id` bigint NOT NULL AUTO_INCREMENT,
  `aco_type` varchar(50) DEFAULT NULL,
  `aco_name` varchar(100) DEFAULT NULL,
  `aco_bank_num` varchar(50) DEFAULT NULL,
  `aco_bank` varchar(100) DEFAULT NULL,
  `aco_bank_name` varchar(50) DEFAULT NULL,
  `aco_cre_date` datetime DEFAULT NULL,
  `aco_org_mon` decimal(18,2) DEFAULT NULL,
  `aco_cur_mon` decimal(18,2) DEFAULT NULL,
  `aco_remark` longtext,
  `aco_inp_user` varchar(50) DEFAULT NULL,
  `aco_inp_date` datetime DEFAULT NULL,
  `aco_alt_date` datetime DEFAULT NULL,
  `aco_alt_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`aco_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='账户表';

-- 5. 销售机会表
DROP TABLE IF EXISTS `sal_opp`;
CREATE TABLE `sal_opp` (
  `opp_id` bigint NOT NULL AUTO_INCREMENT,
  `opp_cor_code` bigint DEFAULT NULL,
  `opp_title` varchar(300) DEFAULT NULL,
  `opp_lev` varchar(50) DEFAULT NULL,
  `opp_exe_date` datetime DEFAULT NULL,
  `opp_des` longtext,
  `opp_remark` longtext,
  `opp_ins_date` datetime DEFAULT NULL,
  `opp_isexe` varchar(10) DEFAULT NULL,
  `opp_state` varchar(10) DEFAULT NULL,
  `opp_upd_date` datetime DEFAULT NULL,
  `opp_inp_user` varchar(50) DEFAULT NULL,
  `opp_upd_user` varchar(50) DEFAULT NULL,
  `opp_isdel` char(1) DEFAULT '0',
  `opp_sign_date` datetime DEFAULT NULL,
  `opp_money` decimal(18,2) DEFAULT NULL,
  `opp_stage` bigint DEFAULT NULL,
  `opp_possible` varchar(50) DEFAULT NULL,
  `opp_sta_remark` varchar(100) DEFAULT NULL,
  `opp_sta_update` datetime DEFAULT NULL,
  `opp_sta_log` longtext,
  `opp_find_date` datetime DEFAULT NULL,
  `opp_user_code` varchar(50) DEFAULT NULL,
  `opp_se_no` bigint DEFAULT NULL,
  PRIMARY KEY (`opp_id`),
  KEY `idx_opp_cor_code` (`opp_cor_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售机会表';

-- 6. 询价表
DROP TABLE IF EXISTS `inquiry`;
CREATE TABLE `inquiry` (
  `inq_id` bigint NOT NULL AUTO_INCREMENT,
  `inq_ssu_id` bigint DEFAULT NULL,
  `inq_pro_id` bigint DEFAULT NULL,
  `inq_title` varchar(100) DEFAULT NULL,
  `inq_price` decimal(18,2) DEFAULT NULL,
  `inq_se_no` bigint DEFAULT NULL,
  `inq_date` datetime DEFAULT NULL,
  `inq_inp_user` varchar(50) DEFAULT NULL,
  `inq_upd_user` varchar(50) DEFAULT NULL,
  `inq_ins_date` datetime DEFAULT NULL,
  `inq_upd_date` datetime DEFAULT NULL,
  `inq_remark` longtext,
  `inq_isdel` char(1) DEFAULT '0',
  PRIMARY KEY (`inq_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='询价表';

-- 7. 报价表
DROP TABLE IF EXISTS `quote`;
CREATE TABLE `quote` (
  `quo_id` bigint NOT NULL AUTO_INCREMENT,
  `quo_opp_id` bigint DEFAULT NULL,
  `quo_title` varchar(100) DEFAULT NULL,
  `quo_price` decimal(18,2) DEFAULT NULL,
  `quo_se_no` bigint DEFAULT NULL,
  `quo_remark` longtext,
  `quo_date` datetime DEFAULT NULL,
  `quo_desc` longtext,
  `quo_ins_date` datetime DEFAULT NULL,
  `quo_upd_date` datetime DEFAULT NULL,
  `quo_inp_user` varchar(50) DEFAULT NULL,
  `quo_upd_user` varchar(50) DEFAULT NULL,
  `quo_isdel` char(1) DEFAULT '0',
  `quo_pro_id` bigint DEFAULT NULL,
  PRIMARY KEY (`quo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='报价表';

-- 8. 销售活动表
DROP TABLE IF EXISTS `sal_pra`;
CREATE TABLE `sal_pra` (
  `pra_id` bigint NOT NULL AUTO_INCREMENT,
  `pra_cor_code` bigint DEFAULT NULL,
  `pra_title` varchar(300) DEFAULT NULL,
  `pra_content` longtext,
  `pra_ins_date` datetime DEFAULT NULL,
  `pra_type` varchar(100) DEFAULT NULL,
  `pra_state` varchar(100) DEFAULT NULL,
  `pra_isPrice` varchar(10) DEFAULT NULL,
  `pra_exe_date` datetime DEFAULT NULL,
  `pra_cost_time` varchar(20) DEFAULT NULL,
  `pra_cus_link` varchar(50) DEFAULT NULL,
  `pra_se_no` bigint DEFAULT NULL,
  `pra_back` longtext,
  `pra_remark` longtext,
  `pra_upd_date` datetime DEFAULT NULL,
  `pra_opp_id` bigint DEFAULT NULL,
  `pra_inp_user` varchar(50) DEFAULT NULL,
  `pra_upd_user` varchar(50) DEFAULT NULL,
  `pra_isdel` char(1) DEFAULT '0',
  `pra_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`pra_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售活动表';

-- 9. 项目任务表
DROP TABLE IF EXISTS `pro_task`;
CREATE TABLE `pro_task` (
  `prta_id` bigint NOT NULL AUTO_INCREMENT,
  `prta_se_no` bigint DEFAULT NULL,
  `prta_pro_id` bigint DEFAULT NULL,
  `prta_sta_name` varchar(300) DEFAULT NULL,
  `prta_name` varchar(50) DEFAULT NULL,
  `prta_title` varchar(300) DEFAULT NULL,
  `prta_rel_date` datetime DEFAULT NULL,
  `prta_change_date` datetime DEFAULT NULL,
  `prta_fin_date` datetime DEFAULT NULL,
  `prta_lev` varchar(50) DEFAULT NULL,
  `prta_state` char(1) DEFAULT NULL,
  `prta_cyc` varchar(50) DEFAULT NULL,
  `prta_tag` longtext,
  `prta_desc` longtext,
  `prta_log` longtext,
  `prta_remark` longtext,
  `prta_isdel` char(1) DEFAULT '0',
  `prta_fct_date` datetime DEFAULT NULL,
  `prta_upd_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`prta_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='项目任务表';

-- 10. 客户服务表
DROP TABLE IF EXISTS `cus_serv`;
CREATE TABLE `cus_serv` (
  `ser_code` bigint NOT NULL AUTO_INCREMENT,
  `ser_cor_code` bigint DEFAULT NULL,
  `ser_title` varchar(300) DEFAULT NULL,
  `ser_cus_link` varchar(50) DEFAULT NULL,
  `ser_method` varchar(100) DEFAULT NULL,
  `ser_content` longtext,
  `ser_exe_date` datetime DEFAULT NULL,
  `ser_cos_time` varchar(50) DEFAULT NULL,
  `ser_state` varchar(10) DEFAULT NULL,
  `ser_se_no` bigint DEFAULT NULL,
  `ser_feedback` longtext,
  `ser_remark` longtext,
  `ser_ins_date` datetime DEFAULT NULL,
  `ser_upd_date` datetime DEFAULT NULL,
  `ser_inp_user` varchar(50) DEFAULT NULL,
  `ser_upd_user` varchar(50) DEFAULT NULL,
  `ser_isdel` char(1) DEFAULT '0',
  `ser_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ser_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='客户服务表';

-- 11. 销售订单表
DROP TABLE IF EXISTS `sal_ord_con`;
CREATE TABLE `sal_ord_con` (
  `sod_code` bigint NOT NULL AUTO_INCREMENT,
  `sod_num` varchar(300) DEFAULT NULL,
  `sod_til` varchar(300) DEFAULT NULL,
  `sod_type_id` bigint DEFAULT NULL,
  `sod_cus_code` bigint DEFAULT NULL,
  `sod_pro_id` bigint DEFAULT NULL,
  `sod_sum_mon` decimal(18,2) DEFAULT NULL,
  `sod_paid_mon` decimal(18,2) DEFAULT NULL,
  `sod_mon_type` varchar(50) DEFAULT NULL,
  `sod_state` varchar(10) DEFAULT NULL,
  `sod_ship_state` varchar(10) DEFAULT NULL,
  `sod_own_code` varchar(50) DEFAULT NULL,
  `sod_deadline` datetime DEFAULT NULL,
  `sod_end_date` datetime DEFAULT NULL,
  `sod_ord_date` datetime DEFAULT NULL,
  `sod_inp_date` datetime DEFAULT NULL,
  `sod_isfail` char(1) DEFAULT '0',
  `sod_remark` longtext,
  `sod_change_date` datetime DEFAULT NULL,
  `sod_paid_method` varchar(20) DEFAULT NULL,
  `sod_inp_code` varchar(50) DEFAULT NULL,
  `sod_cus_con` varchar(100) DEFAULT NULL,
  `sod_se_no` bigint DEFAULT NULL,
  `sod_con_date` datetime DEFAULT NULL,
  `sod_change_user` varchar(50) DEFAULT NULL,
  `sod_app_date` datetime DEFAULT NULL,
  `sod_app_man` varchar(50) DEFAULT NULL,
  `sod_app_desc` longtext,
  `sod_app_isok` char(1) DEFAULT NULL,
  `sod_content` longtext,
  PRIMARY KEY (`sod_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售订单表';

-- 12. 仓库产品表
DROP TABLE IF EXISTS `wms_product`;
CREATE TABLE `wms_product` (
  `wpr_id` bigint NOT NULL AUTO_INCREMENT,
  `wpr_name` varchar(100) DEFAULT NULL,
  `wpr_type_id` bigint DEFAULT NULL,
  `wpr_model` varchar(100) DEFAULT NULL,
  `wpr_unit` bigint DEFAULT NULL,
  `wpr_color` varchar(50) DEFAULT NULL,
  `wpr_size` varchar(50) DEFAULT NULL,
  `wpr_provider` varchar(100) DEFAULT NULL,
  `wpr_up_lim` int DEFAULT NULL,
  `wpr_low_lim` int DEFAULT NULL,
  `wpr_cost_prc` decimal(18,2) DEFAULT NULL,
  `wpr_sale_prc` decimal(18,2) DEFAULT NULL,
  `wpr_pic` longtext,
  `wpr_cuser_code` varchar(50) DEFAULT NULL,
  `wpr_cre_date` datetime DEFAULT NULL,
  `wpr_euser_code` varchar(50) DEFAULT NULL,
  `wpr_edit_date` datetime DEFAULT NULL,
  `wpr_desc` longtext,
  `wpr_remark` longtext,
  `wpr_states` char(1) DEFAULT '1',
  `wpr_range` longtext,
  `wpr_technology` longtext,
  `wpr_problem` longtext,
  `wpr_isdel` char(1) DEFAULT '0',
  `wpr_code` varchar(50) DEFAULT NULL,
  `wpr_iscount` char(1) DEFAULT '1',
  PRIMARY KEY (`wpr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='仓库产品表';

-- 13. 供应商表
DROP TABLE IF EXISTS `sal_supplier`;
CREATE TABLE `sal_supplier` (
  `ssu_id` bigint NOT NULL AUTO_INCREMENT,
  `ssu_code` varchar(300) DEFAULT NULL,
  `ssu_name` varchar(100) DEFAULT NULL,
  `ssu_phone` varchar(50) DEFAULT NULL,
  `ssu_fex` varchar(50) DEFAULT NULL,
  `ssu_email` varchar(50) DEFAULT NULL,
  `ssu_net` varchar(200) DEFAULT NULL,
  `ssu_add` longtext,
  `ssu_prd` longtext,
  `ssu_county` bigint DEFAULT NULL,
  `ssu_pro` bigint DEFAULT NULL,
  `ssu_city` bigint DEFAULT NULL,
  `ssu_zip_code` varchar(50) DEFAULT NULL,
  `ssu_bank` varchar(50) DEFAULT NULL,
  `ssu_bank_code` varchar(50) DEFAULT NULL,
  `ssu_isdel` char(1) DEFAULT '0',
  `ssu_remark` longtext,
  `ssu_inp_user` varchar(50) DEFAULT NULL,
  `ssu_cre_date` datetime DEFAULT NULL,
  `ssu_alt_date` datetime DEFAULT NULL,
  `ssu_alt_user` varchar(50) DEFAULT NULL,
  `ssu_bank_name` varchar(50) DEFAULT NULL,
  `ssu_type_id` bigint DEFAULT NULL,
  PRIMARY KEY (`ssu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='供应商表';

-- 14. 类型列表表
DROP TABLE IF EXISTS `type_list`;
CREATE TABLE `type_list` (
  `typ_id` bigint NOT NULL AUTO_INCREMENT,
  `typ_name` varchar(50) DEFAULT NULL,
  `typ_desc` longtext,
  `typ_type` varchar(50) DEFAULT NULL,
  `typ_isenabled` char(1) DEFAULT '1',
  PRIMARY KEY (`typ_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='类型列表表';

-- 15. 省份表
DROP TABLE IF EXISTS `cus_province`;
CREATE TABLE `cus_province` (
  `prv_id` bigint NOT NULL AUTO_INCREMENT,
  `prv_area_id` bigint DEFAULT NULL,
  `prv_name` varchar(100) DEFAULT NULL,
  `prv_isenabled` varchar(10) DEFAULT '1',
  PRIMARY KEY (`prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='省份表';

-- 16. 城市表
DROP TABLE IF EXISTS `cus_city`;
CREATE TABLE `cus_city` (
  `cit_id` bigint NOT NULL AUTO_INCREMENT,
  `cit_prv_id` bigint DEFAULT NULL,
  `cit_name` varchar(100) DEFAULT NULL,
  `cit_isenabled` varchar(10) DEFAULT '1',
  PRIMARY KEY (`cit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='城市表';

-- 17. 地址表
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address` (
  `adr_id` bigint NOT NULL AUTO_INCREMENT,
  `adr_state` int DEFAULT NULL,
  `adr_name` varchar(50) DEFAULT NULL,
  `adr_add` varchar(200) DEFAULT NULL,
  `adr_cas_id` bigint DEFAULT NULL,
  `adr_cat` varchar(50) DEFAULT NULL,
  `adr_remark` longtext,
  `adr_isdel` char(1) DEFAULT '0',
  `adr_num` int DEFAULT NULL,
  `adr_check_app` int DEFAULT NULL,
  `adr_mail_app` int DEFAULT NULL,
  `adr_vis_app` int DEFAULT NULL,
  `adr_rel` varchar(50) DEFAULT NULL,
  `adr_mail_count` int DEFAULT NULL,
  `adr_isnew` int DEFAULT NULL,
  PRIMARY KEY (`adr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='地址表';

-- 18. 仓库流水表
DROP TABLE IF EXISTS `wms_line`;
CREATE TABLE `wms_line` (
  `wli_id` bigint NOT NULL AUTO_INCREMENT,
  `wli_type_code` varchar(50) DEFAULT NULL,
  `wli_type` varchar(50) DEFAULT NULL,
  `wli_stro_code` varchar(50) DEFAULT NULL,
  `wli_wpr_id` bigint DEFAULT NULL,
  `wli_in_num` decimal(18,2) DEFAULT NULL,
  `wli_out_num` decimal(18,2) DEFAULT NULL,
  `wli_date` datetime DEFAULT NULL,
  `wli_state` char(1) DEFAULT NULL,
  `wli_man` varchar(50) DEFAULT NULL,
  `wli_wms_id` bigint DEFAULT NULL,
  `wli_isdel` char(1) DEFAULT '0',
  `wli_num` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`wli_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='仓库流水表';

-- 19. 销售付款计划表
DROP TABLE IF EXISTS `sal_paid_plan`;
CREATE TABLE `sal_paid_plan` (
  `spd_id` bigint NOT NULL AUTO_INCREMENT,
  `spd_ord_code` bigint DEFAULT NULL,
  `spd_prm_date` datetime DEFAULT NULL,
  `spd_count` int DEFAULT NULL,
  `spd_pay_mon` decimal(18,2) DEFAULT NULL,
  `spd_mon_type` varchar(50) DEFAULT NULL,
  `spd_user_code` varchar(50) DEFAULT NULL,
  `spd_isp` char(1) DEFAULT NULL,
  `spd_resp` varchar(50) DEFAULT NULL,
  `spd_cre_date` datetime DEFAULT NULL,
  `spd_alt_date` datetime DEFAULT NULL,
  `spd_alt_user` varchar(50) DEFAULT NULL,
  `spd_isdel` char(1) DEFAULT '0',
  `spd_content` varchar(100) DEFAULT NULL,
  `spd_cor_code` bigint DEFAULT NULL,
  PRIMARY KEY (`spd_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售付款计划表';

-- 20. 销售过往付款表
DROP TABLE IF EXISTS `spo_paid_past`;
CREATE TABLE `spo_paid_past` (
  `spa_id` bigint NOT NULL AUTO_INCREMENT,
  `spa_code` varchar(300) DEFAULT NULL,
  `spa_spo_id` bigint DEFAULT NULL,
  `spa_aco_id` bigint DEFAULT NULL,
  `spa_ssu_id` bigint DEFAULT NULL,
  `spa_fct_date` datetime DEFAULT NULL,
  `spa_type_id` bigint DEFAULT NULL,
  `spa_pay_type` varchar(50) DEFAULT NULL,
  `spa_pay_mon` decimal(18,2) DEFAULT NULL,
  `spa_in_name` varchar(100) DEFAULT NULL,
  `spa_inp_user` varchar(50) DEFAULT NULL,
  `spa_se_no` bigint DEFAULT NULL,
  `spa_isinv` char(1) DEFAULT NULL,
  `spa_remark` longtext,
  `spa_cre_date` datetime DEFAULT NULL,
  `spa_isdel` char(1) DEFAULT '0',
  `spa_content` varchar(100) DEFAULT NULL,
  `spa_acc_type_id` bigint DEFAULT NULL,
  `spa_alt_date` datetime DEFAULT NULL,
  `spa_alt_user` varchar(50) DEFAULT NULL,
  `spa_undo_date` datetime DEFAULT NULL,
  `spa_undo_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`spa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售过往付款表';

-- 21. 报告权限表
DROP TABLE IF EXISTS `r_rep_lim`;
CREATE TABLE `r_rep_lim` (
  `rrl_id` bigint NOT NULL AUTO_INCREMENT,
  `rrl_rep_code` bigint DEFAULT NULL,
  `rrl_se_no` bigint DEFAULT NULL,
  `rrl_date` datetime DEFAULT NULL,
  `rrl_content` longtext,
  `rrl_isappro` char(1) DEFAULT NULL,
  `rrl_oppro_date` datetime DEFAULT NULL,
  `rrl_isdel` char(1) DEFAULT '0',
  `rrl_app_order` int DEFAULT NULL,
  `rrl_isview` char(1) DEFAULT NULL,
  `rrl_is_all_appro` char(1) DEFAULT NULL,
  `rrl_rec_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`rrl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='报告权限表';

-- 22. 新闻权限表
DROP TABLE IF EXISTS `r_new_lim`;
CREATE TABLE `r_new_lim` (
  `rnl_id` bigint NOT NULL AUTO_INCREMENT,
  `rnl_new_code` bigint DEFAULT NULL,
  `rnl_se_no` bigint DEFAULT NULL,
  `rnl_date` datetime DEFAULT NULL,
  PRIMARY KEY (`rnl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='新闻权限表';

-- 23. 文档模板表
DROP TABLE IF EXISTS `doc_template`;
CREATE TABLE `doc_template` (
  `tmp_id` bigint NOT NULL AUTO_INCREMENT,
  `tmp_name` varchar(50) DEFAULT NULL,
  `tmp_html` longtext,
  `tmp_type` varchar(50) DEFAULT NULL,
  `tmp_mark` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`tmp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='文档模板表';

-- 24. 销售任务表
DROP TABLE IF EXISTS `sal_all_task`;
CREATE TABLE `sal_all_task` (
  `sat_id` bigint NOT NULL AUTO_INCREMENT,
  `sat_date` varchar(50) DEFAULT NULL,
  `sat_se_no` bigint DEFAULT NULL,
  `sat_inp_date` datetime DEFAULT NULL,
  `sat_alt_date` datetime DEFAULT NULL,
  `sat_inp_name` varchar(50) DEFAULT NULL,
  `sat_alt_name` varchar(50) DEFAULT NULL,
  `sat_ht_mon` decimal(18,2) DEFAULT NULL,
  `sat_paid_mon` decimal(18,2) DEFAULT NULL,
  `sat_cus_num` int DEFAULT NULL,
  PRIMARY KEY (`sat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='销售任务表';

-- 25. 关系表 - 产品与订单
DROP TABLE IF EXISTS `r_spo_pro`;
CREATE TABLE `r_spo_pro` (
  `rpp_id` bigint NOT NULL AUTO_INCREMENT,
  `rpp_spo_id` bigint DEFAULT NULL,
  `rpp_pro_id` bigint DEFAULT NULL,
  `rpp_num` decimal(18,2) DEFAULT NULL,
  `rpp_price` decimal(18,2) DEFAULT NULL,
  `rpp_sum_mon` decimal(18,2) DEFAULT NULL,
  `rpp_remark` longtext,
  `rpp_out_num` decimal(18,2) DEFAULT NULL,
  `rpp_real_num` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`rpp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='产品订单关系表';

-- 26. 关系表 - 仓库与产品
DROP TABLE IF EXISTS `r_stro_pro`;
CREATE TABLE `r_stro_pro` (
  `rsp_id` bigint NOT NULL AUTO_INCREMENT,
  `rsp_stro_code` varchar(50) DEFAULT NULL,
  `rsp_pro_id` bigint DEFAULT NULL,
  `rsp_pro_num` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`rsp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='仓库产品关系表';

-- 27. 关系表 - 发货与产品
DROP TABLE IF EXISTS `r_ship_pro`;
CREATE TABLE `r_ship_pro` (
  `rshp_id` bigint NOT NULL AUTO_INCREMENT,
  `rshp_ship_code` varchar(50) DEFAULT NULL,
  `rshp_pro_id` bigint DEFAULT NULL,
  PRIMARY KEY (`rshp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='发货产品关系表';

-- ========================================
-- 表结构定义结束
-- ========================================

-- 创建常用索引
CREATE INDEX idx_sal_emp_name ON `sal_emp` (`se_name`);
CREATE INDEX idx_sal_emp_user_code ON `sal_emp` (`se_user_code`);
CREATE INDEX idx_cus_cor_cus_user ON `cus_cor_cus` (`cor_user_code`);
CREATE INDEX idx_cus_cor_cus_name ON `cus_cor_cus` (`cor_name`);
CREATE INDEX idx_lim_user_enabled ON `lim_user` (`user_isenabled`);
CREATE INDEX idx_sal_opp_cor_code ON `sal_opp` (`opp_cor_code`);
CREATE INDEX idx_sal_opp_user_code ON `sal_opp` (`opp_user_code`);
CREATE INDEX idx_sal_pra_cor_code ON `sal_pra` (`pra_cor_code`);
CREATE INDEX idx_sal_ord_con_cus_code ON `sal_ord_con` (`sod_cus_code`);
CREATE INDEX idx_sal_ord_con_state ON `sal_ord_con` (`sod_state`);
CREATE INDEX idx_wms_product_name ON `wms_product` (`wpr_name`);
CREATE INDEX idx_wms_product_code ON `wms_product` (`wpr_code`);
CREATE INDEX idx_sal_supplier_name ON `sal_supplier` (`ssu_name`);
CREATE INDEX idx_type_list_type ON `type_list` (`typ_type`);
CREATE INDEX idx_address_cas_id ON `address` (`adr_cas_id`);

SET FOREIGN_KEY_CHECKS = 1;

-- 数据库结构创建完成
SELECT 'CCDS完整数据库结构创建完成 - 包含27个核心表' as message;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ccds';
