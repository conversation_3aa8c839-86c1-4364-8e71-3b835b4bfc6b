# 催收系统 (CCDS - Collection Credit Data System)

## 项目简介

催收系统是一个基于Spring Boot的企业级催收管理系统，用于管理银行案件、催收记录、还款记录等业务功能。

## 技术栈

- **后端框架**: Spring Boot 2.7.18
- **数据库**: MySQL 5.7+
- **ORM框架**: MyBatis 3.5+
- **连接池**: Druid 1.2+
- **分页插件**: PageHelper 1.4+
- **前端技术**: JSP + Bootstrap + jQuery
- **构建工具**: Maven 3.6+
- **JDK版本**: JDK 8

## 项目结构

```
ccds-parent/
├── ccds-main/                    # 主应用模块
│   ├── src/main/java/
│   │   └── com/frsoft/ccds/
│   │       ├── CCDSApplication.java      # 启动类
│   │       ├── common/                   # 公共工具类
│   │       │   ├── core/                # 核心类
│   │       │   │   ├── controller/      # 基础控制器
│   │       │   │   ├── domain/          # 基础实体
│   │       │   │   └── page/            # 分页相关
│   │       │   └── utils/               # 工具类
│   │       ├── config/                  # 配置类
│   │       │   ├── MyBatisConfig.java   # MyBatis配置
│   │       │   ├── WebMvcConfig.java    # Web配置
│   │       │   └── DruidConfig.java     # 数据源配置
│   │       ├── system/                  # 系统模块
│   │       │   ├── controller/          # 控制器
│   │       │   ├── domain/              # 实体类
│   │       │   ├── mapper/              # 数据访问层
│   │       │   └── service/             # 业务逻辑层
│   │       └── web/                     # Web控制器
│   │           └── controller/          # Web控制器
│   └── src/main/resources/
│       ├── application.yml              # 主配置文件
│       ├── application-dev.yml          # 开发环境配置
│       ├── mapper/                      # MyBatis映射文件
│       └── mybatis/                     # MyBatis配置
├── pom.xml                              # 父POM文件
└── README.md                            # 项目说明文档
```

## 核心功能模块

### 1. 用户管理 (SysUser)
- 用户登录/登出
- 用户信息管理
- 密码修改
- 用户状态管理

### 2. 银行案件管理 (BankCase)
- 案件信息录入
- 案件查询和筛选
- 案件分配给催收员
- 案件状态跟踪

### 3. 催收记录管理 (PhoRed)
- 催收记录录入
- 催收历史查询
- 催收结果统计

### 4. 还款记录管理 (CasePaid)
- 还款承诺记录
- 实际还款记录
- 还款统计分析

### 5. 员工管理 (SalEmp)
- 催收员信息管理
- 组织架构管理
- 权限分配

## 数据库设计

### 主要数据表

1. **sys_user** - 系统用户表
2. **bank_case** - 银行案件表
3. **pho_red** - 催收记录表
4. **case_paid** - 还款记录表
5. **sal_emp** - 员工表
6. **sal_org** - 组织表
7. **lim_role** - 角色表
8. **lim_group** - 组表
9. **type_list** - 类型字典表

## 快速开始

### 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 5.7+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd ccds-parent
   ```

2. **创建数据库**
   ```sql
   CREATE DATABASE ccds DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **导入数据库脚本**
   ```bash
   # 执行SQL脚本创建表结构
   mysql -u root -p ccds < sql/mysql8/create_complete_106_tables.sql
   ```

4. **修改配置文件**
   ```yaml
   # ccds-main/src/main/resources/application-dev.yml
   spring:
     datasource:
       url: **********************************************************************************************
       username: root
       password: your_password
   ```

5. **编译项目**
   ```bash
   mvn clean compile
   ```

6. **运行项目**
   ```bash
   mvn spring-boot:run -pl ccds-main
   ```

7. **访问系统**
   ```
   http://localhost:8080
   ```

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**************:3306/ccds
    username: root
    password: 123456
```

### MyBatis配置
```yaml
mybatis:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.frsoft.ccds.system.domain
  configuration:
    map-underscore-to-camel-case: true
```

## API接口

### 用户管理接口
- `POST /login` - 用户登录
- `POST /logout` - 用户登出
- `GET /system/user/list` - 用户列表
- `POST /system/user/add` - 新增用户
- `PUT /system/user/edit` - 修改用户

### 案件管理接口
- `GET /system/bankcase/list` - 案件列表
- `POST /system/bankcase/add` - 新增案件
- `PUT /system/bankcase/edit` - 修改案件
- `POST /system/bankcase/assign` - 分配案件

## 开发指南

### 代码规范
- 使用Lombok简化代码
- 统一使用驼峰命名法
- 控制器方法返回AjaxResult
- 数据库字段使用下划线命名

### 新增功能模块
1. 在`domain`包下创建实体类
2. 在`mapper`包下创建Mapper接口
3. 在`resources/mapper`下创建XML映射文件
4. 在`service`包下创建Service接口和实现
5. 在`controller`包下创建控制器

## 部署说明

### 生产环境部署
1. 修改`application-prod.yml`配置
2. 打包项目：`mvn clean package`
3. 运行jar包：`java -jar ccds-main-1.0.0.jar --spring.profiles.active=prod`

### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY ccds-main/target/ccds-main-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 常见问题

### Q: 数据库连接失败？
A: 检查数据库配置信息，确保MySQL服务正常运行，用户名密码正确。

### Q: 编译失败？
A: 确保JDK版本为8+，Maven版本为3.6+，网络连接正常。

### Q: 页面无法访问？
A: 检查端口是否被占用，防火墙设置是否正确。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完成基础用户管理功能
- 完成银行案件管理功能
- 完成催收记录管理功能
- 完成还款记录管理功能

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

- 项目维护者：CCDS Team
- 邮箱：<EMAIL>
- 技术支持：QQ群 123456789
