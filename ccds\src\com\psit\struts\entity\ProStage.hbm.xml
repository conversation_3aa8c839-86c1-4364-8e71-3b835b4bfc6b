<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.ProStage" table="pro_stage" schema="dbo" >
        <id name="staId" type="java.lang.Long">
            <column name="sta_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="project" class="com.psit.struts.entity.Project" fetch="select" not-null="false">
            <column name="sta_pro_id" />
        </many-to-one>
        <property name="staTitle" type="java.lang.String">
            <column name="sta_title" length="300" />
        </property>
        <property name="staAim" type="java.lang.String">
            <column name="sta_aim" length="300" />
        </property>
        <property name="staStartDate" type="java.util.Date">
            <column name="sta_start_date" length="23" />
        </property>
        <property name="staEndDate" type="java.util.Date">
            <column name="sta_end_date" length="23" />
        </property>
        <property name="staRemark" type="java.lang.String">
            <column name="sta_remark" length="1073741823" />
        </property>
        <property name="staInsDate" type="java.util.Date">
            <column name="sta_ins_date" length="23" />
        </property>
        <property name="staModDate" type="java.util.Date">
            <column name="sta_mod_date" length="23" />
        </property>
        <property name="staInpUser" type="java.lang.String">
            <column name="sta_inp_user" length="50" />
        </property>
        <property name="staUpdUser" type="java.lang.String">
            <column name="sta_upd_user" length="50" />
        </property>
        <property name="staIsdel" type="java.lang.String">
            <column name="sta_isdel" length="1" />
        </property>
        <set name="attachments" inverse="true"  cascade="all" where="att_type='psta'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
    </class>
</hibernate-mapping>
