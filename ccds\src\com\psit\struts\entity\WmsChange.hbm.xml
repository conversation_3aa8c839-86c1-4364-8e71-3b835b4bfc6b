<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.WmsChange" table="wms_change" schema="dbo" >
        <id name="wchId" type="java.lang.Long">
            <column name="wch_id" />
            <generator class="identity" />
        </id>
        <property name="checkOutName" type="java.lang.String">
        	 <column name="wch_checkOut" length="50" />
       </property>
        <property name="checkInName" type="java.lang.String">
        	<column name="wch_checkIn" length="50" />
        </property>
        <many-to-one name="wmsStroByWchInWms" class="com.psit.struts.entity.WmsStro" fetch="select" not-null="false">
            <column name="wch_in_wms" length="50" />
        </many-to-one>
        <many-to-one name="wmsStroByWchOutWms" class="com.psit.struts.entity.WmsStro" fetch="select" not-null="false">
            <column name="wch_out_wms" length="50" />
        </many-to-one>
         <property name="wchCode" type="java.lang.String">
            <column name="wch_code" length="50" />
        </property>
        <property name="wchTitle" type="java.lang.String">
            <column name="wch_title" length="1073741823" />
        </property>
        <property name="wchState" type="java.lang.String">
            <column name="wch_state" length="1" />
        </property>
        <property name="wchInDate" type="java.util.Date">
            <column name="wch_in_date" length="23" />
        </property>
        <property name="wchRecMan" type="java.lang.String">
            <column name="wch_rec_man" length="50" />
        </property>
        <property name="wchRemark" type="java.lang.String">
            <column name="wch_remark" length="1073741823" />
        </property>
        <property name="wchIsdel" type="java.lang.String">
            <column name="wch_isdel" length="1" />
        </property>
        <property name="wchOutDate" type="java.util.Date">
            <column name="wch_out_date" length="23" />
        </property>
        <property name="wchInTime" type="java.util.Date">
            <column name="wch_in_time" length="23" />
        </property>
        <property name="wchOutTime" type="java.util.Date">
            <column name="wch_out_time" length="23" />
        </property>
        <property name="wchInpName" type="java.lang.String">
            <column name="wch_inp_name" length="50" />
        </property>
         <property name="wchAltName" type="java.lang.String">
            <column name="wch_alt_name" length="50" />
        </property>
        <property name="wchInpDate" type="java.util.Date">
            <column name="wch_inp_date" length="23" />
        </property>
        <property name="wchAltDate" type="java.util.Date">
            <column name="wch_alt_date" length="23" />
        </property>
        <property name="wchAppDate" type="java.util.Date">
            <column name="wch_App_date" length="23" />
        </property>
        <property name="wchAppMan" type="java.lang.String">
            <column name="wch_App_man" length="50" />
        </property>
        <property name="wchAppDesc" type="java.lang.String">
            <column name="wch_App_desc" length="1073741823" />
        </property>
        <property name="wchAppIsok" type="java.lang.String">
            <column name="wch_App_isok" length="1" />
        </property>
        <property name="wchMatName" type="java.lang.String">
            <column name="wch_mat_name" length="50" />
        </property>
        <property name="wchCanDate" type="java.util.Date">
            <column name="wch_can_date" length="23" />
        </property>
        <property name="wchCanMan" type="java.lang.String">
            <column name="wch_can_man" length="50" />
        </property>
        <set name="RWmsWmses" inverse="true"  order-by="rww_id" cascade="delete">
            <key>
                <column name="wch_id" />
            </key>
            <one-to-many class="com.psit.struts.entity.RWmsWms" />
        </set>
    </class>
</hibernate-mapping>
