-- MySQL 5.7 完整CCDS数据库 - 包含所有重要业务表
-- 手工精确转换，确保项目功能完整运行

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `ccds`;

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 核心业务表 - 确保所有功能正常
-- ========================================

-- 1. 员工表
DROP TABLE IF EXISTS `sal_emp`;
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT,
  `se_so_code` varchar(50) DEFAULT NULL,
  `se_name` varchar(100) DEFAULT NULL,
  `se_ide_code` varchar(50) DEFAULT NULL,
  `se_pos` varchar(50) DEFAULT NULL,
  `se_sex` varchar(50) DEFAULT NULL,
  `se_prob` varchar(50) DEFAULT NULL,
  `se_bir_place` varchar(50) DEFAULT NULL,
  `se_acc_place` varchar(100) DEFAULT NULL,
  `se_birth` varchar(50) DEFAULT NULL,
  `se_marry` varchar(10) DEFAULT NULL,
  `se_type` varchar(50) DEFAULT NULL,
  `se_job_lev` bigint DEFAULT NULL,
  `se_job_cate` varchar(50) DEFAULT NULL,
  `se_job_title` varchar(50) DEFAULT NULL,
  `se_start_day` datetime DEFAULT NULL,
  `se_year_pay` varchar(50) DEFAULT NULL,
  `se_cost_center` varchar(50) DEFAULT NULL,
  `se_email` varchar(50) DEFAULT NULL,
  `se_nation` varchar(50) DEFAULT NULL,
  `se_poli_status` varchar(50) DEFAULT NULL,
  `se_edu` varchar(50) DEFAULT NULL,
  `se_tel` varchar(50) DEFAULT NULL,
  `se_phone` varchar(50) DEFAULT NULL,
  `se_qq` varchar(50) DEFAULT NULL,
  `se_msn` varchar(50) DEFAULT NULL,
  `se_rec_source` varchar(100) DEFAULT NULL,
  `se_prov_fund` varchar(50) DEFAULT NULL,
  `se_job_date` datetime DEFAULT NULL,
  `se_hou_reg` varchar(50) DEFAULT NULL,
  `se_social_code` varchar(50) DEFAULT NULL,
  `se_rap` varchar(50) DEFAULT NULL,
  `se_address` varchar(500) DEFAULT NULL,
  `se_remark` longtext,
  `se_bank_name` varchar(50) DEFAULT NULL,
  `se_bank_card` varchar(50) DEFAULT NULL,
  `se_weal_address` varchar(50) DEFAULT NULL,
  `se_weal_pos` varchar(50) DEFAULT NULL,
  `se_isovertime` varchar(50) DEFAULT NULL,
  `se_attendance` varchar(50) DEFAULT NULL,
  `se_card_num` varchar(50) DEFAULT NULL,
  `se_pic` longtext,
  `se_isenabled` char(1) DEFAULT '1',
  `se_inser_date` datetime DEFAULT NULL,
  `se_code` varchar(50) DEFAULT NULL,
  `se_log` longtext,
  `se_alt_date` datetime DEFAULT NULL,
  `se_inser_user` varchar(50) DEFAULT NULL,
  `se_alt_user` varchar(50) DEFAULT NULL,
  `se_end_date` datetime DEFAULT NULL,
  `se_edc_bac` longtext,
  `se_work_ex` longtext,
  `se_user_code` varchar(50) DEFAULT NULL,
  `se_per_tel` varchar(50) DEFAULT NULL,
  `se_plan_sign_date` datetime DEFAULT NULL,
  `se_sign_date` datetime DEFAULT NULL,
  `se_credit_date` datetime DEFAULT NULL,
  `se_college` varchar(200) DEFAULT NULL,
  `se_transfer` text,
  PRIMARY KEY (`se_no`),
  KEY `idx_se_name` (`se_name`),
  KEY `idx_se_user_code` (`se_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='员工表';

-- 2. 用户表
DROP TABLE IF EXISTS `lim_user`;
CREATE TABLE `lim_user` (
  `user_code` varchar(50) NOT NULL,
  `user_loginName` varchar(50) DEFAULT NULL,
  `user_pwd` varchar(50) DEFAULT NULL,
  `user_up_code` varchar(50) DEFAULT NULL,
  `user_lev` char(1) DEFAULT NULL,
  `user_so_code` varchar(50) DEFAULT NULL,
  `user_se_id` bigint DEFAULT NULL,
  `user_se_name` varchar(100) DEFAULT NULL,
  `user_desc` longtext,
  `user_isenabled` char(1) DEFAULT '1',
  `user_num` varchar(200) DEFAULT NULL,
  `user_role_id` bigint DEFAULT NULL,
  `user_islogin` char(1) DEFAULT NULL,
  `user_ip` varchar(50) DEFAULT NULL,
  `user_fail` int DEFAULT NULL,
  `user_pwd_upd_date` datetime DEFAULT NULL,
  `user_cti_login` varchar(255) DEFAULT NULL,
  `user_cti_pwd` varchar(255) DEFAULT NULL,
  `user_cti_server` varchar(50) DEFAULT NULL,
  `user_cti_phone` varchar(50) DEFAULT NULL,
  `user_grp_id` bigint DEFAULT NULL,
  `user_sms_max_num` int DEFAULT NULL,
  PRIMARY KEY (`user_code`),
  KEY `idx_user_enabled` (`user_isenabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户表';

-- 3. 客户表
DROP TABLE IF EXISTS `cus_cor_cus`;
CREATE TABLE `cus_cor_cus` (
  `cor_code` bigint NOT NULL AUTO_INCREMENT,
  `cor_num` varchar(50) DEFAULT NULL,
  `cor_user_code` varchar(50) DEFAULT NULL,
  `cor_name` varchar(100) DEFAULT NULL,
  `cor_hot` varchar(50) DEFAULT NULL,
  `cor_mne` varchar(50) DEFAULT NULL,
  `cor_lic_code` varchar(50) DEFAULT NULL,
  `cor_org_code` varchar(50) DEFAULT NULL,
  `cor_star` varchar(50) DEFAULT NULL,
  `cor_cre_lev` varchar(50) DEFAULT NULL,
  `cor_cre_lim` varchar(50) DEFAULT NULL,
  `cor_ind_id` bigint DEFAULT NULL,
  `cor_per_size` varchar(50) DEFAULT NULL,
  `cor_acc_bank` varchar(100) DEFAULT NULL,
  `cor_bank_num` varchar(50) DEFAULT NULL,
  `cor_sou_id` bigint DEFAULT NULL,
  `cor_com_inf` longtext,
  `cor_country` bigint DEFAULT NULL,
  `cor_province` bigint DEFAULT NULL,
  `cor_city` bigint DEFAULT NULL,
  `cor_phone` varchar(50) DEFAULT NULL,
  `cor_fex` varchar(50) DEFAULT NULL,
  `cor_net` varchar(500) DEFAULT NULL,
  `cor_zip_code` varchar(50) DEFAULT NULL,
  `cor_address` longtext,
  `cor_remark` longtext,
  `cor_creat_date` datetime DEFAULT NULL,
  `cor_upd_date` datetime DEFAULT NULL,
  `cor_issuc` char(1) DEFAULT NULL,
  `cor_last_date` datetime DEFAULT NULL,
  `cor_temp_tag` varchar(50) DEFAULT NULL,
  `cor_isdelete` char(1) DEFAULT '0',
  `cor_spe_write` longtext,
  `cor_upd_user` varchar(50) DEFAULT NULL,
  `cor_typ_id` bigint DEFAULT NULL,
  `cor_ins_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cor_code`),
  KEY `idx_cor_user_code` (`cor_user_code`),
  KEY `idx_cor_name` (`cor_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='客户表';

-- 4. 银行案件表 (核心业务表)
DROP TABLE IF EXISTS `bank_case`;
CREATE TABLE `bank_case` (
  `cas_id` bigint NOT NULL AUTO_INCREMENT,
  `cas_code` varchar(100) DEFAULT NULL,
  `cas_group` varchar(50) DEFAULT NULL,
  `cas_state` int DEFAULT 1,
  `cas_typ_hid` bigint DEFAULT NULL,
  `cas_name` varchar(50) DEFAULT NULL,
  `cas_phone` varchar(50) DEFAULT NULL,
  `cas_m` decimal(18,2) DEFAULT NULL,
  `cas_paid_m` decimal(18,2) DEFAULT 0.00,
  `cas_se_no` bigint DEFAULT NULL,
  `cas_ins_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `cas_ins_user` varchar(25) DEFAULT NULL,
  `cas_alt_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `cas_alt_user` varchar(25) DEFAULT NULL,
  `cas_remark` longtext,
  `cas_id_no` varchar(50) DEFAULT NULL,
  `cas_address` varchar(500) DEFAULT NULL,
  `cas_work_address` varchar(500) DEFAULT NULL,
  `cas_work_phone` varchar(50) DEFAULT NULL,
  `cas_email` varchar(100) DEFAULT NULL,
  `cas_bank` varchar(100) DEFAULT NULL,
  `cas_card_no` varchar(50) DEFAULT NULL,
  `cas_overdue_date` varchar(200) DEFAULT NULL,
  `cas_pback_p` float DEFAULT NULL,
  `cas_wpost_code` varchar(50) DEFAULT NULL,
  `cas_deadline` varchar(200) DEFAULT NULL,
  `cas_is_host` varchar(50) DEFAULT NULL,
  `cas_bill_date` varchar(200) DEFAULT NULL,
  `cas_last_paid` varchar(200) DEFAULT NULL,
  `cas_count` varchar(100) DEFAULT NULL,
  `cas_left_pri` varchar(100) DEFAULT NULL,
  `cas_assign_ids` longtext,
  `cas_assign_names` longtext,
  `cas_last_assign_time` datetime DEFAULT NULL,
  `cas_overdue_days` int DEFAULT NULL,
  `cas_overdue_days_str` varchar(200) DEFAULT NULL,
  `cas_bir` varchar(50) DEFAULT NULL,
  `cas_mpost_code` varchar(50) DEFAULT NULL,
  `cas_perm_crline` varchar(50) DEFAULT NULL,
  `cas_alt_hold` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cas_id`),
  KEY `idx_cas_se_no` (`cas_se_no`),
  KEY `idx_cas_state` (`cas_state`),
  KEY `idx_cas_name` (`cas_name`),
  KEY `idx_cas_phone` (`cas_phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='银行案件表';

-- 5. 催收记录表
DROP TABLE IF EXISTS `pho_red`;
CREATE TABLE `pho_red` (
  `pr_id` bigint NOT NULL AUTO_INCREMENT,
  `pr_typ_id` bigint DEFAULT NULL,
  `pr_contact` varchar(200) DEFAULT NULL,
  `pr_cas_id` bigint DEFAULT NULL,
  `pr_content` longtext,
  `pr_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `pr_se_no` bigint DEFAULT NULL,
  `pr_con_type` varchar(50) DEFAULT NULL,
  `pr_result` varchar(50) DEFAULT NULL,
  `pr_next_time` datetime DEFAULT NULL,
  `pr_remark` longtext,
  `pr_isdel` char(1) DEFAULT '0',
  PRIMARY KEY (`pr_id`),
  KEY `idx_pr_cas_id` (`pr_cas_id`),
  KEY `idx_pr_se_no` (`pr_se_no`),
  KEY `idx_pr_time` (`pr_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='催收记录表';

-- 6. 还款记录表
DROP TABLE IF EXISTS `case_paid`;
CREATE TABLE `case_paid` (
  `pa_id` bigint NOT NULL AUTO_INCREMENT,
  `pa_state` int DEFAULT NULL,
  `pa_cas_id` bigint DEFAULT NULL,
  `pa_ptp_d` datetime DEFAULT NULL,
  `pa_ptp_num` decimal(18,2) DEFAULT NULL,
  `pa_paid_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `pa_paid_num` decimal(18,2) DEFAULT NULL,
  `pa_se_no` bigint DEFAULT NULL,
  `pa_remark` longtext,
  `pa_type` varchar(50) DEFAULT NULL,
  `pa_method` varchar(50) DEFAULT NULL,
  `pa_ins_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `pa_ins_user` varchar(25) DEFAULT NULL,
  `pa_alt_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `pa_alt_user` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`pa_id`),
  KEY `idx_pa_cas_id` (`pa_cas_id`),
  KEY `idx_pa_se_no` (`pa_se_no`),
  KEY `idx_pa_paid_date` (`pa_paid_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='还款记录表';

-- 7. 项目表
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `pro_id` bigint NOT NULL AUTO_INCREMENT,
  `pro_user_code` varchar(50) DEFAULT NULL,
  `pro_typ_id` bigint DEFAULT NULL,
  `pro_title` varchar(300) DEFAULT NULL,
  `pro_state` varchar(50) DEFAULT NULL,
  `pro_cre_date` datetime DEFAULT NULL,
  `pro_fin_date` datetime DEFAULT NULL,
  `pro_desc` longtext,
  `pro_remark` longtext,
  `pro_inp_user` varchar(50) DEFAULT NULL,
  `pro_upd_user` varchar(50) DEFAULT NULL,
  `pro_ins_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `pro_mod_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `pro_isdel` char(1) DEFAULT '0',
  `pro_cor_code` bigint DEFAULT NULL,
  `pro_period` varchar(50) DEFAULT NULL,
  `pro_pro` varchar(300) DEFAULT NULL,
  `pro_pro_log` longtext,
  PRIMARY KEY (`pro_id`),
  KEY `idx_pro_user_code` (`pro_user_code`),
  KEY `idx_pro_state` (`pro_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='项目表';

-- 8. 地址表
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address` (
  `adr_id` bigint NOT NULL AUTO_INCREMENT,
  `adr_state` int DEFAULT NULL,
  `adr_name` varchar(50) DEFAULT NULL,
  `adr_add` varchar(200) DEFAULT NULL,
  `adr_cas_id` bigint DEFAULT NULL,
  `adr_cat` varchar(50) DEFAULT NULL,
  `adr_remark` longtext,
  `adr_isdel` char(1) DEFAULT '0',
  `adr_num` int DEFAULT NULL,
  `adr_check_app` int DEFAULT NULL,
  `adr_mail_app` int DEFAULT NULL,
  `adr_vis_app` int DEFAULT NULL,
  `adr_rel` varchar(50) DEFAULT NULL,
  `adr_mail_count` int DEFAULT NULL,
  `adr_isnew` int DEFAULT NULL,
  PRIMARY KEY (`adr_id`),
  KEY `idx_adr_cas_id` (`adr_cas_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='地址表';

-- 9. 电话列表表
DROP TABLE IF EXISTS `phone_list`;
CREATE TABLE `phone_list` (
  `phl_id` bigint NOT NULL AUTO_INCREMENT,
  `phl_state` int DEFAULT NULL,
  `phl_name` varchar(50) DEFAULT NULL,
  `phl_num` varchar(50) DEFAULT NULL,
  `phl_cas_id` bigint DEFAULT NULL,
  `phl_cat` varchar(50) DEFAULT NULL,
  `phl_remark` longtext,
  `phl_isdel` char(1) DEFAULT '0',
  `phl_rel` varchar(50) DEFAULT NULL,
  `phl_work_unit` varchar(200) DEFAULT NULL,
  `phl_address` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`phl_id`),
  KEY `idx_phl_cas_id` (`phl_cas_id`),
  KEY `idx_phl_num` (`phl_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='电话列表表';

-- 10. 类型列表表
DROP TABLE IF EXISTS `type_list`;
CREATE TABLE `type_list` (
  `typ_id` bigint NOT NULL AUTO_INCREMENT,
  `typ_name` varchar(50) DEFAULT NULL,
  `typ_desc` longtext,
  `typ_type` varchar(50) DEFAULT NULL,
  `typ_isenabled` char(1) DEFAULT '1',
  PRIMARY KEY (`typ_id`),
  KEY `idx_typ_type` (`typ_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='类型列表表';

-- 11. 权限表
DROP TABLE IF EXISTS `lim_right`;
CREATE TABLE `lim_right` (
  `rig_code` varchar(50) NOT NULL,
  `rig_fun_code` varchar(50) DEFAULT NULL,
  `rig_ope_code` varchar(50) DEFAULT NULL,
  `rig_wms_name` varchar(300) DEFAULT NULL,
  PRIMARY KEY (`rig_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='权限表';

-- 12. 功能表
DROP TABLE IF EXISTS `lim_function`;
CREATE TABLE `lim_function` (
  `fun_code` varchar(50) NOT NULL,
  `fun_desc` longtext,
  `fun_type` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`fun_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='功能表';

-- 13. 操作表
DROP TABLE IF EXISTS `lim_operate`;
CREATE TABLE `lim_operate` (
  `ope_code` varchar(50) NOT NULL,
  `ope_desc` longtext,
  PRIMARY KEY (`ope_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='操作表';

-- 14. 用户权限关系表
DROP TABLE IF EXISTS `r_user_rig`;
CREATE TABLE `r_user_rig` (
  `rur_id` bigint NOT NULL AUTO_INCREMENT,
  `rur_user_code` varchar(50) DEFAULT NULL,
  `rur_rig_code` varchar(50) DEFAULT NULL,
  `rur_type` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`rur_id`),
  KEY `idx_rur_user_code` (`rur_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户权限关系表';

-- 15. 角色表
DROP TABLE IF EXISTS `lim_role`;
CREATE TABLE `lim_role` (
  `rol_id` bigint NOT NULL AUTO_INCREMENT,
  `rol_name` varchar(50) DEFAULT NULL,
  `rol_lev` int DEFAULT NULL,
  `rol_desc` longtext,
  `rol_grp_id` bigint DEFAULT NULL,
  PRIMARY KEY (`rol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='角色表';

-- 16. 组表
DROP TABLE IF EXISTS `lim_group`;
CREATE TABLE `lim_group` (
  `grp_id` bigint NOT NULL AUTO_INCREMENT,
  `grp_name` varchar(50) DEFAULT NULL,
  `grp_desc` varchar(200) DEFAULT NULL,
  `grp_cre_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `grp_cre_man` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`grp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='组表';

-- 17. 组权限关系表
DROP TABLE IF EXISTS `r_group_rig`;
CREATE TABLE `r_group_rig` (
  `rgr_id` bigint NOT NULL AUTO_INCREMENT,
  `rgr_grp_id` bigint DEFAULT NULL,
  `rgr_rig_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`rgr_id`),
  KEY `idx_rgr_grp_id` (`rgr_grp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='组权限关系表';

-- 18. 消息表
DROP TABLE IF EXISTS `message`;
CREATE TABLE `message` (
  `me_code` bigint NOT NULL AUTO_INCREMENT,
  `me_title` varchar(100) DEFAULT NULL,
  `me_content` longtext,
  `me_se_no` bigint DEFAULT NULL,
  `me_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `me_issend` char(1) DEFAULT '0',
  `me_isdel` char(1) DEFAULT '0',
  `me_ins_user` varchar(50) DEFAULT NULL,
  `me_rec_name` longtext,
  PRIMARY KEY (`me_code`),
  KEY `idx_me_se_no` (`me_se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='消息表';

-- 19. 消息权限表
DROP TABLE IF EXISTS `r_mess_lim`;
CREATE TABLE `r_mess_lim` (
  `rml_id` bigint NOT NULL AUTO_INCREMENT,
  `rml_me_code` bigint DEFAULT NULL,
  `rml_se_no` bigint DEFAULT NULL,
  `rml_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `rml_isdel` char(1) DEFAULT '0',
  PRIMARY KEY (`rml_id`),
  KEY `idx_rml_me_code` (`rml_me_code`),
  KEY `idx_rml_se_no` (`rml_se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='消息权限表';

-- 20. 新闻表
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news` (
  `new_code` bigint NOT NULL AUTO_INCREMENT,
  `new_title` varchar(100) DEFAULT NULL,
  `new_type` varchar(100) DEFAULT NULL,
  `new_se_no` bigint DEFAULT NULL,
  `new_content` longtext,
  `new_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `new_isdel` char(1) DEFAULT '0',
  PRIMARY KEY (`new_code`),
  KEY `idx_new_se_no` (`new_se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='新闻表';

-- 21. 新闻权限表
DROP TABLE IF EXISTS `r_new_lim`;
CREATE TABLE `r_new_lim` (
  `rnl_id` bigint NOT NULL AUTO_INCREMENT,
  `rnl_new_code` bigint DEFAULT NULL,
  `rnl_se_no` bigint DEFAULT NULL,
  `rnl_date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`rnl_id`),
  KEY `idx_rnl_new_code` (`rnl_new_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='新闻权限表';

-- 22. 报告表
DROP TABLE IF EXISTS `report`;
CREATE TABLE `report` (
  `rep_code` bigint NOT NULL AUTO_INCREMENT,
  `rep_title` varchar(100) DEFAULT NULL,
  `rep_content` longtext,
  `rep_se_no` bigint DEFAULT NULL,
  `rep_appro_content` longtext,
  `rep_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `rep_appro_date` datetime DEFAULT NULL,
  `rep_isappro` char(1) DEFAULT '0',
  `rep_isdel` char(1) DEFAULT '0',
  PRIMARY KEY (`rep_code`),
  KEY `idx_rep_se_no` (`rep_se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='报告表';

-- 23. 报告权限表
DROP TABLE IF EXISTS `r_rep_lim`;
CREATE TABLE `r_rep_lim` (
  `rrl_id` bigint NOT NULL AUTO_INCREMENT,
  `rrl_rep_code` bigint DEFAULT NULL,
  `rrl_se_no` bigint DEFAULT NULL,
  `rrl_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `rrl_content` longtext,
  `rrl_isappro` char(1) DEFAULT '0',
  `rrl_oppro_date` datetime DEFAULT NULL,
  `rrl_isdel` char(1) DEFAULT '0',
  `rrl_app_order` int DEFAULT NULL,
  `rrl_isview` char(1) DEFAULT '0',
  `rrl_is_all_appro` char(1) DEFAULT '0',
  `rrl_rec_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`rrl_id`),
  KEY `idx_rrl_rep_code` (`rrl_rep_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='报告权限表';

-- 24. 附件表
DROP TABLE IF EXISTS `attachment`;
CREATE TABLE `attachment` (
  `att_id` bigint NOT NULL AUTO_INCREMENT,
  `att_name` longtext,
  `att_size` bigint DEFAULT NULL,
  `att_path` longtext,
  `att_isJunk` char(1) DEFAULT '0',
  `att_type` varchar(50) DEFAULT NULL,
  `att_fk_id` bigint DEFAULT NULL,
  `att_ins_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `att_ins_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`att_id`),
  KEY `idx_att_fk_id` (`att_fk_id`),
  KEY `idx_att_type` (`att_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='附件表';

-- 25. 锁表
DROP TABLE IF EXISTS `lock_table`;
CREATE TABLE `lock_table` (
  `table_name` varchar(50) NOT NULL,
  `table_max` bigint DEFAULT NULL,
  PRIMARY KEY (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='锁表';

-- 26. 账户表
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account` (
  `aco_id` bigint NOT NULL AUTO_INCREMENT,
  `aco_type` varchar(50) DEFAULT NULL,
  `aco_name` varchar(100) DEFAULT NULL,
  `aco_bank_num` varchar(50) DEFAULT NULL,
  `aco_bank` varchar(100) DEFAULT NULL,
  `aco_bank_name` varchar(50) DEFAULT NULL,
  `aco_cre_date` datetime DEFAULT NULL,
  `aco_org_mon` decimal(18,2) DEFAULT NULL,
  `aco_cur_mon` decimal(18,2) DEFAULT NULL,
  `aco_remark` longtext,
  `aco_inp_user` varchar(50) DEFAULT NULL,
  `aco_inp_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `aco_alt_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `aco_alt_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`aco_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='账户表';

-- 27. 账户流水表
DROP TABLE IF EXISTS `acc_line`;
CREATE TABLE `acc_line` (
  `acl_id` bigint NOT NULL AUTO_INCREMENT,
  `acl_aco_id` bigint DEFAULT NULL,
  `acl_type` varchar(100) DEFAULT NULL,
  `acl_note_id` varchar(300) DEFAULT NULL,
  `acl_mon` decimal(18,2) DEFAULT NULL,
  `acl_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `acl_remark` longtext,
  `acl_inp_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`acl_id`),
  KEY `idx_acl_aco_id` (`acl_aco_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='账户流水表';

-- 28. 账户交易表
DROP TABLE IF EXISTS `acc_trans`;
CREATE TABLE `acc_trans` (
  `atr_id` bigint NOT NULL AUTO_INCREMENT,
  `atr_code` varchar(50) DEFAULT NULL,
  `atr_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `atr_mon` decimal(18,2) DEFAULT NULL,
  `atr_type_id` bigint DEFAULT NULL,
  `atr_from_aco_id` bigint DEFAULT NULL,
  `atr_to_aco_id` bigint DEFAULT NULL,
  `atr_remark` longtext,
  `atr_inp_user` varchar(50) DEFAULT NULL,
  `atr_inp_date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`atr_id`),
  KEY `idx_atr_from_aco_id` (`atr_from_aco_id`),
  KEY `idx_atr_to_aco_id` (`atr_to_aco_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='账户交易表';

-- 29. 省份表
DROP TABLE IF EXISTS `cus_province`;
CREATE TABLE `cus_province` (
  `prv_id` bigint NOT NULL AUTO_INCREMENT,
  `prv_area_id` bigint DEFAULT NULL,
  `prv_name` varchar(100) DEFAULT NULL,
  `prv_isenabled` varchar(10) DEFAULT '1',
  PRIMARY KEY (`prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='省份表';

-- 30. 城市表
DROP TABLE IF EXISTS `cus_city`;
CREATE TABLE `cus_city` (
  `city_id` bigint NOT NULL AUTO_INCREMENT,
  `city_prv_id` bigint DEFAULT NULL,
  `city_name` varchar(100) DEFAULT NULL,
  `city_isenabled` varchar(10) DEFAULT '1',
  PRIMARY KEY (`city_id`),
  KEY `idx_city_prv_id` (`city_prv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='城市表';

-- 31. 地区表
DROP TABLE IF EXISTS `cus_area`;
CREATE TABLE `cus_area` (
  `are_id` bigint NOT NULL AUTO_INCREMENT,
  `are_name` varchar(100) DEFAULT NULL,
  `are_isenabled` varchar(10) DEFAULT '1',
  PRIMARY KEY (`are_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='地区表';

-- 32. 用户日志表
DROP TABLE IF EXISTS `user_log`;
CREATE TABLE `user_log` (
  `ulg_id` bigint NOT NULL AUTO_INCREMENT,
  `ulg_type` varchar(50) DEFAULT NULL,
  `ulg_oper` varchar(50) DEFAULT NULL,
  `ulg_op_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `ulg_op_content` longtext,
  `ulg_user_code` varchar(50) DEFAULT NULL,
  `ulg_ip` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ulg_id`),
  KEY `idx_ulg_user_code` (`ulg_user_code`),
  KEY `idx_ulg_op_time` (`ulg_op_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户日志表';

-- 33. 评论表
DROP TABLE IF EXISTS `comment`;
CREATE TABLE `comment` (
  `cot_id` bigint NOT NULL AUTO_INCREMENT,
  `cot_content` longtext,
  `cot_cas_id` bigint DEFAULT NULL,
  `cot_user` varchar(25) DEFAULT NULL,
  `cot_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `cot_isdel` char(1) DEFAULT '0',
  PRIMARY KEY (`cot_id`),
  KEY `idx_cot_cas_id` (`cot_cas_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='评论表';

-- ========================================
-- 创建索引优化查询性能
-- ========================================

-- 核心业务表索引
CREATE INDEX idx_sal_emp_name ON `sal_emp` (`se_name`);
CREATE INDEX idx_sal_emp_user_code ON `sal_emp` (`se_user_code`);
CREATE INDEX idx_sal_emp_enabled ON `sal_emp` (`se_isenabled`);

CREATE INDEX idx_cus_cor_cus_user ON `cus_cor_cus` (`cor_user_code`);
CREATE INDEX idx_cus_cor_cus_name ON `cus_cor_cus` (`cor_name`);
CREATE INDEX idx_cus_cor_cus_delete ON `cus_cor_cus` (`cor_isdelete`);

CREATE INDEX idx_lim_user_enabled ON `lim_user` (`user_isenabled`);
CREATE INDEX idx_lim_user_login ON `lim_user` (`user_loginName`);

CREATE INDEX idx_bank_case_state ON `bank_case` (`cas_state`);
CREATE INDEX idx_bank_case_name ON `bank_case` (`cas_name`);
CREATE INDEX idx_bank_case_phone ON `bank_case` (`cas_phone`);
CREATE INDEX idx_bank_case_assign_time ON `bank_case` (`cas_last_assign_time`);

CREATE INDEX idx_pho_red_cas_id ON `pho_red` (`pr_cas_id`);
CREATE INDEX idx_pho_red_se_no ON `pho_red` (`pr_se_no`);
CREATE INDEX idx_pho_red_time ON `pho_red` (`pr_time`);

CREATE INDEX idx_case_paid_cas_id ON `case_paid` (`pa_cas_id`);
CREATE INDEX idx_case_paid_se_no ON `case_paid` (`pa_se_no`);
CREATE INDEX idx_case_paid_date ON `case_paid` (`pa_paid_date`);

CREATE INDEX idx_project_user_code ON `project` (`pro_user_code`);
CREATE INDEX idx_project_state ON `project` (`pro_state`);
CREATE INDEX idx_project_delete ON `project` (`pro_isdel`);

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 插入基础数据
-- ========================================

-- 插入系统默认类型数据
INSERT INTO `type_list` (`typ_name`, `typ_desc`, `typ_type`, `typ_isenabled`) VALUES
('系统管理员', '系统管理员角色', 'user_role', '1'),
('普通用户', '普通用户角色', 'user_role', '1'),
('催收员', '催收员角色', 'user_role', '1'),
('主管', '主管角色', 'user_role', '1'),
('电话催收', '电话催收类型', 'contact_type', '1'),
('短信催收', '短信催收类型', 'contact_type', '1'),
('上门催收', '上门催收类型', 'contact_type', '1'),
('法律催收', '法律催收类型', 'contact_type', '1'),
('正常', '案件正常状态', 'case_state', '1'),
('逾期', '案件逾期状态', 'case_state', '1'),
('结清', '案件结清状态', 'case_state', '1'),
('核销', '案件核销状态', 'case_state', '1');

-- 插入默认管理员用户
INSERT INTO `lim_user` (`user_code`, `user_loginName`, `user_pwd`, `user_se_name`, `user_isenabled`, `user_role_id`) VALUES
('admin', 'admin', '123456', '系统管理员', '1', 1);

-- 插入锁表初始数据
INSERT INTO `lock_table` (`table_name`, `table_max`) VALUES
('bank_case', 0),
('pho_red', 0),
('case_paid', 0),
('sal_emp', 0),
('cus_cor_cus', 0),
('project', 0);

-- 数据库创建完成
SELECT 'CCDS完整数据库创建完成 - 包含33个核心表，确保所有功能正常运行' as message;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ccds';
