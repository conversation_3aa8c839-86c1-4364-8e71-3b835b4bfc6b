/*标准版详情*/
function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','C_DT'],
		['ID_NO','C_CODE','STATE','BACK_DT'],
		['AMT','RMB','GB','MY'],
		['PAID','M_CAT','AGE','DAYS'],
		['CL_AREA','EMP_NAME','LAST_CL','PR_COUNT'],
		['PRI','LEFT_PRI','OV_INT','OV_P'],
		['MOB','PHO','W_PHO','HOM'],
		['COM','W_ADDR','REG','M_ADDR'],
		['PTP','CP','COUNT','MIN_P'],
		['LAST_M','TIPS_DT','DEAD_L','TREMARK']
	);
}
function getLayout2(){
	return  new Array(
		['AREA','ASS_TM',['ASS_HIS',3]],
		['H_POST','W_POST','M_POST','EMAIL'],
		['ACC','F_BANK','HOST','ALT_HOLD'],
		['NOOUT','BILL_DT','CYCLE','P_CRLINE'],
		['START_DT','STOP_DT','LAST_P_DT','LAST_P'],
		['LAST_C_DT','LAST_R_DT','OV_DT','G_AMT'],
		['APP_NO','C_L','LOAN_T','DELAY_LV'],
		['BACK_AMT','P_L','P_COUNT','CL_T'],
		['FILE_NO','LOAN_DT','POS','PART'],
		['BIR','SC_PC_NO',['SC_NO',3]],
		[['PROD',3],['BIZ',3]],
		['C1_NAME','C1_ID_NO','C1_HM_PHO','C1_W_PHO'],
		['C1_MOB','C1_COM',['C1_ADR',3]],
		['C2_NAME','C2_ID_NO','C2_HM_PHO','C2_W_PHO'],
		['C2_MOB','C2_COM',['C2_ADR',3]],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		[['O_REC',3],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}