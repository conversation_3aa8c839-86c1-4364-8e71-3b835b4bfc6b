<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.CaseHp" table="case_hp" schema="dbo" >
        <id name="chId" type="java.lang.Long">
            <column name="ch_id" />
            <generator class="identity" />
        </id>
         <many-to-one name="bankCase" class="com.frsoft.ccds.entity.BankCase" fetch="select" not-null="false">
        	<column name="ch_cas_id" />
        </many-to-one>

        <property name="chChkState" type="java.lang.Integer">
            <column name="ch_chk_state" />
        </property>
        <property name="chTyp" type="java.lang.Integer">
            <column name="ch_typ"  />
        </property>
        <property name="chText" type="java.lang.String">
            <column name="ch_text" length="**********" />
        </property>
        <property name="chCat1" type="java.lang.String">
            <column name="ch_cat_1" length="20" />
        </property>
        <property name="chCat2" type="java.lang.String">
            <column name="ch_cat_2" length="20" />
        </property>
         <many-to-one name="chAddress" class="com.frsoft.ccds.entity.Address" fetch="select" not-null="false">
        	<column name="ch_adr_id" />
        </many-to-one>
        <property name="chMsgState" type="java.lang.Integer">
            <column name="ch_msg_state" />
        </property>

        <property name="chRes" type="java.lang.String">
            <column name="ch_res" length="**********" />
        </property>
        <property name="chAppUser" type="java.lang.String">
            <column name="ch_app_user" length="20" />
        </property>
        <property name="chSurUser" type="java.lang.String">
            <column name="ch_sur_user" length="20" />
        </property>
        <property name="chAppTime" type="java.util.Date">
            <column name="ch_app_time" length="23" />
        </property>
        <property name="chSurTime" type="java.util.Date">
            <column name="ch_sur_time" length="23" />
        </property>
        <property name="chRemark" type="java.lang.String">
            <column name="ch_remark" length="**********" />
        </property>
        <property name="chContUser" type="java.lang.String">
            <column name="ch_cont_user" length="20" />
        </property>
        <property name="chAdr" type="java.lang.String">
            <column name="ch_adr" length="1000" />
        </property>
        <property name="chCount" type="java.lang.Integer">
            <column name="ch_count" />
        </property>
         <property name="chUpdMan" type="java.lang.String">
            <column name="ch_upd_man" length="25" />
        </property>
        <property name="chUpdTime" type="java.util.Date">
            <column name="ch_upd_time" length="23" />
        </property>
    </class>
</hibernate-mapping>
