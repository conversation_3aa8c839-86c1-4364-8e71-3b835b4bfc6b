 <%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>地址操作历史</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/cus.css"/>
	
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/case.js"></script>
    <script type="text/javascript">
    	
    	function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			var hurCat = convHurCat(obj.hurCat);
			datas = [obj.hurOpTime, hurCat, obj.hurOpCont, obj.hurOper ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars = [];
			pars.op = "showAddHis";
			pars.adrId="${adrId}";
			
			var loadFunc = "loadList";
			var cols=[
				{name:"操作时间",renderer:"time",width:'14%'},
				{name:"分类",width:'10%'},
				{name:"操作内容",align:"left",width:'64%'},
				{name:"操作人",width:'12%'}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("addHisTab","dataList");
		gridEl.config.isShort=false;
		gridEl.config.isResize=false;
    	gridEl.config.sortable=false;
		createProgressBar();
   		window.onload=function(){
			loadList();
		}
    </script>
</head>
  
  <body>
  	<div id="dataList" class="dataList"></div>
  </body>
</html>