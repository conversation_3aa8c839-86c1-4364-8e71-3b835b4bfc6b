<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.UserArea" table="user_area" schema="dbo" >
        <id name="uarId" type="java.lang.Long">
            <column name="uar_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="uarUser" class="com.frsoft.base.entity.LimUser">
            <column name="uar_user_code" length="50" />
        </many-to-one>
        <many-to-one name="uarArea" class="com.frsoft.base.entity.TypeList">
            <column name="uar_area_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
