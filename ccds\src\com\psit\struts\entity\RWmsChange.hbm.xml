<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RWmsChange" table="r_wms_change" schema="dbo" >
        <id name="rwcId" type="java.lang.Long">
            <column name="rwc_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false"> 
            <column name="rwc_pro_id"/>
        </many-to-one>
        <many-to-one name="wmsCheck" class="com.psit.struts.entity.WmsCheck" fetch="select" not-null="false">
            <column name="rwc_wmc_code" />
        </many-to-one>
        <property name="rwcDifferent" type="java.lang.Double">
            <column name="rwc_different" />
        </property>
        <property name="rmcType" type="java.lang.String">
            <column name="rmc_type" length="50" />
        </property>
        <property name="rmcRemark" type="java.lang.String">
            <column name="rmc_remark" length="1073741823" />
        </property>
        <property name="rmcWmsCount" type="java.lang.Double">
            <column name="rmc_wms_count" />
        </property>
        <property name="rmcRealNum" type="java.lang.Double">
            <column name="rmc_real_num" />
        </property>
    </class>
</hibernate-mapping>
