#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终清理脚本 - 清理转换后的MySQL文件中的MSSQL残留语法
"""

import re
import sys

def final_cleanup(sql_content):
    """最终清理MySQL SQL文件"""
    
    print("开始最终清理...")
    
    # 1. 移除所有IF NOT EXISTS和BEGIN/END块
    sql_content = re.sub(r'IF\s+NOT\s+EXISTS[^;]*;', '', sql_content, flags=re.IGNORECASE | re.DOTALL)
    sql_content = re.sub(r'\bBEGIN\b', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'\bEND\b', '', sql_content, flags=re.IGNORECASE)
    
    # 2. 修复数据类型 - 移除反引号包围的数据类型
    type_fixes = [
        (r'`bigint`\s+IDENTITY\(1,1\)\s+NOT\s+NULL', 'bigint NOT NULL AUTO_INCREMENT'),
        (r'`int`\s+IDENTITY\(1,1\)\s+NOT\s+NULL', 'int NOT NULL AUTO_INCREMENT'),
        (r'`bigint`', 'bigint'),
        (r'`int`', 'int'),
        (r'`varchar`\((\d+)\)', r'varchar(\1)'),
        (r'`nvarchar`\((\d+)\)', r'varchar(\1)'),
        (r'`nvarchar`\(max\)', 'longtext'),
        (r'`varchar`\(max\)', 'longtext'),
        (r'`char`\((\d+)\)', r'char(\1)'),
        (r'`nchar`\((\d+)\)', r'char(\1)'),
        (r'`text`', 'text'),
        (r'`ntext`', 'longtext'),
        (r'`datetime`', 'datetime'),
        (r'`date`', 'date'),
        (r'`time`', 'time'),
        (r'`decimal`\((\d+),\s*(\d+)\)', r'decimal(\1,\2)'),
        (r'`numeric`\((\d+),\s*(\d+)\)', r'decimal(\1,\2)'),
        (r'`money`', 'decimal(19,4)'),
        (r'`smallmoney`', 'decimal(10,4)'),
        (r'`float`', 'double'),
        (r'`real`', 'float'),
        (r'`bit`', 'tinyint(1)'),
        (r'`image`', 'longblob'),
        (r'`uniqueidentifier`', 'varchar(36)'),
        (r'`timestamp`', 'timestamp')
    ]
    
    for pattern, replacement in type_fixes:
        sql_content = re.sub(pattern, replacement, sql_content, flags=re.IGNORECASE)
    
    # 3. 修复索引语句
    sql_content = re.sub(
        r'IF\s+NOT\s+EXISTS[^;]*CREATE\s+INDEX\s+`([^`]+)`\s+ON\s+`([^`]+)`\s*\(\s*`([^`]+)`\s*\)[^;]*;',
        r'CREATE INDEX `\1` ON `\2` (`\3`);',
        sql_content, flags=re.IGNORECASE | re.DOTALL
    )
    
    # 4. 移除WITH子句
    sql_content = re.sub(r'WITH\s*\([^)]+\)', '', sql_content, flags=re.IGNORECASE)
    
    # 5. 清理多余的分号和空行
    sql_content = re.sub(r';\s*;\s*;', ';', sql_content)
    sql_content = re.sub(r'\n\s*;\s*\n', '\n', sql_content)
    sql_content = re.sub(r'\n\s*\n\s*\n', '\n\n', sql_content)
    
    # 6. 确保每个CREATE TABLE都有正确的结尾
    def fix_create_table(match):
        table_content = match.group(0)
        table_name_match = re.search(r'CREATE\s+TABLE\s+`(\w+)`', table_content, re.IGNORECASE)
        if table_name_match:
            table_name = table_name_match.group(1)
            
            # 确保有PRIMARY KEY
            if 'PRIMARY KEY' not in table_content.upper():
                # 查找第一个AUTO_INCREMENT字段作为主键
                auto_inc_match = re.search(r'`(\w+)`\s+\w+\s+NOT\s+NULL\s+AUTO_INCREMENT', table_content, re.IGNORECASE)
                if auto_inc_match:
                    pk_field = auto_inc_match.group(1)
                    table_content = table_content.rstrip(')') + f',\nPRIMARY KEY (`{pk_field}`)\n)'
            
            # 确保有ENGINE和字符集
            if 'ENGINE=' not in table_content.upper():
                table_content = table_content.rstrip(';') + ' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;'
            
            return f"DROP TABLE IF EXISTS `{table_name}`;\n{table_content}"
        
        return table_content
    
    # 匹配CREATE TABLE语句
    pattern = r'CREATE\s+TABLE\s+`\w+`\s*\([^;]+\);?'
    sql_content = re.sub(pattern, fix_create_table, sql_content, flags=re.IGNORECASE | re.DOTALL)
    
    # 7. 移除重复的use语句
    sql_content = re.sub(r'use\s+ccds\s*;', '', sql_content, flags=re.IGNORECASE)
    
    # 8. 最终清理
    sql_content = re.sub(r'\n\s*\n\s*\n', '\n\n', sql_content)
    
    return sql_content

def process_file(input_file, output_file):
    """处理单个文件"""
    try:
        print(f"正在处理文件: {input_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        cleaned_content = final_cleanup(content)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print(f"清理完成: {output_file}")
        return True
        
    except Exception as e:
        print(f"处理失败 {input_file}: {e}")
        return False

def main():
    """主函数"""
    files_to_clean = [
        ("mysql8_CCDS_structure.sql", "mysql8_CCDS_structure_clean.sql"),
        ("mysql8_CCDS_data.sql", "mysql8_CCDS_data_clean.sql"),
        ("mysql8_CCDS_cashbus.sql", "mysql8_CCDS_cashbus_clean.sql")
    ]
    
    success_count = 0
    
    for input_name, output_name in files_to_clean:
        if process_file(input_name, output_name):
            success_count += 1
    
    print(f"\n清理完成! 成功: {success_count}/{len(files_to_clean)}")

if __name__ == "__main__":
    main()
