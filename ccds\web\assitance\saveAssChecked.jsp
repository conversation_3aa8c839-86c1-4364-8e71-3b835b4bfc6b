<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>    
    <title>添加协催记录</title>
    <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/common.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	<script type="text/javascript" src="js/caseAss.js"></script>
	<script type="text/javascript" src="js/config.js"></script>
	
    <script type="text/javascript">  
		function initForm(){
			//loadAssTypeOfSave('chTyp','${CUS_VER_ID}');
			loadAssType('chTyp',"${assTypeNames}", "${assTypeValues}");
		} 
		function check(){
			var errStr = "";
		 	if(isEmpty("assRes")){
				errStr+="未填写协催内容！\n";
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}else{
			    waitSubmit("save");
				waitSubmit("doCancel");
				return $("saveForm").submit();
			}				  
		}
		
		window.onload=function(){
			initForm();
		}
	
  </script> 
</head>
  <body>
  <div class="inputDiv">
  	<form action="assitanceAction.do" method="post" id="saveForm">
  	  	<input type="hidden" name="op" value="saveAssChecked">
  	  	<input type="hidden" name="caseId" value="${caseId}">
  	  	<table class="dashTab inputForm" cellpadding="0" cellspacing="0">
			<tbody>
            	<tr>
                    <th>协催类型：</th>
                    <td><select class="inputSize2" id="chTyp" name="caseHp.chTyp"></select></td>
                    <th>协催人：</th>
                    <td><input class="inputSize2" name="caseHp.chSurUser" onBlur="autoShort(this,20)" value="${CUR_USER.userSeName}" /></td>
                </tr>
            	<tr class="noBorderBot">
                    <th class="required">协催内容：<span class="red">*</span></th>
                    <td colspan="3"><textarea class="inputSize2L" rows="8" name="caseHp.chRes" id="assRes" onBlur="autoShort(this,4000)"></textarea></td>
                </tr>
                <tr class="submitTr">
                    <td colspan="4">
                    <input type="button" class="butSize1" id="save" value="保存" onClick="check()">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"></td>
                </tr>	
            </tbody>
					
	  </table>
	</form>
  </div>
  </body>
</html>
