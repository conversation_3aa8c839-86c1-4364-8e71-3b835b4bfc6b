package com.frsoft.ccds.common.core.domain.model;

import lombok.Data;
import java.io.Serializable;
import java.util.Set;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */
@Data
public class LoginUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 用户唯一标识 */
    private String token;

    /** 登录时间 */
    private Long loginTime;

    /** 过期时间 */
    private Long expireTime;

    /** 登录IP地址 */
    private String ipaddr;

    /** 登录地点 */
    private String loginLocation;

    /** 浏览器类型 */
    private String browser;

    /** 操作系统 */
    private String os;

    /** 权限列表 */
    private Set<String> permissions;

    /** 用户编码 */
    private String userCode;

    /** 用户名 */
    private String username;

    /** 密码 */
    private String password;

    /** 状态 0正常 1停用 */
    private String status;

    public LoginUser() {
    }

    public LoginUser(String userCode, String username, String password, Set<String> permissions) {
        this.userCode = userCode;
        this.username = username;
        this.password = password;
        this.permissions = permissions;
    }
}