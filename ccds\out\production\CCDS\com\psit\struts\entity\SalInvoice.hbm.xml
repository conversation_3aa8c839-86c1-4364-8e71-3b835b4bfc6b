<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.SalInvoice" table="sal_invoice" schema="dbo" >
        <id name="sinId" type="java.lang.Long">
            <column name="sin_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salOrdCon" class="com.frsoft.cis.entity.SalOrdCon" fetch="select" not-null="false">
            <column name="sin_ord_code" length="50" />
        </many-to-one>
        <many-to-one name="salInvType" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="sin_type" />
        </many-to-one>
        <property name="sinCon" type="java.lang.String">
            <column name="sin_con" length="1073741823" />
        </property>
        <property name="sinMon" type="java.lang.Double">
            <column name="sin_mon" precision="18" />
        </property>
        <property name="sinMonType" type="java.lang.String">
            <column name="sin_mon_type" precision="50" />
        </property>
        <property name="sinDate" type="java.util.Date">
            <column name="sin_date" length="23" />
        </property>
        <property name="sinRemark" type="java.lang.String">
            <column name="sin_remark" length="1073741823" />
        </property>
        <property name="sinCode" type="java.lang.String">
            <column name="sin_code" length="100" />
        </property>
        <property name="sinIsPaid" type="java.lang.String">
            <column name="sin_isPaid" length="1" />
        </property>
        <property name="sinIsPlaned" type="java.lang.String">
            <column name="sin_isPlaned" length="50" />
        </property>
        <property name="sinResp" type="java.lang.String">
            <column name="sin_resp" length="50" />
        </property>
        <property name="sinUserCode" type="java.lang.String">
            <column name="sin_user_code" length="50" />
        </property>
        <property name="sinAltUser" type="java.lang.String">
            <column name="sin_alt_user" length="50" />
        </property>
        <property name="sinCreDate" type="java.util.Date">
            <column name="sin_cre_date" length="23" />
        </property>
        <property name="sinAltDate" type="java.util.Date">
            <column name="sin_alt_date" length="23" />
        </property>
        <property name="sinIsdel" type="java.lang.String">
            <column name="sin_isdel" length="1" />
        </property>
        <property name="sinIsrecieve" type="java.lang.String">
            <column name="sin_isrecieve" length="1" />
        </property>
        <many-to-one name="salPurOrd" class="com.psit.struts.entity.SalPurOrd" fetch="select" not-null="false">
        	<column name="sin_spo_id"/>
        </many-to-one>
    </class>
</hibernate-mapping>
