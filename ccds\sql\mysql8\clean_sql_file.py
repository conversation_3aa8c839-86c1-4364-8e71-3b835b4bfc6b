#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理SQL文件，移除存储过程和其他不兼容的语句
"""

import re

def clean_sql_file(input_file, output_file):
    """清理SQL文件"""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("开始清理SQL文件...")
    
    # 1. 移除存储过程
    content = re.sub(r'EXEC dbo\.sp_executesql.*?;', '', content, flags=re.DOTALL)
    content = re.sub(r'/\*\*\*\*\*\* 对象:\s*StoredProcedure.*?\*\*\*\*\*\*/', '', content, flags=re.DOTALL)
    
    # 2. 移除其他MSSQL特有的注释
    content = re.sub(r'/\*\*\*\*\*\* 对象:.*?\*\*\*\*\*\*/', '', content, flags=re.DOTALL)
    
    # 3. 清理多余的分号和空行
    content = re.sub(r';\s*;\s*', ';\n', content)
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 4. 修复语法错误
    content = re.sub(r'`cas_area_1`varchar', '`cas_area_1` varchar', content)
    content = re.sub(r'`cas_area_2`varchar', '`cas_area_2` varchar', content)
    content = re.sub(r'`cas_area_3`varchar', '`cas_area_3` varchar', content)
    
    # 5. 确保每个CREATE TABLE都有ENGINE
    def add_engine(match):
        table_sql = match.group(0)
        if 'ENGINE=' not in table_sql:
            table_sql = table_sql.rstrip(';') + ' ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;'
        return table_sql
    
    content = re.sub(r'CREATE TABLE `\w+`\s*\([^;]+\);?', add_engine, content, flags=re.DOTALL)
    
    # 6. 移除空的CREATE TABLE语句
    content = re.sub(r'DROP TABLE IF EXISTS `\w+`;\s*CREATE TABLE `\w+`\s*\(\s*\)\s*ENGINE[^;]*;', '', content)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"清理完成: {output_file}")

def main():
    """主函数"""
    clean_sql_file("mysql57_ALL_106_TABLES_FINAL.sql", "mysql57_ALL_106_TABLES_CLEAN.sql")

if __name__ == "__main__":
    main()
