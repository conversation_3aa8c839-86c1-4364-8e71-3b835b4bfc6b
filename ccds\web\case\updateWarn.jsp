<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>    
    <title>更新警告</title>
    <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	 <script type="text/javascript" src="js/common.js"></script>
	 <script type="text/javascript">
    function check(){
		var errStr = "";
		if(isEmpty("warn")&&checkLength("warn",300)){
			errStr+="- 警告内容不能超过300个字！\n";
		}
        if(errStr!=""){
			errStr+="\n请返回修改...";
			alert(errStr);
			return false;
		}
		else{
			waitSubmit("save","保存中...");
			waitSubmit("doCancel");
			return $("warnForm").submit();
		}
	}

	</script>
</head>
  <body>
  <div class="inputDiv">
  	<form action="caseAction.do" method="post" id="warnForm">
  	<input type="hidden" name="op" value="saveWarn">
  	 <input type="hidden" name="casId"  value="${casId}"/>
		<table class="dashTab inputForm" cellpadding="0" cellspacing="0">
			<tbody>
                <tr class="noBorderBot">
                    <th>警告内容：</th>
                    <td colspan="3"><textarea class="inputSize2L" rows="10" id="warn" name="warn" onBlur="autoShort(this,300)">${warn}</textarea></td>
                </tr>
                <tr class="submitTr">
                    <td colspan="4">
                    <input type="button" class="butSize1" id="save" value="保存" onClick="check()">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"></td>
                </tr>	
            </tbody>					
	  </table>
	</form>
  </div>
  </body>
</html>
