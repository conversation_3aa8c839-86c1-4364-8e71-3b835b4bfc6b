<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-bean"  prefix="bean"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<base href="<%=basePath%>"></base>

		<title>协催管理</title>
		<link rel="shortcut icon" href="favicon.ico" />
		<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
        <meta http-equiv="pragma" content="no-cache">
        <meta http-equiv="cache-control" content="no-cache">
        <meta http-equiv="expires" content="0">
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<style type="text/css">
		.inputSize2 {
			width:100px;
		}
        #assTypeCount {
			margin:0 0 10px 0;
			padding:10px;
			text-align:left;
			border:#f3ae4c 1px solid;
			background:#fdfaca; 
		}
		#assTypeCountTil {
			color: #b36810;
			font-size:14px;
			font-weight:bold;
		}
		#assTypeCountTxt {
			padding:5px;
			padding:5px 5px 5px 10px;
			font-size:14px;
		}
		#assTypeCountTxt span {
			margin:0 10px 0 0;
		}
        </style>
		<script type="text/javascript" src="js/prototype.js"></script>
		<script type="text/javascript" src="js/common.js"></script>
		<script type="text/javascript" src="js/formCheck.js"></script>
		<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
		<script type="text/javascript" src="js/config.js"></script>
		<script type="text/javascript" src="js/caseAss.js"></script>
		<script language="javaScript" type="text/javascript">
		function toBatchAssRes(){
			if(checkBoxIsEmpty("priKey")){
				var assIds = getBacthIds("-",false)[0];
				if(assIds!=''){
					openPostWindow("assitanceAction.do",[["op","toBatchAssRes"],["assIds",assIds.substring(0,assIds.length-1)],["assType",$("assType").value]],"conAssWin");
				}
			}
		}
		
		function toSendMsg(chId){
			if(chId!=""){
				assPopDiv('SMS',chId);
			}
			else{
				alert("发送号码为空！");
				return;
			}
		}
	
		/*function outCaseAss(){
			var args = ["${state}",$("assType").value, $("bankName").value,
					$("caseName").value, $("startTime").value, $("endTime").value,
					$("caseCode").value, $("batCode").value, $("caseNum").value, 
					$("caseDate1").value,$("caseDate2").value ];
			assPopDiv(5, args);
		}*/
		
		/*function loadFilter(){
			setCurItemStyle($("assType").value,["2","3","4","5","6","7","8","9","11","12","13","14"]);
		}*/
		
		//列表筛选按钮链接
		function filterList(assType){
			/*$("assType").value=assType;
			$("toolsBarTop").show();
			$("listSearch").show();
			$("dataList").show();
			$("unDoList").hide();
			$("out").show();
			gridEl = new MGrid("listAssTab${state}"+assType,"dataList");
    		gridEl.config.hasCheckBox = true;*/
			$("assType").value=assType;
			gridEl = new MGrid("listAssTab${state}"+assType,"dataList");
			gridEl.config.hasCheckBox = true;
			loadList();
		}
		
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			var caseClass="", casCode="", casName="",casM="",casPaidM="",caseClState="", casNum = "";
			if(obj.bankCase){
				dataId = obj.chId+"-"+obj.bankCase.casId+"-"+obj.bankCase.casState;
				var relFunc = "descPop('caseAction.do?op=caseDesc&caseId="+obj.bankCase.casId+"&view=ass')";
				caseClass = getCaseColor(obj.bankCase);
				casCode = "<a href=\"javascript:void(0)\" "+ ((caseClass!="")?("class=\""+caseClass+"\""):"") +" onclick=\""+relFunc+";return false;\">"+obj.bankCase.casCode+"&nbsp;</a>";
				casName = obj.bankCase.casName;
				casM = obj.bankCase.casM;
				casPaidM = obj.bankCase.casPaidM;
				caseClState = obj.bankCase.typeList?obj.bankCase.typeList.typName:"新案";
				casNum = obj.bankCase.casNum;
			}
			var funcCol = "";
			if($("assType").value=="13"&&"${state}"!="-2"&&"${HAS_SMS}"=="1"&&"${SMS_NEED_AUD}"=="1"){
				funcCol = "<image class='hand' alt='发短信' src='images/content/email_go.png' onclick=\"toSendMsg('"+obj.chId+"')\"/>&nbsp;&nbsp;&nbsp;"
			}
			if("${state}"=="0"){
				funcCol += "<img src=\"images/content/execute.gif\" class=\"hand\" alt=\"完成协催\"onClick=\"assPopDiv(1,['"+obj.chId+"','"+obj.chTyp+"']);return false;\"/>";
			}
			else if("${state}"=="-2"){
				funcCol += "<img onClick=\"assPopDiv('UPD_ASS','"+obj.chId+"')\" class=\"hand\" src=\"images/content/edit.gif\" alt=\"编辑\"/>&nbsp;&nbsp;&nbsp;<img src=\"images/content/approve.gif\" class=\"hand\" alt=\"同意协催申请\"onClick=\"audAss('"+obj.chId+"');return false;\"/>";
			}
			/*else {
				funcCol = "<img src=\"images/content/execute.gif\" class=\"hand\" alt=\"确认核查\"onClick=\"assPopDiv(1,['"+obj.chId+"','"+obj.chTyp+"']);return false;\"/>";
			}*/
			switch($("assType").value){
				case '2': datas = [obj.chId,caseClState, casCode, casNum, casName, casM, casPaidM, obj.chAdr!=''?obj.chAdr:(obj.chAddress?obj.chAddress.adrAdd:""), obj.chText,obj.chAppTime, obj.chAppUser, obj.chSurTime, obj.chSurUser, obj.chRes, funcCol]; break;	
				case '13':
					datas = [obj.chId,caseClState, casCode, casNum, casName, casM, casPaidM, obj.chAdr, obj.chText,obj.chAppTime, obj.chAppUser, obj.chSurTime, obj.chSurUser, (obj.chMsgState=='1'?"<span class='deepGreen'>[已发送]</span>":"")+obj.chRes, funcCol]; break;	
				default:
					datas = [obj.chId,caseClState, casCode, casNum, casName, casM, casPaidM, obj.chText,obj.chAppTime, obj.chAppUser, obj.chSurTime, obj.chSurUser, obj.chRes, funcCol]; break;	
			}
			return [datas,className,dblFunc,dataId];
		}
		function getFormArgs(){
			var pars;
			pars = $("searchForm").serialize(true);
			pars.op = "listCaseAss";
			pars.state="${state}";
			return pars;
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "assitanceAction.do";
			var pars = getFormArgs();
			var loadFunc = "loadList";
			var cols;
			switch($("assType").value){
				case '2':
						cols = [
								{name:"ID"},
								{name:"催收状态"},
								{name:"个案序列号",align:"left"},
								{name:"证件号"},
								{name:getCasNameTxt("${CUS_VER_ID}")},
								{name:"委案金额",align:"right",renderer:"money"},
								{name:"还款金额",align:"right",renderer:"money"},
								{name:"地址",align:"left"},
								{name:"申请内容"},
								{name:"申请时间",renderer:"time"},
								{name:"申请人"},
								{name:"协催时间",renderer:"time"},
								{name:"协催人"},
								{name:"协催内容"},
								{name:"操作",isSort:false}
								];
						break;
				case '13':
						cols = [
								{name:"ID"},
								{name:"催收状态"},
								{name:"个案序列号",align:"left"},
								{name:"证件号"},
								{name:getCasNameTxt("${CUS_VER_ID}")},
								{name:"委案金额",align:"right",renderer:"money"},
								{name:"还款金额",align:"right",renderer:"money"},
								{name:"发送号码"},
								{name:"短信内容"},
								{name:"申请时间",renderer:"time"},
								{name:"申请人"},
								{name:"协催时间",renderer:"time"},
								{name:"协催人"},
								{name:"协催内容"},
								{name:"操作",isSort:false}
								];
						break;
				default :
						cols = [
								{name:"ID"},
								{name:"催收状态"},
								{name:"个案序列号",align:"left"},
								{name:"证件号"},
								{name:getCasNameTxt("${CUS_VER_ID}")},
								{name:"委案金额",align:"right",renderer:"money"},
								{name:"还款金额",align:"right",renderer:"money"},
								{name:"申请内容"},
								{name:"申请时间",renderer:"time"},
								{name:"申请人"},
								{name:"协催时间",renderer:"time"},
								{name:"协催人"},
								{name:"协催内容"},
								{name:"操作",isSort:false}
								];
						break;
			}
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
			//loadFilter();
		}
    	var gridEl;
	    createProgressBar();
     	window.onload=function(){
			//loadAssType("assType","4","${CUS_VER_ID}");
			loadAssType("assType","${assTypeNames}", "${assTypeValues}","4");
			gridEl = new MGrid("listAssTab${state}"+$("assType").value,"dataList");
			gridEl.config.hasCheckBox = true;
			$("casNameTxt").innerHTML=getCasNameTxt("${CUS_VER_ID}");
			$("BankTxt").innerHTML=getBankTxt("${CUS_VER_ID}");
			loadCaseState('caseState');
			$("caseState").value="0";//默认正常状态
			

			loadList();
			//增加清空按钮
			createCancelButton(loadList,'searchForm',-50,5,'searButton','after');
			closeProgressBar();
		}

  </script>
</head>


  <body>
 <div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>协催管理 > <c:if test="${state==0||state==-2}">待处理协催</c:if><c:if test="${state==1}">协催记录</c:if> <span id="changeFuncBt" onMouseOver="popFuncMenu(['caseAss',<c:if test="${state==0||state==-2}">2</c:if><c:if test="${state==1}">3</c:if>,],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['caseAss',<c:if test="${state==0||state==-2}">2</c:if><c:if test="${state==1}">3</c:if>],true)" onMouseOut="popFuncMenu(['caseAss',<c:if test="${state==0||state==-2}">2</c:if><c:if test="${state==1}">3</c:if>],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
 			</div>
          		<table class="mainTab" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>
                            <div id="tabType">
                            	<c:if test="${state!=1}">
                                <div id="tabType1" onClick="self.location.href='assitanceAction.do?op=toListCaseAss&state=-2'">协催申请</div><div id="tabType2" onClick="self.location.href='assitanceAction.do?op=toListCaseAss&state=0'">待办协催</div>
                                <script type="text/javascript">loadListTab("${state}",["-2","0"]);</script>
                                </c:if>
                                <c:if test="${state==1}">
                                <div id="tabType1" class="tabTypeWhite" onClick="self.location.href='assitanceAction.do?op=toListCaseAss&state=${state}'">协催记录</div>
                                </c:if>
                            </div>
                        </th>
                        <td>
                            <a href="javascript:void(0)" onClick="assPopDiv(7);return false;" class="newBlueButton">导入协催记录</a>
                        </td>
                    </tr>
            	</table>
				<script type="text/javascript">loadTabTypeWidth();</script>
				<div id="listContent">
                    <c:if test="${state==-2||state==0}">
                    <div id="unDoList" >
                        <c:if test="${!empty assTypeList}">
                        <div id="assTypeCount">
                            <div id="assTypeCountTil">未处理的申请：</div>
                            <div id="assTypeCountTxt"></div>
                        </div>
                        <script type="text/javascript">
                            //生成未处理申请列表
                            var typNames = new Array('申请信函','核准地址','<bean:message bundle="CUSTOM_STR" key="ass.type3" />',
                            		'<bean:message bundle="CUSTOM_STR" key="ass.type4" />','<bean:message bundle="CUSTOM_STR" key="ass.type5" />',
                            		'<bean:message bundle="CUSTOM_STR" key="ass.type6" />','<bean:message bundle="CUSTOM_STR" key="ass.type7" />',
                            		'<bean:message bundle="CUSTOM_STR" key="ass.type8" />','<bean:message bundle="CUSTOM_STR" key="ass.type9" />',
                            		'<bean:message bundle="CUSTOM_STR" key="ass.type10" />','<bean:message bundle="CUSTOM_STR" key="ass.type11" />',
                            		'<bean:message bundle="CUSTOM_STR" key="ass.type12" />','<bean:message bundle="CUSTOM_STR" key="ass.type13" />',
                            		'<bean:message bundle="CUSTOM_STR" key="ass.type14" />'); 
                            function addAssCount(typIndex,assNum){
                                var typName = '';
                                for(var i=0; i<typNames.length; i++){
                                    if(typIndex==(i+1).toString()){
                                        typName = typNames[i];
                                    }
                                }
                                var spanEl = document.createElement('span');
                                var aEl = document.createElement('a');
                               // aEl.href = "assitanceAction.do?op=toListCaseAss&state=0&assType="+typIndex;
                                aEl.href="javascript:void(0)";
                                aEl.onclick=function(){ filterList(typIndex); };
                                aEl.innerHTML = typName + "&nbsp;(" + assNum + "条)";
                                spanEl.appendChild(aEl);
                                $('assTypeCountTxt').appendChild(spanEl);
                            }
                        </script>
                        <c:forEach items="${assTypeList}" var="atList">
                            <script type="text/javascript">addAssCount("${atList.assType}","${atList.assNum}");</script>
                        </c:forEach>
                        </c:if>
                 	</div>
                    </c:if>
                    <div class="listSearch" id="listSearch">
                        <form style="margin:0; padding:0;" id="searchForm" class="listSearchForm" onSubmit="loadList();return false;">
                            <table cellpadding="0" cellspacing="0">
                            <tr>
                            	<th>协催类型：</th>
                        		<td><select id="assType" name="assType" class="inputSize2"></select></td>
                                <th><span id="bankTxt"></span>：</th>
                                <td colspan="3"><input style="width:152px;" class="inputSize2 inputBoxAlign" type="text" id="bankName" name="bankName" onBlur="autoShort(this,100)"/>
                            <c:if test="${!empty bankList}">
                            <select class="inputSize2 inputBoxAlign" style="width:70px" onChange="setValueFromSel(this,'bankName')">
                                <option value="">请选择</option>
                                <c:forEach items="${bankList}" var="bList">
                                <option value="${bList.typName}">${bList.typName}</option>
                                </c:forEach>
                            </select>
                            </c:if>
                            <c:if test="${empty bankList}">
                                <select class="inputSize2 inputBoxAlign" disabled="disabled" style="width:70px">
                                    <option>未添加</option>
                                </select>
                            </c:if></td>
                                <th>批次号：</th>
                            	<td colspan="3"><input class="inputSize2" style="width:314px" type="text" id="batCodeStr" name="batCode" onDblClick="cleanBatCodeInput()"/><input type="text" style="display:none" id="batIds" name="batIds"/>&nbsp;<button class="butSize2" onClick="showBatList()" style="width:40px">选择</button>
                               <!-- <input style="width:100px;" class="inputSize2" type="text" id="batCode" name="batCode" onBlur="autoShort(this,100)"/>&nbsp;<button class="butSize2" onClick="addDivBrow(22)" style="width:40px">选择</button>--></td>
                           </tr>
                           <tr>
                            	<th>催收区域：</th>
                                <td>
                                <select name='clArea' class="inputSize2 inputBoxAlign">
                               <c:if test="${!empty userAreaList}">
                                    <option value="<c:forEach items="${userAreaList}" var="uArea">${uArea.uarArea.typId},</c:forEach>">全部</option><c:forEach items="${userAreaList}" var="userArea"><option value="${userArea.uarArea.typId}">${userArea.uarArea.typName}</option></c:forEach>
                                </c:if>
                                <c:if test="${empty userAreaList}">
                                    <c:if test="${!empty areaList}">
                                        <option value="">全部</option><c:forEach items="${areaList}" var="area"><option value="${area.typId}">${area.typName}</option></c:forEach>
                                    </c:if>
                                    <c:if test="${empty areaList}">
                                        <option value="">未添加</option>
                                    </c:if>
                                </c:if>
                                </select></td>
                            	<!--<th>部门：</th>
                        		<td><select id="org" name="org" class="inputSize2 inputBoxAlign"><c:if test="${!empty orgList}"><c:forEach items="${orgList}" var="oList"><option value="${oList.soCode}">${oList.soName}</option></c:forEach></c:if></select></td>-->
                                <th><span id="casNameTxt"></span>：</th>
                                <td><input class="inputSize2 inputBoxAlign" style="width:85px;" type="text" name="caseName" onBlur="autoShort(this,50)"/></td>
                                <th>证件号：</th>
                                <td><input class="inputSize2 inputBoxAlign" style="width:85px;" type="text" id="caseNum" name="caseNum" onBlur="autoShort(this,50)"/></td>
                                <th>个案序列号：</th>
                            	<td><input class="inputSize2 inputBoxAlign" type="text" name="caseCode" onBlur="autoShort(this,100)"/></td>
                            	<th>委案日期：</th>
                            	<td><input name="caseDate1" id="caseDate1" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({onpicked:function(){$('caseDate2').focus();},maxDate:'#F{$dp.$D(\'caseDate2\')}'})"/>&nbsp;到
                                <input name="caseDate2" id="caseDate2" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'caseDate1\')}'})"/></td>
                             </tr>
                             <tr>
                             	<th>案件状态：</th>
	                            <td><select  id="caseState" name="casState" class="inputSize2"><option value="u">未退案</option></select></td>
                             	<th>催收状态：</th>
                            	<td><c:if test="${!empty clStateList}"><select name="clState" class="inputSize2 inputBoxAlign"><option value="">请选择</option><option value="NULL">新案</option><c:forEach items="${clStateList}" var="cStateList"><option value="${cStateList.typId}">${cStateList.typName}</option></c:forEach></select></c:if><c:if test="${empty clStateList}"><select class="inputSize2 inputBoxAlign" disabled="disabled"><option>未添加</option></select></c:if></td>
                             	<th>申请人：</th>
                               	<td><input class="inputSize2 inputBoxAlign" style="width:85px;" type="text" name="appMan" onBlur="autoShort(this,20)"/></td>
                                <th>申请时间：</th>
                                <td colspan=2><input name="startTime" id="staTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:104px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('endTim');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'endTim\')}'})"/>&nbsp;到&nbsp;<input name="endTime" id="endTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:102px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'staTim\')}'})"/></td>
                                <td colspan=2>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize3 inputBoxAlign" value="查询"/></td>
                            </tr>
                            </table>
                        </form>
                    </div>
                    <div id="toolsBarTop" class="bottomBar">
                    	<c:if test="${state==0}">
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="toBatchAssRes()">完成协催</span>
                        </c:if>
                        <c:if test="${state==-2}">
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="toBatchPass()">同意协催</span>
                        </c:if>
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="cancelBatchAss(1,'案件协催')">撤销协催</span>
                        <c:if test="${state!=-2}">
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="assPopDiv(5)">导出查询结果</span>
                        </c:if>
                    </div>
	         		<div id="dataList" class="dataList"></div>
				</div>
			</div>
		</div>
	</body>
</html>
