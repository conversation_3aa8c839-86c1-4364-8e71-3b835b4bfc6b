function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE_WITH_REMARK','C_DT'],
		['ID_NO','C_CODE','STATE','BACK_DT'],
		['AMT','SINR_BASE_AMT','SINR_TOTAL_AMT','P_CRLINE'],
		['CL_T','PAID_LEFT','LAST_M','PR_COUNT'],
		['SINR_NO','APP_NO','SINR_LOAN_NO','LOAN_T'],
		['LOAN_DT','COUNT','P_COUNT','DAYS'],
		['SINR_CLAIM_DAYS','SINR_REC_DAYS','SINR_SBS_STATUS','SINR_CLAIM_AMT'],
		['SINR_PRICP','SINR_PREM','SINR_INT','SINR_BACK_AMT'],
		['SINR_PREM_TOPAY','DAMAGE','LAST_P_DT','LAST_P'],
		['F_BANK','SINR_STORE','SINR_DEP','SINR_SRC'],
		['SINR_BRAND_NO','SINR_BRAND_NAME','SINR_CAR_MODEL','SINR_CAR_LICENCE'],
		['MOB','SINR_PHONE_STATUS','EMAIL','SINR_AREA'],
		['PHO','HOM',['SINR_LIVE_INF',3]],
		['COM','W_PHO',['W_ADDR',3]],
		['C1_NAME','C1_HM_PHO',['C1_MOB',3]],
		['C2_NAME','C2_HM_PHO',['C2_MOB',3]],
		['C3_NAME','C3_HM_PHO',['C3_MOB',3]],
		['C4_NAME','C4_HM_PHO',['C4_MOB',3]],
		['C5_NAME','C5_HM_PHO',['C5_MOB',3]],
		['PTP','CP',['TIPS_DT',3]],
		['CL_AREA','EMP_NAME','LAST_CL','LAST_VIS'],
		[['TREMARK',7]]
	);
}
function getLayout2(){
	return  new Array(
		['ASS_TM',['ASS_HIS',5]],
		[['ATT',7]]
	);
}