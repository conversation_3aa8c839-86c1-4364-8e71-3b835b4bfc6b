<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.ExtraInf" table="extra_inf" schema="dbo" >
        <id name="exiId" type="java.lang.Long">
            <column name="exi_id" />
            <generator class="identity" />
        </id>
        <property name="exiIdNumber" type="java.lang.String">
            <column name="exi_id_number" length="25" />
        </property>
        <property name="exiType" type="java.lang.String">
            <column name="exi_type" length="50" />
        </property>
        <property name="exiContent" type="java.lang.String">
            <column name="exi_content" length="1073741823" />
        </property>
        <property name="exiCreTime" type="java.util.Date">
            <column name="exi_cre_time" length="23" />
        </property>
        <property name="exiUpdTime" type="java.util.Date">
            <column name="exi_upd_time" length="23" />
        </property>
        <property name="exiCreMan" type="java.lang.String">
            <column name="exi_cre_man" length="25" />
    	</property>
        <property name="exiUpdMan" type="java.lang.String">
            <column name="exi_upd_man" length="25" />
    	</property>
        <property name="exiName" type="java.lang.String">
            <column name="exi_name" length="50" />
    	</property>
        <property name="exiRemark" type="java.lang.String">
            <column name="exi_remark" length="1073741823" />
        </property>
    </class>
</hibernate-mapping>
