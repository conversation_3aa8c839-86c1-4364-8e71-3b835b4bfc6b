<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.LimRight" table="lim_right" schema="dbo">
        <id name="rigCode" type="java.lang.String">
            <column name="rig_code" length="50" />
            <generator class="assigned" />
        </id>
        <many-to-one name="limOperate" class="com.frsoft.base.entity.LimOperate" fetch="select" not-null="false">
            <column name="rig_ope_code" length="50" />
        </many-to-one>
        <many-to-one name="limFunction" class="com.frsoft.base.entity.LimFunction" fetch="select" not-null="false">
            <column name="rig_fun_code" length="50" />
        </many-to-one>
        <property name="rigWmsName" type="java.lang.String">
            <column name="rig_wms_name" length="300" />
        </property>
        <set name="RRoleRigs" inverse="true">
            <key>
                <column name="rrr_rig_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.base.entity.RRoleRig" />
        </set>
    </class>
</hibernate-mapping>
