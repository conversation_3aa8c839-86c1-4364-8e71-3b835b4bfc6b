<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>清空通道</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
    	body{
			background:#fff;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
    	function confirmForm(){
    		$("serverId").value = parent.document.getElementById("serverId").value;
			waitSubmit("toConfirm");
			waitSubmit("toCancel");
			$("cfmForm").submit();
		}
  </script>
  </head>
  
  <body> 
  	<div> 
        <br/>
        <form id="cfmForm" action="ctiServerAction.do" method="post" style="margin:0px">
        <input type="hidden" name="op" value="cleanChannel"/>
        <input type="hidden" name="serverId"/>
        <input type="hidden" name="channelNo" value="${channelNo}"/>
        <input type="hidden" name="uCode" value="${uCode}"/>
        &nbsp;&nbsp;&nbsp;确定要清空此通道吗？<br/><br/>
        <button id="toConfirm" class ="butSize1" onClick="confirmForm()">确定</button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <button id="toCancel" class ="butSize1" onClick="cancel()">取消</button>
        </form>		 
        
    </div> 
	</body>
</html>
