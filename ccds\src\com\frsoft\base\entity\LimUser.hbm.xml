<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.base.entity.LimUser" table="lim_user" schema="dbo">
        <id name="userCode" type="java.lang.String">
            <column name="user_code" length="50" />
            <generator class="assigned" />
        </id>
        <many-to-one name="limRole" class="com.frsoft.base.entity.LimRole" fetch="select" not-null="false">
            <column name="user_role_id"/>
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="user_se_id" />
        </many-to-one>
        <many-to-one name="salOrg" class="com.frsoft.base.entity.SalOrg" fetch="select" not-null="false">
            <column name="user_so_code" />
        </many-to-one>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select" not-null="false">
            <column name="user_up_code" />
        </many-to-one>
        <property name="userLoginName" type="java.lang.String">
            <column name="user_loginName" length="50" />
        </property>
        <property name="userIsLogin" type="java.lang.String">
            <column name="user_islogin" length="1" />
        </property>
        <property name="userIp" type="java.lang.String">
            <column name="user_ip" length="50" />
        </property>
        <property name="userPwd" type="java.lang.String">
            <column name="user_pwd" length="50" />
        </property>
        <property name="userLev" type="java.lang.String">
            <column name="user_lev" length="1" />
        </property>
        <property name="userSeName" type="java.lang.String">
            <column name="user_se_name" length="100" />
        </property>
        <property name="userDesc" type="java.lang.String">
            <column name="user_desc" length="1073741823" />
        </property>
        <property name="userIsenabled" type="java.lang.String">
            <column name="user_isenabled" length="1" />
        </property>
        <property name="userNum" type="java.lang.String">
            <column name="user_num" length="50" />
        </property>
        <property name="userFail" type="java.lang.Integer">
            <column name="user_fail" />
        </property>
        <property name="userPwdUpdDate" type="java.util.Date">
            <column name="user_pwd_upd_date" length="23" />
        </property>
        <property name="userCtiLogin" type="java.lang.String">
            <column name="user_cti_login" length="255" />
        </property>
        <property name="userCtiPwd" type="java.lang.String">
            <column name="user_cti_pwd" length="255" />
        </property>
         <property name="userCtiServer" type="java.lang.String">
            <column name="user_cti_server" length="100" />
        </property>
         <property name="userCtiPhone" type="java.lang.String">
            <column name="user_cti_phone" length="50" />
        </property>
        <property name="userSmsMaxNum" type="java.lang.Integer">
            <column name="user_sms_max_num" />
        </property>
        <property name="userChannel" type="java.lang.Integer">
            <column name="user_channel" />
        </property>
        <property name="userMdvId" type="java.lang.Long">
            <column name="user_mdv_id" />
        </property>
        <property name="userLastMobMac" type="java.lang.String">
            <column name="user_last_mob_mac" length="50" />
        </property>
         <property name="userLastMobTime" type="java.util.Date">
        <column name="user_last_mob_time" ></column>
        </property>
        <many-to-one name="userGroup" class="com.frsoft.base.entity.LimGroup">
        	<column name="user_grp_id" />
        </many-to-one>
        <many-to-one name="userCtiServerObj" class="com.frsoft.ccds.entity.CtiServer">
        	<column name="user_ctis_id" />
        </many-to-one>
        <set name="limUsers" inverse="true" >
            <key>
                <column name="user_up_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.base.entity.LimUser" />
        </set>
        <set name="RUserRigs" inverse="true">
            <key>
                <column name="rur_user_code" />
            </key>
            <one-to-many class="com.frsoft.base.entity.RUserRig" />
        </set>
    </class>
</hibernate-mapping>
