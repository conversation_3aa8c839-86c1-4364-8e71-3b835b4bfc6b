-- 催收系统数据库初始化脚本
-- 数据库: ccds
-- 版本: 1.0.0
-- 创建时间: 2024-01-01

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `ccds`;

-- 系统用户表
CREATE TABLE IF NOT EXISTS `sys_user` (
  `user_code` varchar(50) NOT NULL COMMENT '用户编码',
  `user_login_name` varchar(50) NOT NULL COMMENT '登录名',
  `user_pwd` varchar(100) NOT NULL COMMENT '密码',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `user_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `user_cti_server` varchar(100) DEFAULT NULL COMMENT 'CTI服务器',
  `user_cti_phone` varchar(20) DEFAULT NULL COMMENT 'CTI电话',
  `user_sms_max_num` int DEFAULT NULL COMMENT '短信最大发送数',
  PRIMARY KEY (`user_code`),
  UNIQUE KEY `uk_user_login_name` (`user_login_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- 员工表
CREATE TABLE IF NOT EXISTS `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT COMMENT '员工编号',
  `se_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `se_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `se_so_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `se_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `se_phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `se_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `se_position` varchar(50) DEFAULT NULL COMMENT '职位',
  `se_entry_date` date DEFAULT NULL COMMENT '入职日期',
  PRIMARY KEY (`se_no`),
  KEY `idx_se_user_code` (`se_user_code`),
  KEY `idx_se_so_code` (`se_so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 组织表
CREATE TABLE IF NOT EXISTS `sal_org` (
  `so_code` varchar(50) NOT NULL COMMENT '组织编码',
  `so_name` varchar(100) NOT NULL COMMENT '组织名称',
  `so_con_area` varchar(100) DEFAULT NULL COMMENT '管辖区域',
  `so_loc` varchar(100) DEFAULT NULL COMMENT '位置',
  `so_user_code` varchar(50) DEFAULT NULL COMMENT '用户编码',
  `so_emp_num` varchar(20) DEFAULT NULL COMMENT '员工数量',
  `so_resp` varchar(200) DEFAULT NULL COMMENT '职责',
  `so_org_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `so_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `so_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  `so_up_code` varchar(50) DEFAULT NULL COMMENT '上级编码',
  `so_cost_center` varchar(50) DEFAULT NULL COMMENT '成本中心',
  `so_org_nature` varchar(50) DEFAULT NULL COMMENT '组织性质',
  PRIMARY KEY (`so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织表';

-- 角色表
CREATE TABLE IF NOT EXISTS `lim_role` (
  `rol_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `rol_name` varchar(50) NOT NULL COMMENT '角色名称',
  `rol_lev` int DEFAULT NULL COMMENT '角色级别',
  `rol_desc` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `rol_grp_id` bigint DEFAULT NULL COMMENT '组ID',
  PRIMARY KEY (`rol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 组表
CREATE TABLE IF NOT EXISTS `lim_group` (
  `grp_id` bigint NOT NULL AUTO_INCREMENT COMMENT '组ID',
  `grp_name` varchar(50) NOT NULL COMMENT '组名称',
  `grp_desc` varchar(200) DEFAULT NULL COMMENT '组描述',
  `grp_cre_time` datetime DEFAULT NULL COMMENT '创建时间',
  `grp_cre_man` varchar(50) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`grp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组表';

-- 类型字典表
CREATE TABLE IF NOT EXISTS `type_list` (
  `typ_id` bigint NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `typ_name` varchar(50) NOT NULL COMMENT '类型名称',
  `typ_desc` varchar(200) DEFAULT NULL COMMENT '类型描述',
  `typ_type` varchar(50) DEFAULT NULL COMMENT '类型分类',
  `typ_isenabled` char(1) DEFAULT '1' COMMENT '是否启用(0否 1是)',
  PRIMARY KEY (`typ_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='类型字典表';

-- 银行案件表
CREATE TABLE IF NOT EXISTS `bank_case` (
  `cas_id` bigint NOT NULL AUTO_INCREMENT COMMENT '案件ID',
  `cas_code` varchar(50) DEFAULT NULL COMMENT '案件编码',
  `cas_group` varchar(50) DEFAULT NULL COMMENT '案件组',
  `cas_state` int DEFAULT '0' COMMENT '案件状态(0待分配 1催收中 2已结案 3暂停催收 4法务处理)',
  `cas_typ_hid` bigint DEFAULT NULL COMMENT '类型ID',
  `cas_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `cas_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `cas_m` decimal(15,2) DEFAULT NULL COMMENT '案件金额',
  `cas_paid_m` decimal(15,2) DEFAULT '0.00' COMMENT '已还金额',
  `cas_se_no` bigint DEFAULT NULL COMMENT '员工编号',
  `cas_ins_time` datetime DEFAULT NULL COMMENT '创建时间',
  `cas_ins_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `cas_alt_time` datetime DEFAULT NULL COMMENT '修改时间',
  `cas_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  `cas_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `cas_id_no` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `cas_address` varchar(200) DEFAULT NULL COMMENT '地址',
  `cas_work_address` varchar(200) DEFAULT NULL COMMENT '工作地址',
  `cas_work_phone` varchar(20) DEFAULT NULL COMMENT '工作电话',
  `cas_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `cas_bank` varchar(50) DEFAULT NULL COMMENT '银行',
  `cas_card_no` varchar(30) DEFAULT NULL COMMENT '银行卡号',
  `cas_overdue_date` date DEFAULT NULL COMMENT '逾期日期',
  `cas_pback_p` decimal(5,2) DEFAULT NULL COMMENT '回收率',
  `cas_wpost_code` varchar(10) DEFAULT NULL COMMENT '工作邮编',
  `cas_deadline` date DEFAULT NULL COMMENT '截止日期',
  `cas_is_host` char(1) DEFAULT '0' COMMENT '是否主案件',
  `cas_bill_date` date DEFAULT NULL COMMENT '账单日期',
  `cas_last_paid` date DEFAULT NULL COMMENT '最后还款日期',
  `cas_count` int DEFAULT '0' COMMENT '催收次数',
  `cas_left_pri` decimal(15,2) DEFAULT NULL COMMENT '剩余本金',
  `cas_assign_ids` varchar(200) DEFAULT NULL COMMENT '分配ID列表',
  `cas_assign_names` varchar(200) DEFAULT NULL COMMENT '分配姓名列表',
  `cas_last_assign_time` datetime DEFAULT NULL COMMENT '最后分配时间',
  `cas_overdue_days` int DEFAULT '0' COMMENT '逾期天数',
  `cas_overdue_days_str` varchar(20) DEFAULT NULL COMMENT '逾期天数字符串',
  `cas_bir` date DEFAULT NULL COMMENT '生日',
  `cas_mpost_code` varchar(10) DEFAULT NULL COMMENT '邮编',
  `cas_perm_crline` decimal(15,2) DEFAULT NULL COMMENT '永久信用额度',
  `cas_alt_hold` char(1) DEFAULT '0' COMMENT '是否暂停',
  PRIMARY KEY (`cas_id`),
  KEY `idx_cas_code` (`cas_code`),
  KEY `idx_cas_name` (`cas_name`),
  KEY `idx_cas_phone` (`cas_phone`),
  KEY `idx_cas_se_no` (`cas_se_no`),
  KEY `idx_cas_state` (`cas_state`),
  KEY `idx_cas_ins_time` (`cas_ins_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='银行案件表';

-- 催收记录表
CREATE TABLE IF NOT EXISTS `pho_red` (
  `pr_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `pr_typ_id` bigint DEFAULT NULL COMMENT '类型ID',
  `pr_contact` varchar(50) DEFAULT NULL COMMENT '联系人',
  `pr_cas_id` bigint NOT NULL COMMENT '案件ID',
  `pr_content` text COMMENT '催收内容',
  `pr_time` datetime DEFAULT NULL COMMENT '催收时间',
  `pr_se_no` bigint DEFAULT NULL COMMENT '员工编号',
  `pr_con_type` varchar(20) DEFAULT NULL COMMENT '联系方式',
  `pr_result` varchar(50) DEFAULT NULL COMMENT '催收结果',
  `pr_next_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `pr_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `pr_isdel` char(1) DEFAULT '0' COMMENT '是否删除(0否 1是)',
  PRIMARY KEY (`pr_id`),
  KEY `idx_pr_cas_id` (`pr_cas_id`),
  KEY `idx_pr_se_no` (`pr_se_no`),
  KEY `idx_pr_time` (`pr_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='催收记录表';

-- 还款记录表
CREATE TABLE IF NOT EXISTS `case_paid` (
  `pa_id` bigint NOT NULL AUTO_INCREMENT COMMENT '还款ID',
  `pa_state` int DEFAULT '0' COMMENT '状态(0承诺中 1已还款 2逾期未还 3部分还款)',
  `pa_cas_id` bigint NOT NULL COMMENT '案件ID',
  `pa_ptp_d` datetime DEFAULT NULL COMMENT '承诺还款日期',
  `pa_ptp_num` decimal(15,2) DEFAULT NULL COMMENT '承诺还款金额',
  `pa_paid_date` datetime DEFAULT NULL COMMENT '实际还款日期',
  `pa_paid_num` decimal(15,2) DEFAULT NULL COMMENT '实际还款金额',
  `pa_se_no` bigint DEFAULT NULL COMMENT '员工编号',
  `pa_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `pa_type` varchar(20) DEFAULT NULL COMMENT '还款类型',
  `pa_method` varchar(20) DEFAULT NULL COMMENT '还款方式',
  `pa_ins_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pa_ins_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `pa_alt_time` datetime DEFAULT NULL COMMENT '修改时间',
  `pa_alt_user` varchar(50) DEFAULT NULL COMMENT '修改用户',
  PRIMARY KEY (`pa_id`),
  KEY `idx_pa_cas_id` (`pa_cas_id`),
  KEY `idx_pa_se_no` (`pa_se_no`),
  KEY `idx_pa_paid_date` (`pa_paid_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='还款记录表';

-- 插入初始数据
-- 默认管理员用户
INSERT INTO `sys_user` (`user_code`, `user_login_name`, `user_pwd`, `user_name`, `user_isenabled`) 
VALUES ('admin', 'admin', '21232f297a57a5a743894a0e4a801fc3', '系统管理员', '1')
ON DUPLICATE KEY UPDATE `user_name` = '系统管理员';

-- 默认组织
INSERT INTO `sal_org` (`so_code`, `so_name`, `so_isenabled`) 
VALUES ('ORG001', '总公司', '1')
ON DUPLICATE KEY UPDATE `so_name` = '总公司';

-- 默认员工
INSERT INTO `sal_emp` (`se_name`, `se_user_code`, `se_so_code`, `se_isenabled`, `se_position`) 
VALUES ('系统管理员', 'admin', 'ORG001', '1', '管理员')
ON DUPLICATE KEY UPDATE `se_name` = '系统管理员';

-- 默认角色
INSERT INTO `lim_role` (`rol_name`, `rol_lev`, `rol_desc`) 
VALUES ('系统管理员', 1, '系统管理员角色')
ON DUPLICATE KEY UPDATE `rol_desc` = '系统管理员角色';

-- 默认组
INSERT INTO `lim_group` (`grp_name`, `grp_desc`, `grp_cre_time`, `grp_cre_man`) 
VALUES ('管理组', '系统管理组', NOW(), 'admin')
ON DUPLICATE KEY UPDATE `grp_desc` = '系统管理组';

-- 默认类型字典
INSERT INTO `type_list` (`typ_name`, `typ_desc`, `typ_type`, `typ_isenabled`) VALUES
('电话催收', '电话催收类型', 'COLLECTION_TYPE', '1'),
('短信催收', '短信催收类型', 'COLLECTION_TYPE', '1'),
('上门催收', '上门催收类型', 'COLLECTION_TYPE', '1'),
('承诺还款', '承诺还款结果', 'COLLECTION_RESULT', '1'),
('拒绝还款', '拒绝还款结果', 'COLLECTION_RESULT', '1'),
('无人接听', '无人接听结果', 'COLLECTION_RESULT', '1'),
('全额还款', '全额还款类型', 'PAYMENT_TYPE', '1'),
('部分还款', '部分还款类型', 'PAYMENT_TYPE', '1'),
('银行转账', '银行转账方式', 'PAYMENT_METHOD', '1'),
('现金还款', '现金还款方式', 'PAYMENT_METHOD', '1')
ON DUPLICATE KEY UPDATE `typ_desc` = VALUES(`typ_desc`);

COMMIT;
