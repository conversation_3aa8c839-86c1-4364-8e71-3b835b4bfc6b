<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>    
    <title>协催内容</title>
    <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/common.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	<script type="text/javascript" src="js/caseAss.js"></script>
	
    <script type="text/javascript">    
		function showWarn(isShow){
			if(isShow==1){
				$("warnTh").style.color="#000";
				$("caseWarn").disabled=false;
				$("caseWarn").style.backgroundColor="#FFF";
			}
			else{
				$("warnTh").style.color="#999";
				$("caseWarn").disabled=true;
				$("caseWarn").style.backgroundColor="#CCC";
				$("caseWarn").innerHTML="";
			}
		}  
		function initForm(){
			switch('${assType}'){
				case '3': $('red').checked = true; showWarn(1); break;
				default: $('none').checked = true;
			}
		} 
		function check(){
			var errStr = "";
		 	/*if(isEmpty("assRes")){
				errStr+="未填写协催内容！\n";
			}*/
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}else{
			    waitSubmit("save","提交中...");
				waitSubmit("doCancel");
				return $("updAssForm").submit();
			}				  
		}
		
		window.onload=function(){
			initForm();
		}
	
  </script> 
</head>
  <body>
  <div class="inputDiv">
  	<form action="assitanceAction.do" method="post" id="updAssForm">
  	  	<input type="hidden" name="op" value="saveAssRes">
  	  	<input type="hidden" name="assId" value="${assId}">
  	  	<table class="dashTab inputForm" cellpadding="0" cellspacing="0">
			<tbody>
            	<tr>
                    <th>协催内容：</th>
                    <td colspan="3"><textarea class="inputSize2L" rows="5" name="assRes" id="assRes" onBlur="autoShort(this,4000)"></textarea></td>
                </tr>
                <tr>
                	<th>案件状态：</th>
                    <td colspan="3" class="longTd">
                    	<input type="radio" id="none" name="color" value="-1" onClick="showWarn(0)"/><label for="none">不更改</label>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    <input type="radio" id="blue" name="color" value="2" onClick="showWarn(0)"/><label for="blue">标蓝</label>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                    <input type="radio" id="red" name="color" value="1" onClick="showWarn(1)" /><label for="red">标红</label>
                    </td>
                </tr>
                <tr class="noBorderBot">
                	<th id="warnTh" style="color:#999">案件警告：</th>
                    <td colspan="3">
	                    <textarea class="inputSize2L" rows="3" name="caseWarn" id="caseWarn" style="background-color:#CCC" onBlur="autoShort(this,300)" disabled></textarea>
                    </td>
                </tr>
                <tr class="submitTr">
                    <td colspan="4">
                    <input type="button" class="butSize1" id="save" value="提交" onClick="check()">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"></td>
                </tr>	
            </tbody>
					
	  </table>
	</form>
  </div>
  </body>
</html>
