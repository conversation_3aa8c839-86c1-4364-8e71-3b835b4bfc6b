<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>修改地址信息</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="cache-control" content="no-cache"/>
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
    	body{
			background-color:#fff;
		}
		.inputForm tbody th{
			width:70px;
		}
		.inputForm tbody td{
			width:200px;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
  </head>
  
  <body>
    <form action="caseAction.do" method="post">  
        <input type="hidden" name="op" value="alertCaseAdd">
    	<input type="hidden" name="casId" value="${bankCase.casId}">
        <table class="dashTab inputForm single" cellpadding="0" cellspacing="0">
        	<tbody>
           	<tr>
               	<th>省份：</th>
               	<td><input class="inputSize2 inputBoxAlign" style="width:105px" type="text" id="area1" name="casArea1" value="<c:out value="${bankCase.casArea1}"/>"  onBlur="autoShort(this,50)" />&nbsp;<select class="inputBoxAlign inputSize4" style="width:60px;" id="area1Sel" onChange="setCityValue(1)"><c:if test="${!empty provList}"><c:forEach var="provList" items="${provList}"><option value="${provList.areId}">${provList.areName}</option></c:forEach></c:if></select></td>
            </tr>
            <tr>
            	<th>城市：</th>
                <td><input class="inputSize2 inputBoxAlign" style="width:105px" type="text" id="area2" name="casArea2" value="<c:out value="${bankCase.casArea2}"/>"  onBlur="autoShort(this,50)" />&nbsp;<select class="inputBoxAlign inputSize4" style="width:60px;" id="area2Sel" onChange="setCityValue(2)"></select></td>
            </tr>
            <tr class="noBorderBot">
            	<th>区县：</th>
                <td><input class="inputSize2 inputBoxAlign" style="width:105px" type="text" id="area3" name="casArea3" value="<c:out value="${bankCase.casArea3}"/>"  onBlur="autoShort(this,50)" />&nbsp;<select class="inputBoxAlign inputSize4" style="width:60px;" id="area3Sel" onChange="setCityValue(3)"></select></td>
          	</tr>
           	<tr class="submitTr">
           		<td colspan="2">
                	<input id="caseSave" class="butSize1" type="submit" value="保存" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()"/>
                </td>
          	</tr>
           </tbody>
        </table>
	  	</form>
	</body>
</html>
