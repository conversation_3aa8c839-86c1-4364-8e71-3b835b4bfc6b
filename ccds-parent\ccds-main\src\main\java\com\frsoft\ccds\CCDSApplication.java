package com.frsoft.ccds;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 催收系统启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication
@EnableTransactionManagement
public class CCDSApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(CCDSApplication.class, args);
        System.out.println("催收系统启动成功！");
    }
}
