package com.frsoft.ccds.common.core.service;

import java.util.List;

/**
 * 通用Service基类
 * 
 * @param <T> 实体类型
 * <AUTHOR>
 */
public interface BaseService<T> {
    
    /**
     * 根据ID查询
     * 
     * @param id 主键ID
     * @return 实体对象
     */
    T selectById(Long id);
    
    /**
     * 查询所有记录
     * 
     * @return 实体列表
     */
    List<T> selectAll();
    
    /**
     * 根据条件查询列表
     * 
     * @param entity 查询条件
     * @return 实体列表
     */
    List<T> selectList(T entity);
    
    /**
     * 根据条件查询单个记录
     * 
     * @param entity 查询条件
     * @return 实体对象
     */
    T selectOne(T entity);
    
    /**
     * 根据条件查询记录数
     * 
     * @param entity 查询条件
     * @return 记录数
     */
    int selectCount(T entity);
    
    /**
     * 新增记录
     * 
     * @param entity 实体对象
     * @return 影响行数
     */
    int insert(T entity);
    
    /**
     * 批量新增
     * 
     * @param list 实体列表
     * @return 影响行数
     */
    int insertBatch(List<T> list);
    
    /**
     * 修改记录
     * 
     * @param entity 实体对象
     * @return 影响行数
     */
    int update(T entity);
    
    /**
     * 批量修改
     * 
     * @param list 实体列表
     * @return 影响行数
     */
    int updateBatch(List<T> list);
    
    /**
     * 保存或更新（根据ID判断）
     * 
     * @param entity 实体对象
     * @return 影响行数
     */
    int saveOrUpdate(T entity);
    
    /**
     * 根据ID删除
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);
    
    /**
     * 根据ID批量删除
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteByIds(Long[] ids);
    
    /**
     * 根据条件删除
     * 
     * @param entity 删除条件
     * @return 影响行数
     */
    int deleteByCondition(T entity);
    
    /**
     * 根据ID逻辑删除
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int logicDeleteById(Long id);
    
    /**
     * 根据ID批量逻辑删除
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int logicDeleteByIds(Long[] ids);
    
    /**
     * 数据验证
     * 
     * @param entity 实体对象
     * @param isUpdate 是否为更新操作
     */
    void validate(T entity, boolean isUpdate);
    
    /**
     * 设置创建信息
     * 
     * @param entity 实体对象
     */
    void setCreateInfo(T entity);
    
    /**
     * 设置更新信息
     * 
     * @param entity 实体对象
     */
    void setUpdateInfo(T entity);
}
