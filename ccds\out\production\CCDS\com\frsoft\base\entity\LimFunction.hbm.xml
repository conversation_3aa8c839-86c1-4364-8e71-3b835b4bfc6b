<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.LimFunction" table="lim_function" schema="dbo">
        <id name="funCode" type="java.lang.String">
            <column name="fun_code" length="50" />
            <generator class="assigned" />
        </id>
        <property name="funDesc" type="java.lang.String">
            <column name="fun_desc" length="1073741823" />
        </property>
        <property name="funType" type="java.lang.String">
            <column name="fun_type" length="50" />
        </property>
        <set name="limRights" inverse="true">
            <key>
                <column name="rig_fun_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.base.entity.LimRight" />
        </set>
    </class>
</hibernate-mapping>
