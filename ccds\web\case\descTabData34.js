function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','C_DT'],
		['ID_NO','C_CODE','AREA','STATE'],
		['AMT',['RMB',1,'退出不良金额'],['GB',1,'出催金额'],['MY',1,'降期金额']],
		['LAST_M','PAID','M_CAT','AGE'],
		['EMP','ASS_TM','ASS_HIS','BACK_DT'],
		['MOB','PHO','HOM','H_POST'],
		['COM','W_PHO','W_ADDR','W_POST'],
		['REG','M_ADDR','EMAIL','COUNT'],
		['PTP','CP',['BACK_AMT',3]],
		['TIPS_DT','DEAD_L',['TREMARK',3]]
	);
}
function getLayout2(){
	return  new Array(
		[['PRI',1,'分期本金'],['LEFT_PRI',1,'本金'],'LOAN_DT','OVERDUE_NUM'],
		['LOAN_RATE','DAYS','OVERDUE_M','OV_INT'],
		['MONTH_P','MIN_P','PR_COUNT','OV_P'],
		['CL_AREA','ACC','LAST_CL','LAST_VIS'],
		['M_POST','REG_POST','F_BANK','HOST'],
		['ALT_HOLD','NOOUT','BILL_DT','CYCLE'],
		['P_CRLINE','G_AMT','OVER_LIMIT','P_L'],
		['START_DT','STOP_DT','LAST_P_DT','LAST_P'],
		['LAST_C_DT','LAST_R_DT','OV_DT','OVERDUE_ONCE'],
		['APP_NO','C_L','LOAN_T','DELAY_LV'],
		['P_COUNT','CL_T','LOAN_END_DT','BIR'],
		['POS','PART','PROD','BIZ'],
		[['FILE_NO',3],'SC_PC_NO','SC_NO'],
		['C1_NAME','C1_ID_NO','C1_HM_PHO','C1_W_PHO'],
		['C1_MOB','C1_COM',['C1_ADR',3]],
		['C2_NAME','C2_ID_NO','C2_HM_PHO','C2_W_PHO'],
		['C2_MOB','C2_COM',['C2_ADR',3]],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		['C4_NAME','C4_ID_NO','C4_HM_PHO','C4_W_PHO'],
		['C4_MOB','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		['C6_NAME','C6_ID_NO','C6_HM_PHO','C6_W_PHO'],
		['C6_MOB','C6_COM',['C6_ADR',3]],
		[['O_REC',3],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}