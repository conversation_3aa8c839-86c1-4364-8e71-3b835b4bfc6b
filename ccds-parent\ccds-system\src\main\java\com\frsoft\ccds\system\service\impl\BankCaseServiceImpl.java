package com.frsoft.ccds.system.service.impl;

import com.frsoft.ccds.system.domain.BankCase;
import com.frsoft.ccds.system.mapper.BankCaseMapper;
import com.frsoft.ccds.system.service.IBankCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 银行案件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class BankCaseServiceImpl implements IBankCaseService {
    
    @Autowired
    private BankCaseMapper bankCaseMapper;

    /**
     * 查询银行案件
     * 
     * @param casId 银行案件主键
     * @return 银行案件
     */
    @Override
    public BankCase selectBankCaseByCasId(Long casId) {
        return bankCaseMapper.selectBankCaseByCasId(casId);
    }

    /**
     * 查询银行案件列表
     * 
     * @param bankCase 银行案件
     * @return 银行案件
     */
    @Override
    public List<BankCase> selectBankCaseList(BankCase bankCase) {
        return bankCaseMapper.selectBankCaseList(bankCase);
    }

    /**
     * 新增银行案件
     * 
     * @param bankCase 银行案件
     * @return 结果
     */
    @Override
    public int insertBankCase(BankCase bankCase) {
        bankCase.setCasInsTime(new Date());
        return bankCaseMapper.insertBankCase(bankCase);
    }

    /**
     * 修改银行案件
     * 
     * @param bankCase 银行案件
     * @return 结果
     */
    @Override
    public int updateBankCase(BankCase bankCase) {
        bankCase.setCasAltTime(new Date());
        return bankCaseMapper.updateBankCase(bankCase);
    }

    /**
     * 批量删除银行案件
     * 
     * @param casIds 需要删除的银行案件主键
     * @return 结果
     */
    @Override
    public int deleteBankCaseByCasIds(Long[] casIds) {
        return bankCaseMapper.deleteBankCaseByCasIds(casIds);
    }

    /**
     * 删除银行案件信息
     * 
     * @param casId 银行案件主键
     * @return 结果
     */
    @Override
    public int deleteBankCaseByCasId(Long casId) {
        return bankCaseMapper.deleteBankCaseByCasId(casId);
    }
    
    /**
     * 根据员工编号查询案件列表
     * 
     * @param seNo 员工编号
     * @return 案件列表
     */
    @Override
    public List<BankCase> selectBankCaseListBySeNo(Long seNo) {
        return bankCaseMapper.selectBankCaseListBySeNo(seNo);
    }
    
    /**
     * 根据案件状态查询案件列表
     * 
     * @param casState 案件状态
     * @return 案件列表
     */
    @Override
    public List<BankCase> selectBankCaseListByState(Integer casState) {
        return bankCaseMapper.selectBankCaseListByState(casState);
    }
    
    /**
     * 根据客户姓名模糊查询案件列表
     * 
     * @param casName 客户姓名
     * @return 案件列表
     */
    @Override
    public List<BankCase> selectBankCaseListByName(String casName) {
        return bankCaseMapper.selectBankCaseListByName(casName);
    }
    
    /**
     * 根据客户电话查询案件列表
     * 
     * @param casPhone 客户电话
     * @return 案件列表
     */
    @Override
    public List<BankCase> selectBankCaseListByPhone(String casPhone) {
        return bankCaseMapper.selectBankCaseListByPhone(casPhone);
    }
    
    /**
     * 统计案件总数
     * 
     * @param bankCase 查询条件
     * @return 案件总数
     */
    @Override
    public int countBankCase(BankCase bankCase) {
        return bankCaseMapper.countBankCase(bankCase);
    }
    
    /**
     * 分配案件给员工
     * 
     * @param casIds 案件ID数组
     * @param seNo 员工编号
     * @param assignUser 分配用户
     * @return 结果
     */
    @Override
    public int assignBankCaseToEmployee(Long[] casIds, Long seNo, String assignUser) {
        return bankCaseMapper.assignBankCaseToEmployee(casIds, seNo, assignUser);
    }
}
