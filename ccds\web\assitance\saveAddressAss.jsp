<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>

<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>【无效】新建地址核准</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>

	<script type="text/javascript" >
    	function check(){
			var errStr = "";
			if(isEmpty("text")){
				errStr+="- 未填写申请内容！\n";
			 }
			 if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			 else{
				waitSubmit("save","保存中...");
				waitSubmit("doCancel");
				return $("create").submit();
			 }
		}
		
  </script></head>
  
  <body>

  <div class="inputDiv">
  	<form id="create" action="assitanceAction.do" method="post">
  		<input type="hidden" name="op" value="saveAddressAss" />
  		  <input type="hidden" name="casId"  value="${address.bankCase.casId}"/>
  		  <input type="hidden" name="adrId"  value="${address.adrId}"/>
          <input type="hidden" name="category" value="${address.adrNum}"/>
          <input type="hidden" name="address"  value="${address.adrAdd}"/>
        <table class="dashTab inputForm" cellpadding="0" cellspacing="0">
            <tbody>
                <tr>
                    <th>地址：</th>
                    <td colspan="3" class="longTd"><input type="text" class="inputSize2L" name="adrStr" onBlur="autoShort(this,200)" value="${address.adrAdd}"/></td>
                </tr>
                <tr class="noBorderBot">
                    <th class="required">申请内容：<span class="red">*</span></th>
                    <td colspan="3"><textarea class="inputSize2L" rows="10" id="text" name="text" onBlur="autoShort(this,4000)"></textarea></td>
                </tr>
                <tr class="submitTr">
                    <td colspan="4">
                    <input id="save" class="butSize1" type="button" value="保存" onClick="check()" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                    </td>
                </tr>
            </tbody>
        </table>
	</form>
    </div>
  </body>
</html>
