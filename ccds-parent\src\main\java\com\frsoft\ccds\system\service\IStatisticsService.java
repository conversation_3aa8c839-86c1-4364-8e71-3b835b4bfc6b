package com.frsoft.ccds.system.service;

import java.util.Map;

/**
 * 统计分析服务接口
 * 
 * <AUTHOR>
 */
public interface IStatisticsService {
    
    /**
     * 获取首页统计数据
     * 
     * @return 统计数据
     */
    Map<String, Object> getHomeStatistics();
    
    /**
     * 获取案件统计数据
     * 
     * @return 案件统计
     */
    Map<String, Object> getCaseStatistics();
    
    /**
     * 获取催收统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 催收统计
     */
    Map<String, Object> getCollectionStatistics(String startDate, String endDate);
    
    /**
     * 获取还款统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 还款统计
     */
    Map<String, Object> getPaymentStatistics(String startDate, String endDate);
    
    /**
     * 获取员工绩效统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 员工绩效
     */
    Map<String, Object> getEmployeePerformance(String startDate, String endDate);
    
    /**
     * 获取逾期分析数据
     * 
     * @return 逾期分析
     */
    Map<String, Object> getOverdueAnalysis();
    
    /**
     * 获取回收率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 回收率统计
     */
    Map<String, Object> getRecoveryRateStatistics(String startDate, String endDate);
}
