<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.TaLim" table="ta_lim" schema="dbo" >
        <id name="taLimId" type="java.lang.Long">
            <column name="ta_lim_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salTask" class="com.frsoft.base.entity.SalTask" fetch="select" not-null="false">
            <column name="ta_task_id" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="ta_se_no"/>
        </many-to-one>
        <property name="taIsdel" type="java.lang.String">
            <column name="ta_isdel" length="1" />
        </property>
        <property name="taFinDate" type="java.util.Date">
            <column name="ta_fin_date" length="23" />
        </property>
         <property name="taIsfin" type="java.lang.String">
            <column name="ta_isfin" length="1" />
        </property>
        <property name="taDesc" type="java.lang.String">
            <column name="ta_desc" length="1073741823" />
        </property>
        <property name="taName" type="java.lang.String">
            <column name="ta_name" length="50" />
        </property>
          <set name="attachments" inverse="true"  order-by="att_date desc" cascade="all" where="att_type='ta'">
            <key>
                <column name="att_fk_id"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
    </class>
</hibernate-mapping>
