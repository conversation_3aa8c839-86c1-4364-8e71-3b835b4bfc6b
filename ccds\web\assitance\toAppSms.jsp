<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
int count=0,count1=0;
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>    
    <title>申请短信</title>
    <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	 <script type="text/javascript" src="js/common.js"></script>
	 <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">       
		function check(){
			var errStr = "";
		 	if(isEmpty("content")){
				errStr+="- 未填写相应的内容！\n";
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				waitSubmit("dosend","提交中...");
				waitSubmit("doCancel");
				return $("create").submit();			  
			}
		}
		
		window.onload=function(){
			loadPopWinHeight(285);
		}
  </script> 
</head>
  <body>
  <div class="inputDiv">
  	<form action="assitanceAction.do" method="post" name="create">
  	<input type="hidden" name="op" value="saveAss">
  	<input type="hidden" name="caseId" value="${caseId}">
  	<input type="hidden" name="typeId" value="10">
  	<input type="hidden" name="assId" value="${assId}">
		<table class="dashTab inputForm" cellpadding="0" cellspacing="0">
			<tbody>
            	<tr>
                	<th>发送号码：</th>
                    <td>
                    <c:if test="${!empty phoneLists}"><ul class="scrollBarNoStyle" style="height:100px; width:440px; margin:0;padding:0;"><c:forEach items="${phoneLists}" var="pho"><li><!--<input type="checkbox" name="phlIds" value="${pho.phlId}" id="phlCB${pho.phlId}"/>--><input type="checkbox" name="phlNums" value="${pho.phlNum}" id="phlCB${pho.phlId}"/><label for="phlCB${pho.phlId}">${pho.phlName}&nbsp;-&nbsp;${pho.phlNum}</label></li></c:forEach></ul></c:if>
                    <c:if test="${empty phoneLists}"><textarea class="inputSize2L" rows="5" name="phlNums" onBlur="autoShort(this,1000)"><c:out value="${assPhones}" /></textarea></c:if>
                    </td>
                </tr>
            	<tr class="noBorderBot">
                	<th>短信内容：<span class='red'>*</span></th>
                    <td><textarea class="inputSize2L" rows="5" name="content" id="content" onBlur="autoShort(this,2000)"><c:out value="${appContext}" /></textarea></td>
               </tr>
                <tr class="submitTr">
                    <td colspan="2">
                    <input type="button" class="butSize1" id="dosend" value="提交" onClick="check()">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"></td>
                </tr>	
            </tbody>
					
	  </table>
	</form>
  </div>
  </body>
</html>
