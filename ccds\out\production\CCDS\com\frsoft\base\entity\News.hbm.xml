<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.News" table="news" schema="dbo">
        <id name="newCode" type="java.lang.Long">
            <column name="new_code"/>
            <generator class="identity"/>
        </id>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select"  not-null="false">
            <column name="new_se_no"/>
        </many-to-one>
        <property name="newTitle" type="java.lang.String">
            <column name="new_title" length="100" />
        </property>
        <property name="newType" type="java.lang.String">
            <column name="new_type" length="50" />
        </property>
         <property name="newInsUser" type="java.lang.String">
            <column name="new_ins_user" length="50" />
        </property>
         <property name="newUpdUser" type="java.lang.String">
            <column name="new_upd_user" length="50" />
        </property>
         <property name="newUpdDate" type="java.util.Date">
            <column name="new_upd_date" length="23" />
        </property>
        <property name="newContent" type="java.lang.String">
            <column name="new_content" length="1073741823" />
        </property>
        <property name="newIstop" type="java.lang.String">
            <column name="new_istop" length="1" />
        </property>
        <property name="newDate" type="java.util.Date">
            <column name="new_date" length="23" />
        </property>
        <set name="RNewLims" inverse="true" cascade="all" >
            <key>
                <column name="rnl_new_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.base.entity.RNewLim" />
        </set>
        <set name="attachments" inverse="true"  cascade="all" where="att_type='news'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
    </set>
        
    </class>
</hibernate-mapping>
