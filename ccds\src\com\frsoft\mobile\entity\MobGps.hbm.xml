<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.mobile.entity.MobGps" table="mob_gps" schema="dbo" >
        <id name="mgpsId" type="java.lang.Long">
            <column name="mgps_id" />
            <generator class="identity" />
        </id>
        <property name="mgpsMacAddr" type="java.lang.String">
            <column name="mgps_mac_addr" length="50" not-null="true" />
        </property>
        <property name="mgpsDeviceModel" type="java.lang.String">
            <column name="mgps_device_model" length="100" />
        </property>
        <property name="mgpsVerName" type="java.lang.String">
            <column name="mgps_ver_name" length="50" />
        </property>
        <property name="mgpsTime" type="java.util.Date">
            <column name="mgps_time" length="23" not-null="true" />
        </property>
        <property name="mgpsLatitude" type="java.lang.String">
            <column name="mgps_latitude" length="50" not-null="true" />
        </property>
        <property name="mgpsLongitude" type="java.lang.String">
            <column name="mgps_longitude" length="50" not-null="true" />
        </property>
        <property name="mgpsAltitude" type="java.lang.String">
            <column name="mgps_altitude" length="50" />
        </property>
        <property name="mgpsAccuracy" type="java.lang.String">
            <column name="mgps_accuracy" length="50" />
        </property>
        <property name="mgpsBearing" type="java.lang.String">
            <column name="mgps_bearing" length="50" />
        </property>
        <property name="mgpsSpeed" type="java.lang.String">
            <column name="mgps_speed" length="50" />
        </property>
        <property name="mgpsProvider" type="java.lang.String">
            <column name="mgps_provider" length="50" />
        </property>
    </class>
</hibernate-mapping>
