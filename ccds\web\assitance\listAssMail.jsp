<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<base href="<%=basePath%>"></base>

		<title>信函管理</title>
		<link rel="shortcut icon" href="favicon.ico" />
		<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
        <meta http-equiv="pragma" content="no-cache">
        <meta http-equiv="cache-control" content="no-cache">
        <meta http-equiv="expires" content="0">
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
        <style type="text/css">
        .inputSize2 {
			width:100px;
		}
        </style>
		<script type="text/javascript" src="js/prototype.js"></script>
		<script type="text/javascript" src="js/common.js"></script>
        <script type="text/javascript" src="js/caseAss.js"></script>
		<script type="text/javascript" src="js/formCheck.js"></script>
		<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
        <script type="text/javascript" src="js/config.js"></script>
		<script language="javaScript" type="text/javascript">
		var assType=1;
		
		function backMail(chId){
			if(confirm("确定已退信？")){
				var url ="assitanceAction.do" ;
				var pars = "op=backMail&chId="+chId ;
				new Ajax.Request(url,{
					method : 'post',
					parameters : pars,
					onSuccess : function(transport){
						var resp = transport.responseText;
						if(resp=="1"){
							loadList();
						}
					},
					onfailure : function(response){
						if (transport.status == 404)
							alert("您访问的url地址不存在！");
						else
							alert("Error: status code is " + transport.status);
					}
				});
			}
		}
		
		function toBatchAssRes(){
			if(checkBoxIsEmpty("priKey")){
				var assIds = getBacthIds("-",false)[0];
				if(assIds!=''){
					openPostWindow("assitanceAction.do",[["op","toBatchAssRes"],["assIds",assIds.substring(0,assIds.length-1)],["assType",assType]],"conAssWin");
				}
			}
		}
		
		function outCaseAss(){
			/*var args = ["${state}",assType, $("bankName").value,
					$("caseName").value, $("startTime").value, $("endTime").value,
					$("caseCode").value, $("batCode").value ];*/
			assPopDiv(5);
		}
		
		function outAssMail(){
			if(checkBoxIsEmpty("priKey")){
				assPopDiv(6);
			}
		}
		
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			var caseClass="", casCode="", casName="",casM="",casPaidM="";
			if(obj.bankCase){
				dataId = obj.chId+"-"+obj.bankCase.casId+"-"+obj.bankCase.casState;
				var relFunc = "descPop('caseAction.do?op=caseDesc&caseId="+obj.bankCase.casId+"&view=ass')";
				caseClass = getCaseColor(obj.bankCase);
				casCode = "<a href=\"javascript:void(0)\" "+ ((caseClass!="")?("class=\""+caseClass+"\""):"") +" onclick=\""+relFunc+";return false;\">"+obj.bankCase.casCode+"&nbsp;</a>";
				casName = obj.bankCase.casName;
				casM = obj.bankCase.casM;
				casPaidM = obj.bankCase.casPaidM;
			}
			var funcCol = "";
			if("${state}"=="0"){
				funcCol = "<img src=\"images/content/execute.gif\" class=\"hand\" alt=\"完成发信\"onClick=\"assPopDiv(1,['"+obj.chId+"','"+obj.chTyp+"'])\"/>";
			}
			else if("${state}"=="-2"){
				funcCol = "<img src=\"images/content/approve.gif\" class=\"hand\" alt=\"同意信函申请\"onClick=\"audAss('"+obj.chId+"')\"/>";
			}
			else{
				funcCol = "<img src=\"images/content/cancel.gif\" class=\"hand\" alt=\"退信\"onClick=\"backMail('"+obj.chId+"')\"/>";
			}
			datas = [obj.chId,obj.bankCase.typeList?obj.bankCase.typeList.typName:"新案", casCode, casName, casM, casPaidM, (obj.chMsgState==-1?"<span class='red bold'>[已退信]</span>":"")+(obj.chAdr!=''?obj.chAdr:(obj.chAddress?obj.chAddress.adrAdd:"")), obj.chAdr?(obj.chAddress.adrMailCount?obj.chAddress.adrMailCount:0):'', obj.chText, obj.chCat1, obj.chCat2, obj.chContUser,obj.chAppTime, obj.chAppUser, obj.chSurTime, obj.chSurUser, obj.chRes, funcCol];
			return [datas,className,dblFunc,dataId];
		}
		function getFormArgs(){
			var pars;
			pars = $("searchForm").serialize(true);
			pars.op = "listCaseAss";
			pars.state="${state}";
			return pars;
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "assitanceAction.do";
			var pars = getFormArgs();
			
			var loadFunc = "loadList";
			var cols = [
						{name:"ID"},
						{name:"催收状态"},
						{name:"个案序列号",align:"left"},
						{name:getCasNameTxt("${CUS_VER_ID}")},
						{name:"委案金额",align:"right",renderer:"money"},
						{name:"还款金额",align:"right",renderer:"money"},
						{name:"地址",align:"left"},
						{name:"信函次数"},
						{name:"申请内容",align:"left"},
						{name:"地址类别"},
						{name:"信函模板"},
						{name:"联系人"},
						{name:"申请时间",renderer:"time"},
						{name:"申请人"},
						{name:"协催时间",renderer:"time"},
						{name:"协催人"},
						{name:"协催内容",align:"left"},
						{name:"操作",isSort:false}
						];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
    	var gridEl = new MGrid("listAssMailTab","dataList");
    	gridEl.config.hasCheckBox = true;
	    createProgressBar();
     	window.onload=function(){
			$("casNameTxt").innerHTML=getCasNameTxt("${CUS_VER_ID}");
			$("bankTxt").innerHTML=getBankTxt("${CUS_VER_ID}");
			loadCaseState('caseState');
			$("caseState").value="0";//默认正常状态

			loadList();
			//增加清空按钮
			createCancelButton(loadList,'searchForm',-50,5,'searButton','after');
			closeProgressBar();
		}

  </script>
</head>


  <body>
 <div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>协催管理 > <c:if test="${state==0||state==-2}">待处理信函</c:if><c:if test="${state==1}">信函记录</c:if> <span id="changeFuncBt" onMouseOver="popFuncMenu(['caseAss',<c:if test="${state==0||state==-2}">0</c:if><c:if test="${state==1}">1</c:if>,],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['caseAss',<c:if test="${state==0||state==-2}">0</c:if><c:if test="${state==1}">1</c:if>],true)" onMouseOut="popFuncMenu(['caseAss',<c:if test="${state==0||state==-2}">0</c:if><c:if test="${state==1}">1</c:if>],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
 			</div>
          		<table class="mainTab" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>
                            <div id="tabType">
                            	<c:if test="${state!=1}">
                                <div id="tabType1" onClick="self.location.href='assitanceAction.do?op=toListCaseAss&state=-2&dfType=1'">信函申请</div><div id="tabType2" onClick="self.location.href='assitanceAction.do?op=toListCaseAss&state=0&dfType=1'">待发信函</div>
                                <script type="text/javascript">loadListTab("${state}",["-2","0"]);</script>
                                </c:if>
                                <c:if test="${state==1}">
                                <div id="tabType1" class="tabTypeWhite" onClick="self.location.href='assitanceAction.do?op=toListCaseAss&state=1&dfType=1'">信函记录</div>
                                </c:if>
                            </div>
                        </th>
                        <td>
                            <a href="javascript:void(0)" onClick="assPopDiv(7);return false;" class="newBlueButton">导入协催记录</a>
                        </td>
                    </tr>
            	</table>
				<script type="text/javascript">loadTabTypeWidth();</script>
				<div id="listContent">  
                	<div class="listSearch" id="listSearch">
                    	<form style="margin:0; padding:0;" id="searchForm"  class="listSearchForm" onSubmit="loadList();return false;">
                        	<input type="hidden" id="assType" name="assType" value="1"/>
                           	<!--部门：<select id="org" name="org" class="inputSize2 inputBoxAlign"><c:if test="${!empty orgList}"><c:forEach items="${orgList}" var="oList"><option value="${oList.soCode}">${oList.soName}</option></c:forEach></c:if></select>&nbsp;&nbsp;-->
                          	<table cellpadding="0" cellspacing="0">
                            	<tr>
                           			<th>催收区域：</th>
                                    <td><select name='clArea' class="inputSize2 inputBoxAlign">
                                       <c:if test="${!empty userAreaList}">
                                            <option value="<c:forEach items="${userAreaList}" var="uArea">${uArea.uarArea.typId},</c:forEach>">全部</option><c:forEach items="${userAreaList}" var="userArea"><option value="${userArea.uarArea.typId}">${userArea.uarArea.typName}</option></c:forEach>
                                        </c:if>
                                        <c:if test="${empty userAreaList}">
                                            <c:if test="${!empty areaList}">
                                                <option value="">全部</option><c:forEach items="${areaList}" var="area"><option value="${area.typId}">${area.typName}</option></c:forEach>
                                            </c:if>
                                            <c:if test="${empty areaList}">
                                                <option value="">未添加</option>
                                            </c:if>
                                        </c:if>
                                        </select>
                                    </td>
                                    <th><span id="bankTxt"></span>：</th>
                                    <td><input style="width:90px;" class="inputSize2 inputBoxAlign" type="text" id="bankName" name="bankName" onBlur="autoShort(this,100)"/><c:if test="${!empty bankList}">
                                       <select class="inputSize2 inputBoxAlign" style="width:70px" onChange="setValueFromSel(this,'bankName')">
                                            <option value="">请选择</option>
                                            <c:forEach items="${bankList}" var="bList">
                                            <option value="${bList.typName}">${bList.typName}</option>
                                            </c:forEach>
                                        </select>
                                        </c:if>
                                        <c:if test="${empty bankList}">
                                            <select class="inputSize2 inputBoxAlign" disabled="disabled" style="width:70px">
                                                <option>未添加</option>
                                            </select>
                                        </c:if>
                                    </td>
                                    <th>批次号：</th>
                                    <td colspan="3"><input class="inputSize2  inputBoxAlign" style="width:300px" type="text" id="batCodeStr" name="batCode" onDblClick="cleanBatCodeInput()"/><input type="text" style="display:none" id="batIds" name="batIds"/>&nbsp;<button class="butSize2  inputBoxAlign" onClick="showBatList()" style="width:40px">选择</button><!--<input class="inputSize2 inputBoxAlign" type="text" id="batCode" name="batCode" onBlur="autoShort(this,100)" style="width:120px"/>&nbsp;<button class="butSize2 inputBoxAlign" onClick="addDivBrow(22)" style="width:40px">选择</button>--></td>
                           			<th>个案序列号：</th>
                                    <td><input class="inputSize2 inputBoxAlign" type="text" name="caseCode" onBlur="autoShort(this,100)"/></td>
                                </tr>
                                <tr>
                                	<th>信函模板：</th>
                                    <td><c:if test="${!empty templateList}"><select name="tmpName" class="inputSize2 inputBoxAlign">
                                            <option value="">全部</option>
                                            <c:forEach items="${templateList}" var="t">
                                            <option value="${t.tmpName}">${t.tmpName}</option>
                                            </c:forEach>
                                        </select>
                                        </c:if><c:if test="${empty templateList}"><select class="inputSize2 inputBoxAlign" disabled="disabled">
                                                <option>未添加模板</option>
                                            </select>
                                        </c:if></td>
                                    <th>委案金额：</th>
                                    <td><input class="inputSize2 inputBoxAlign" style="width:76px" type="text" name="casMMin" onBlur="checkIsNum(this)"/>&nbsp;-&nbsp;<input class="inputSize2 inputBoxAlign" style="width:76px" type="text" name="casMMax" onBlur="checkIsNum(this)"/></td>
                                    <th><span id="casNameTxt"></span>：</th>
                                    <td><input  style="width:90px" class="inputSize2 inputBoxAlign" type="text" name="caseName" onBlur="autoShort(this,50)"/></td>
                                    <th>申请时间：</th>
                                    <td><input name="startTime" id="staTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('endTim');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'endTim\')}'})"/>&nbsp;到&nbsp;<input name="endTime" id="endTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'staTim\')}'})"/></td>
                                    <th>申请人：</th>
                                    <td><input class="inputSize2 inputBoxAlign" type="text" name="appMan" onBlur="autoShort(this,20)"/></td>
                                </tr>
                                <tr>
                                	<th>案件状态：</th>
	                            	<td><select  id="caseState" name="casState" class="inputSize2"><option value="u">未退案</option></select></td>
                                    <td colspan=2>&nbsp;&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize3 inputBoxAlign" value="查询"/></td>
                                </tr>
                            </table>
                        </form>
                    </div>
                    <div id="toolsBarTop" class="bottomBar">
                    	<c:if test="${state==0}">
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="toBatchAssRes()">批量确认信函</span>
                        </c:if>
                       	<c:if test="${state==-2}">
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="toBatchPass()">同意协催</span>
                        </c:if>
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="cancelBatchAss(1,'信函')">撤销信函</span>
                        <c:if test="${state!=-2}">
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="outAssMail()">导出信函</span>
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="outCaseAss()">导出查询结果</span>
                        </c:if>
                    </div>
	         		<div id="dataList" class="dataList"></div>
				</div>
			</div>
		</div>
	</body>
</html>
