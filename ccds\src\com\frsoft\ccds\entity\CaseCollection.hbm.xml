<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.CaseCollection" table="case_collection" schema="dbo" >
        <id name="ccId" type="java.lang.Long">
            <column name="cc_id" />
            <generator class="identity" />
        </id>
        <property name="ccCasIds" type="java.lang.String">
            <column name="cc_cas_ids" length="1073741823" />
        </property>
        <property name="ccIdNo" type="java.lang.String">
            <column name="cc_id_no" length="50" />
        </property>
        <many-to-one name="ccCbat" class="com.frsoft.ccds.entity.CaseBat" fetch="select" not-null="false">
            <column name="cc_cbat_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
