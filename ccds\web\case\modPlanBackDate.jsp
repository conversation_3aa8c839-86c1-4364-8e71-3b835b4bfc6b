<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic" prefix="logic"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>修改预计退案日</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="cache-control" content="no-cache"/>
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
    	body{
			background-color:#fff;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>  
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
    	window.onload=function(){
			dateInit("planBackDate","${bankCase.casBackdateP}");
		}
    </script>
  </head>
  
  <body>
    <form action="caseAction.do" method="post">  
        <input type="hidden" name="op" value="modPlanBackDate" />
    	<input type="hidden" name="casId" value="${bankCase.casId}" />
        <table class="dashTab inputForm single" cellpadding="0" cellspacing="0">
        	<tbody>
           	<tr class="noBorderBot">
               	<th>预计退案日：</th>
               	<td><input type="text" id="planBackDate" name="planBackDate" readonly class="inputSize2 Wdate" style="cursor:hand" value="" ondblClick="clearInput(this)" onClick="WdatePicker({skin:'default',dateFmt:'yyyy-MM-dd'})"/></td>
            </tr>
           	<tr class="submitTr">
           		<td colspan="2">
                	<input id="caseSave" class="butSize1" type="submit" value="保存" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()"/>
                </td>
          	</tr>
           </tbody>
        </table>
	  	</form>
	</body>
</html>
