<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="ccds-main" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="ccds-common" target="1.8" />
      <module name="ccds-core" target="1.8" />
      <module name="ccds-framework" target="1.8" />
      <module name="ccds-system" target="1.8" />
      <module name="ccds-web" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ccds-common" options="" />
      <module name="ccds-core" options="" />
      <module name="ccds-framework" options="" />
      <module name="ccds-web" options="" />
    </option>
  </component>
</project>