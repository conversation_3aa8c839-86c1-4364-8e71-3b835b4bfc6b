<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.law.entity.LawPaid" table="law_paid" schema="dbo" >
        <id name="lwpaId" type="java.lang.Long">
            <column name="lwpa_id" />
            <generator class="identity" />
        </id>
        <property name="lwpaAmt" type="java.lang.Double">
            <column name="lwpa_amt" precision="18" />
        </property>
        <property name="lwpaDate" type="java.util.Date">
            <column name="lwpa_date" length="23" />
        </property>
        <property name="lwpaName" type="java.lang.String">
            <column name="lwpa_name" length="100" />
        </property>
        <property name="lwpaMan" type="java.lang.String">
            <column name="lwpa_man" length="50" />
        </property>
        <property name="lwpaPayMed" type="java.lang.Integer">
            <column name="lwpa_pay_med" />
        </property>
        <property name="lwpaFileCode" type="java.lang.String">
            <column name="lwpa_file_code" length="100" />
        </property>
        <property name="lwpaRemark" type="java.lang.String">
            <column name="lwpa_remark" length="500" />
        </property>
        <property name="lwpaCreMan" type="java.lang.String">
            <column name="lwpa_cre_man" length="50" />
        </property>
        <property name="lwpaCreTime" type="java.util.Date">
            <column name="lwpa_cre_time" length="23" />
        </property>
        <property name="lwpaUpdMan" type="java.lang.String">
            <column name="lwpa_upd_man" length="50" />
        </property>
        <property name="lwpaUpdTime" type="java.util.Date">
            <column name="lwpa_upd_time" length="23" />
        </property>
        <many-to-one name="lwpaLwc" class="com.frsoft.law.entity.LawCase">
            <column name="lwpa_lwc_id" />
        </many-to-one>
        <many-to-one name="lwpaType" class="com.frsoft.base.entity.TypeList">
            <column name="lwpa_type_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
