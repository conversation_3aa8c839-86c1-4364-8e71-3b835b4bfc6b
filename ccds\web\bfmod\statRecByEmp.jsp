<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>催收员通话量时长统计</title>
    
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/bfmod.css"/>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
	<script type="text/javascript" src="js/formCheck.js"></script>
	<script type="text/javascript" src="js/bfmod.js"></script>
  	<script language="javascript" type="text/javascript">
	  	function check(){
			var errStr = "";
			if(isEmpty("statDateStart")&&isEmpty("statDateStart")){
				errStr+="未选择录音日期！";
			}
			if(errStr!=""){
				alert(errStr);
				return false;
			}
			else{
				loadList();
			}
	   	}    
  	
  		function initPage(){
  			var menuItems = $("statTopMenuBar").childNodes;
  			menuItems[1].className = "statTopMenuCur";
  			for(var i=0; i<menuItems.length; i++){
  				if(menuItems[i].className != "statTopMenuCur"){
  	  				menuItems[i].className="statTopMenu";
  	  				menuItems[i].onmouseover=function(){
  	  					this.className='statTopMenuOver';
  	  				}
  	  				menuItems[i].onmouseout=function(){
  	  					this.className='statTopMenu';
  	  				}
  				}
  			}
  			
  		}
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			datas = [obj.empName,obj.call1,obj.validCall1,formatSecond(obj.duration1),formatSecond(obj.validDuration1),
			         	obj.call2,obj.validCall2,formatSecond(obj.duration2),formatSecond(obj.validDuration2),
			         	obj.call3,obj.validCall3,formatSecond(obj.duration3),formatSecond(obj.validDuration3),
			         	obj.call4,obj.validCall4,formatSecond(obj.duration4),formatSecond(obj.validDuration4),
			         	obj.totalCall,obj.totalValidCall,formatSecond(obj.totalDuration),formatSecond(obj.totalValidDuration) ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(){
			var url = "soundRecordAction.do";
			var pars =  $("searchForm").serialize(true);
			pars.op="statRecByEmp";
			var loadFunc = "loadList";
			var cols=[];
			gridEl.init(url,pars,cols,loadFunc);
			gridEl.loadData(dataMapper,columnCreator,errInfoCallBack);
		}
		function columnCreator(jsonData,colArr){
			var dateCol = [];
			dateCol.push("<tr><th rowspan='2' style='height:50px; width:4%;'>催收员</th>");
			dateCol.push("<th colspan='4' style='width:18%;'>8:00前</th>");
			dateCol.push("<th colspan='4' style='width:18%;'>8:00-12:00</th>");
			dateCol.push("<th colspan='4' style='width:18%;'>12:00-18:00</th>");
			dateCol.push("<th colspan='4' style='width:18%;'>18:00以后</th>");
			colArr.push({html:dateCol.join("")});

			colArr.push({html:"<th rowspan='2' style='width:5%;'>总通话量</th>"});
			colArr.push({html:"<th rowspan='2' style='width:5%;'>总有效通话量</th>"});
			colArr.push({html:"<th rowspan='2' style='width:7%;'>总通话时长</th>"});
			colArr.push({html:"<th rowspan='2' style='width:7%;'>总有效通话时长</th></tr><tr>"});
			
			for(var j=0; j<16; j++){
				if(j%4==0){
					colArr.push({html:"<th style='width:4%;'>通话量</th>"});
				}
				else if(j%4==1){
					colArr.push({html:"<th style='width:4%;'>有效通话量</th>"});
				}
				else if(j%4==2){
					colArr.push({html:"<th style='width:5%;'>通话时长</th>"});
				}
				else{
					colArr.push({html:"<th style='width:5%;'>有效通话时长</th>"});
				}
			}
		}
		
    	var gridEl = new MGrid("statRecByEmpTab","dataList");
		gridEl.config.hasPage = false;
		gridEl.config.sortable = false;
		gridEl.config.isShort = false;
		gridEl.config.isResize = false;
		gridEl.config.listType = "simpleStat";
		gridEl.config.className = "noBr";
		createProgressBar();
		window.onload=function(){
			initPage();
			closeProgressBar();
		} 
  	</script>
  </head>
  
  <body>
    <div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>录音管理 > 通话统计 <span id="changeFuncBt" onMouseOver="popFuncMenu(['rec',1],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['rec',1],true)" onMouseOut="popFuncMenu(['rec',1],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
           	</div>
  	   		<table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                             <div id="tabType1" class="tabTypeWhite" onClick="self.location.href='soundRecordAction.do?op=toStatRecByChannel'">通话统计</div>
                        </div>
                     </th>
                </tr>
            </table>
            <script type="text/javascript">loadTabTypeWidth();</script>
            <div id="listContent">
            	<div id="statTopMenuBar"><span onclick="self.location.href='soundRecordAction.do?op=toStatRecByChannel'">通道通电量统计</span><span onclick="self.location.href='soundRecordAction.do?op=toStatRecByEmp'">催收员通电量时长统计</span></div>
            	<c:if test="${empty ctisList}">
			   		<div class="grayBack" style="padding:10px; font-size:14px">未添加录音服务器，请在<a href="ctiServerAction.do?op=toListCtis">服务器设置</a>中添加</div>
			    </c:if>
			    <c:if test="${!empty ctisList}">
			  	<div class="listSearch">
			  		<form class="listSearchForm" id="searchForm" onSubmit="check();return false;" >
                    <table cellpadding="0" cellspacing="0">
                    	<tr>
                    		<th>&nbsp;<b>选择录音服务器：</b></th>
                    		<td><select id="serverId" name="serverId" class="inputSize2"><c:forEach items="${ctisList}" var="ctis"><option value="${ctis.ctisId}">${ctis.ctisName}</option></c:forEach></select>
                    		<c:forEach items="${ctisList}" var="ctish">
                    		<input type="hidden" id="ctiServer${ctish.ctisId}" value="${ctish.ctisIp}"/>
                    		</c:forEach>
                    		</td>
                    	</tr>
                    	<tr>	
                    		<th>部门：</th>
                       		<td><select id="org" name="orgCode" class="inputSize2 inputBoxAlign"  onChange="loadOrgEmp(this)"><option></option><c:if test="${!empty orgList}"><c:forEach items="${orgList}" var="oList"><option value="${oList.soCode}">${oList.soName}</option></c:forEach></c:if></select></td>
                           	<th>催收员：</th>
                            <td><input style="width:60px;" class="inputSize2 inputBoxAlign" type="text" id="empName" name="empName" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty empList}"><select id="empSel" class="inputSize2 inputBoxAlign" style="width:70px" onChange="setEmpNameFromSel(this,'empName')"><option value="">请选择</option><c:forEach items="${empList}" var="eList"><option value="${eList.seName}">${eList.seName}</option></c:forEach></select></c:if><c:if test="${empty empList}"><select class="inputSize2 inputBoxAlign" style="width:70px" disabled="disabled"><option>未添加</option></select></c:if></td>
                    		<th>录音日期：</th>
                            <td><input name="statDateStart" id="statDateStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({onpicked:function(){$('statDateEnd').focus();},maxDate:'#F{$dp.$D(\'statDateEnd\')}'})"/>&nbsp;到&nbsp;<input name="statDateEnd" id="statDateEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'statDateStart\')}'})"/>
                       		&nbsp;&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize1 inputBoxAlign" value="查询"/></td>
                         </tr>
                     </table>
                    </form>
			   	</div>
			  	<div id="errMsgLayer" class="redWarn" style="display:none; margin:0;"></div>
                <div id="dataList" class="dataList"></div>	
                </c:if>
            </div>
  		</div> 
	</div>
  </body>
  
</html>
