package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 员工实体类
 * 对应数据库表: sal_emp
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class SalEmp {
    
    /** 员工编号 */
    private Long seNo;
    
    /** 销售组织编码 */
    private String seSoCode;
    
    /** 员工姓名 */
    private String seName;
    
    /** 身份证号 */
    private String seIdeCode;
    
    /** 职位 */
    private String sePos;
    
    /** 性别 */
    private String seSex;
    
    /** 试用期 */
    private String seProb;
    
    /** 出生地 */
    private String seBirPlace;
    
    /** 户口所在地 */
    private String seAccPlace;
    
    /** 出生日期 */
    private String seBirth;
    
    /** 婚姻状况 */
    private String seMarry;
    
    /** 员工类型 */
    private String seType;
    
    /** 职级 */
    private Long seJobLev;
    
    /** 职位类别 */
    private String seJobCate;
    
    /** 职称 */
    private String seJobTitle;
    
    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seStartDay;
    
    /** 年薪 */
    private String seYearPay;
    
    /** 成本中心 */
    private String seCostCenter;
    
    /** 邮箱 */
    private String seEmail;
    
    /** 民族 */
    private String seNation;
    
    /** 政治面貌 */
    private String sePoliStatus;
    
    /** 学历 */
    private String seEdu;
    
    /** 电话 */
    private String seTel;
    
    /** 手机 */
    private String sePhone;
    
    /** QQ */
    private String seQq;
    
    /** MSN */
    private String seMsn;
    
    /** 招聘来源 */
    private String seRecSource;
    
    /** 公积金 */
    private String seProvFund;
    
    /** 工作日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seJobDate;
    
    /** 户口登记 */
    private String seHouReg;
    
    /** 社保编码 */
    private String seSocialCode;
    
    /** 推荐人 */
    private String seRap;
    
    /** 地址 */
    private String seAddress;
    
    /** 备注 */
    private String seRemark;
    
    /** 银行名称 */
    private String seBankName;
    
    /** 银行卡号 */
    private String seBankCard;
    
    /** 福利地址 */
    private String seWealAddress;
    
    /** 福利职位 */
    private String seWealPos;
    
    /** 是否加班 */
    private String seIsovertime;
    
    /** 考勤 */
    private String seAttendance;
    
    /** 卡号 */
    private String seCardNum;
    
    /** 照片 */
    private String sePic;
    
    /** 是否启用 */
    private String seIsenabled;
    
    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seInserDate;
    
    /** 员工编码 */
    private String seCode;
    
    /** 日志 */
    private String seLog;
    
    /** 修改日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seAltDate;
    
    /** 创建用户 */
    private String seInserUser;
    
    /** 修改用户 */
    private String seAltUser;
    
    /** 离职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seEndDate;
    
    /** 教育背景 */
    private String seEdcBac;
    
    /** 工作经历 */
    private String seWorkEx;
    
    /** 用户编码 */
    private String seUserCode;
    
    /** 个人电话 */
    private String sePerTel;
    
    /** 计划签约日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sePlanSignDate;
    
    /** 签约日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seSignDate;
    
    /** 信用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date seCreditDate;
    
    /** 毕业院校 */
    private String seCollege;
    
    /** 调动记录 */
    private String seTransfer;
    
    // 关联对象
    /** 销售组织 */
    private SalOrg salOrg;
    
    /** 角色信息 */
    private LimRole limRole;
}
