<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.ProActor" table="pro_actor" schema="dbo" >
        <id name="actId" type="java.lang.Long">
            <column name="act_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="act_se_no"/>
        </many-to-one>
        <many-to-one name="project" class="com.psit.struts.entity.Project" fetch="select" not-null="false">
            <column name="act_pro_id" />
        </many-to-one>
        <property name="actIsdel" type="java.lang.String">
            <column name="act_isdel" length="1" />
        </property>
        <property name="actDuty" type="java.lang.String">
            <column name="act_duty" length="100" />
        </property>
    </class>
</hibernate-mapping>
