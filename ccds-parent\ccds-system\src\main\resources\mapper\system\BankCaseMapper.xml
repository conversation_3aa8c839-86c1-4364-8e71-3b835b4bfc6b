<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frsoft.ccds.system.mapper.BankCaseMapper">
    
    <resultMap type="BankCase" id="BankCaseResult">
        <result property="casId"                column="cas_id"                />
        <result property="casCode"              column="cas_code"              />
        <result property="casGroup"             column="cas_group"             />
        <result property="casState"             column="cas_state"             />
        <result property="casTypHid"            column="cas_typ_hid"           />
        <result property="casName"              column="cas_name"              />
        <result property="casPhone"             column="cas_phone"             />
        <result property="casM"                 column="cas_m"                 />
        <result property="casPaidM"             column="cas_paid_m"            />
        <result property="casSeNo"              column="cas_se_no"             />
        <result property="casInsTime"           column="cas_ins_time"          />
        <result property="casInsUser"           column="cas_ins_user"          />
        <result property="casAltTime"           column="cas_alt_time"          />
        <result property="casAltUser"           column="cas_alt_user"          />
        <result property="casRemark"            column="cas_remark"            />
        <result property="casIdNo"              column="cas_id_no"             />
        <result property="casAddress"           column="cas_address"           />
        <result property="casWorkAddress"       column="cas_work_address"      />
        <result property="casWorkPhone"         column="cas_work_phone"        />
        <result property="casEmail"             column="cas_email"             />
        <result property="casBank"              column="cas_bank"              />
        <result property="casCardNo"            column="cas_card_no"           />
        <result property="casOverdueDate"       column="cas_overdue_date"      />
        <result property="casPbackP"            column="cas_pback_p"           />
        <result property="casWpostCode"         column="cas_wpost_code"        />
        <result property="casDeadline"          column="cas_deadline"          />
        <result property="casIsHost"            column="cas_is_host"           />
        <result property="casBillDate"          column="cas_bill_date"         />
        <result property="casLastPaid"          column="cas_last_paid"         />
        <result property="casCount"             column="cas_count"             />
        <result property="casLeftPri"           column="cas_left_pri"          />
        <result property="casAssignIds"         column="cas_assign_ids"        />
        <result property="casAssignNames"       column="cas_assign_names"      />
        <result property="casLastAssignTime"    column="cas_last_assign_time"  />
        <result property="casOverdueDays"       column="cas_overdue_days"      />
        <result property="casOverdueDaysStr"    column="cas_overdue_days_str"  />
        <result property="casBir"               column="cas_bir"               />
        <result property="casMpostCode"         column="cas_mpost_code"        />
        <result property="casPermCrline"        column="cas_perm_crline"       />
        <result property="casAltHold"           column="cas_alt_hold"          />
    </resultMap>

    <sql id="selectBankCaseVo">
        select cas_id, cas_code, cas_group, cas_state, cas_typ_hid, cas_name, cas_phone, cas_m, cas_paid_m, cas_se_no, 
               cas_ins_time, cas_ins_user, cas_alt_time, cas_alt_user, cas_remark, cas_id_no, cas_address, cas_work_address, 
               cas_work_phone, cas_email, cas_bank, cas_card_no, cas_overdue_date, cas_pback_p, cas_wpost_code, cas_deadline, 
               cas_is_host, cas_bill_date, cas_last_paid, cas_count, cas_left_pri, cas_assign_ids, cas_assign_names, 
               cas_last_assign_time, cas_overdue_days, cas_overdue_days_str, cas_bir, cas_mpost_code, cas_perm_crline, cas_alt_hold 
        from bank_case
    </sql>

    <select id="selectBankCaseList" parameterType="BankCase" resultMap="BankCaseResult">
        <include refid="selectBankCaseVo"/>
        <where>  
            <if test="casCode != null  and casCode != ''"> and cas_code like concat('%', #{casCode}, '%')</if>
            <if test="casGroup != null  and casGroup != ''"> and cas_group = #{casGroup}</if>
            <if test="casState != null "> and cas_state = #{casState}</if>
            <if test="casTypHid != null "> and cas_typ_hid = #{casTypHid}</if>
            <if test="casName != null  and casName != ''"> and cas_name like concat('%', #{casName}, '%')</if>
            <if test="casPhone != null  and casPhone != ''"> and cas_phone like concat('%', #{casPhone}, '%')</if>
            <if test="casSeNo != null "> and cas_se_no = #{casSeNo}</if>
            <if test="casInsUser != null  and casInsUser != ''"> and cas_ins_user = #{casInsUser}</if>
            <if test="casIdNo != null  and casIdNo != ''"> and cas_id_no like concat('%', #{casIdNo}, '%')</if>
            <if test="casBank != null  and casBank != ''"> and cas_bank like concat('%', #{casBank}, '%')</if>
        </where>
        order by cas_ins_time desc
    </select>
    
    <select id="selectBankCaseByCasId" parameterType="Long" resultMap="BankCaseResult">
        <include refid="selectBankCaseVo"/>
        where cas_id = #{casId}
    </select>
        
    <insert id="insertBankCase" parameterType="BankCase" useGeneratedKeys="true" keyProperty="casId">
        insert into bank_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="casCode != null">cas_code,</if>
            <if test="casGroup != null">cas_group,</if>
            <if test="casState != null">cas_state,</if>
            <if test="casTypHid != null">cas_typ_hid,</if>
            <if test="casName != null">cas_name,</if>
            <if test="casPhone != null">cas_phone,</if>
            <if test="casM != null">cas_m,</if>
            <if test="casPaidM != null">cas_paid_m,</if>
            <if test="casSeNo != null">cas_se_no,</if>
            <if test="casInsTime != null">cas_ins_time,</if>
            <if test="casInsUser != null">cas_ins_user,</if>
            <if test="casAltTime != null">cas_alt_time,</if>
            <if test="casAltUser != null">cas_alt_user,</if>
            <if test="casRemark != null">cas_remark,</if>
            <if test="casIdNo != null">cas_id_no,</if>
            <if test="casAddress != null">cas_address,</if>
            <if test="casWorkAddress != null">cas_work_address,</if>
            <if test="casWorkPhone != null">cas_work_phone,</if>
            <if test="casEmail != null">cas_email,</if>
            <if test="casBank != null">cas_bank,</if>
            <if test="casCardNo != null">cas_card_no,</if>
            <if test="casOverdueDate != null">cas_overdue_date,</if>
            <if test="casPbackP != null">cas_pback_p,</if>
            <if test="casWpostCode != null">cas_wpost_code,</if>
            <if test="casDeadline != null">cas_deadline,</if>
            <if test="casIsHost != null">cas_is_host,</if>
            <if test="casBillDate != null">cas_bill_date,</if>
            <if test="casLastPaid != null">cas_last_paid,</if>
            <if test="casCount != null">cas_count,</if>
            <if test="casLeftPri != null">cas_left_pri,</if>
            <if test="casAssignIds != null">cas_assign_ids,</if>
            <if test="casAssignNames != null">cas_assign_names,</if>
            <if test="casLastAssignTime != null">cas_last_assign_time,</if>
            <if test="casOverdueDays != null">cas_overdue_days,</if>
            <if test="casOverdueDaysStr != null">cas_overdue_days_str,</if>
            <if test="casBir != null">cas_bir,</if>
            <if test="casMpostCode != null">cas_mpost_code,</if>
            <if test="casPermCrline != null">cas_perm_crline,</if>
            <if test="casAltHold != null">cas_alt_hold,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="casCode != null">#{casCode},</if>
            <if test="casGroup != null">#{casGroup},</if>
            <if test="casState != null">#{casState},</if>
            <if test="casTypHid != null">#{casTypHid},</if>
            <if test="casName != null">#{casName},</if>
            <if test="casPhone != null">#{casPhone},</if>
            <if test="casM != null">#{casM},</if>
            <if test="casPaidM != null">#{casPaidM},</if>
            <if test="casSeNo != null">#{casSeNo},</if>
            <if test="casInsTime != null">#{casInsTime},</if>
            <if test="casInsUser != null">#{casInsUser},</if>
            <if test="casAltTime != null">#{casAltTime},</if>
            <if test="casAltUser != null">#{casAltUser},</if>
            <if test="casRemark != null">#{casRemark},</if>
            <if test="casIdNo != null">#{casIdNo},</if>
            <if test="casAddress != null">#{casAddress},</if>
            <if test="casWorkAddress != null">#{casWorkAddress},</if>
            <if test="casWorkPhone != null">#{casWorkPhone},</if>
            <if test="casEmail != null">#{casEmail},</if>
            <if test="casBank != null">#{casBank},</if>
            <if test="casCardNo != null">#{casCardNo},</if>
            <if test="casOverdueDate != null">#{casOverdueDate},</if>
            <if test="casPbackP != null">#{casPbackP},</if>
            <if test="casWpostCode != null">#{casWpostCode},</if>
            <if test="casDeadline != null">#{casDeadline},</if>
            <if test="casIsHost != null">#{casIsHost},</if>
            <if test="casBillDate != null">#{casBillDate},</if>
            <if test="casLastPaid != null">#{casLastPaid},</if>
            <if test="casCount != null">#{casCount},</if>
            <if test="casLeftPri != null">#{casLeftPri},</if>
            <if test="casAssignIds != null">#{casAssignIds},</if>
            <if test="casAssignNames != null">#{casAssignNames},</if>
            <if test="casLastAssignTime != null">#{casLastAssignTime},</if>
            <if test="casOverdueDays != null">#{casOverdueDays},</if>
            <if test="casOverdueDaysStr != null">#{casOverdueDaysStr},</if>
            <if test="casBir != null">#{casBir},</if>
            <if test="casMpostCode != null">#{casMpostCode},</if>
            <if test="casPermCrline != null">#{casPermCrline},</if>
            <if test="casAltHold != null">#{casAltHold},</if>
         </trim>
    </insert>

    <update id="updateBankCase" parameterType="BankCase">
        update bank_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="casCode != null">cas_code = #{casCode},</if>
            <if test="casGroup != null">cas_group = #{casGroup},</if>
            <if test="casState != null">cas_state = #{casState},</if>
            <if test="casTypHid != null">cas_typ_hid = #{casTypHid},</if>
            <if test="casName != null">cas_name = #{casName},</if>
            <if test="casPhone != null">cas_phone = #{casPhone},</if>
            <if test="casM != null">cas_m = #{casM},</if>
            <if test="casPaidM != null">cas_paid_m = #{casPaidM},</if>
            <if test="casSeNo != null">cas_se_no = #{casSeNo},</if>
            <if test="casInsTime != null">cas_ins_time = #{casInsTime},</if>
            <if test="casInsUser != null">cas_ins_user = #{casInsUser},</if>
            <if test="casAltTime != null">cas_alt_time = #{casAltTime},</if>
            <if test="casAltUser != null">cas_alt_user = #{casAltUser},</if>
            <if test="casRemark != null">cas_remark = #{casRemark},</if>
            <if test="casIdNo != null">cas_id_no = #{casIdNo},</if>
            <if test="casAddress != null">cas_address = #{casAddress},</if>
            <if test="casWorkAddress != null">cas_work_address = #{casWorkAddress},</if>
            <if test="casWorkPhone != null">cas_work_phone = #{casWorkPhone},</if>
            <if test="casEmail != null">cas_email = #{casEmail},</if>
            <if test="casBank != null">cas_bank = #{casBank},</if>
            <if test="casCardNo != null">cas_card_no = #{casCardNo},</if>
            <if test="casOverdueDate != null">cas_overdue_date = #{casOverdueDate},</if>
            <if test="casPbackP != null">cas_pback_p = #{casPbackP},</if>
            <if test="casWpostCode != null">cas_wpost_code = #{casWpostCode},</if>
            <if test="casDeadline != null">cas_deadline = #{casDeadline},</if>
            <if test="casIsHost != null">cas_is_host = #{casIsHost},</if>
            <if test="casBillDate != null">cas_bill_date = #{casBillDate},</if>
            <if test="casLastPaid != null">cas_last_paid = #{casLastPaid},</if>
            <if test="casCount != null">cas_count = #{casCount},</if>
            <if test="casLeftPri != null">cas_left_pri = #{casLeftPri},</if>
            <if test="casAssignIds != null">cas_assign_ids = #{casAssignIds},</if>
            <if test="casAssignNames != null">cas_assign_names = #{casAssignNames},</if>
            <if test="casLastAssignTime != null">cas_last_assign_time = #{casLastAssignTime},</if>
            <if test="casOverdueDays != null">cas_overdue_days = #{casOverdueDays},</if>
            <if test="casOverdueDaysStr != null">cas_overdue_days_str = #{casOverdueDaysStr},</if>
            <if test="casBir != null">cas_bir = #{casBir},</if>
            <if test="casMpostCode != null">cas_mpost_code = #{casMpostCode},</if>
            <if test="casPermCrline != null">cas_perm_crline = #{casPermCrline},</if>
            <if test="casAltHold != null">cas_alt_hold = #{casAltHold},</if>
        </trim>
        where cas_id = #{casId}
    </update>

    <delete id="deleteBankCaseByCasId" parameterType="Long">
        delete from bank_case where cas_id = #{casId}
    </delete>

    <delete id="deleteBankCaseByCasIds" parameterType="String">
        delete from bank_case where cas_id in 
        <foreach item="casId" collection="array" open="(" separator="," close=")">
            #{casId}
        </foreach>
    </delete>

    <select id="selectBankCaseListBySeNo" parameterType="Long" resultMap="BankCaseResult">
        <include refid="selectBankCaseVo"/>
        where cas_se_no = #{seNo}
        order by cas_ins_time desc
    </select>

    <select id="selectBankCaseListByState" parameterType="Integer" resultMap="BankCaseResult">
        <include refid="selectBankCaseVo"/>
        where cas_state = #{casState}
        order by cas_ins_time desc
    </select>

    <select id="selectBankCaseListByName" parameterType="String" resultMap="BankCaseResult">
        <include refid="selectBankCaseVo"/>
        where cas_name like concat('%', #{casName}, '%')
        order by cas_ins_time desc
    </select>

    <select id="selectBankCaseListByPhone" parameterType="String" resultMap="BankCaseResult">
        <include refid="selectBankCaseVo"/>
        where cas_phone like concat('%', #{casPhone}, '%')
        order by cas_ins_time desc
    </select>

    <select id="countBankCase" parameterType="BankCase" resultType="int">
        select count(*) from bank_case
        <where>  
            <if test="casCode != null  and casCode != ''"> and cas_code like concat('%', #{casCode}, '%')</if>
            <if test="casGroup != null  and casGroup != ''"> and cas_group = #{casGroup}</if>
            <if test="casState != null "> and cas_state = #{casState}</if>
            <if test="casTypHid != null "> and cas_typ_hid = #{casTypHid}</if>
            <if test="casName != null  and casName != ''"> and cas_name like concat('%', #{casName}, '%')</if>
            <if test="casPhone != null  and casPhone != ''"> and cas_phone like concat('%', #{casPhone}, '%')</if>
            <if test="casSeNo != null "> and cas_se_no = #{casSeNo}</if>
            <if test="casInsUser != null  and casInsUser != ''"> and cas_ins_user = #{casInsUser}</if>
            <if test="casIdNo != null  and casIdNo != ''"> and cas_id_no like concat('%', #{casIdNo}, '%')</if>
            <if test="casBank != null  and casBank != ''"> and cas_bank like concat('%', #{casBank}, '%')</if>
        </where>
    </select>

    <update id="assignBankCaseToEmployee">
        update bank_case set 
            cas_se_no = #{seNo},
            cas_last_assign_time = now(),
            cas_alt_time = now(),
            cas_alt_user = #{assignUser}
        where cas_id in 
        <foreach item="casId" collection="casIds" open="(" separator="," close=")">
            #{casId}
        </foreach>
    </update>

</mapper>
