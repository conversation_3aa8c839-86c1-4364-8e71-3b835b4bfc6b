<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.mobile.entity.Checkin" table="checkin" schema="dbo" >
        <id name="ckiId" type="java.lang.Long">
            <column name="cki_id" />
            <generator class="identity" />
        </id>
        <property name="ckiEmpId" type="java.lang.Long">
            <column name="cki_emp_id" />
        </property>
        <property name="ckiMacAddr" type="java.lang.String">
            <column name="cki_mac_addr" length="50" />
        </property>
        <property name="ckiDeviceModel" type="java.lang.String">
            <column name="cki_device_model" length="100" />
        </property>
        <property name="ckiVerName" type="java.lang.String">
            <column name="cki_ver_name" length="50" />
        </property>
        <property name="ckiLongitude" type="java.lang.String">
            <column name="cki_longitude" length="50" />
        </property>
        <property name="ckiLatitude" type="java.lang.String">
            <column name="cki_latitude" length="50" />
        </property>
        <property name="ckiAddress" type="java.lang.String">
            <column name="cki_address" length="100" />
        </property>
        <property name="ckiPoi" type="java.lang.String">
            <column name="cki_poi" length="100" />
        </property>
        <property name="ckiRemark" type="java.lang.String">
            <column name="cki_remark" length="500" />
        </property>
        <property name="ckiMan" type="java.lang.String">
            <column name="cki_man" length="50" />
        </property>
        <property name="ckiTime" type="java.util.Date">
            <column name="cki_time" length="23" />
        </property>
        <many-to-one name="ckiVisRecord" class="com.frsoft.ccds.entity.VisRecord">
            <column name="cki_vr_id" />
        </many-to-one>
        <many-to-one name="ckiLimUser" class="com.frsoft.base.entity.LimUser">
            <column name="cki_user_code" />
        </many-to-one>
    </class>
</hibernate-mapping>
