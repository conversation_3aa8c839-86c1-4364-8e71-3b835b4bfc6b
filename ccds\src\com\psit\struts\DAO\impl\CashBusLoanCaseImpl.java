package com.psit.struts.DAO.impl;

import com.psit.struts.DAO.CashBusLoanCaseDAO;
import com.psit.struts.entity.CashBusLoanCase;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.classic.Session;
import org.springframework.orm.hibernate3.HibernateCallback;

import java.sql.SQLException;
import java.util.List;

public class CashBusLoanCaseImpl extends TemplateReward implements CashBusLoanCaseDAO {
    protected static final Log log = LogFactory.getLog(AccountDAOImpl.class);
    @Override
    public long getCount(String loanRefId, String batchName) {
        Session session = (Session)super.getSession();
        String sql = "";
        if (loanRefId != null && !loanRefId.equals("") ) {
            if (sql.length() > 0) {
                sql = sql + " and loanRefId  like '%" + loanRefId + "%'";
            } else {
                sql = " where loanRefId  like '%" + loanRefId + "%'";
            }
        }
        if ((batchName!=null && !batchName.equals(""))) {
            if (sql.length() > 0) {
                sql = sql + " and status = '" + batchName + "'";
            } else {
                sql = " where status ='" + batchName + "'";
            }
        }

        String queryString = "select count(id) from CashBusLoanCase " + sql;
        Query query = session.createQuery(queryString);
        int count = Integer.parseInt(query.uniqueResult().toString());
        return count;
    }

    @Override
    public List findAll() {
        return this.getHibernateTemplate().find("from CashBusLoanCase");
    }

    @Override
    public void save(CashBusLoanCase cashBusLoanCase) {
        log.debug("saving CashBusWorkOrder instance");

        try {
            this.getHibernateTemplate().save(cashBusLoanCase);
            log.debug("save successful");
        } catch (RuntimeException var3) {
            log.error("save failed", var3);
            throw var3;
        }
    }

    @Override
    public CashBusLoanCase findById(Long id) {
        log.debug("getting Account instance with id: " + id);

        try {
            CashBusLoanCase instance = (CashBusLoanCase)this.getHibernateTemplate().get(CashBusLoanCase.class.getName(), id);
            return instance;
        } catch (RuntimeException var3) {
            log.error("get failed", var3);
            throw var3;
        }
    }

    @Override
    public void update(CashBusLoanCase cashBuscashBusLoanCase) {
        super.getHibernateTemplate().update(cashBuscashBusLoanCase);
    }

    @Override
    public void delete(CashBusLoanCase cashBuscashBusLoanCase) {
        log.debug("deleting CashBusWorkOrder instance");

        try {
            this.getHibernateTemplate().delete(cashBuscashBusLoanCase);
            log.debug("delete successful");
        } catch (RuntimeException var3) {
            log.error("delete failed", var3);
            throw var3;
        }
    }

    @Override
    public List<CashBusLoanCase> findByLoanRefId(String batchName , String loanRefId, int offset, int limit) {

        return this.find("from CashBusLoanCase cblc where cblc.batchName =? and cblc.loanRefId=? order by cblc.id ",new Object[]{batchName, loanRefId},offset , limit);
    }

    @Override
    public long findByLoanRefIdCount(String batchName, String loanRefId) {
        return countFind("from CashBusLoanCase cblc where cblc.batchName =? and cblc.loanRefId=? ",new Object[]{batchName, loanRefId});
    }

    @Override
    public List<CashBusLoanCase> findByLoanRefId(String batchName, String loanRefId) {
        return findByLoanRefId(batchName,loanRefId,0,Integer.MAX_VALUE);
    }

    @Override
    public List<CashBusLoanCase> findByBatchName(String batchName, int offset, int limit) {
        return this.find("from CashBusLoanCase cblc where cblc.batchName=? order by cblc.id", new Object[]{ batchName }, offset , limit);
    }

    @Override
    public long findByBatchNameCount(String batchName) {
        return countFind("from CashBusLoanCase cblc where cblc.batchName=? ", new Object[]{ batchName });
    }

    @Override
    public List<CashBusLoanCase> findByBatchName(String batchName) {
        return findByBatchName(batchName,0,Integer.MAX_VALUE);
    }

    @Override
    public List<CashBusLoanCase> search(String batchName, String phone, String name , final  int offset , final  int limit) {
        Object[] params = new Object[3];
        final StringBuilder sqlBuilder = new StringBuilder("from CashBusLoanCase cblc where cblc.batchName=? ");
        params[0] = batchName;
        if (StringUtils.isNotBlank(phone)) {
            sqlBuilder.append(String.format(
                    "and ( cblc.workPhone like '%%%s%%' or cblc.userPhone like '%%%s%% or cblc.relativePhone like '%%%s%%' or cblc.socialPhone like '%%%s%%' ) "
                    , phone, phone, phone, phone
            ));
            Object[] paramsl = new Object[]{batchName};
            params = paramsl;
        }
        if (StringUtils.isNotBlank(name) ) {
            sqlBuilder.append(String.format("and cblc.name like '%%%s%%%' ", name));
            Object[] paramsl = new String[params.length + 1];
            for (int index = 0; index < params.length; index++) {
                paramsl[index] = params[index];
            }
            paramsl[params.length ]  = name;
            params                   = paramsl;
        }
        sqlBuilder.append(" order by cblc.id");
        return find(sqlBuilder.toString(), params, offset, limit);
    }

    @Override
    public long searchCount(String batchName, String phone, String name) {
        Object[] params = new Object[3];
        final StringBuilder sqlBuilder = new StringBuilder("from CashBusLoanCase cblc where cblc.batchName=? ");
        params[0] = batchName;
        if (StringUtils.isNotBlank(phone)) {
            sqlBuilder.append(String.format(
                    "and ( cblc.workPhone like '%%%s%%' or cblc.userPhone like '%%%s%% or cblc.relativePhone like '%%%s%%' or cblc.socialPhone like '%%%s%%' ) "
                    , phone, phone, phone, phone
            ));
            Object[] paramsl = new Object[]{batchName};
            params = paramsl;
        }
        if (StringUtils.isNotBlank(name)) {
            sqlBuilder.append(String.format("and cblc.name like '%%%s%%%' ", name));
            Object[] paramsl = new String[params.length + 1];
            for (int index = 0; index < params.length; index++) {
                paramsl[index] = params[index];
            }
            paramsl[params.length ]  = name;
            params                   = paramsl;
        }
        return countFind(sqlBuilder.toString(), params);
    }

    @Override
    public List<CashBusLoanCase> search(String batchName, String key, final  int offset , final  int limit) {
        StringBuilder sqlBuilder = new StringBuilder("from CashBusLoanCase cblc where cblc.batchName=? ");
        if (StringUtils.isNotBlank(key)) {
            sqlBuilder.append(String.format(" and ( cblc.workPhone like '%%%s%%' or cblc.userPhone like '%%%s%%' or cblc.relativePhone like '%%%s%%' ",key,key,key));
            sqlBuilder.append(String.format(" or cblc.socialPhone like '%%%s%%' or cblc.name like '%%%s%%' or cblc.userCardNo like '%%%s%%' ) ", key, key, key));
        }
        sqlBuilder.append(" order by cblc.id");
        return find(sqlBuilder.toString(), new Object[]{batchName}, offset, limit);
    }

    @Override
    public long searchCount(String batchName, String key) {
        StringBuilder sqlBuilder = new StringBuilder("from CashBusLoanCase cblc where cblc.batchName=? ");
        if (StringUtils.isNotBlank(key)) {
            sqlBuilder.append(String.format(" and ( cblc.workPhone like '%%%s%%' or cblc.userPhone like '%%%s%%' or cblc.relativePhone like '%%%s%%' ", key, key, key));
            sqlBuilder.append(String.format(" or cblc.socialPhone like '%%%s%%' or cblc.name like '%%%s%%' or cblc.userCardNo like '%%%s%%' ) ", key, key, key));
        }
        return countFind(sqlBuilder.toString(), new Object[]{batchName });
    }
}
