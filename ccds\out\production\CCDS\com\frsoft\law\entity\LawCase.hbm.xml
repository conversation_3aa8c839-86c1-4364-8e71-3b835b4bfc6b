<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.law.entity.LawCase" table="law_case" schema="dbo" >
        <id name="lwcId" type="java.lang.Long">
            <column name="lwc_id" />
            <generator class="identity" />
        </id>
        <property name="lwcState" type="java.lang.String">
            <column name="lwc_state" length="50" />
        </property>
        <property name="lwcProc" type="java.lang.String">
            <column name="lwc_proc" length="50" />
        </property>
        <property name="lwcName" type="java.lang.String">
            <column name="lwc_name" length="100" />
        </property>
        <property name="lwcIdCode" type="java.lang.String">
            <column name="lwc_id_code" length="50" />
        </property>
        <property name="lwcConsigner" type="java.lang.String">
            <column name="lwc_consigner" length="100" />
        </property>
        <property name="lwcDefendant" type="java.lang.String">
            <column name="lwc_defendant" length="100" />
        </property>
        <property name="lwcCost" type="java.lang.Double">
            <column name="lwc_cost" precision="18" />
        </property>
        <property name="lwcPaid" type="java.lang.Double">
            <column name="lwc_paid" precision="18" />
        </property>
        <property name="lwcTarget" type="java.lang.String">
            <column name="lwc_target" length="50" />
        </property>
        <property name="lwcDate" type="java.util.Date">
            <column name="lwc_date" length="23" />
        </property>
        <property name="lwcContact" type="java.lang.String">
            <column name="lwc_contact" length="200" />
        </property>
        <property name="lwcRemark" type="java.lang.String">
            <column name="lwc_remark" length="500" />
        </property>
        <property name="lwcCreMan" type="java.lang.String">
            <column name="lwc_cre_man" length="25" />
        </property>
        <property name="lwcCreTime" type="java.util.Date">
            <column name="lwc_cre_time" length="23" />
        </property>
        <property name="lwcUpdMan" type="java.lang.String">
            <column name="lwc_upd_man" length="25" />
        </property>
        <property name="lwcUpdTime" type="java.util.Date">
            <column name="lwc_upd_time" length="23" />
        </property>
        <property name="lwcFilingDate" type="java.util.Date">
            <column name="lwc_filing_date" length="23" />
        </property>
        <property name="lwcNo" type="java.lang.String">
            <column name="lwc_no" length="100" />
        </property>
        <property name="lwcJudge" type="java.lang.String">
            <column name="lwc_judge" length="50" />
        </property>
        <property name="lwcJudgeContact" type="java.lang.String">
            <column name="lwc_judge_contact" length="200" />
        </property>
        <property name="lwcFstHearingDate" type="java.util.Date">
            <column name="lwc_fst_hearing_date" length="23" />
        </property>
        <property name="lwcEndDate" type="java.util.Date">
            <column name="lwc_end_date" length="23" />
        </property>
        <property name="lwcActDate" type="java.util.Date">
            <column name="lwc_act_date" length="23" />
        </property>
        <property name="lwcActNo" type="java.lang.String">
            <column name="lwc_act_no" length="100" />
        </property>
        <property name="lwcActEndDate" type="java.util.Date">
            <column name="lwc_act_end_date" length="23" />
        </property>
        <property name="lwcLegalPayDate" type="java.util.Date">
            <column name="lwc_legal_pay_date" length="23" />
        </property>
        <property name="lwcPresvPayDate" type="java.util.Date">
            <column name="lwc_presv_pay_date" length="23" />
        </property>
        <property name="lwcAssetPresv" type="java.lang.String">
            <column name="lwc_asset_presv" length="4000" />
        </property>
        <property name="lwcServRs" type="java.lang.String">
            <column name="lwc_serv_rs" length="100" />
        </property>
        <property name="lwcJudgment" type="java.lang.String">
            <column name="lwc_judgment" length="4000" />
        </property>
        <property name="lwcApproveMan" type="java.lang.String">
            <column name="lwc_approve_man" length="25" />
        </property>
        <property name="lwcApproveTime" type="java.util.Date">
            <column name="lwc_approve_time" length="23" />
        </property>
        <property name="lwcLawyer" type="java.lang.String">
            <column name="lwc_lawyer" length="50" />
        </property>
        <property name="lwcLawyerContact" type="java.lang.String">
            <column name="lwc_lawyer_contact" length="200" />
        </property>
        <many-to-one name="lwcBankCase" class="com.frsoft.ccds.entity.BankCase">
            <column name="lwc_cas_id" />
        </many-to-one>
        <many-to-one name="lwcCourt" class="com.frsoft.base.entity.TypeList">
            <column name="lwc_court_id" />
        </many-to-one>
        <many-to-one name="lwcType" class="com.frsoft.base.entity.TypeList">
            <column name="lwc_type_id" />
        </many-to-one>
        <many-to-one name="lwcProcSt" class="com.frsoft.base.entity.TypeList">
            <column name="lwc_proc_st_id" />
        </many-to-one>
        <many-to-one name="lwcEmp" class="com.frsoft.base.entity.SalEmp">
            <column name="lwc_emp_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
