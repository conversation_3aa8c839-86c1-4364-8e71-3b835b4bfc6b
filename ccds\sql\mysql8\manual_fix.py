#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动修复脚本 - 修复CREATE TABLE语句格式
"""

import re

def manual_fix(sql_content):
    """手动修复MySQL SQL文件"""
    
    print("开始手动修复...")
    
    # 1. 修复CREATE DATABASE语句
    sql_content = re.sub(r'CREATE DATABASE\s*\n', 'CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n', sql_content)
    
    # 2. 分割成行处理
    lines = sql_content.split('\n')
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        if line.startswith('DROP TABLE IF EXISTS'):
            # 添加DROP语句
            fixed_lines.append(lines[i])
            i += 1
            
            # 跳过空行
            while i < len(lines) and not lines[i].strip():
                i += 1
            
            # 处理CREATE TABLE语句
            if i < len(lines) and lines[i].strip().startswith('CREATE TABLE'):
                table_lines = []
                paren_count = 0
                
                # 收集整个CREATE TABLE语句
                while i < len(lines):
                    current_line = lines[i]
                    table_lines.append(current_line)
                    
                    # 计算括号
                    paren_count += current_line.count('(') - current_line.count(')')
                    
                    # 如果括号平衡，检查是否结束
                    if paren_count == 0 and ('PRIMARY KEY' in current_line or ')' in current_line):
                        break
                    
                    i += 1
                
                # 修复CREATE TABLE语句
                table_sql = '\n'.join(table_lines)
                
                # 确保有ENGINE和分号
                if 'ENGINE=' not in table_sql.upper():
                    table_sql = table_sql.rstrip().rstrip(')') + '\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;'
                elif not table_sql.strip().endswith(';'):
                    table_sql = table_sql.rstrip() + ';'
                
                fixed_lines.append(table_sql)
                fixed_lines.append('')  # 添加空行
        else:
            # 其他行直接添加
            if line:
                fixed_lines.append(lines[i])
        
        i += 1
    
    return '\n'.join(fixed_lines)

def main():
    """主函数"""
    input_file = "mysql8_CCDS_structure_final.sql"
    output_file = "mysql8_CCDS_structure_manual.sql"
    
    try:
        print(f"正在修复文件: {input_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixed_content = manual_fix(content)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"修复完成: {output_file}")
        
    except Exception as e:
        print(f"修复失败: {e}")

if __name__ == "__main__":
    main()
