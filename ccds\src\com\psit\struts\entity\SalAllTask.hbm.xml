<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.SalAllTask" table="sal_all_task" schema="dbo" >
        <id name="satId" type="java.lang.Long">
            <column name="sat_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="sat_se_no" />
        </many-to-one>
        <property name="satDate" type="java.lang.String">
            <column name="sat_date" length="50" />
        </property>
        <property name="satInpDate" type="java.util.Date">
            <column name="sat_inp_date" length="23" />
        </property>
        <property name="satAltDate" type="java.util.Date">
            <column name="sat_alt_date" length="23" />
        </property>
        <property name="satInpName" type="java.lang.String">
            <column name="sat_inp_name" length="50" />
        </property>
        <property name="satAltName" type="java.lang.String">
            <column name="sat_alt_name" length="50" />
        </property>
        <property name="satHtMon" type="java.lang.Double">
            <column name="sat_ht_mon" precision="18" />
        </property>
        <property name="satPaidMon" type="java.lang.Double">
            <column name="sat_paid_mon" precision="18" />
        </property>
        <property name="satCusNum" type="java.lang.Integer">
            <column name="sat_cus_num" />
        </property>
    </class>
</hibernate-mapping>
