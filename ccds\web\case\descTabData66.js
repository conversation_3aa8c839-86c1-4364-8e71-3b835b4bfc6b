function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','C_DT'],
		['ID_NO','C_CODE','STATE','BACK_DT'],
		['AMT','RMB','GB','MY'],
		['PAID','M_CAT','AGE','DAYS'],
		['PRI',['LEFT_PRI',1,'首付金额'],['LOAN_RATE',1,'未逾期/逾期利率'],['MONTH_P',1,'期款']],
		[['START_DT',1,'首次还款月'],['LAST_P',1,'累计还款'],['CL_T',1,'时段'],['FILE_NO',1,'合同号']],
		['LOAN_DT',['ACC',1,'客户代扣卡号'],'F_BANK','LAST_P_DT'],
		['P_COUNT','AREA','PROD','BIZ'],
		['OVERDUE_M','OV_INT','OV_P','OVERDUE_NUM'],
		['MOB','PHO','W_PHO','HOM'],
		['COM','W_ADDR','REG','M_ADDR'],
		['PTP','CP','COUNT',['MIN_P',1,'预付款']],
		['TIPS_DT','DEAD_L',['PR_COUNT',3]],
		['CL_AREA','EMP_NAME','LAST_CL','LAST_VIS'],
		[['TREMARK',7]]
	);
}
function getLayout2(){
	return  new Array(
		['ASS_TM',['ASS_HIS',5]],
		['H_POST','W_POST','M_POST','EMAIL'],
		['HOST','ALT_HOLD','NOOUT','BILL_DT'],
		['STOP_DT','LAST_C_DT','LAST_R_DT','CYCLE'],
		['OV_DT','G_AMT','APP_NO','C_L'],
		['DELAY_LV','BACK_AMT','P_L','P_CRLINE'],
		['LOAN_T','POS','PART','BIR'],
		[['SC_PC_NO',3],['SC_NO',3]],
		['C1_NAME','C1_ID_NO','C1_HM_PHO','C1_W_PHO'],
		['C1_MOB','C1_COM',['C1_ADR',3]],
		['C2_NAME','C2_ID_NO','C2_HM_PHO','C2_W_PHO'],
		['C2_MOB','C2_COM',['C2_ADR',3]],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		['C4_NAME','C4_ID_NO','C4_HM_PHO','C4_W_PHO'],
		['C4_MOB','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		['C6_NAME','C6_ID_NO','C6_HM_PHO','C6_W_PHO'],
		['C6_MOB','C6_COM',['C6_ADR',3]],
		[['O_REC',3],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}