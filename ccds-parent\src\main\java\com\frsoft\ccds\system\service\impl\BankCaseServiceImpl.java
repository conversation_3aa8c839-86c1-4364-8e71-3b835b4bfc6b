package com.frsoft.ccds.system.service.impl;

import com.frsoft.ccds.common.core.service.impl.BaseServiceImpl;
import com.frsoft.ccds.common.exception.BusinessException;
import com.frsoft.ccds.common.utils.SecurityUtils;
import com.frsoft.ccds.common.utils.StringUtils;
import com.frsoft.ccds.common.utils.ValidationUtils;
import com.frsoft.ccds.system.domain.BankCase;
import com.frsoft.ccds.system.mapper.BankCaseMapper;
import com.frsoft.ccds.system.service.IBankCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 银行案件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class BankCaseServiceImpl extends BaseServiceImpl<BankCaseMapper, BankCase> implements IBankCaseService {

    /**
     * 查询银行案件
     * 
     * @param casId 银行案件主键
     * @return 银行案件
     */
    @Override
    public BankCase selectBankCaseByCasId(Long casId) {
        return baseMapper.selectBankCaseByCasId(casId);
    }

    /**
     * 查询银行案件列表
     *
     * @param bankCase 银行案件
     * @return 银行案件
     */
    @Override
    public List<BankCase> selectBankCaseList(BankCase bankCase) {
        return baseMapper.selectBankCaseList(bankCase);
    }

    /**
     * 新增银行案件
     *
     * @param bankCase 银行案件
     * @return 结果
     */
    @Override
    public int insertBankCase(BankCase bankCase) {
        return insert(bankCase);
    }

    /**
     * 修改银行案件
     *
     * @param bankCase 银行案件
     * @return 结果
     */
    @Override
    public int updateBankCase(BankCase bankCase) {
        return update(bankCase);
    }

    /**
     * 批量删除银行案件
     * 
     * @param casIds 需要删除的银行案件主键
     * @return 结果
     */
    @Override
    public int deleteBankCaseByCasIds(Long[] casIds) {
        return baseMapper.deleteBankCaseByCasIds(casIds);
    }

    /**
     * 删除银行案件信息
     *
     * @param casId 银行案件主键
     * @return 结果
     */
    @Override
    public int deleteBankCaseByCasId(Long casId) {
        return baseMapper.deleteBankCaseByCasId(casId);
    }
    
    /**
     * 根据员工编号查询案件列表
     * 
     * @param seNo 员工编号
     * @return 案件列表
     */
    @Override
    public List<BankCase> selectBankCaseListBySeNo(Long seNo) {
        return baseMapper.selectBankCaseListBySeNo(seNo);
    }

    /**
     * 根据案件状态查询案件列表
     *
     * @param casState 案件状态
     * @return 案件列表
     */
    @Override
    public List<BankCase> selectBankCaseListByState(Integer casState) {
        return baseMapper.selectBankCaseListByState(casState);
    }

    /**
     * 根据客户姓名模糊查询案件列表
     *
     * @param casName 客户姓名
     * @return 案件列表
     */
    @Override
    public List<BankCase> selectBankCaseListByName(String casName) {
        return baseMapper.selectBankCaseListByName(casName);
    }

    /**
     * 根据客户电话查询案件列表
     *
     * @param casPhone 客户电话
     * @return 案件列表
     */
    @Override
    public List<BankCase> selectBankCaseListByPhone(String casPhone) {
        return baseMapper.selectBankCaseListByPhone(casPhone);
    }

    /**
     * 统计案件总数
     *
     * @param bankCase 查询条件
     * @return 案件总数
     */
    @Override
    public int countBankCase(BankCase bankCase) {
        return baseMapper.countBankCase(bankCase);
    }
    
    /**
     * 分配案件给员工
     * 
     * @param casIds 案件ID数组
     * @param seNo 员工编号
     * @param assignUser 分配用户
     * @return 结果
     */
    @Override
    public int assignBankCaseToEmployee(Long[] casIds, Long seNo, String assignUser) {
        // 验证参数
        if (casIds == null || casIds.length == 0) {
            throw new BusinessException("请选择要分配的案件");
        }
        if (seNo == null) {
            throw new BusinessException("请选择分配的员工");
        }

        return baseMapper.assignBankCaseToEmployee(casIds, seNo, assignUser);
    }

    /**
     * 验证银行案件数据
     *
     * @param bankCase 银行案件
     * @param isUpdate 是否为更新操作
     */
    @Override
    public void validate(BankCase bankCase, boolean isUpdate) {
        super.validate(bankCase, isUpdate);
        if (bankCase == null) {
            throw new BusinessException("案件信息不能为空");
        }

        // 验证必填字段
        ValidationUtils.validateRequired(bankCase.getCasName(), "客户姓名");
        ValidationUtils.validateRequired(bankCase.getCasPhone(), "客户电话");
        ValidationUtils.validateRequired(bankCase.getCasM(), "案件金额");

        // 验证字段长度
        ValidationUtils.validateLength(bankCase.getCasName(), "客户姓名", 1, 50);
        ValidationUtils.validateLength(bankCase.getCasCode(), "案件编码", 1, 50);

        // 验证手机号格式
        ValidationUtils.validateMobile(bankCase.getCasPhone(), "客户电话");

        // 验证身份证号格式
        ValidationUtils.validateIdCard(bankCase.getCasIdNo(), "身份证号");

        // 验证邮箱格式
        ValidationUtils.validateEmail(bankCase.getCasEmail(), "邮箱");

        // 验证银行卡号格式
        ValidationUtils.validateBankCard(bankCase.getCasCardNo(), "银行卡号");

        // 验证金额范围
        if (bankCase.getCasM() != null) {
            ValidationUtils.validateRange(bankCase.getCasM(), "案件金额", 0, *********);
        }

        // 验证案件编码唯一性（如果是新增）
        if (!isUpdate && StringUtils.isNotEmpty(bankCase.getCasCode())) {
            BankCase existCase = new BankCase();
            existCase.setCasCode(bankCase.getCasCode());
            List<BankCase> existCases = baseMapper.selectList(existCase);
            if (!existCases.isEmpty()) {
                throw new BusinessException("案件编码已存在");
            }
        }
    }
}
