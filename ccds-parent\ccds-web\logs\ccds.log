2025-06-24 11:21:39 [main] INFO  com.frsoft.ccds.CCDSApplication - Starting CCDSApplication using Java 1.8.0_441 on itxinfei with PID 34440 (D:\cuishou\ccds-parent\ccds-web\target\classes started by itxinfei in D:\cuishou\ccds-parent\ccds-web)
2025-06-24 11:21:39 [main] DEBUG com.frsoft.ccds.CCDSApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-24 11:21:39 [main] INFO  com.frsoft.ccds.CCDSApplication - The following 1 profile is active: "dev"
2025-06-24 11:21:39 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.frsoft.ccds.CCDSApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/frsoft/ccds/common/core/controller/BaseController.class] cannot be opened because it does not exist
2025-06-24 11:21:39 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.frsoft.ccds.CCDSApplication]; nested exception is java.io.FileNotFoundException: class path resource [com/frsoft/ccds/common/core/controller/BaseController.class] cannot be opened because it does not exist
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:188)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:756)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:572)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.frsoft.ccds.CCDSApplication.main(CCDSApplication.java:14)
Caused by: java.io.FileNotFoundException: class path resource [com/frsoft/ccds/common/core/controller/BaseController.class] cannot be opened because it does not exist
	at org.springframework.core.io.ClassPathResource.getInputStream(ClassPathResource.java:203)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:55)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.createMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:86)
	at org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory.getMetadataReader(ConcurrentReferenceCachingMetadataReaderFactory.java:73)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:81)
	at org.springframework.context.annotation.ConfigurationClassParser.asSourceClass(ConfigurationClassParser.java:699)
	at org.springframework.context.annotation.ConfigurationClassParser$SourceClass.getSuperClass(ConfigurationClassParser.java:1013)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:340)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:249)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:198)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:303)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:249)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:206)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:174)
	... 13 common frames omitted
2025-06-24 11:22:03 [main] INFO  com.frsoft.ccds.CCDSApplication - Starting CCDSApplication using Java 1.8.0_441 on itxinfei with PID 33172 (D:\cuishou\ccds-parent\ccds-web\target\classes started by itxinfei in D:\cuishou\ccds-parent\ccds-web)
2025-06-24 11:22:03 [main] DEBUG com.frsoft.ccds.CCDSApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-24 11:22:03 [main] INFO  com.frsoft.ccds.CCDSApplication - The following 1 profile is active: "dev"
2025-06-24 11:22:03 [main] DEBUG o.m.s.boot.autoconfigure.MybatisAutoConfiguration - Searching for mappers annotated with @Mapper
2025-06-24 11:22:03 [main] DEBUG o.m.s.boot.autoconfigure.MybatisAutoConfiguration - Using auto-configuration base package 'com.frsoft.ccds'
2025-06-24 11:22:03 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/devops/repository/com/frsoft/ccds-system/1.0.0/ccds-system-1.0.0.jar!/com/frsoft/ccds/system/mapper/SysUserMapper.class]
2025-06-24 11:22:03 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysUserMapper' and 'com.frsoft.ccds.system.mapper.SysUserMapper' mapperInterface
2025-06-24 11:22:03 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-24 11:22:03 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-24 11:22:04 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-24 11:22:04 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-24 11:22:04 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-24 11:22:04 [main] DEBUG org.mybatis.spring.SqlSessionFactoryBean - Parsed configuration file: 'class path resource [mybatis/mybatis-config.xml]'
2025-06-24 11:22:04 [main] DEBUG org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'URL [jar:file:/D:/devops/repository/com/frsoft/ccds-system/1.0.0/ccds-system-1.0.0.jar!/mapper/system/SysUserMapper.xml]'
2025-06-24 11:22:04 [main] DEBUG o.m.s.boot.autoconfigure.MybatisAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-24 11:22:04 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e2924a11-0076-4aa8-b5cd-0d0e7f8ba8cb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-24 11:22:04 [main] INFO  com.frsoft.ccds.CCDSApplication - Started CCDSApplication in 1.651 seconds (JVM running for 1.849)
2025-06-24 11:22:51 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 11:32:34 [main] INFO  com.frsoft.ccds.CCDSApplication - Starting CCDSApplication using Java 1.8.0_441 on itxinfei with PID 34132 (D:\cuishou\ccds-parent\ccds-web\target\classes started by itxinfei in D:\cuishou\ccds-parent\ccds-web)
2025-06-24 11:32:34 [main] DEBUG com.frsoft.ccds.CCDSApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-24 11:32:34 [main] INFO  com.frsoft.ccds.CCDSApplication - The following 1 profile is active: "dev"
2025-06-24 11:32:35 [main] DEBUG o.m.s.boot.autoconfigure.MybatisAutoConfiguration - Searching for mappers annotated with @Mapper
2025-06-24 11:32:35 [main] DEBUG o.m.s.boot.autoconfigure.MybatisAutoConfiguration - Using auto-configuration base package 'com.frsoft.ccds'
2025-06-24 11:32:35 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/devops/repository/com/frsoft/ccds-system/1.0.0/ccds-system-1.0.0.jar!/com/frsoft/ccds/system/mapper/SysUserMapper.class]
2025-06-24 11:32:35 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysUserMapper' and 'com.frsoft.ccds.system.mapper.SysUserMapper' mapperInterface
2025-06-24 11:32:35 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-24 11:32:35 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-24 11:32:35 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-24 11:32:35 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-24 11:32:35 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-24 11:32:36 [main] DEBUG org.mybatis.spring.SqlSessionFactoryBean - Parsed configuration file: 'class path resource [mybatis/mybatis-config.xml]'
2025-06-24 11:32:36 [main] DEBUG org.mybatis.spring.SqlSessionFactoryBean - Parsed mapper file: 'URL [jar:file:/D:/devops/repository/com/frsoft/ccds-system/1.0.0/ccds-system-1.0.0.jar!/mapper/system/SysUserMapper.xml]'
2025-06-24 11:32:36 [main] DEBUG o.m.s.boot.autoconfigure.MybatisAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-24 11:32:36 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 227fdcac-cae7-4c30-ac6d-a0040a51cba7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-24 11:32:36 [main] INFO  com.frsoft.ccds.CCDSApplication - Started CCDSApplication in 1.699 seconds (JVM running for 1.904)
2025-06-24 11:33:17 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
