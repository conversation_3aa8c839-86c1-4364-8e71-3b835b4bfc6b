<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.base.entity.CusProvince" table="cus_province" schema="dbo" >
        <id name="prvId" type="java.lang.Long">
            <column name="prv_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="cusArea" class="com.frsoft.base.entity.CusArea" fetch="select" not-null="false">
            <column name="prv_area_id" />
        </many-to-one>
        <property name="prvName" type="java.lang.String">
            <column name="prv_name" length="100" />
        </property>
        <property name="prvIsenabled" type="java.lang.String">
            <column name="prv_isenabled" length="10" />
        </property>
    
        <set name="cusCities" inverse="true" cascade="all">
            <key>
                <column name="city_prv_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.CusCity" />
        </set>
    </class>
</hibernate-mapping>
