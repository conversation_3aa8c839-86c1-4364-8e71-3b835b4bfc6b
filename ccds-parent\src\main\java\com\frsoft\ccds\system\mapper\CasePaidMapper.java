package com.frsoft.ccds.system.mapper;

import com.frsoft.ccds.system.domain.CasePaid;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 还款记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface CasePaidMapper {
    
    /**
     * 查询还款记录
     * 
     * @param paId 还款记录主键
     * @return 还款记录
     */
    CasePaid selectCasePaidByPaId(Long paId);
    
    /**
     * 查询还款记录列表
     * 
     * @param casePaid 还款记录
     * @return 还款记录集合
     */
    List<CasePaid> selectCasePaidList(CasePaid casePaid);
    
    /**
     * 新增还款记录
     * 
     * @param casePaid 还款记录
     * @return 结果
     */
    int insertCasePaid(CasePaid casePaid);
    
    /**
     * 修改还款记录
     * 
     * @param casePaid 还款记录
     * @return 结果
     */
    int updateCasePaid(CasePaid casePaid);
    
    /**
     * 删除还款记录
     * 
     * @param paId 还款记录主键
     * @return 结果
     */
    int deleteCasePaidByPaId(Long paId);
    
    /**
     * 批量删除还款记录
     * 
     * @param paIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCasePaidByPaIds(Long[] paIds);
    
    /**
     * 根据案件ID查询还款记录列表
     * 
     * @param casId 案件ID
     * @return 还款记录列表
     */
    List<CasePaid> selectCasePaidListByCasId(@Param("casId") Long casId);
    
    /**
     * 根据员工编号查询还款记录列表
     * 
     * @param seNo 员工编号
     * @return 还款记录列表
     */
    List<CasePaid> selectCasePaidListBySeNo(@Param("seNo") Long seNo);
    
    /**
     * 统计还款记录总数
     * 
     * @param casePaid 查询条件
     * @return 还款记录总数
     */
    int countCasePaid(CasePaid casePaid);
}
