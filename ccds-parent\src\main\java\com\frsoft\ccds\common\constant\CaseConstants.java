package com.frsoft.ccds.common.constant;

/**
 * 案件相关常量
 * 
 * <AUTHOR>
 */
public class CaseConstants {
    
    /**
     * 案件状态
     */
    public static final class CaseState {
        /** 待分配 */
        public static final Integer PENDING_ASSIGN = 0;
        /** 催收中 */
        public static final Integer IN_COLLECTION = 1;
        /** 已结案 */
        public static final Integer CLOSED = 2;
        /** 暂停催收 */
        public static final Integer PAUSED = 3;
        /** 法务处理 */
        public static final Integer LEGAL_PROCESS = 4;
    }
    
    /**
     * 催收结果
     */
    public static final class CollectionResult {
        /** 承诺还款 */
        public static final String PROMISE_PAYMENT = "承诺还款";
        /** 拒绝还款 */
        public static final String REFUSE_PAYMENT = "拒绝还款";
        /** 无人接听 */
        public static final String NO_ANSWER = "无人接听";
        /** 空号 */
        public static final String EMPTY_NUMBER = "空号";
        /** 停机 */
        public static final String SHUTDOWN = "停机";
        /** 忙音 */
        public static final String BUSY = "忙音";
        /** 其他 */
        public static final String OTHER = "其他";
    }
    
    /**
     * 联系方式类型
     */
    public static final class ContactType {
        /** 电话 */
        public static final String PHONE = "电话";
        /** 短信 */
        public static final String SMS = "短信";
        /** 邮件 */
        public static final String EMAIL = "邮件";
        /** 上门 */
        public static final String VISIT = "上门";
        /** 其他 */
        public static final String OTHER = "其他";
    }
    
    /**
     * 还款状态
     */
    public static final class PaymentState {
        /** 承诺中 */
        public static final Integer PROMISED = 0;
        /** 已还款 */
        public static final Integer PAID = 1;
        /** 逾期未还 */
        public static final Integer OVERDUE = 2;
        /** 部分还款 */
        public static final Integer PARTIAL_PAID = 3;
    }
    
    /**
     * 还款类型
     */
    public static final class PaymentType {
        /** 全额还款 */
        public static final String FULL_PAYMENT = "全额还款";
        /** 部分还款 */
        public static final String PARTIAL_PAYMENT = "部分还款";
        /** 最低还款 */
        public static final String MINIMUM_PAYMENT = "最低还款";
    }
    
    /**
     * 还款方式
     */
    public static final class PaymentMethod {
        /** 银行转账 */
        public static final String BANK_TRANSFER = "银行转账";
        /** 现金 */
        public static final String CASH = "现金";
        /** 支票 */
        public static final String CHECK = "支票";
        /** 网银 */
        public static final String ONLINE_BANKING = "网银";
        /** 第三方支付 */
        public static final String THIRD_PARTY = "第三方支付";
        /** 其他 */
        public static final String OTHER = "其他";
    }
    
    /**
     * 逾期天数分级
     */
    public static final class OverdueDaysLevel {
        /** M1: 1-30天 */
        public static final String M1 = "M1";
        /** M2: 31-60天 */
        public static final String M2 = "M2";
        /** M3: 61-90天 */
        public static final String M3 = "M3";
        /** M4: 91-120天 */
        public static final String M4 = "M4";
        /** M5: 121-150天 */
        public static final String M5 = "M5";
        /** M6: 151-180天 */
        public static final String M6 = "M6";
        /** M7+: 180天以上 */
        public static final String M7_PLUS = "M7+";
    }
    
    /**
     * 根据逾期天数获取分级
     */
    public static String getOverdueDaysLevel(Integer overdueDays) {
        if (overdueDays == null || overdueDays <= 0) {
            return "";
        }
        
        if (overdueDays <= 30) {
            return OverdueDaysLevel.M1;
        } else if (overdueDays <= 60) {
            return OverdueDaysLevel.M2;
        } else if (overdueDays <= 90) {
            return OverdueDaysLevel.M3;
        } else if (overdueDays <= 120) {
            return OverdueDaysLevel.M4;
        } else if (overdueDays <= 150) {
            return OverdueDaysLevel.M5;
        } else if (overdueDays <= 180) {
            return OverdueDaysLevel.M6;
        } else {
            return OverdueDaysLevel.M7_PLUS;
        }
    }
}
