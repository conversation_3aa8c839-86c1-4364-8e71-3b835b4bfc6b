<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.autotask.entity.AutoTaskPlan" table="auto_task_plan" schema="dbo" >
    	<id name="atpId" type="java.lang.Long">
            <column name="atp_id" />
            <generator class="identity" />
        </id>
		<property name="atpState" type="java.lang.Integer">
            <column name="atp_state" />
        </property>
        <property name="atpPlanTime" type="java.util.Date">
            <column name="atp_plan_time" length="23" />
        </property>
        <property name="atpExecTime" type="java.util.Date">
            <column name="atp_exec_time" length="23" />
        </property>
        <property name="atpEndTime" type="java.util.Date">
            <column name="atp_end_time" length="23" />
        </property>
        <property name="atpTotalAmt" type="java.lang.Double">
            <column name="atp_total_amt" precision="18" />
        </property>
		<property name="atpTotalNum" type="java.lang.Integer">
            <column name="atp_total_num" />
        </property>
		<property name="atpType" type="java.lang.String">
            <column name="atp_type"  length="25"/>
        </property>
		<property name="atpCasIds" type="java.lang.String">
            <column name="atp_cas_ids"/>
        </property>
		<property name="atpReport" type="java.lang.String">
            <column name="atp_report"/>
        </property>
        <property name="atpCreTime" type="java.util.Date">
            <column name="atp_cre_time" length="23" />
        </property>
        <property name="atpCreMan" type="java.lang.String">
            <column name="atp_cre_man" length="50" />
        </property>
        <property name="atpUpdTime" type="java.util.Date">
            <column name="atp_upd_time" length="23" />
        </property>
        <property name="atpUpdMan" type="java.lang.String">
            <column name="atp_upd_man" length="50" />
        </property>
        <property name="atpUndoTime" type="java.util.Date">
            <column name="atp_undo_time" length="23" />
        </property>
        <property name="atpUndoMan" type="java.lang.String">
            <column name="atp_undo_man" length="50" />
        </property>
    </class>
</hibernate-mapping>
