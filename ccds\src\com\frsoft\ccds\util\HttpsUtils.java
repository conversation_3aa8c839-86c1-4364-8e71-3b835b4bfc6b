package com.frsoft.ccds.util;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethodBase;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.*;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.httpclient.protocol.Protocol;
import org.apache.commons.httpclient.protocol.ProtocolSocketFactory;
import org.apache.commons.io.IOUtils;
import org.apache.log4j.Logger;


/**
 * Created by comfan on 2017/7/20.
 */
public class HttpsUtils {
    /** 测试用(开发者需换成自己的appkey和secret) */
    String key = "";
    String secret = "";
    protected String defaultCharset = "UTF-8";
    protected Logger log;

    public HttpsUtils() {
        log = Logger.getLogger("transMsgLog");
    }

    public String getDefaultCharset() {
        return defaultCharset;
    }

    public void setDefaultCharset(String defaultCharset) {
        this.defaultCharset = defaultCharset;
    }

    @Deprecated
    public Map<String, Object> paramsInit(String method,
                                          Map<String, Object> paramsMap) {
        Map<String, Object> map = new HashMap<String, Object>();
        long time = System.currentTimeMillis() / 1000;

        StringBuilder paramString = new StringBuilder();
        List<String> paramList = new ArrayList<String>();
        for (Iterator<String> it = paramsMap.keySet().iterator(); it.hasNext();) {
            String key1 = it.next();
            String param = key1 + ":" + paramsMap.get(key1);
            paramList.add(param);
        }
        String[] params = paramList.toArray(new String[paramList.size()]);
        Arrays.sort(params);
        for (String param : params) {
            paramString.append(param).append(",");
        }
        paramString.append("method").append(":").append(method).append(",");
        paramString.append("time").append(":").append(time).append(",");
        paramString.append("secret").append(":").append(secret);
        // System.out.println(paramString.toString().trim());

        String sign = DigestUtils.md5Hex(paramString.toString().trim());

        Map<String, Object> systemMap = new HashMap<String, Object>();
        systemMap.put("ver", "1.0");
        systemMap.put("sign", sign);
        systemMap.put("key", key);
        systemMap.put("time", time);

        map.put("system", systemMap);
        map.put("method", method);
        map.put("params", paramsMap);
        map.put("id", "123456");
        return map;
    }

    public String request(final String url,final String method,final Map<String, String> httpParams,final Map<String,String> httpHeaders) {
        return request(url, method, httpParams, httpHeaders, true);
    }
    public String request(final String url,final String method,final Map<String, String> httpParams,final Map<String,String> httpHeaders , boolean urlModel) {
        ProtocolSocketFactory fcty  = new CCDSSecureProtocolSocketFactory();
        Protocol.registerProtocol("https", new Protocol("https", fcty, 443));
        HttpClient client           = new HttpClient();
        // 使用POST方法
        HttpMethodBase httpMethod   = null;
        if ("post".equalsIgnoreCase(method)) {
            httpMethod = new PostMethod(
                    url);
        } else if ("get".equalsIgnoreCase(method)) {
            httpMethod = new GetMethod(url);
        } else if ("put".equalsIgnoreCase(method)) {
            httpMethod = new PutMethod(url);
        }
        log.debug(
                String.format("URL:[%s] \n\tHttpMethod:[%s] %s, \n\tHeaders:[%s] , \n\tParameters:[%s]",
                        url,method, urlModel?"urlMode":"", httpHeaders, httpParams
                )
        );
        try {
            final List<NameValuePair> params = new ArrayList<NameValuePair>();
            //  添加参数到params
            {
                final Iterator<Map.Entry<String, String>> iterator = httpParams.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, String> param = iterator.next();
                    String value = param.getValue();
                    params.add(new NameValuePair(param.getKey(), value));
                }
            }
            if(urlModel || "get".equalsIgnoreCase(method)) {
                httpMethod.setQueryString(params.toArray(new NameValuePair[params.size()]));
            }else if("post".equalsIgnoreCase(method)){
                PostMethod postMethod = (PostMethod)httpMethod;
                postMethod.addParameters(params.toArray(new NameValuePair[params.size()]));
                httpMethod = postMethod;
            }else{
                final HttpMethodParams httpMethodParams = httpMethod.getParams();
                final Iterator<Map.Entry<String, String>> iterator = httpParams.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, String> param = iterator.next();
                    httpMethodParams.setParameter(param.getKey(), param.getValue());
                }
            }
            //  添加http headers
            {
                final Iterator<Map.Entry<String, String>> headerIt = httpHeaders.entrySet().iterator();
                while (headerIt.hasNext()) {
                    Map.Entry<String, String> header = headerIt.next();
                    String value = header.getValue();
                    httpMethod.setRequestHeader(header.getKey(), value);
                }
            }
            client.executeMethod(httpMethod);
            final InputStream inputStream = httpMethod.getResponseBodyAsStream();
            final String restult = IOUtils.toString(inputStream,defaultCharset);
            log.debug(String.format("get response by charset [%s] is \n[ %s ]",defaultCharset,restult));
            return restult;
        } catch (Exception e) {
            log.error(
                    String.format("Http通信失败!Url:[%s],HttpMethod:[%s:%s],Headers:[%s],Parameters:[%s]" ,
                            url,method,httpMethod,httpHeaders,httpParams
                    ),
            e);
        } finally {
            // 释放连接
            httpMethod.releaseConnection();
        }
        return null;
    }

    /**
     * 获取输入流
     * @param url           URL
     * @param method        HTTP方法
     * @param httpParams    HTTP参数
     * @param httpHeaders   HTTP头
     * @param urlModel      是否URL模式
     * @return  输入流
     */
    public InputStream requestInput(final String url,final String method,final Map<String, String> httpParams,final Map<String,String> httpHeaders , boolean urlModel) {
        ProtocolSocketFactory fcty  = new CCDSSecureProtocolSocketFactory();
        Protocol.registerProtocol("https", new Protocol("https", fcty, 443));
        HttpClient client           = new HttpClient();
        // 使用POST方法
        HttpMethodBase httpMethod   = null;
        if ("post".equalsIgnoreCase(method)) {
            httpMethod = new PostMethod(
                    url);
        } else if ("get".equalsIgnoreCase(method)) {
            httpMethod = new GetMethod(url);
        } else if ("put".equalsIgnoreCase(method)) {
            httpMethod = new PutMethod(url);
        }
        log.debug(
                String.format("URL:[%s] \n\tHttpMethod:[%s] %s, \n\tHeaders:[%s] , \n\tParameters:[%s]",
                        url,method, urlModel?"urlMode":"", httpHeaders, httpParams
                )
        );
        try {
            final List<NameValuePair> params = new ArrayList<NameValuePair>();
            //  添加参数到params
            {
                final Iterator<Map.Entry<String, String>> iterator = httpParams.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, String> param = iterator.next();
                    String value = param.getValue();
                    params.add(new NameValuePair(param.getKey(), value));
                }
            }
            if (urlModel || "get".equalsIgnoreCase(method)) {
                httpMethod.setQueryString(params.toArray(new NameValuePair[params.size()]));
            } else if ("post".equalsIgnoreCase(method)) {
                PostMethod postMethod = (PostMethod) httpMethod;
                postMethod.addParameters(params.toArray(new NameValuePair[params.size()]));
                httpMethod = postMethod;
            } else {
                final HttpMethodParams httpMethodParams = httpMethod.getParams();
                final Iterator<Map.Entry<String, String>> iterator = httpParams.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, String> param = iterator.next();
                    httpMethodParams.setParameter(param.getKey(), param.getValue());
                }
            }
            //  添加http headers
            {
                final Iterator<Map.Entry<String, String>> headerIt = httpHeaders.entrySet().iterator();
                while (headerIt.hasNext()) {
                    Map.Entry<String, String> header = headerIt.next();
                    String value = header.getValue();
                    httpMethod.setRequestHeader(header.getKey(), value);
                }
            }
            client.executeMethod(httpMethod);
            final InputStream inputStream = httpMethod.getResponseBodyAsStream();
            return inputStream;
        }catch (Exception e){
            log.error(e);
        }finally {
            httpMethod.releaseConnection();
        }
        return new ByteArrayInputStream(new byte[0]);
    }

    /**
     * 获取输入流
     * @param url       URL
     * @param method    HTTP方法
     * @return  输入流
     */
    public InputStream requestInput(final String url,final String method) {
        return requestInput(url, method, new HashMap<String, String>(), new HashMap<String, String>(), true);
    }
}
