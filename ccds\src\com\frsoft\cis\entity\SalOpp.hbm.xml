<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.SalOpp" table="sal_opp" schema="dbo" >
        <id name="oppId" type="java.lang.Long">
            <column name="opp_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="cusCorCus" class="com.frsoft.cis.entity.CusCorCus"  fetch="select" not-null="false">
            <column name="opp_cor_code" length="50" />
        </many-to-one>
        <many-to-one name="salEmp1" class="com.frsoft.base.entity.SalEmp" fetch="select"  not-null="false">
            <column name="opp_se_no_1" length="50" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="opp_se_no" />
        </many-to-one>
        <property name="oppTitle" type="java.lang.String">
            <column name="opp_title" length="300" />
        </property>
        <property name="oppLev" type="java.lang.String">
            <column name="opp_lev" length="50" />
        </property>
        <property name="oppExeDate" type="java.util.Date">
            <column name="opp_exe_date" length="23" />
        </property>
        <property name="oppUpdDate" type="java.util.Date">
            <column name="opp_upd_date" length="23" />
        </property>
        <property name="oppDes" type="java.lang.String">
            <column name="opp_des" length="1073741823" />
        </property>
        <property name="oppRemark" type="java.lang.String">
            <column name="opp_remark" length="1073741823" />
        </property>
        <property name="oppInsDate" type="java.util.Date">
            <column name="opp_ins_date" length="23" />
        </property>
        <property name="oppIsexe" type="java.lang.String">
            <column name="opp_isexe" length="10" />
        </property>
        <property name="oppState" type="java.lang.String">
            <column name="opp_state" length="10" />
        </property>
        <property name="oppSignDate" type="java.util.Date">
            <column name="opp_sign_date" length="23" />
        </property>
        <property name="oppFindDate" type="java.util.Date">
            <column name="opp_find_date" length="23" />
        </property>
        <property name="oppMoney" type="java.lang.Double">
            <column name="opp_money" precision="18"/>
        </property>
         <many-to-one name="oppStage" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="opp_stage" />
        </many-to-one>
         <property name="oppPossible" type="java.lang.String">
            <column name="opp_possible" length="50"/>
        </property>
        <property name="oppStaRemark" type="java.lang.String">
            <column name="opp_sta_remark" length="100"/>
        </property>
         <property name="oppStaUpdate" type="java.util.Date">
            <column name="opp_sta_update" length="23"/>
        </property>
         <property name="oppStaLog" type="java.lang.String">
            <column name="opp_sta_log" length="1073741823"/>
        </property>
        <set name="salPras" inverse="true"  order-by="pra_ins_date desc" cascade="all">
            <key>
                <column name="pra_opp_id"/>
            </key>
            <one-to-many class="com.frsoft.cis.entity.SalPra" />
        </set>
        <property name="oppInpUser" type="java.lang.String">
            <column name="opp_inp_user" length="50" />
        </property>
        <property name="oppUpdUser" type="java.lang.String">
            <column name="opp_upd_user" length="50" />
        </property>
        <property name="oppIsDel" type="java.lang.String">
            <column name="opp_isdel" length="1" />
        </property>
    </class>
</hibernate-mapping>
