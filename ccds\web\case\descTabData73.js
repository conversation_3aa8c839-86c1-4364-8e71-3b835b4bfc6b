
function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','C_DT'],
		['ID_NO','C_CODE','STATE','BACK_DT'],
		['AMT','RMB','GB','MY'],
		['PAID_LEFT','M_CAT','AGE','DAYS'],
		[['LAST_M',3],['AREA',3]],
		['PRI','LEFT_PRI','LOAN_DT','OVERDUE_NUM'],
		['LOAN_RATE','MONTH_P','OVERDUE_M','OV_INT'],
		['MOB','PHO','W_PHO','HOM'],
		['COM','W_ADDR','REG','M_ADDR'],
		['PTP','CP','COUNT','MIN_P'],
		['TIPS_DT','DEAD_L','PR_COUNT','OV_P'],
		['CL_AREA','EMP_NAME','LAST_CL','LAST_VIS'],
		[['TREMARK',7]]
	);
}
function getLayout2(){
	return  new Array(
		['ASS_TM',['ASS_HIS',5]],
		['H_POST','W_POST','M_POST','REG_POST'],
		['ACC','F_BANK','HOST','ALT_HOLD'],
		['NOOUT','BILL_DT','CYCLE','P_CRLINE'],
		['START_DT','STOP_DT','LAST_P_DT','LAST_P'],
		['LAST_C_DT','LAST_R_DT','OV_DT','OVERDUE_ONCE'],
		['G_AMT','OVER_LIMIT','BACK_AMT','P_L'],
		['APP_NO','C_L','LOAN_T','DELAY_LV'],
		['P_COUNT','CL_T','LOAN_END_DT','BIR'],
		['POS','PART','PROD','BIZ'],
		['EMAIL','FILE_NO','SC_PC_NO','SC_NO'],
		[['C1_NAME',1,'共同申请人姓名'],['C1_ID_NO',1,'共同申请人证件'],['C1_HM_PHO',1,'共同申请人家庭电话'],['C1_W_PHO',1,'共同申请人单位电话']],
		[['C1_MOB',1,'共同申请人手机'],['C1_COM',1,'共同申请人单位'],['C1_ADR',3,'共同申请人地址']],
		['C2_NAME','C2_ID_NO','C2_HM_PHO','C2_W_PHO'],
		['C2_MOB','C2_COM',['C2_ADR',3]],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		['C4_NAME','C4_ID_NO','C4_HM_PHO','C4_W_PHO'],
		['C4_MOB','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		['C6_NAME','C6_ID_NO','C6_HM_PHO','C6_W_PHO'],
		['C6_MOB','C6_COM',['C6_ADR',3]],
		[['O_REC',3],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}