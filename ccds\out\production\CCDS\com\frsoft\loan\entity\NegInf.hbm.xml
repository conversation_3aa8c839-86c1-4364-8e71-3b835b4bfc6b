<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.NegInf" table="neg_inf" schema="dbo" >
        <id name="neiId" type="java.lang.Long">
            <column name="nei_id" />
            <generator class="identity" />
        </id>
        <property name="neiName" type="java.lang.String">
            <column name="nei_name" length="100" />
        </property>
        <property name="neiInf" type="java.lang.String">
            <column name="nei_inf" length="500" />
        </property>
         <property name="neiInsUser" type="java.lang.String">
            <column name="nei_ins_user" length="50" />
        </property>
        <property name="neiInsTime" type="java.util.Date">
            <column name="nei_ins_time" length="23" />
        </property>
        <property name="neiAltUser" type="java.lang.String">
            <column name="nei_alt_user" length="25" />
        </property>
        <property name="neiAltTime" type="java.util.Date">
            <column name="nei_alt_time" length="23" />
        </property>
        <many-to-one name="neiSeason" class="com.frsoft.loan.entity.LoanSeason">
            <column name="nei_lse_id" />
        </many-to-one>
    </class>
</hibernate-mapping>
