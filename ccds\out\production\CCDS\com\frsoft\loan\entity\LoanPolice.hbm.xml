<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.LoanPolice" table="loan_police" schema="dbo" >
        <id name="lpolId" type="java.lang.Long">
            <column name="lpol_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="lpolLoanSeason" class="com.frsoft.loan.entity.LoanSeason" fetch="select">
            <column name="lpol_lse_id" />
        </many-to-one>
        <property name="lpolName" type="java.lang.String">
            <column name="lpol_name" length="50" />
        </property>
        <property name="lpolCardType" type="java.lang.String">
            <column name="lpol_card_type" length="20" />
        </property>
        <property name="lpolCardNum" type="java.lang.String">
            <column name="lpol_card_num" length="50" />
        </property>
        <property name="lpolContent" type="java.lang.String">
            <column name="lpol_content" length="1073741823" />
        </property>
        <property name="lpolInsUser" type="java.lang.String">
            <column name="lpol_ins_user" length="25" />
        </property>
        <property name="lpolInsTime" type="java.util.Date">
            <column name="lpol_ins_time" length="23" />
        </property>
        <property name="lpolAltUser" type="java.lang.String">
            <column name="lpol_alt_user" length="25" />
        </property>
        <property name="lpolAltTime" type="java.util.Date">
            <column name="lpol_alt_time" length="23" />
        </property>
    </class>
</hibernate-mapping>
