-- 添加最后一批重要表到CCDS数据库
-- 确保达到接近106个表的目标

USE `ccds`;

-- 61. 访问记录表
DROP TABLE IF EXISTS `vis_record`;
CREATE TABLE `vis_record` (
  `vr_id` bigint NOT NULL AUTO_INCREMENT,
  `vr_state` int DEFAULT NULL,
  `vr_adr_id` bigint DEFAULT NULL,
  `vr_cas_id` bigint DEFAULT NULL,
  `vr_num` int DEFAULT NULL,
  `vr_typ_id1` bigint DEFAULT NULL,
  `vr_typ_id2` bigint DEFAULT NULL,
  `vr_typ_id3` bigint DEFAULT NULL,
  `vr_typ_id` bigint DEFAULT NULL,
  `vr_name` varchar(50) DEFAULT NULL,
  `vr_sex` varchar(1) DEFAULT NULL,
  `vr_age` int DEFAULT NULL,
  `vr_req` longtext,
  `vr_remark` longtext,
  `vr_report` longtext,
  `vr_est_date` datetime DEFAULT NULL,
  `vr_rel_date` datetime DEFAULT NULL,
  `vr_app_user` varchar(25) DEFAULT NULL,
  `vr_app_time` datetime DEFAULT NULL,
  `vr_bk_time` datetime DEFAULT NULL,
  `vr_rec_user` longtext,
  `vr_adr` varchar(200) DEFAULT NULL,
  `vr_rs` varchar(50) DEFAULT NULL,
  `vr_is_prt` char(1) DEFAULT NULL,
  PRIMARY KEY (`vr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 62. 访问记录分配表
DROP TABLE IF EXISTS `vis_rec_ass`;
CREATE TABLE `vis_rec_ass` (
  `vra_id` bigint NOT NULL AUTO_INCREMENT,
  `vra_vr_id` bigint DEFAULT NULL,
  `vra_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`vra_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 63. 案件核查表
DROP TABLE IF EXISTS `case_hp`;
CREATE TABLE `case_hp` (
  `ch_id` bigint NOT NULL AUTO_INCREMENT,
  `ch_chk_state` int DEFAULT NULL,
  `ch_typ` int DEFAULT NULL,
  `ch_text` longtext,
  `ch_cat_1` varchar(20) DEFAULT NULL,
  `ch_cat_2` varchar(20) DEFAULT NULL,
  `ch_adr_id` bigint DEFAULT NULL,
  `ch_msg_state` int DEFAULT NULL,
  `ch_cas_id` bigint DEFAULT NULL,
  `ch_res` longtext,
  `ch_app_user` varchar(20) DEFAULT NULL,
  `ch_sur_user` varchar(20) DEFAULT NULL,
  `ch_app_time` datetime DEFAULT NULL,
  `ch_sur_time` datetime DEFAULT NULL,
  `ch_remark` longtext,
  `ch_cont_user` varchar(20) DEFAULT NULL,
  `ch_adr` varchar(1000) DEFAULT NULL,
  `ch_count` int DEFAULT NULL,
  `ch_upd_time` datetime DEFAULT NULL,
  `ch_upd_man` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`ch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 64. 案件批次表
DROP TABLE IF EXISTS `case_batch`;
CREATE TABLE `case_batch` (
  `cbat_id` bigint NOT NULL AUTO_INCREMENT,
  `cbat_name` varchar(100) DEFAULT NULL,
  `cbat_desc` longtext,
  `cbat_cre_time` datetime DEFAULT NULL,
  `cbat_cre_man` varchar(25) DEFAULT NULL,
  `cbat_upd_time` datetime DEFAULT NULL,
  `cbat_upd_man` varchar(25) DEFAULT NULL,
  `cbat_state` int DEFAULT NULL,
  `cbat_count` int DEFAULT NULL,
  `cbat_file_path` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`cbat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 65. 案件收集表
DROP TABLE IF EXISTS `case_collection`;
CREATE TABLE `case_collection` (
  `cc_id` bigint NOT NULL AUTO_INCREMENT,
  `cc_cas_ids` longtext,
  `cc_cbat_id` bigint DEFAULT NULL,
  `cc_id_no` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 66. 用户区域表
DROP TABLE IF EXISTS `user_area`;
CREATE TABLE `user_area` (
  `uar_id` bigint NOT NULL AUTO_INCREMENT,
  `uar_user_code` varchar(50) DEFAULT NULL,
  `uar_area_id` bigint DEFAULT NULL,
  PRIMARY KEY (`uar_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 67. 项目阶段表
DROP TABLE IF EXISTS `pro_stage`;
CREATE TABLE `pro_stage` (
  `sta_id` bigint NOT NULL AUTO_INCREMENT,
  `sta_pro_id` bigint DEFAULT NULL,
  `sta_title` varchar(300) DEFAULT NULL,
  `sta_aim` varchar(300) DEFAULT NULL,
  `sta_start_date` datetime DEFAULT NULL,
  `sta_end_date` datetime DEFAULT NULL,
  `sta_remark` longtext,
  `sta_ins_date` datetime DEFAULT NULL,
  `sta_mod_date` datetime DEFAULT NULL,
  `sta_isdel` char(1) DEFAULT NULL,
  `sta_inp_user` varchar(50) DEFAULT NULL,
  `sta_upd_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`sta_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 68. 销售组织表
DROP TABLE IF EXISTS `sal_org`;
CREATE TABLE `sal_org` (
  `so_code` varchar(50) NOT NULL,
  `so_name` varchar(50) DEFAULT NULL,
  `so_con_area` longtext,
  `so_loc` longtext,
  `so_user_code` varchar(50) DEFAULT NULL,
  `so_emp_num` varchar(50) DEFAULT NULL,
  `so_resp` longtext,
  `so_org_code` varchar(50) DEFAULT NULL,
  `so_remark` longtext,
  `so_isenabled` char(1) DEFAULT NULL,
  `so_up_code` varchar(50) DEFAULT NULL,
  `so_cost_center` varchar(100) DEFAULT NULL,
  `so_org_nature` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`so_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 69. 订单产品关系表
DROP TABLE IF EXISTS `r_ord_pro`;
CREATE TABLE `r_ord_pro` (
  `rop_id` bigint NOT NULL AUTO_INCREMENT,
  `rop_ord_code` bigint DEFAULT NULL,
  `rop_pro_id` bigint DEFAULT NULL,
  `rop_num` decimal(18,2) DEFAULT NULL,
  `rop_real_price` decimal(18,2) DEFAULT NULL,
  `rop_remark` longtext,
  `rop_price` decimal(18,2) DEFAULT NULL,
  `rop_zk` varchar(50) DEFAULT NULL,
  `rop_out_num` decimal(18,2) DEFAULT NULL,
  `rop_real_num` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`rop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 70. 询价产品关系表
DROP TABLE IF EXISTS `r_inq_pro`;
CREATE TABLE `r_inq_pro` (
  `rqp_id` bigint NOT NULL AUTO_INCREMENT,
  `rqp_inq_id` bigint DEFAULT NULL,
  `rqp_wpr_id` bigint DEFAULT NULL,
  `rqp_num` decimal(18,2) DEFAULT NULL,
  `rqp_price` decimal(18,2) DEFAULT NULL,
  `rqp_all_price` decimal(18,2) DEFAULT NULL,
  `rqp_remark` longtext,
  PRIMARY KEY (`rqp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 71. 报价产品关系表
DROP TABLE IF EXISTS `r_quo_pro`;
CREATE TABLE `r_quo_pro` (
  `rup_id` bigint NOT NULL AUTO_INCREMENT,
  `rup_quo_id` bigint DEFAULT NULL,
  `rup_wpr_id` bigint DEFAULT NULL,
  `rup_num` decimal(18,2) DEFAULT NULL,
  `rup_price` decimal(18,2) DEFAULT NULL,
  `rup_all_price` decimal(18,2) DEFAULT NULL,
  `rup_remark` longtext,
  PRIMARY KEY (`rup_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 72. 销售过往付款表
DROP TABLE IF EXISTS `sal_paid_past`;
CREATE TABLE `sal_paid_past` (
  `sps_id` bigint NOT NULL AUTO_INCREMENT,
  `sps_ord_code` bigint DEFAULT NULL,
  `sps_fct_date` datetime DEFAULT NULL,
  `sps_count` int DEFAULT NULL,
  `sps_type_id` bigint DEFAULT NULL,
  `sps_pay_type` varchar(50) DEFAULT NULL,
  `sps_pay_mon` decimal(18,2) DEFAULT NULL,
  `sps_mon_type` varchar(50) DEFAULT NULL,
  `sps_user_code` varchar(50) DEFAULT NULL,
  `sps_se_no` bigint DEFAULT NULL,
  `sps_isinv` char(1) DEFAULT NULL,
  `sps_remark` longtext,
  `sps_alt_user` varchar(50) DEFAULT NULL,
  `sps_cre_date` datetime DEFAULT NULL,
  `sps_alt_date` datetime DEFAULT NULL,
  `sps_isdel` char(1) DEFAULT NULL,
  `sps_code` varchar(300) DEFAULT NULL,
  `sps_aco_id` bigint DEFAULT NULL,
  `sps_out_name` varchar(100) DEFAULT NULL,
  `sps_content` varchar(100) DEFAULT NULL,
  `sps_acc_type_id` bigint DEFAULT NULL,
  `sps_undo_date` datetime DEFAULT NULL,
  `sps_undo_user` varchar(50) DEFAULT NULL,
  `sps_cus_id` bigint DEFAULT NULL,
  PRIMARY KEY (`sps_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 73. 账户锁表
DROP TABLE IF EXISTS `acc_lock`;
CREATE TABLE `acc_lock` (
  `table_name` varchar(50) NOT NULL,
  `table_max` bigint DEFAULT NULL,
  PRIMARY KEY (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 74. 额外信息表
DROP TABLE IF EXISTS `extra_inf`;
CREATE TABLE `extra_inf` (
  `exi_id` bigint NOT NULL AUTO_INCREMENT,
  `exi_id_number` varchar(50) DEFAULT NULL,
  `exi_type` varchar(50) DEFAULT NULL,
  `exi_content` text,
  `exi_cre_time` datetime DEFAULT NULL,
  `exi_cre_man` varchar(25) DEFAULT NULL,
  `exi_upd_time` datetime DEFAULT NULL,
  `exi_upd_man` varchar(25) DEFAULT NULL,
  `exi_name` varchar(50) DEFAULT NULL,
  `exi_remark` text,
  PRIMARY KEY (`exi_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 75. 银行客户表
DROP TABLE IF EXISTS `bank_customer`;
CREATE TABLE `bank_customer` (
  `bc_id` bigint NOT NULL AUTO_INCREMENT,
  `bc_name` varchar(50) DEFAULT NULL,
  `bc_id_no` varchar(50) DEFAULT NULL,
  `bc_phone` varchar(50) DEFAULT NULL,
  `bc_address` varchar(500) DEFAULT NULL,
  `bc_work_address` varchar(500) DEFAULT NULL,
  `bc_work_phone` varchar(50) DEFAULT NULL,
  `bc_email` varchar(100) DEFAULT NULL,
  `bc_bank` varchar(100) DEFAULT NULL,
  `bc_card_no` varchar(50) DEFAULT NULL,
  `bc_m` decimal(18,2) DEFAULT 0.00,
  `lc_principal` decimal(18,2) DEFAULT NULL,
  `lc_interest` decimal(18,2) DEFAULT NULL,
  `lc_penalty` decimal(18,2) DEFAULT NULL,
  `lc_fee` decimal(18,2) DEFAULT NULL,
  `lc_total` decimal(18,2) DEFAULT NULL,
  `bc_overdue_date` varchar(200) DEFAULT NULL,
  `bc_pback_p` float DEFAULT NULL,
  `bc_wpost_code` varchar(50) DEFAULT NULL,
  `bc_deadline` varchar(200) DEFAULT NULL,
  `bc_is_host` varchar(50) DEFAULT NULL,
  `bc_bill_date` varchar(200) DEFAULT NULL,
  `bc_last_paid` varchar(200) DEFAULT NULL,
  `bc_count` varchar(100) DEFAULT NULL,
  `bc_left_pri` varchar(100) DEFAULT NULL,
  `bc_overdue_days` int DEFAULT NULL,
  `bc_overdue_days_str` varchar(200) DEFAULT NULL,
  `bc_bir` varchar(50) DEFAULT NULL,
  `bc_mpost_code` varchar(50) DEFAULT NULL,
  `bc_perm_crline` varchar(50) DEFAULT NULL,
  `bc_alt_hold` varchar(50) DEFAULT NULL,
  `bc_ins_time` datetime DEFAULT NULL,
  `bc_ins_user` varchar(25) DEFAULT NULL,
  `bc_alt_time` datetime DEFAULT NULL,
  `bc_alt_user` varchar(25) DEFAULT NULL,
  `bc_remark` longtext,
  PRIMARY KEY (`bc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

SELECT 'CCDS数据库最终扩展完成 - 添加了最后一批重要表' as message;
