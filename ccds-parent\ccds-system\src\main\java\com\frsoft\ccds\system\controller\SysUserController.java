package com.frsoft.ccds.system.controller;

import com.frsoft.ccds.common.core.controller.BaseController;
import com.frsoft.ccds.common.core.domain.AjaxResult;
import com.frsoft.ccds.common.core.domain.model.LoginUser;
import com.frsoft.ccds.system.domain.SysUser;
import com.frsoft.ccds.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户信息
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;

    /**
     * 获取用户列表
     */
    @PreAuthorize("hasPermission('system:user:list')")
    @GetMapping("/list")
    public AjaxResult list(SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        return AjaxResult.success(list);
    }

    /**
     * 根据用户编码获取详细信息
     */
    @PreAuthorize("hasPermission('system:user:query')")
    @GetMapping(value = "/{userCode}")
    public AjaxResult getInfo(@PathVariable("userCode") String userCode) {
        return AjaxResult.success(userService.selectUserByUserCode(userCode));
    }

    /**
     * 新增用户
     */
    @PreAuthorize("hasPermission('system:user:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("hasPermission('system:user:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("hasPermission('system:user:remove')")
    @DeleteMapping("/{userCodes}")
    public AjaxResult remove(@PathVariable String[] userCodes) {
        return toAjax(userService.deleteUserByUserCodes(userCodes));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("hasPermission('system:user:resetPwd')")
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        return toAjax(userService.resetUserPassword(user.getUserCode(), user.getUserPwd()));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("hasPermission('system:user:edit')")
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 导入用户数据
     *
     * @param file 导入文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @PreAuthorize("hasPermission('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        // TODO: 实现Excel导入功能
        return AjaxResult.success("导入成功");
    }

    /**
     * 导出用户数据
     */
    @PreAuthorize("hasPermission('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        // TODO: 实现Excel导出功能
    }


}