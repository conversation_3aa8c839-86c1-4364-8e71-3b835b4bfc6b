<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>导出信函</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/> 
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>  
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript">
		function check(){
			var errStr = "";
			if(isEmpty("tmpId")){
				errStr+="- 未选择导出模板！\n";
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				waitSubmit("doOut");
				waitSubmit("doCancel");
				return $("outForm").submit();
			}
		}
		window.onload = function (){
			if("${listType}"=="vis"){
				$("outForm").action = "visRecordAction.do";
				$("ids").value = getBacthIds();
			}
			else{
				$("ids").value = getBacthIds('-')[0];
			}
			
		}
  	</script>
  </head>
  <body>
  	<div class="inputDiv">
        <form id="outForm" action="assitanceAction.do" method="post">
            <input type="hidden" name="op" value="outMail" />
            <input type="hidden" id="ids" name="ids"/>
            <table class="dashTab inputForm single" cellpadding="0" cellspacing="0">                       
                   <tr class="noBorderBot">
                       	<th>信函模板：</th>
                        <td>
                        <c:if test="${!empty templateList}">
                        <select id="tmpId" name="tmpId" class="inputSize2 inputBoxAlign">
                            <c:forEach items="${templateList}" var="t">
                            <option value="${t.tmpId}">${t.tmpName}</option>
                            </c:forEach>
                        </select>
                        </c:if>
                        <c:if test="${empty templateList}">
                            <select id="tmpId" class="inputSize2 inputBoxAlign" disabled="disabled">
                                <option>未添加模板</option>
                            </select>
                        </c:if>
                        </td>
                   	</tr>
                    <tr class="submitTr">
                        <td colspan="2">
                        <input id="doOut" class="butSize1" type="button" value="导出" onClick="check()" />
                        &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;
                        <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                        </td>
                    </tr>                   
            </table>
        </form>
    </div>
  </body>
</html>
