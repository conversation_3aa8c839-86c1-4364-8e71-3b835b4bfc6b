<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.SalEmp" table="sal_emp" schema="dbo" >
        <id name="seNo" type="java.lang.Long">
            <column name="se_no"  />
            <generator class="identity" />
        </id>
        <many-to-one name="salOrg" class="com.frsoft.base.entity.SalOrg" fetch="select" not-null="false">
            <column name="se_so_code" length="50" />
        </many-to-one>
         <many-to-one name="limRole" class="com.frsoft.base.entity.LimRole" fetch="select" not-null="false">
            <column name="se_job_lev"/>
        </many-to-one>
        <!-- <one-to-one name="limUser" property-ref="salEmp"/>  -->
        <property name="seName" type="java.lang.String">
            <column name="se_name" length="50" />
        </property>
         <property name="seCode" type="java.lang.String">
            <column name="se_code" length="50" />
        </property>
        <property name="seUserCode" type="java.lang.String">
            <column name="se_user_code" length="50" />
        </property>
        <property name="seIdeCode" type="java.lang.String">
            <column name="se_ide_code" length="50" />
        </property>
        <property name="sePos" type="java.lang.String">
            <column name="se_pos" length="50" />
        </property>
        <property name="seSex" type="java.lang.String">
            <column name="se_sex" length="50" />
        </property>
        <property name="seProb" type="java.lang.String">
            <column name="se_prob" length="50" />
        </property>
        <property name="seBirPlace" type="java.lang.String">
            <column name="se_bir_place" length="50" />
        </property>
        <property name="seAccPlace" type="java.lang.String">
            <column name="se_acc_place" length="100" />
        </property>
        <property name="seBirth" type="java.lang.String">
            <column name="se_birth" length="50" />
        </property>
        <property name="seMarry" type="java.lang.String">
            <column name="se_marry" length="10" />
        </property>
        <property name="seType" type="java.lang.String">
            <column name="se_type" length="50" />
        </property>
        <property name="seJobCate" type="java.lang.String">
            <column name="se_job_cate" length="50" />
        </property>
        <property name="seJobTitle" type="java.lang.String">
            <column name="se_job_title" length="50" />
        </property>
        <property name="seStartDay" type="java.util.Date">
            <column name="se_start_day" length="23" />
        </property>
        <property name="seYearPay" type="java.lang.String">
            <column name="se_year_pay" length="50" />
        </property>
        <property name="seCostCenter" type="java.lang.String">
            <column name="se_cost_center" length="50" />
        </property>
        <property name="seEmail" type="java.lang.String">
            <column name="se_email" length="50" />
        </property>
        <property name="seNation" type="java.lang.String">
            <column name="se_nation" length="50" />
        </property>
        <property name="sePoliStatus" type="java.lang.String">
            <column name="se_poli_status" length="50" />
        </property>
        <property name="seEdu" type="java.lang.String">
            <column name="se_edu" length="50" />
        </property>
        <property name="seTel" type="java.lang.String">
            <column name="se_tel" length="50" />
        </property>
        <property name="sePhone" type="java.lang.String">
            <column name="se_phone" length="50" />
        </property>
        <property name="seQq" type="java.lang.String">
            <column name="se_qq" length="50" />
        </property>
        <property name="seMsn" type="java.lang.String">
            <column name="se_msn" length="50" />
        </property>
        <property name="seRecSource" type="java.lang.String">
            <column name="se_rec_source" length="100" />
        </property>
        <property name="seProvFund" type="java.lang.String">
            <column name="se_prov_fund" length="50" />
        </property>
        <property name="seJobDate" type="java.util.Date">
            <column name="se_job_date" length="23" />
        </property>
        <property name="seHouReg" type="java.lang.String">
            <column name="se_hou_reg" length="50" />
        </property>
        <property name="seSocialCode" type="java.lang.String">
            <column name="se_social_code" length="50" />
        </property>
        <property name="seRap" type="java.lang.String">
            <column name="se_rap" length="50" />
        </property>
        <property name="seAddress" type="java.lang.String">
            <column name="se_address" length="**********" />
        </property>
        <property name="seRemark" type="java.lang.String">
            <column name="se_remark" length="**********" />
        </property>
        <property name="seEdcBac" type="java.lang.String">
            <column name="se_edc_bac" length="**********" />
        </property>
        <property name="seWorkEx" type="java.lang.String">
            <column name="se_work_ex" length="**********" />
        </property>
        <property name="seBankName" type="java.lang.String">
            <column name="se_bank_name" length="50" />
        </property>
        <property name="seBankCard" type="java.lang.String">
            <column name="se_bank_card" length="50" />
        </property>
        <property name="seWealAddress" type="java.lang.String">
            <column name="se_weal_address" length="50" />
        </property>
        <property name="seWealPos" type="java.lang.String">
            <column name="se_weal_pos" length="50" />
        </property>
        <property name="seIsovertime" type="java.lang.String">
            <column name="se_isovertime" length="50" />
        </property>
        <property name="seAttendance" type="java.lang.String">
            <column name="se_attendance" length="50" />
        </property>
        <property name="seCardNum" type="java.lang.String">
            <column name="se_card_num" length="50" />
        </property>
         <property name="sePic" type="java.lang.String">
            <column name="se_pic" length="**********" />
        </property>
        <property name="seIsenabled" type="java.lang.String">
            <column name="se_isenabled" length="10" />
        </property>
        <property name="seInserDate" type="java.util.Date">
            <column name="se_inser_date" length="23" />
        </property>
         <property name="seAltDate" type="java.util.Date">
            <column name="se_alt_date" length="23" />
        </property>
         <property name="seLog" type="java.lang.String">
            <column name="se_log" length="**********" />
        </property>
         <property name="seInserUser" type="java.lang.String">
            <column name="se_inser_user" length="50" />
        </property>
        <property name="seAltUser" type="java.lang.String">
            <column name="se_alt_user" length="50" />
        </property>
        <property name="seEndDate" type="java.util.Date">
            <column name="se_end_date" length="23" />
        </property>
        <property name="sePerTel" type="java.lang.String">
            <column name="se_per_tel" length="50" />
        </property>
        <property name="sePlanSignDate" type="java.util.Date">
            <column name="se_plan_sign_date" length="23" />
        </property>
        <property name="seSignDate" type="java.util.Date">
            <column name="se_sign_date" length="23" />
        </property>
        <property name="seCreditDate" type="java.util.Date">
            <column name="se_credit_date" length="23" />
        </property>
        <property name="seCollege" type="java.lang.String">
            <column name="se_college" length="200" />
        </property>
        <property name="seTransfer" type="java.lang.String">
            <column name="se_transfer" length="**********" />
        </property>
        <set name="messages" inverse="true"  order-by="me_code" cascade="all">
            <key>
                <column name="me_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.Message" />
        </set>
        <set name="rmessLims" inverse="true"  order-by="rml_id" cascade="all">
            <key>
                <column name="rml_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.RMessLim" />
        </set>
        <set name="reports" inverse="true"  order-by="rep_code" cascade="all">
            <key>
                <column name="rep_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.Report" />
        </set>
        <set name="rrepLims" inverse="true"  order-by="rrl_id" cascade="all">
            <key>
                <column name="rrl_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.RRepLim" />
        </set>
        <set name="news" inverse="true"  order-by="new_code" cascade="all">
            <key>
                <column name="new_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.News" />
        </set>
        <set name="rnewLims" inverse="true"  order-by="rnl_id" cascade="all">
            <key>
                <column name="rnl_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.RNewLim" />
        </set>
        <set name="schedules" inverse="true"  order-by="sch_id" cascade="all">
            <key>
                <column name="sch_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.Schedule" />
        </set>
        <set name="salTasks" inverse="true"  order-by="st_id" cascade="all">
            <key>
                <column name="st_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.SalTask" />
        </set>
        <set name="taLims" inverse="true"  order-by="ta_lim_id" cascade="all">
            <key>
                <column name="ta_se_no"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.TaLim" />
        </set>
        <!-- 
        <set name="salPaidPasts" inverse="true"  order-by="sps_id" cascade="all">
            <key>
                <column name="sps_se_no"/>
            </key>
            <one-to-many class="com.frsoft.cis.entity.SalPaidPast" />
        </set>
        <set name="cusCorCus" inverse="true"  order-by="cor_code" cascade="all">
            <key>
                <column name="cor_se_no"/>
            </key>
            <one-to-many class="com.frsoft.cis.entity.CusCorCus" />
        </set>
        <set name="cusContacts" inverse="true"  order-by="con_id" cascade="all">
            <key>
                <column name="con_se_no"/>
            </key>
            <one-to-many class="com.frsoft.cis.entity.CusContact" />
        </set> 
         <set name="salPras" inverse="true"  order-by="pra_id" cascade="all">
            <key>
                <column name="pra_se_no"/>
            </key>
            <one-to-many class="com.frsoft.cis.entity.SalPra" />
        </set>
        <set name="salOrdCons" inverse="true"  order-by="sod_code" cascade="all">
            <key>
                <column name="sod_se_no"/>
            </key>
            <one-to-many class="com.frsoft.cis.entity.SalOrdCon" />
        </set> 
        
        <set name="bankCase" inverse="true"  order-by="cas_id" cascade="all">
            <key>
                <column name="cas_se_no"/>
            </key>
            <one-to-many class="com.frsoft.ccds.entity.BankCase" />
        </set>
       
        <set name="phoRed" inverse="true"  order-by="pr_id" cascade="all">
            <key>
                <column name="pr_se_no"/>
            </key>
            <one-to-many class="com.frsoft.ccds.entity.PhoRed" />
        </set> -->
        <set name="attachments" inverse="true"  cascade="all" where="att_type='emp'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
    	</set>
    </class>
</hibernate-mapping>
