<%@ page language="java" pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>选择案件</title>
    
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/case.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/chooseBrow.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
  	<script language="javascript" type="text/javascript">
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			dataId = obj.casId;
			var dblFunc="descPop('caseAction.do?op=caseDesc&caseId="+obj.casId+"&view=cp')";
			var casCode="<a href=\"javascript:void(0)\" onclick=\"chooseCase('"+obj.casId+"','"+encodeString(obj.casCode)+"','"+(obj.salEmp?obj.salEmp.seNo:"")+"','"+(obj.salEmp?encodeString(obj.salEmp.seName):"")+"');return false;\">"+obj.casCode+"</a>";
			className = getCaseColor(obj);
			datas = [casCode, obj.casDate, obj.casName, obj.casCaCd, obj.casM, obj.salEmp?obj.salEmp.seName:"", obj.casPaidM ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars = $("searchForm").serialize(true);
			pars.op = "listCasesToChoose";
			
			var loadFunc = "loadList";
			var cols=[
				{name:"个案序列号",align:"left"},
				{name:"委案日期",renderer:"date"},
				{name:getCasNameTxt("${CUS_VER_ID}")},
				{name:getCasCaCdTxt("${CUS_VER_ID}"),align:"left"},
				{name:"委案金额",renderer:"money",align:"right"},
				{name:"催收员"},
				{name:"已还款",renderer:"money",align:"right"}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("selectCaseTab","dataList");
		createProgressBar();
		window.onload=function(){
			loadList();
			//增加清空按钮
			createCancelButton(loadList,'searchForm',-50,5);
			$("casNameTxt").innerHTML=getCasNameTxt("${CUS_VER_ID}");
			$("casCaCdTxt").innerHTML=getCasCaCdTxt("${CUS_VER_ID}");
		}
  	</script>
  </head>
  
  <body>
  	<div class="browLayer">
        <div class="listSearch">
            <form style="margin:0px; padding:0px;" id="searchForm" onSubmit="loadList();return false;">
                 个案序列号：<input style="width:100px" class="inputSize2 inputBoxAlign" type="text" id="caseCode" name="caseCode" onBlur="autoShort(this,100)"/>&nbsp;  
                 <span id="casNameTxt"></span>：<input style="width:80px" class="inputSize2 inputBoxAlign" type="text" id="caseName" name="caseName" onBlur="autoShort(this,50)"/>&nbsp;    
                 <span id="casCaCdTxt"></span>：<input style="width:100px" class="inputSize2 inputBoxAlign" type="text" id="casCaCd" name="casCaCd" onBlur="autoShort(this,50)"/>&nbsp;
                 <input type="submit" class="butSize3 inputBoxAlign" value="查询"/>&nbsp;&nbsp;
            </form>
        </div>
        <div id="dataList" class="dataList"></div>
	</div>
  </body>
  
</html>
