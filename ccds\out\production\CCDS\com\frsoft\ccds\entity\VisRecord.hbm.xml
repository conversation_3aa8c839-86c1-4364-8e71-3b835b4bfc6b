<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.VisRecord" table="vis_record" schema="dbo" >
        <id name="vrId" type="java.lang.Long">
            <column name="vr_id" />
            <generator class="identity" />
        </id>
        <property name="vrState" type="java.lang.Integer">
            <column name="vr_state" />
        </property>
        <many-to-one name="vrAddress" class="com.frsoft.ccds.entity.Address" fetch="select" not-null="false">
            <column name="vr_adr_id"/>
        </many-to-one>
        <many-to-one name="bankCase" class="com.frsoft.ccds.entity.BankCase" fetch="select" not-null="false">
            <column name="vr_cas_id"/>
        </many-to-one>
        <property name="vrNum" type="java.lang.Integer">
            <column name="vr_num" />
        </property>
        <many-to-one name="vrArea3" class="com.frsoft.base.entity.CusCity" fetch="select" not-null="false">
            <column name="vr_typ_id1"/>
        </many-to-one>
        <many-to-one name="vrArea2" class="com.frsoft.base.entity.CusProvince" fetch="select" not-null="false">
            <column name="vr_typ_id2"/>
        </many-to-one>
        <many-to-one name="vrArea1" class="com.frsoft.base.entity.CusArea" fetch="select" not-null="false">
            <column name="vr_typ_id3"/>
        </many-to-one>
        <many-to-one name="typeListRe" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="vr_typ_id"/>
        </many-to-one>
        <property name="vrName" type="java.lang.String">
            <column name="vr_name" length="50" />
        </property>
        <property name="vrSex" type="java.lang.String">
            <column name="vr_sex" length="1" />
        </property>
        <property name="vrAge" type="java.lang.Integer">
            <column name="vr_age" />
        </property>
        <property name="vrReq" type="java.lang.String">
            <column name="vr_req" length="1073741823" />
        </property>
        <property name="vrRemark" type="java.lang.String">
            <column name="vr_remark" length="1073741823" />
        </property>
        <property name="vrReport" type="java.lang.String">
            <column name="vr_report" length="1073741823" />
        </property>
        <property name="vrEstDate" type="java.util.Date">
            <column name="vr_est_date" length="23" />
        </property>
        <property name="vrRelDate" type="java.util.Date">
            <column name="vr_rel_date" length="23" />
        </property>
        <property name="vrAppUser" type="java.lang.String">
            <column name="vr_app_user" length="25" />
        </property>
        <property name="vrRecUser" type="java.lang.String">
            <column name="vr_rec_user" length="1073741823" />
        </property>
        <property name="vrAppTime" type="java.util.Date">
            <column name="vr_app_time" length="23" />
        </property>
        <property name="vrBkTime" type="java.util.Date">
        	<column name="vr_bk_time" length="23"></column>
        </property>
        <property name="vrAdr" type="java.lang.String">
            <column name="vr_adr" length="500" />
        </property>
        <property name="vrRs" type="java.lang.String">
            <column name="vr_rs" length="50" />
        </property>
        <property name="vrIsPrt" type="java.lang.String">
            <column name="vr_is_prt" length="1" />
        </property>
        <set name="visRASet" cascade="all" inverse="true">
			<key column="vra_vr_id"></key>
			<one-to-many class="com.frsoft.ccds.entity.VisRecAss"/>
		</set>
		<set name="attachments" inverse="true"  cascade="all" where="att_type='vis'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
    	</set>
    </class>
</hibernate-mapping>
