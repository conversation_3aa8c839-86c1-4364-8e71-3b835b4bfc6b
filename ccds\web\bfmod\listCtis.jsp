<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>通道列表</title>
    
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="js/bfmod.js"></script>
  	<script language="javascript" type="text/javascript">
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			dataId = obj.ctisId;
			datas = [obj.ctisName, obj.ctisIp, obj.ctisCreTime,  obj.ctisCreMan, obj.ctisUpdTime, obj.ctisUpdMan, "<img onClick=\"ctiPopDiv(11,'"+dataId+"')\" class=\"hand\" src=\"images/content/edit.gif\" alt=\"编辑\"/>&nbsp;&nbsp;<img onClick=\"ctiDelDiv(1,'"+dataId+"')\" class=\"hand\" src=\"images/content/del.gif\" alt=\"删除\"/>" ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "ctiServerAction.do";
			var pars = [];
			pars.op="listCtis";
			var loadFunc = "loadList";
			var cols=[
				{name:"服务器名称"},
				{name:"服务器IP"},
				{name:"添加时间",renderer:"time"},
				{name:"添加人"},
				{name:"修改时间",renderer:"time"},
				{name:"修改人"},
				{name:"操作"}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("ctisListTab","dataList");
    	gridEl.config.sortable = false;
    	//gridEl.config.hasCheckBox = true;
		createProgressBar();
		window.onload=function(){
			loadList();	
			//增加清空按钮
			//createCancelButton(loadList,'searchForm',-50,5,'searButton','after');
		}
  	</script>
  </head>
  
  <body>
    <div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>录音管理 > 服务器设置 <span id="changeFuncBt" onMouseOver="popFuncMenu(['rec',2],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['rec',2],true)" onMouseOut="popFuncMenu(['rec',2],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
           	</div>
  	   		<table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                             <div id="tabType1" class="tabTypeWhite" onClick="self.location.href='ctiServerAction.do?op=toListCtis'">服务器设置</div>
                        </div>
                     </th>
                     <td>
                    	<a href="javascript:void(0)" onClick="ctiPopDiv(1);return false;" class="newBlueButton">添加服务器</a>
                    </td>
                </tr>
            </table>
            <script type="text/javascript">loadTabTypeWidth();</script>
            <div id="listContent">
               <!--  <div class="listSearch">
                	<form class="listSearchForm" id="searchForm" onSubmit="loadList();return false;" >
                    <table cellpadding="0" cellspacing="0">
                        <tr>
                        	<th>证件号：</th>
                            <td>
                            	<input class="inputSize2 inputBoxAlign" type="text" id="idNumber" name="idNumber" onBlur="autoShort(this,25)"/>
                            	<input type="checkbox" class="inputBoxAlign" id="isMatchFst" name="isMatchFst" value="1" /><label for="isMatchFst" style="font-size:11px">匹配开头</label>&nbsp;&nbsp;&nbsp;
                            </td>    
                         	<th>姓名：</th>
                            <td><input class="inputSize2" type="text" name="exiName" onBlur="autoShort(this,50)"/></td>    
                         	<th>信息类型：</th>
                            <td><input class="inputSize2" type="text" name="type" onBlur="autoShort(this,50)"/></td>
                         	<th>信息内容：</th>
                            <td><input class="inputSize2" style="width:150px" type="text" name="content" onBlur="autoShort(this,500)"/></td>
                            <th>导入日期：</th>
                            <td><input name="creDateStart" id="staTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('endTim');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'endTim\')}'})"/>&nbsp;到&nbsp;<input name="creDateEnd" id="endTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'staTim\')}'})"/></td>
                            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize1 inputBoxAlign" value="查询"/></td>
                         </tr>
                     </table>
                    </form>
                </div>
                <div id="toolsBarTop" class="bottomBar">
                    <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="cancelBatchAss(4,'案人数据')">删除案人数据</span>
                </div> -->
                <div id="dataList" class="dataList"></div>	
                
            </div>
  		</div> 
	</div>
  </body>
  
</html>
