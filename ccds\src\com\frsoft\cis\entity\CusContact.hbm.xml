<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.CusContact" table="cus_contact" schema="dbo"  >
        <id name="conId" type="java.lang.Long">
            <column name="con_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="cusCorCus" class="com.frsoft.cis.entity.CusCorCus" fetch="select" not-null="false">
            <column name="con_cor_code" length="50" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select"  not-null="false">
            <column name="con_se_no" length="50" />
        </many-to-one>
        <property name="conName" type="java.lang.String">
            <column name="con_name" length="50" />
        </property>
        <property name="conSex" type="java.lang.String">
            <column name="con_sex" length="10" />
        </property>
        <property name="conDep" type="java.lang.String">
            <column name="con_dep" length="1073741823" />
        </property>
        <property name="conService" type="java.lang.String">
            <column name="con_service" length="100" />
        </property>
        <property name="conLev" type="java.lang.String">
            <column name="con_lev" length="50" />
        </property>
        <property name="conPhone" type="java.lang.String">
            <column name="con_phone" length="50" />
        </property>
        <property name="conWorkPho" type="java.lang.String">
            <column name="con_work_pho" length="50" />
        </property>
        <property name="conHomePho" type="java.lang.String">
            <column name="con_home_pho" length="50" />
        </property>
        <property name="conFex" type="java.lang.String">
            <column name="con_fex" length="50" />
        </property>
        <property name="conZipCode" type="java.lang.String">
            <column name="con_zip_code" length="50" />
        </property>
        <property name="conEmail" type="java.lang.String">
            <column name="con_email" length="100" />
        </property>
        <property name="conQq" type="java.lang.String">
            <column name="con_qq" length="50" />
        </property>
        <property name="conMsn" type="java.lang.String">
            <column name="con_msn" length="100" />
        </property>
        <property name="conAdd" type="java.lang.String">
            <column name="con_add" length="1073741823" />
        </property>
        <property name="conOthLink" type="java.lang.String">
            <column name="con_oth_link" length="1073741823" />
        </property>
        <property name="conType" type="java.lang.String">
            <column name="con_type" length="50" />
        </property>
        <property name="conBir" type="java.util.Date">
            <column name="con_bir" length="23" />
        </property>
        <property name="conHob" type="java.lang.String">
            <column name="con_hob" length="100" />
        </property>
        <property name="conTaboo" type="java.lang.String">
            <column name="con_taboo" length="100" />
        </property>
        <property name="conEdu" type="java.lang.String">
            <column name="con_edu" length="100" />
        </property>
        <property name="conPhoto" type="java.lang.String">
            <column name="con_photo" />
        </property>
        <property name="conRemark" type="java.lang.String">
            <column name="con_remark" length="1073741823" />
        </property>
        <property name="conCreDate" type="java.util.Date">
            <column name="con_cre_date" length="23" />
        </property>
        <property name="conModDate" type="java.util.Date">
            <column name="con_mod_date" length="23" />
        </property>
        <property name="conInpUser" type="java.lang.String">
            <column name="con_inp_user" length="50" />
        </property>
        <property name="conUpdUser" type="java.lang.String">
            <column name="con_upd_user" length="50" />
        </property>
        <property name="conIsDel" type="java.lang.String">
            <column name="con_isdel" length="1" />
        </property>
    </class>
</hibernate-mapping>
