<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.WmsWarIn" table="wms_war_in" schema="dbo" >
        <id name="wwiId" type="java.lang.Long">
            <column name="wwi_id"/>
            <generator class="identity" />
        </id>
        <many-to-one name="wmsStro" class="com.psit.struts.entity.WmsStro" fetch="select" not-null="false">
            <column name="wwi_stro_code" length="50" />
        </many-to-one>
        <many-to-one name="salPurOrd" class="com.psit.struts.entity.SalPurOrd" fetch="select" not-null="false">
            <column name="wwi_spo_code"/>
        </many-to-one>
        <property name="wwiOpman" type="java.lang.String">
            <column name="wwi_user_code" length="50" />
        </property>
        <property name="wwiCode" type="java.lang.String">
            <column name="wwi_code" length="50" />
        </property>
        <property name="wwiTitle" type="java.lang.String">
            <column name="wwi_title" length="1073741823" />
        </property>
        <property name="wwiInDate" type="java.util.Date">
            <column name="wwi_in_date" length="23" />
        </property>
        <property name="wwiState" type="java.lang.String">
            <column name="wwi_state" length="1" />
        </property>
        <property name="wwiRemark" type="java.lang.String">
            <column name="wwi_remark" length="1073741823" />
        </property>
        <property name="wwiIsdel" type="java.lang.String">
            <column name="wwi_isdel" length="1" />
        </property>
        <property name="wwiInpName" type="java.lang.String">
            <column name="wwi_inp_name" length="50" />
        </property>
        <property name="wwiAltName" type="java.lang.String">
            <column name="wwi_alt_name" length="50" />
        </property>
        <property name="wwiInpTime" type="java.util.Date">
            <column name="wwi_inp_time" length="23" />
        </property>
        <property name="wwiAltTime" type="java.util.Date">
            <column name="wwi_alt_time" length="23" />
        </property>
                <property name="wwiAppDate" type="java.util.Date">
            <column name="wwi_app_date" length="23" />
        </property>
        <property name="wwiAppMan" type="java.lang.String">
            <column name="wwi_app_man" length="50" />
        </property>
        <property name="wwiAppDesc" type="java.lang.String">
            <column name="wwi_app_desc" length="1073741823" />
        </property>
        <property name="wwiAppIsok" type="java.lang.String">
            <column name="wwi_app_isok" length="1" />
        </property>
        <property name="wwiCanDate" type="java.util.Date">
            <column name="wwi_can_date" length="23" />
        </property>
        <property name="wwiCanMan" type="java.lang.String">
            <column name="wwi_can_man" length="50" />
        </property>
        <set name="RWinPros" inverse="true"  cascade="delete" order-by="rwi_id">
            <key>
                <column name="rwi_win_id"/>
            </key>
            <one-to-many class="com.psit.struts.entity.RWinPro" />
        </set>
    </class>
</hibernate-mapping>
