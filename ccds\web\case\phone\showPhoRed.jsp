 <%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>历史电催记录</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
    
    	function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			datas = [obj.prTime, obj.prName, obj.prContact,  obj.prConType, obj.prContent, obj.typeList?obj.typeList.typName:"", obj.prPtpDate, obj.prPtpNum, obj.salEmp?obj.salEmp.seName:"" ];
			return [datas,className,dblFunc,dataId];
		}
    	function loadList(sortCol,isDe,pageSize,curP){
			var url = "";
			var pars = [];
			<logic:notEmpty name="isStat">
			url = "caseStatAction.do";
			pars.op = "listStatPRCat";
			pars.typName="${typName}";
			pars.empId="${empId}";
			pars.startDate="${startDate}";
			pars.endDate="${endDate}";
			pars.empIds="${empIds}";
			pars.bankName="${bankName}";
			pars.clArea="${clArea}";
			</logic:notEmpty>
			<logic:empty name="isStat">
			url = "phoneListAction.do";
			pars.op = "showPhoRe";
			if($("isAllCaseN").checked){
				pars.casId="${casId}";
			}
			pars.phone="${phone}";
			</logic:empty>
			var loadFunc = "loadList";
			var cols=[
				{name:"通话时间",width:'9%'},
				{name:"通话对象",width:'5%'},
				{name:"电话号码",align:"left",width:'11%'},
				{name:"电话类别",width:'10%'},
				{name:"通话内容",align:"left",width:'22%'},
				{name:"通话结果",width:'6%'},
				{name:"承诺日期",renderer:'date',width:'10%'},
				{name:"承诺金额",renderer:'moneyOrEmt',align:'right',width:'9%'},	
				{name:"催收员",width:'8%'}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("phoRedTab","dataList");
		gridEl.config.isShort=false;
		gridEl.config.isResize=false;
    	gridEl.config.sortable=false;
		createProgressBar();
		window.onload=function(){
			loadList();
		}
    </script>
</head>
  
  <body>
  	<logic:notEmpty name="isStat">
    	<div style="background-color:#2D5188; color:#FFF; padding:5px 5px 5px 10px; text-align:left;"><span class="bold bigger">${typName}</span>&nbsp;&nbsp;&nbsp;${empName}&nbsp;&nbsp;&nbsp;[${startDate} ～ ${endDate}]</div>
    </logic:notEmpty>
  	<logic:empty name="isStat">
  	<div style="padding:5px; text-align:left;"><input type="radio" id="isAllCaseN" name="isAllCase" value="0" checked onclick="loadList()"/><label for="isAllCaseN">本案催记</label>&nbsp;&nbsp;&nbsp;<input type="radio"  id="isAllCaseY" name="isAllCase" value="1"  onclick="loadList()" /><label for="isAllCaseY">同号码所有催记</label></div>
  	</logic:empty>
  	<div id="dataList" class="dataList"></div>
  </body>
</html>