<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
  	<base href="<%=basePath%>"></base>
    <title>自动分案</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
		#batState {
			padding:5px 2px 2px 2px;
		}
		#batState table {
			width:98%;
			font-size:12px;
			text-align:left;
		}
		#batState th {
			padding:4px 2px 2px 2px;
			text-align:right;
			border-bottom:#FFF 1px solid;
			background:#f0f5fd;
		}
		#batState td {
			padding:2px;
			text-align:left;
		}
		
		.inputLayer {
			text-align:left;
			padding:0 0 5px 0;
		}
		.inputTit {
			font-size:14px;
			font-weight:bold;
			color:#fff;
			background-color:#999;
			padding:4px 0 2px 4px;
		}
		.inputTit a {
			font-size:12px;
			font-weight:normal;
		}
		#empPctTitle {
			color:#999;
			padding:4px 0 0 4px;
		}
		#empPctLayer{
			margin:0;
			padding:2px;
			width:98%;
			height:280px;
			border:0;
		}
		#empPctLayer table {
			width:100%;
		}
		#empPctLayer td {
			padding:5px;
			border-bottom:#ddd 1px solid;
		}
		#empPctLayer .inputSize2 {
			width:80px;
		}
		#setAssignEmp {
			padding:5px;
		}
		
		#assignRsTab {
			width:100%;
		}
		#assignRsTab th, #assignRsTab td{
			font-size:12px;
			text-align:right;
		}
		
		#assignEmpForm, #saveAssignForm {
			margin:0;
			padding:0;
		}
		
		#showAssignRs {
			padding:5px;
		}
		#rsTabLayer {
			border:0;
			height:385px;
		}
		#rsTabLayer table{
			width:98%;
			font-size:12px;
			text-align:left;
		}
		#rsTabLayer .rsHead{
			background:#666;
			color:#FFF;
			padding:2px;
		}
		#rsTabLayer th{
			/*background:;*/
			border-bottom:#CCC 1px solid;
			padding:2px 0 0 5px;
		}
		#rsTabLayer td{
			/*background:;*/
			border-bottom:#ccc 1px solid;
			color:#666;
			padding:2px;
		}
    </style>
	<script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>  
    <script type="text/javascript" src="js/case.js"></script>
	<!--<script type="text/javascript" src="js/config.js"></script>-->
	<script type="text/javascript">
		function setIsAutoRecycle(){
			if($("isAutoRecycle").checked){
				showRecycleDate();
			}
			else{
				hideRecycleDate();
			}
		}
		
		function initPage(){
			$("totalAmt").innerHTML = "￥"+changeTwoDecimal(${totalAmt});
			$("assigned").innerHTML = (${totalCount}-${unAssignCount}).toString()+"(￥"+changeTwoDecimal(${totalAmt}-${unAssignAmt})+")";
			$("unAssign").innerHTML = (${unAssignCount}).toString()+"(￥"+changeTwoDecimal(${unAssignAmt})+")";
			if(canShowRecycleMod('${REG_TYPE}','${REG_FUNCTIONS}')){
				$("recycleInput").show();
			}
		}
		
		function treeCallBack(selectedNodes){
			if($('empPctTitle').style.visibility=='hidden'){
				$('empPctTitle').style.visibility='';
			}
			if(selectedNodes[0]!=''){
				$("nodeIds").value = selectedNodes[0];
				var empIds = selectedNodes[0].split(',');
				var empNames =  selectedNodes[1].split('];');
				var pctLayerHTML = [];
				for(var i=0;i<empIds.length;i++){
					if(empIds[i]!=''){
						pctLayerHTML.push("<tr><td style='width:30%; text-align:right'>"+trim(empNames[i])+"]</td><td><input class='inputBoxAlign inputSize2' type='text' name='assPct' onblur='checkPrice(this)'/>%</td></tr>");
					}
				}
				$('empPctLayer').innerHTML="<table class='normal' cellpadding=0 cellspacing=0>"+pctLayerHTML.join("")+"</table>";
			}
			else{
				$('empPctLayer').innerHTML="";
			}
		}
		function toShowAssignRs(){
			var assPctInps = $N("assPct");
			var emptyInps = [];
			for(var i=0;i<assPctInps.length;i++){
				if(isEmptyOfE(assPctInps[i])){
					emptyInps.push(1);
				}
			}
			if(isEmpty("nodeIds")){
				alert("未选择催收员！");
			}
			else if(emptyInps.length!=0&&emptyInps.length!=assPctInps.length){
				alert("有部分分配比例未填写！");
			}
			else{
				waitSubmit("nextBtn");
				createProgressBar();
				var url="caseAction.do";
				var pars = $("assignEmpForm").serialize(true);
				pars.op="toAutoAssign";
				//pars.batId="${caseBat.cbatId}";
				pars.totalAmt = "${totalAmt}";
				
				new Ajax.Request(url,{
					method : 'post',
					parameters : pars,
					onSuccess : function(response){
						closeProgressBar();
						$("setAssignEmp").hide();
						$("showAssignRs").show();
						if(response.responseText!=""){
							var dataList = response.responseText.evalJSON().list;
							loadAssignRsTable(dataList);
						}
						else{
							$("rsTabLayer").innerHTML="<span class='gray'>没有待分配案件</span>";
						}
					},
					onfailure : function(response){
						closeProgressBar();
						if (transport.status == 404) { alert("您访问的url地址不存在！"); }
						else { alert("Error: status code is " + transport.status); }
					}
				});
			}
		}
		
		function toReturn(){
			restoreSubmit("nextBtn");
			$("showAssignRs").hide();
			$("setAssignEmp").show();
		}
		
		function loadAssignRsTable(dataList){
			var tabHtml = [];
			tabHtml.push("<table id='assignRsTab' cellpadding='0' cellspacing='0' style='width:100%'>");
			var obj ;
			tabHtml.push("<tr class='rsHead'><th>催收员</th><th>案件数量</th><th>案件金额</th><th>比例</th></tr>");
			/*if($("assignType0").checked==true){
				tabHtml.push("<tr class='rsHead'><th>催收员</th><th>案件数量</th></tr>");
			}
			else{
				tabHtml.push("<tr class='rsHead'><th>催收员</th><th>案件金额</th></tr>");
			}*/
			for(var i=0; i<dataList.length; i++){
				obj = dataList[i];
				/*tabHtml.push("<tr><th>"+obj.name+"</th><td>");
				if($("assignType1").checked==true){ tabHtml.push("￥"+changeTwoDecimal(obj.num)); }
				else { tabHtml.push(obj.num); };*/
				tabHtml.push("<tr><th style='width:10%'>"+obj.name+"</th><td style='width:25%'>"+obj.num+"</td><td style='width:50%'>￥"+changeTwoDecimal(obj.amt)+"");
				tabHtml.push("<input type='hidden' name='empId' value='"+obj.id+"' /><input type='hidden' name='caseIds' value='"+obj.caseIds+"' /></td><td style='width:15%'>"+obj.pct+"</td></tr>");
			}
			tabHtml.push("</table>");
			$("rsTabLayer").innerHTML=tabHtml.join("");
		}
		function toSaveAssignRs(){
			waitSubmit("saveBtn");
			waitSubmit("retBtn");
			return $("saveAssignForm").submit();
		}
		
		createProgressBar();
		window.onload=function(){
			initPage();
			closeProgressBar();
      	}
		
  	</script>
  </head>
  
  <body style="background:#fff">
  	<div  id="batState">
  	<table cellpadding="0" cellspacing="0">
        <tr>
            <th>总案量：</th>
            <td><span id="totalCount">${totalCount}&nbsp;</span></td>
            <th>总金额：</th>
            <td><span id="totalAmt"></span></td>
        </tr>
        <tr>
            <th>已分配：</th>
            <td><span id="assigned"></span></td>
            <th>未分配：</th>
            <td><span id="unAssign"></span></td>
        </tr>
    </table>
    </div>
  	<div id="setAssignEmp">
    <form id="assignEmpForm">
    	<input type="hidden" name="caseIds" value="${caseIds}"/>
    	<input type="hidden" name="caseEmps" value="${caseEmps}"/>
    	<input type="hidden" name="caseMs" value="${caseMs}"/>
    	<input type="hidden" name="caseNums" value="${caseNums}"/>
    	<!--<input type="hidden" name="isAll" value="1"/>-->
    	<div class="inputLayer">
    		<div class="inputTit">分配选项</div>
    		&nbsp;&nbsp;<input type="checkbox" class="inputBoxAlign" id="assIsAll" name="isAll" value="1" checked/><label for="assIsAll">包括已分配案件</label>&nbsp;&nbsp;<input type="checkbox"class="inputBoxAlign"  id="assIsShuffle" name="isShuffle" value="1"/><label for="assIsShuffle">案件随机排序</label>
    	</div>
        <div class="inputLayer">
        	<div class="inputTit">分配方式</div>
        	&nbsp;&nbsp;<input type="radio" id="assignType0" name="assignType" value="0" checked/><label for="assignType0">按数量分配&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
            <input type="radio" id="assignType1" name="assignType" value="1"/><label for="assignType1">按金额分配&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
            <input type="radio" id="assignType2" name="assignType" value="2"/><label for="assignType2">综合分配</label>
        </div>
        <div class="inputLayer">
        	<div class="inputTit">催收员&nbsp;&nbsp;<a title="点击选择员工" href="javascript:void(0)" onClick="parent.addDivBrow(12,'getNames_CB');return false;" style="padding:2px;">[点击选择]</a></div>
            <!--<a id="nodeNames" class="nodeNamesLayer" title="点击选择员工" href="javascript:void(0)" onClick="parent.addDivBrow(12,'getNames');return false;"></a>-->
            <div id='empPctTitle' style="visibility:hidden">填写分配比例(全部留空则平均分配)</div>
            <div id="empPctLayer" class='divWithScroll'></div>
            <input type="hidden" id="nodeIds" name="empIds"/>
        </div>
        <div class="submitLayer"><button id="nextBtn" onClick="toShowAssignRs()">下一步</button></div>
    </form>
	</div>
    <div id="showAssignRs" style="display:none">
    <form id="saveAssignForm" method="post" action="caseAction.do">
    	<input type="hidden" name="op" value="saveAutoAssign" />
    	<div class="inputLayer">
	        <div>   
	           	<span id="recycleInput" style="display:none">
	           		&nbsp;&nbsp;<input type="checkbox" class="inputBoxAlign" name="isAutoRecycle" id="isAutoRecycle" value="1" onclick="setIsAutoRecycle()"><label for="isAutoRecycle">自动收回</label>&nbsp;&nbsp;&nbsp;
		      		<span id="recycleDateTxt" class="lightGray">收回日期：</span>
					<input type="text" id="recycleDate" disabled name="recycleDate" readonly class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:100px;" ondblClick="clearInput(this)" onClick="WdatePicker({skin:'default',dateFmt:'yyyy-MM-dd'})"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				</span>
				<input type="checkbox" class="inputBoxAlign" name="isClear" id="clearTremark" value="c_tr"><label for="clearTremark">清空催收小结</label>&nbsp;<input type="checkbox" class="inputBoxAlign" name="isClear" id="clearPrCount" value="c_prc" checked><label for="clearPrCount">跟进次数清零</label></div>
    	</div>
    	<div id="rsTabLayer" class='divWithScroll'></div>
    	<div class="submitLayer">
        	<button id="saveBtn" onClick="toSaveAssignRs()">确定分配</button>
        	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <button id="retBtn" onClick="toReturn()">上一步</button>
       	</div>
    </form>
    </div>
  </body>
  
</html>
