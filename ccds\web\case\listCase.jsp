<%@ page language="java" pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>案件列表</title>
    
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/case.js?v=2016012813"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
  	<script language="javascript" type="text/javascript">
		var listCount = 0;
		//载入标签样式
		function loadTabType(listType){
			if(listType!=''){
				switch(listType){
					//未分配
					case '0':
						$("tabType1").className="tabTypeWhite";
						setOtherStyle(1,3);
						break;
						
					//已分配
					case '1':
						$("tabType2").className="tabTypeWhite";
						setOtherStyle(2,3);
						break;
						
					//全部
					case 'a':
						$("tabType3").className="tabTypeWhite";
						setOtherStyle(3,3);
						break;
				}
			}
		}
		
		function initCaseState(){
			if($("toolsBarTop")!=null && "${casBat.cbatState}"!='2'){
				$("toolsBarTop").show();
				//$("toolsBarBottom").show();
			}
		}
		
		function opListAll(type){
			if(listCount>0){
				if(type == "0"){
					batchOp(9,false);
				}
				else{
					batchOp(14,false);
				}
			}
			else{
				alert("当前列表为空！");
				return false;
			}
		}
			
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			dataId = obj.casId+"-"+obj.casState;
			var dblFunc="descPop('caseAction.do?op=caseDesc&caseId="+obj.casId+"&view=case')";
			var casCode="<a href=\"javascript:void(0)\" onclick=\""+dblFunc+";return false;\">"+obj.casCode+"</a>";
			var areName = obj.casArea1;
			var prvName =  obj.casArea2;
			var cityName = obj.casArea3;
			var location = areName+"&nbsp;"+prvName+"&nbsp;"+cityName;
			var outState=getVisState(obj.casOutState);
			className = getCaseColor(obj);
			if("${SYS_CODE}"=="S"&&"${CUS_VER_ID}"!="20"){
				datas = [obj.casId, casCode, obj.casDate, obj.casName, obj.casNum, location, obj.casM,(obj.casM>0&&obj.casPaidM>0)?((obj.casM>obj.casPaidM)?(obj.casM-obj.casPaidM):0):obj.casM, obj.typeList?obj.typeList.typName:"新案", obj.casPrTime, obj.salEmp?obj.salEmp.seName:"", obj.casPrCount, obj.casLastAssignTime, outState, obj.casExcLim, obj.casTremark ];
			}
			else{
				datas = [obj.casId, casCode, obj.casDate, obj.casName, obj.casNum, location, obj.casCaCd, obj.casM,(obj.casM>0&&obj.casPaidM>0)?((obj.casM>obj.casPaidM)?(obj.casM-obj.casPaidM):0):obj.casM, obj.typeList?obj.typeList.typName:"新案", obj.casPrTime, obj.salEmp?obj.salEmp.seName:"", obj.casPrCount, obj.casLastAssignTime, obj.casPtpM, obj.casCpM, obj.casPaidM, outState, obj.casExcLim, obj.casTremark ];
			}
			listCount++;
			return [datas,className,dblFunc,dataId];
		}
		function getFormArgs(){
			var pars = $("searchForm").serialize(true);
			pars.op = "listCases";
			pars.hasEmp="${hasEmp}";
			pars.cbatId = "${casBat.cbatId}";
			return pars;
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars = getFormArgs();
			var loadFunc = "loadList";
			var cols=[];
			if("${SYS_CODE}"=="S"&&"${CUS_VER_ID}"!="20"){
				cols=[
					{name:"ID"},
					{name:"个案序列号",align:"left"},
					{name:"委案日期",renderer:"date"},
					{name:getCasNameTxt("${CUS_VER_ID}")},
					{name:"证件号"},
					{name:"地区（省市区）"},
					{name:"委案金额",renderer:"money",align:"right"},
					{name:"委案余额",isSort:false,align:"right",renderer:"money"},
					{name:"催收状态"},
					{name:"上次通电",renderer:"time"},
					{name:"催收员"},
					{name:"跟进次数"},
					{name:"分配时间",renderer:"time"},
					{name:"外访状态"},
					{name:"账龄"},
					{name:"催收小结",align:"left"}
				];
			}
			else{
				cols=[
					{name:"ID"},
					{name:"个案序列号",align:"left"},
					{name:"委案日期",renderer:"date"},
					{name:getCasNameTxt("${CUS_VER_ID}")},
					{name:"证件号"},
					{name:"地区（省市区）"},
					{name:getCasCaCdTxt("${CUS_VER_ID}"),align:"left"},
					{name:"委案金额",renderer:"money",align:"right"},
					{name:"委案余额",isSort:false,align:"right",renderer:"money"},
					{name:"催收状态"},
					{name:"上次通电",renderer:"time"},
					{name:"催收员"},
					{name:"跟进次数"},
					{name:"分配时间",renderer:"time"},
					{name:"PTP金额",renderer:"money",align:"right"},
					{name:"CP金额",renderer:"money",align:"right"},
					{name:"已还款",renderer:"money",align:"right"},
					{name:"外访状态"},
					{name:"账龄"},
					{name:"催收小结",align:"left"}
				];
			}
			
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("caseListTab","dataList");
    	if('${casBat.cbatState}'!='2'){
    		gridEl.config.hasCheckBox = true;
    	}
		createProgressBar();
		window.onload=function(){
			initCaseState();
			loadTabType("${hasEmp}");
			loadPaidState('paidState');
			loadList();
			//增加清空按钮
			createCancelButton(loadList,'searchForm',-50,5,'searButton','after');
			$("casNameTxt").innerHTML=getCasNameTxt("${CUS_VER_ID}");
			$("casCaCdTxt").innerHTML=getCasCaCdTxt("${CUS_VER_ID}");
		}
  	</script>
  </head>
  
  <body>
    <div id="mainbox">
    	<div id="contentbox">
        	<div id="idRangeTip" class="floatTipsDiv" style="display:none;">&nbsp;用,分隔ID号，用-表示ID范围（,和-都为英文符号），示例：1085,1099,1100-1200,1202&nbsp;</div>
        	<div id="hasRepInBatTip" class="floatTipsDiv" style="display:none;">&nbsp;查询是否在同批次下有共债案件，需先在批次管理执行归并案件之后才可查询&nbsp;</div>
        	<div id="title">
            	<table>
                	<tr>
                    	<th>数据管理 > 批次管理 > 案件列表 <span id="changeFuncBt" onMouseOver="popFuncMenu(['case',-1],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['case',-1],true)" onMouseOut="popFuncMenu(['case',-1],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
           	</div>
  	   		<table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                             <div id="tabType1" onClick="self.location.href='caseAction.do?op=toListCases&cbatId=${casBat.cbatId}&hasEmp=0'">未分配</div>
                             <div id="tabType2" onClick="self.location.href='caseAction.do?op=toListCases&cbatId=${casBat.cbatId}&hasEmp=1'">已分配</div>
                             <div id="tabType3" onClick="self.location.href='caseAction.do?op=toListCases&cbatId=${casBat.cbatId}&hasEmp=a'">全部</div>
                            
                        </div>
                     </th>
                </tr>
            </table>
            <script type="text/javascript">loadTabTypeWidth();</script>
            <div id="listContent">
            	<div class="bold italic middle" style="text-align:left; padding:0 0 5px 0;">批次：${casBat.cbatCode}&nbsp;&nbsp;[${casBat.bankTypeList.typName}]&nbsp;&nbsp;<span class="gray">${casBat.caseTypeList.typName}</span></div>
                <div class="listSearch">
                	<form class="listSearchForm" id="searchForm" onSubmit="loadList();return false;" >
                    <table cellpadding="0" cellspacing="0">
                        <tr>
                         	<!--<th>个案序列号：</th>
                            <td><input  class="inputSize2 inputBoxAlign" type="text" id="caseCode" name="caseCode" value="${caseCode}" onBlur="autoShort(this,100)"/></td>  -->
                         	<th><span id="casNameTxt"></span>：</th>
                            <td><input style="width:80px" class="inputSize2 inputBoxAlign" type="text" id="caseName" name="caseName" value="${caseName}" onBlur="autoShort(this,50)"/></td>    
                         	<th><span id="casCaCdTxt"></span>：</th>
                            <td><input style="width:100px" class="inputSize2 inputBoxAlign" type="text" id="casCaCd" name="casCaCd" value="${casCaCd}" onBlur="autoShort(this,50)"/></td>
                         	<th>证件号：</th>
                            <td><input style="width:100px" class="inputSize2 inputBoxAlign" type="text" id="caseNum" name="caseNum" value="${caseNum}" onBlur="autoShort(this,25)"/></td>
                            <th>还款情况：</th>
                        	<td><select style="width:100px"id="paidState" name="paidState" class="inputSize2 inputBoxAlign"></select></td>
                            <th>案件ID&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('idRangeTip',0,10)" onMouseOut="floatTipsLayer('idRangeTip')"/>：</th>
                            <td><input class="inputSize2 inputBoxAlign" type="text" id="idRange" name="idRange" onBlur="formatIdRangeInput(this)"/></td>
                            <th>批次共债&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('hasRepInBatTip',0,10)" onMouseOut="floatTipsLayer('hasRepInBatTip')"/>：</th>
                            <td><select class="inputSize2 inputBoxAlign" name="hasRepInBat"><option></option><option value="1">是</option><option value="0">否</option></select></td>
                            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize1 inputBoxAlign" value="查询"/></td>
                         </tr>
                     </table>
                    </form>
                </div>
                <div id="toolsBarTop" class="rsOpBarLayer bottomBar" style="display:none">
                    <li><img src='images/content/group_go.gif' class="imgAlign" />&nbsp;分案操作：
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(0)">快速分案</span>
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="opListAll('1')">查询结果分案</span>
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(1,false,'${casBat.cbatId}')">批次自动分案</span>
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(2)">手动推荐分案</span>
                    </li>
                    <li><img src='images/content/page_green.gif' class="imgAlign" />&nbsp;案件操作：
                        <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(3)">暂停案件</span>
                       <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(4)">关闭案件</span>
                       <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(5)">退案</span>
                       <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(6)">恢复案件</span>
                       <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(7)">添加评语</span>
                       <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(18)">案件标色</span>
                   		<span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(8)">修改催收状态</span>
                        
                    <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(21)">申请协催</span>
                   		<span id="batDelCase" style="display:none" class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(11)">删除案件</span>
                   </li>
                   <li><img src='images/content/database_go.gif' class="imgAlign" />&nbsp;导出操作：
                       <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="opListAll('0')">导出查询结果</span>
                       <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="batchOp(10,true)">导出所选案件</span>
                   </li>
                   <script type="text/javascript">displayLimAllow("ca017","batDelCase");</script>
                </div>
                <div id="dataList" class="dataList"></div>
            </div>
  		</div> 
	</div>
  </body>
  
</html>
