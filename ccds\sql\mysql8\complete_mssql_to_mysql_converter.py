#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的MSSQL到MySQL转换脚本
处理所有MSSQL语法差异，支持完整的数据库结构转换
"""

import re
import os
import sys
import chardet

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def read_sql_file(file_path):
    """读取SQL文件，自动检测编码"""
    encoding = detect_encoding(file_path)
    print(f"检测到文件编码: {encoding}")
    
    # 尝试多种编码
    encodings = [encoding, 'utf-8', 'gbk', 'gb2312', 'utf-16', 'utf-16le', 'utf-16be']
    
    for enc in encodings:
        if enc:
            try:
                with open(file_path, 'r', encoding=enc) as f:
                    content = f.read()
                    print(f"成功使用编码 {enc} 读取文件")
                    return content
            except (UnicodeDecodeError, UnicodeError):
                continue
    
    raise Exception("无法读取文件，尝试了多种编码都失败")

def convert_mssql_to_mysql(sql_content):
    """将MSSQL语法转换为MySQL语法"""
    
    print("开始转换MSSQL语法...")
    
    # 1. 移除MSSQL特有的SET语句
    sql_content = re.sub(r'SET\s+ANSI_NULLS\s+(ON|OFF)', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'SET\s+QUOTED_IDENTIFIER\s+(ON|OFF)', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'SET\s+ANSI_PADDING\s+(ON|OFF)', '', sql_content, flags=re.IGNORECASE)
    
    # 2. 处理GO语句
    sql_content = re.sub(r'\bGO\b', ';', sql_content, flags=re.IGNORECASE)
    
    # 3. 移除MSSQL注释
    sql_content = re.sub(r'/\*\*\*\*\*\*[^*]*\*\*\*\*\*\*/', '', sql_content, flags=re.IGNORECASE)
    
    # 4. 处理IF NOT EXISTS语句
    # 匹配完整的IF NOT EXISTS ... BEGIN ... CREATE TABLE ... END块
    def replace_if_not_exists(match):
        table_content = match.group(1)
        # 提取表名
        table_match = re.search(r'CREATE\s+TABLE\s+\[dbo\]\.\[(\w+)\]', table_content, re.IGNORECASE)
        if table_match:
            table_name = table_match.group(1)
            return f"DROP TABLE IF EXISTS `{table_name}`;\n{table_content}"
        return table_content
    
    pattern = r'IF\s+NOT\s+EXISTS\s*\([^)]+\)\s*BEGIN\s*(CREATE\s+TABLE[^;]+;)\s*END'
    sql_content = re.sub(pattern, replace_if_not_exists, sql_content, flags=re.IGNORECASE | re.DOTALL)
    
    # 5. 转换方括号为反引号
    sql_content = re.sub(r'\[dbo\]\.\[(\w+)\]', r'`\1`', sql_content)
    sql_content = re.sub(r'\[(\w+)\]', r'`\1`', sql_content)
    
    # 6. 移除COLLATE子句
    sql_content = re.sub(r'\s+COLLATE\s+[\w_]+', '', sql_content, flags=re.IGNORECASE)
    
    # 7. 转换数据类型
    type_mappings = [
        # IDENTITY转换
        (r'`(\w+)`\s+bigint\s+IDENTITY\(1,1\)\s+NOT\s+NULL', r'`\1` bigint NOT NULL AUTO_INCREMENT'),
        (r'`(\w+)`\s+int\s+IDENTITY\(1,1\)\s+NOT\s+NULL', r'`\1` int NOT NULL AUTO_INCREMENT'),
        
        # 数据类型转换
        (r'\bbigint\b', 'bigint'),
        (r'\bint\b', 'int'),
        (r'varchar\((\d+)\)', r'varchar(\1)'),
        (r'nvarchar\((\d+)\)', r'varchar(\1)'),
        (r'nvarchar\(max\)', 'longtext'),
        (r'varchar\(max\)', 'longtext'),
        (r'char\((\d+)\)', r'char(\1)'),
        (r'nchar\((\d+)\)', r'char(\1)'),
        (r'\btext\b', 'text'),
        (r'\bntext\b', 'longtext'),
        (r'\bdatetime\b', 'datetime'),
        (r'\bdate\b', 'date'),
        (r'\btime\b', 'time'),
        (r'decimal\((\d+),\s*(\d+)\)', r'decimal(\1,\2)'),
        (r'numeric\((\d+),\s*(\d+)\)', r'decimal(\1,\2)'),
        (r'\bmoney\b', 'decimal(19,4)'),
        (r'\bsmallmoney\b', 'decimal(10,4)'),
        (r'\bfloat\b', 'double'),
        (r'\breal\b', 'float'),
        (r'\bbit\b', 'tinyint(1)'),
        (r'\bimage\b', 'longblob'),
        (r'varbinary\(max\)', 'longblob'),
        (r'varbinary\((\d+)\)', r'varbinary(\1)'),
        (r'binary\((\d+)\)', r'binary(\1)'),
        (r'\buniqueidentifier\b', 'varchar(36)'),
        (r'\btimestamp\b', 'timestamp')
    ]
    
    for pattern, replacement in type_mappings:
        sql_content = re.sub(pattern, replacement, sql_content, flags=re.IGNORECASE)
    
    # 8. 转换主键约束
    sql_content = re.sub(
        r'CONSTRAINT\s+`([^`]+)`\s+PRIMARY\s+KEY\s+CLUSTERED\s*\(\s*`([^`]+)`\s+ASC\s*\)\s*WITH\s*\([^)]+\)',
        r'PRIMARY KEY (`\2`)',
        sql_content, flags=re.IGNORECASE
    )
    
    sql_content = re.sub(
        r'CONSTRAINT\s+`([^`]+)`\s+PRIMARY\s+KEY\s+CLUSTERED\s*\(\s*`([^`]+)`\s*\)',
        r'PRIMARY KEY (`\2`)',
        sql_content, flags=re.IGNORECASE
    )
    
    # 9. 移除MSSQL特有的子句
    sql_content = re.sub(r'\s+ON\s+`PRIMARY`', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'\s+WITH\s*\([^)]+\)', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'\s+ASC\s*([,)])', r'\1', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'\s+DESC\s*([,)])', r'\1', sql_content, flags=re.IGNORECASE)
    
    # 10. 转换索引
    sql_content = re.sub(
        r'CREATE\s+NONCLUSTERED\s+INDEX\s+`([^`]+)`\s+ON\s+`(\w+)`\s*\(\s*`(\w+)`\s*\)',
        r'CREATE INDEX `\1` ON `\2` (`\3`);',
        sql_content, flags=re.IGNORECASE
    )
    
    # 11. 处理CREATE TABLE语句，确保每个表都有DROP IF EXISTS
    def add_drop_table(match):
        table_name = match.group(1)
        return f"DROP TABLE IF EXISTS `{table_name}`;\nCREATE TABLE `{table_name}`"
    
    sql_content = re.sub(r'CREATE\s+TABLE\s+`(\w+)`', add_drop_table, sql_content, flags=re.IGNORECASE)
    
    # 12. 为每个CREATE TABLE添加ENGINE和字符集
    def add_engine_charset(match):
        table_content = match.group(0)
        if not table_content.strip().endswith(';'):
            table_content = table_content.rstrip() + ';'
        
        # 如果已经有ENGINE，就不添加
        if 'ENGINE=' not in table_content.upper():
            table_content = table_content.rstrip(';') + ' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;'
        
        return table_content
    
    # 匹配完整的CREATE TABLE语句
    pattern = r'CREATE\s+TABLE\s+`\w+`\s*\([^;]+\);'
    sql_content = re.sub(pattern, add_engine_charset, sql_content, flags=re.IGNORECASE | re.DOTALL)
    
    # 13. 清理多余的空行
    sql_content = re.sub(r'\n\s*\n\s*\n', '\n\n', sql_content)
    sql_content = re.sub(r'^\s+', '', sql_content, flags=re.MULTILINE)
    
    # 14. 添加MySQL头部
    mysql_header = """-- MySQL 8.0 版本的CCDS数据库结构
-- 从MSSQL自动转换生成

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ccds`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

"""
    
    mysql_footer = """
SET FOREIGN_KEY_CHECKS = 1;
"""
    
    return mysql_header + sql_content + mysql_footer

def convert_file(input_file, output_file):
    """转换单个文件"""
    try:
        print(f"正在转换文件: {input_file}")
        sql_content = read_sql_file(input_file)
        
        print("正在转换SQL语法...")
        mysql_content = convert_mssql_to_mysql(sql_content)
        
        print(f"正在写入文件: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(mysql_content)
        
        print(f"转换完成: {output_file}")
        return True
        
    except Exception as e:
        print(f"转换失败 {input_file}: {e}")
        return False

def main():
    """主函数 - 转换所有MSSQL文件"""
    base_dir = "../"
    mysql_dir = "./"
    
    # 确保输出目录存在
    os.makedirs(mysql_dir, exist_ok=True)
    
    # 要转换的文件列表
    files_to_convert = [
        ("CCDS.sql", "mysql8_CCDS_structure.sql"),
        ("CCDS_data.sql", "mysql8_CCDS_data.sql"),
        ("CCDS_in_cashBus.sql", "mysql8_CCDS_cashbus.sql")
    ]
    
    success_count = 0
    total_count = len(files_to_convert)
    
    for input_name, output_name in files_to_convert:
        input_path = os.path.join(base_dir, input_name)
        output_path = os.path.join(mysql_dir, output_name)
        
        if os.path.exists(input_path):
            if convert_file(input_path, output_path):
                success_count += 1
        else:
            print(f"文件不存在: {input_path}")
    
    print(f"\n转换完成! 成功: {success_count}/{total_count}")
    print("输出文件位置: ccds/sql/mysql8/")

if __name__ == "__main__":
    main()
