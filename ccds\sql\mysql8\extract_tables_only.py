#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
只提取CREATE TABLE语句，忽略所有其他语句
"""

import re

def extract_create_tables_only(content):
    """只提取CREATE TABLE语句"""
    
    print("开始提取CREATE TABLE语句...")
    
    # 提取所有DROP TABLE和CREATE TABLE语句
    table_statements = []
    
    # 匹配DROP TABLE IF EXISTS和CREATE TABLE的组合
    pattern = r'(DROP TABLE IF EXISTS `\w+`;[\s\n]*CREATE TABLE `\w+`\s*\([^;]+\)[^;]*;)'
    
    matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
    
    for match in matches:
        # 清理每个表语句
        table_sql = match
        
        # 移除MSSQL特有语法
        table_sql = re.sub(r'\s+ON\s+`PRIMARY`', '', table_sql)
        table_sql = re.sub(r'\s+TEXTIMAGE_ON\s+`PRIMARY`', '', table_sql)
        
        # 确保有ENGINE
        if 'ENGINE=' not in table_sql:
            table_sql = re.sub(r'(\);)', r') ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;', table_sql)
        
        table_statements.append(table_sql)
    
    return table_statements

def main():
    """主函数"""
    
    with open('mysql57_ALL_106_TABLES_FINAL_CLEAN.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    table_statements = extract_create_tables_only(content)
    
    print(f"提取到 {len(table_statements)} 个表")
    
    # 生成最终SQL
    header = """-- MySQL 5.7 完整CCDS数据库 - 所有表结构
-- 纯净版本，只包含CREATE TABLE语句

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `ccds`;

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 所有表的结构定义
-- ========================================

"""
    
    footer = """
-- ========================================
-- 创建索引
-- ========================================

-- 核心表索引
CREATE INDEX idx_sal_emp_name ON `sal_emp` (`se_name`);
CREATE INDEX idx_cus_cor_cus_user ON `cus_cor_cus` (`cor_user_code`);
CREATE INDEX idx_lim_user_enabled ON `lim_user` (`user_isenabled`);
CREATE INDEX idx_bank_case_state ON `bank_case` (`cas_state`);
CREATE INDEX idx_pho_red_cas_id ON `pho_red` (`pr_cas_id`);
CREATE INDEX idx_case_paid_cas_id ON `case_paid` (`pa_cas_id`);

SET FOREIGN_KEY_CHECKS = 1;

-- 数据库创建完成
SELECT 'CCDS完整数据库创建完成' as message;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ccds';
"""
    
    final_sql = header + '\n\n'.join(table_statements) + footer
    
    with open('mysql57_PURE_TABLES_ONLY.sql', 'w', encoding='utf-8') as f:
        f.write(final_sql)
    
    print("纯净表结构文件已生成: mysql57_PURE_TABLES_ONLY.sql")

if __name__ == "__main__":
    main()
