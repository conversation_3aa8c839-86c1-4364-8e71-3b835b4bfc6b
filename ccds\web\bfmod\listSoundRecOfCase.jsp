 <%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>录音记录</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="js/bfmod.js"></script>
    <script type="text/javascript">
    	function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			datas = [getCallType(obj.callType), obj.localPhone, obj.callPhone,  obj.recStartTime, obj.recEndTime, formatSecond(obj.duration), getRecFile(obj.soundRecPath,obj.id), obj.userName ];
			return [datas,className,dblFunc,dataId];
		}
    	
    	function loadList(sortCol,isDe,pageSize,curP){
			var url = "soundRecordAction.do";
			var pars = [];
			pars.op = "listSoundRecOfCase";
			pars.casId = "${casId}";
			pars.callPhone = "${callPhone}";
			pars.serverId = $("serverId").value;
			var loadFunc = "loadList";
			var cols=[
				{name:"呼叫类型",width:'6%'},
				{name:"本机号码",width:'12%'},
				{name:"对方号码",width:'12%'},
				{name:"录音时间",renderer:'stime',width:'20%'},
				{name:"结束时间",renderer:'time',width:'20%'},
				{name:"通话时长",width:'10%'},
				{name:"录音文件",width:'10%'},
				{name:"员工姓名",width:'10%'}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper,null,errInfoCallBack);
		}
		
    	var gridEl = new MGrid("soundRecList","dataList");
		gridEl.config.isShort=false;
		gridEl.config.isResize=false;
    	gridEl.config.sortable=false;
		createProgressBar();
		window.onload=function(){
			loadList();
		}
    </script>
</head>
  
  <body>
  	<c:if test="${empty ctisList}">
   		<div class="grayBack" style="padding:10px; font-size:14px">未添加录音服务器，请在<a href="ctiServerAction.do?op=toListCtis">服务器设置</a>中添加</div>
    </c:if>
    <c:if test="${!empty ctisList}">
  	<div class="listSearch">
   		选择录音服务器：
   		<select id="serverId" class="inputSize2 inputBoxAlign" onchange="loadList()"><c:forEach items="${ctisList}" var="ctis"><option value="${ctis.ctisId}">${ctis.ctisName}</option></c:forEach></select>
        <c:forEach items="${ctisList}" var="ctish">
        <input type="hidden" id="ctiServer${ctish.ctisId}" value="${ctish.ctisIp}"/>
        </c:forEach>
   	</div>
  	<div id="errMsgLayer" class="redWarn" style="display:none; margin:0;"></div>
    <div id="dataList" class="dataList"></div>
     <input id="getDownloadAccess" type="hidden" style="display:none" />
     <script type="text/javascript">displayLimAllow("r_cased047","getDownloadAccess");</script>
    </c:if>
  </body>
</html>