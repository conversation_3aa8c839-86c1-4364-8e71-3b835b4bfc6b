<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.WmsCheck" table="wms_check" schema="dbo" >
        <id name="wmcId" type="java.lang.Long">
            <column name="wmc_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsStro" class="com.psit.struts.entity.WmsStro" fetch="select" not-null="false">
            <column name="wmc_stro_code" length="50" />
        </many-to-one>
       <property name="wmcOpman" type="java.lang.String">
      		 <column name="wmc_user_code" length="50" />
       </property>
        <property name="wmcTitle" type="java.lang.String">
            <column name="wmc_title" length="1073741823" />
        </property>
         <property name="wmcCode" type="java.lang.String">
            <column name="wmc_code" length="50" />
        </property>
        <property name="wmcDate" type="java.util.Date">
            <column name="wmc_date" length="23" />
        </property>
        <property name="wmcState" type="java.lang.String">
            <column name="wmc_state" length="1" />
        </property>
        <property name="wmcRemark" type="java.lang.String">
            <column name="wmc_remark" length="1073741823" />
        </property>
        <property name="wmcIsdel" type="java.lang.String">
            <column name="wmc_isdel" length="1" />
        </property>
        <property name="wmcInpName" type="java.lang.String">
            <column name="wmc_inp_name" length="50" />
        </property>
        <property name="wmcAltName" type="java.lang.String">
            <column name="wmc_alt_name" length="50" />
        </property>
        <property name="wmcInpDate" type="java.util.Date">
            <column name="wmc_inp_date" length="23" />
        </property>
        <property name="wmcAltDate" type="java.util.Date">
            <column name="wmc_alt_date" length="23" />
        </property>
        <property name="wmcAppDate" type="java.util.Date">
            <column name="wmc_app_date" length="23" />
        </property>
        <property name="wmcAppMan" type="java.lang.String">
            <column name="wmc_app_man" length="50" />
        </property>
        <property name="wmcAppDesc" type="java.lang.String">
            <column name="wmc_app_desc" length="1073741823" />
        </property>
        <property name="wmcAppIsok" type="java.lang.String">
            <column name="wmc_app_isok" length="1" />
        </property>
        <property name="wmcCanDate" type="java.util.Date">
            <column name="wmc_can_date" length="23" />
        </property>
        <property name="wmcCanMan" type="java.lang.String">
            <column name="wmc_can_man" length="50" />
        </property>
        <set name="RWmsChanges" inverse="true"  order-by="rwc_id" cascade="delete">
            <key>
                <column name="rwc_wmc_code" />
            </key>
            <one-to-many class="com.psit.struts.entity.RWmsChange" />
        </set>
    </class>
</hibernate-mapping>
