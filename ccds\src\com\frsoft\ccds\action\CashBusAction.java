package com.frsoft.ccds.action;

import com.frsoft.base.biz.UserBIZ;
import com.frsoft.base.entity.LimUser;
import com.frsoft.base.entity.TypeList;
import com.frsoft.ccds.biz.CaseBIZ;
import com.frsoft.ccds.biz.CaseBatBIZ;
import com.frsoft.ccds.entity.CaseBat;
import com.frsoft.ccds.util.CashBusFacade;
import com.frsoft.util.format.GetDate;
import com.frsoft.util.format.StringFormat;
import com.mchange.lang.ByteUtils;
import com.psit.struts.DAO.CashBusLoanCaseDAO;
import com.psit.struts.DAO.CashBusWorkOrderDAO;
import com.psit.struts.entity.CashBusLoanCase;
import jxl.Workbook;
import jxl.write.*;
import jxl.write.Number;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.actions.DispatchAction;
import org.apache.struts.upload.FormFile;
import org.apache.struts.upload.MultipartRequestHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.Boolean;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 现金巴士接入功能Action
 * Created by comfan on 2017/7/20.
 */
public class CashBusAction extends DispatchAction {
    protected Logger log;
    private CashBusFacade cashBusFacade;
    private String contextUrlPrefix;
    private FormFile uploadFile;
    private String runEnv;

    UserBIZ userBiz;
    CaseBatBIZ caseBatBiz;
    CaseBIZ caseBiz;
    CashBusLoanCaseDAO loanCaseDAO;

    public String getContextUrlPrefix() {
        return contextUrlPrefix;
    }

    public void setContextUrlPrefix(String contextUrlPrefix) {
        this.contextUrlPrefix = contextUrlPrefix;
    }

    public CashBusAction() {
        log = Logger.getLogger("transMsgLog");
    }

    public CashBusFacade getCashBusFacade() {
        return cashBusFacade;
    }

    public void setCashBusFacade(CashBusFacade cashBusFacade) {
        this.cashBusFacade = cashBusFacade;
    }

    public FormFile getUploadFile() {
        return uploadFile;
    }

    public void setUploadFile(FormFile uploadFile) {
        this.uploadFile = uploadFile;
    }

    public UserBIZ getUserBiz() {
        return userBiz;
    }

    public void setUserBiz(UserBIZ userBiz) {
        this.userBiz = userBiz;
    }

    public CaseBatBIZ getCaseBatBiz() {
        return caseBatBiz;
    }

    public void setCaseBatBiz(CaseBatBIZ caseBatBiz) {
        this.caseBatBiz = caseBatBiz;
    }

    public CaseBIZ getCaseBiz() {
        return caseBiz;
    }

    public void setCaseBiz(CaseBIZ caseBiz) {
        this.caseBiz = caseBiz;
    }

    public String getRunEnv() {
        return runEnv;
    }

    public void setRunEnv(String runEnv) {
        this.runEnv = runEnv;
    }

    public CashBusLoanCaseDAO getLoanCaseDAO() {
        return loanCaseDAO;
    }

    public void setLoanCaseDAO(CashBusLoanCaseDAO loanCaseDAO) {
        this.loanCaseDAO = loanCaseDAO;
    }

    public ActionForward execute(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) throws Exception {
        return this.isLimitAllow(request)?super.execute(mapping, form, request, response):mapping.findForward("limError");
    }

    protected boolean isLimitAllow(HttpServletRequest request) {
        String methodName = request.getParameter("op");
        String[][] methLim = new String[][]{{"getbatchs", "ca023"},
                {"getbatch",  "ca024"},
                {"refreshLoanCase",  "ca024"},
                {"loadIncomeBill", "ca025"},
                {"incomeBill", "ca025"},
                {"queryLoanInBillStat", "ca027"},
                {"queryLoanReimDetailt", "ca028"},
                {"loadReqSensitiveInfo", "ca029"},
                {"reqSensitiveInfo", "ca029"},
                {"reqSensitiveInfoStat", "ca029"},
                {"sensitiveInfo", "ca029"},
                {"cashBusResource", "ca030"},
                {"loadRecord", "ca031"},
                {"record", "ca031"},
                {"loadDayRepayment", "ca032"},
                {"dayRepayment", "ca032"},
                {"loadMonthBillVerify", "ca033"},
                {"monthBillVerify", "ca033"},
                {"exportMonthBill", "ca033"},
                {"loadExportCase", "ca034"},
                {"getWorkOrder", "ca035"}
        };
        Enumeration<String> parameterNames = request.getParameterNames();
        Map<String, List> httpParam = new HashMap<String, List>();
        while(parameterNames.hasMoreElements()){
            String key = parameterNames.nextElement();
            String[] values = request.getParameterValues(key);
            if(values!=null && values.length>0){
                List<String> paramValues = new ArrayList<String>();
                for (String value_ : values) {
                    paramValues.add(value_);
                }
                httpParam.put(key , paramValues);
            }else{
                httpParam.put(key , null);
            }
        }
        log.debug(String.format("HTTP Parameters      %s  " , httpParam));
        if (null != runEnv && "debug".equalsIgnoreCase(runEnv)) {
            log.debug("运行在调试模式下，不检查权限.");
            return true;
        }else {
            boolean limit = this.userBiz.getLimit(request, methodName, methLim);
            log.debug(String.format("当前用户是否有权限[%s]  ", limit));
            return limit;
        }
    }

    /**
     * 获取所有批次
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward getbatchs(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setCharacterEncoding("utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("不支持utf-8", e);
        }
        try {
            JSONObject allBatchs = cashBusFacade.getAllBatchs();
            if ("0000".equals(allBatchs.optString("resultCode"))) {
                JSONArray batchs = allBatchs.optJSONArray("result");
                Iterator batchIt = batchs.iterator();
                while (batchIt.hasNext()) {
                    try {
                        JSONObject batchJson = (JSONObject) batchIt.next();
                        long withdrawTime = batchJson.optLong("withdrawTime");
                        final Date date = new Date(withdrawTime);
                        final DateFormat dateFormat = getZhCnDateFormat();
                        batchJson.put("withdrawTime", dateFormat.format(date));
                        batchJson.put("withdrawTimeSrc", withdrawTime);
                        String batchName = batchJson.optString("name");
                        String batchNameEncoded = URLEncoder.encode(batchName, cashBusFacade.getHttpsUtils().getDefaultCharset());
                        batchJson.put("nameEncoded", batchNameEncoded);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("失败!", e);
                    }
                }
                log.info(batchs);
                request.setAttribute("batchList", batchs);
            } else {
                request.setAttribute("errorMessage", allBatchs.optString("resultDesc"));
//                return mapping.findForward("error");
                return mapping.findForward("showBatchs");
            }
        } catch (Exception e) {
            //TODO exception log
            request.setAttribute("errorMessage", e.getMessage());
//            return mapping.findForward("error");
            return mapping.findForward("showBatchs");

        }
        return mapping.findForward("showBatchs");
    }

    /**
     * 获取批次（贷款）信息
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward getbatch(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setCharacterEncoding("utf-8");
        } catch (UnsupportedEncodingException e) {
        }
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchname"), "");
        String offsetStr = StringUtils.defaultIfBlank(request.getParameter("offset"), "0");
        String limitStr = StringUtils.defaultIfBlank(request.getParameter("limit"), "10");
        String searchKey = StringUtils.defaultIfBlank(request.getParameter("searchKey"), "");
        int offset = 0;
        int limit = 10;
        try {
//            if ("get".equalsIgnoreCase(request.getMethod())) {
//            }
            batchName = new String(batchName.getBytes("ISO-8859-1"), "UTF-8");
            request.setAttribute("batchNameNoEncode", batchName);
            request.setAttribute("batchName", URLEncoder.encode(batchName, cashBusFacade.getHttpsUtils().getDefaultCharset()));
        } catch (UnsupportedEncodingException e) {
        }
        try {
            offset = Integer.valueOf(offsetStr);
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "页偏移无效");
        }
        try {
            limit = Integer.valueOf(limitStr);
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "页容量无效");
        }
        request.setAttribute("offset", offset);
        request.setAttribute("limit", limit);
        try {
            JSONObject batchRes = cashBusFacade.getLoanCase(batchName, searchKey, offset, limit);
            if ("0000".equals(batchRes.optString("resultCode"))) {
                JSONObject result = batchRes.optJSONObject("result");
                long totalCount = result.optLong("totalCount");
                request.setAttribute("totalCount", totalCount);
                if (totalCount == 0 && StringUtils.isBlank((String) request.getAttribute("loadData")) && StringUtils.isBlank(searchKey)) {
                    return refreshLoanCase( mapping, form, request, response );
                }
                JSONArray resultList = result.optJSONArray("resultList");
                Iterator resultIt = resultList.iterator();
                while (resultIt.hasNext()) {
                    try {
                        JSONObject item = (JSONObject) resultIt.next();
                        long repaymentTimeStamp = item.optLong("repaymentDate");
                        final DateFormat dateFormat = getZhCnDateFormat();
                        final Date repaymentDate = new Date(repaymentTimeStamp);
                        item.put("repaymentDate", dateFormat.format(repaymentDate));
                        item.put("repaymentDateTs", repaymentTimeStamp);
                        String userCardNo = item.optString("userCardNo");
                        userCardNo = StringUtils.right(userCardNo, 4);
                        item.put("userCardNoR4", userCardNo);
                    } catch (Exception e) {
                        log.error("cashBus", e);
                    }
                }
                log.info(resultList);
                request.setAttribute("resultList", resultList);
            } else {
                request.setAttribute("errorMessage", batchRes.optString("resultDesc"));
            }
        } catch (Exception e) {
            request.setAttribute("errorMessage", e.getMessage());
            log.error("cashBus", e);
        }
        return mapping.findForward("showBatch");
    }

    /**
     * 获取工单信息
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward getWorkOrder(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setCharacterEncoding("utf-8");
        } catch (UnsupportedEncodingException e) {
        }
        String offsetStr = StringUtils.defaultIfBlank(request.getParameter("offset"), "0");
        String limitStr = StringUtils.defaultIfBlank(request.getParameter("limit"), "10");
        int offset = 0;
        int limit = 10;
        try {
            offset = Integer.valueOf(offsetStr);
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "页偏移无效");
        }
        try {
            limit = Integer.valueOf(limitStr);
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "页容量无效");
        }
        request.setAttribute("offset", offset);
        request.setAttribute("limit", limit);
        try {
            JSONObject workOrder = cashBusFacade.getWorkOrder(offset, limit);
            if ("0000".equals(workOrder.optString("resultCode"))) {
                JSONObject result = workOrder.optJSONObject("result");
                int totalCount = result.optInt("totalCount");
                request.setAttribute("totalCount", totalCount);
                JSONArray resultList = result.optJSONArray("resultList");
                Iterator resultIt = resultList.iterator();
                while (resultIt.hasNext()) {
                    try {
                        JSONObject item = (JSONObject) resultIt.next();
                        long created = item.optLong("created");
                        final DateFormat dateFormat = getZhCnDateFormat();
                        final Date createdDate = new Date(created);
                        item.put("createdDate", dateFormat.format(createdDate));
                        final Date updatedDate = new Date(item.optLong("updated"));
                        item.put("updatedDate", dateFormat.format(updatedDate));
                    } catch (Exception e) {
                        log.error("cashBus", e);
                    }
                }
                log.info(resultList);
                request.setAttribute("resultList", resultList);
            } else {
                request.setAttribute("errorMessage", workOrder.optString("resultDesc"));
            }
        } catch (Exception e) {
            request.setAttribute("errorMessage", e.getMessage());
            log.error("cashBus", e);
        }
        return mapping.findForward("workOrder");
    }

    /**
     * 获取工单信息
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward dayFailedRepayment(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setCharacterEncoding("utf-8");
        } catch (UnsupportedEncodingException e) {
        }
        String repaymentTime = StringUtils.defaultIfBlank(request.getParameter("repaymentTime"), getCommonDayFormat().format(new Date()));
        Date day = new Date();
        try {
            day = getCommonDayFormat().parse(repaymentTime);
        } catch (Exception e) {
            log.error("cashBus", e);
        }
        try {
            JSONObject faildReplayment = cashBusFacade.dayFaildReplayment(day.getTime());
            if ("0000".equals(faildReplayment.optString("resultCode"))) {
                JSONArray resultList = faildReplayment.optJSONArray("result");
                Iterator resultIt = resultList.iterator();
                while (resultIt.hasNext()) {
                    try {
                        JSONObject item = (JSONObject) resultIt.next();
                        final DateFormat dateFormat = getZhCnDateFormat();
                        final Date repaymentDate = new Date(item.optLong("repaymentDate"));
                        item.put("repaymentDate", dateFormat.format(repaymentDate));
                    } catch (Exception e) {
                        log.error("cashBus", e);
                    }
                }
                log.info(resultList);
                request.setAttribute("resultList", resultList);
            } else {
                request.setAttribute("errorMessage", faildReplayment.optString("resultDesc"));
            }
        } catch (Exception e) {
            request.setAttribute("errorMessage", e.getMessage());
            log.error("cashBus", e);
        }
        return mapping.findForward("dayFaildRepayment");
    }

    /**
     * 载入入账请求页面
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward loadIncomeBill(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchname"), "");
        final String loanRefId = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        String originalAmountDue = StringUtils.defaultIfBlank(request.getParameter("originalAmountDue"), "");
        String repaymentDate = StringUtils.defaultIfBlank(request.getParameter("repaymentDate"), String.valueOf(new Date().getTime()));
        int originalAmountDuei = 0;
        try {
            batchName = new String(batchName.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.warn(e);
        }
        request.setAttribute("loanRefId", loanRefId);
        try {
            originalAmountDuei = Double.valueOf(originalAmountDue).intValue();
            request.setAttribute("originalAmountDue", originalAmountDuei);
        } catch (NumberFormatException e) {
        }
        try {
            repaymentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date(Long.valueOf(repaymentDate)));
            request.setAttribute("repaymentDate", repaymentDate);
        } catch (NumberFormatException e) {
        }
        request.setAttribute("batchName", batchName);
        return mapping.findForward("incomeBillRequest");
    }

    /**
     * 请求入账,及请求结果
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward incomeBill(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        final String repaymentMethod = StringUtils.defaultIfBlank(request.getParameter("repaymentMethod"), "");
        final String collectedAmount = StringUtils.defaultIfBlank(request.getParameter("collectedAmount"), "");
        final String repaymentTime = StringUtils.defaultIfBlank(request.getParameter("repaymentTime"), "");
        final String comment = StringUtils.defaultIfBlank(request.getParameter("comment"), "");
        int collectedAmounti = 0;
        long repaymentTimel = 0;
        if (StringUtils.isBlank(repaymentMethod)) {
            isSuccess = false;
            errorMessage = "还款方式不能为空!请填写，如:支付宝、银联卡等";
        } else if (StringUtils.isBlank(collectedAmount)) {
            isSuccess = false;
            errorMessage = "请填写还款金额";
        } else if (StringUtils.isBlank(repaymentTime)) {
            isSuccess = false;
            errorMessage = "请选择还款日期!";
        } else {
            try {
                collectedAmounti = Integer.valueOf(collectedAmount);
            } catch (NumberFormatException e) {
                isSuccess = false;
                errorMessage = "现金巴士仅支持还整数金额!";
            }
            try {
                repaymentTimel = new SimpleDateFormat("yyyy/MM/dd").parse(repaymentTime).getTime();
            } catch (ParseException e) {
                isSuccess = false;
                errorMessage = "还款日期不正确!";
            }
        }
        if (isSuccess) {
            boolean hasPic = false;//   是否存在截图
            final String imageDir = new StringBuilder("/imageRecords/").append(new SimpleDateFormat("yMd").format(new Date())).append("/").toString();
            final String path = request.getRealPath(imageDir);
            /**
             * 原理 它是先存到 暂时存储室，然后在真正写到 对应目录的硬盘上，
             * 按理来说 当上传一个文件时，其实是上传了两份，第一个是以 .tem 格式的
             * 然后再将其真正写到 对应目录的硬盘上
             */
            final File repository = new File(path);
            if (!repository.exists()) {
                repository.mkdirs();
            }
            final StringBuilder picPathBuilder = new StringBuilder(contextUrlPrefix).append(imageDir);
            try {
                MultipartRequestHandler multipartRequestHandler = form.getMultipartRequestHandler();
                Hashtable fileElements = multipartRequestHandler.getFileElements();
                FormFile picUrlFormFile = (FormFile) fileElements.get("picUrl");
                String sourceFileName = picUrlFormFile.getFileName();
                if (hasPic =
                        (picUrlFormFile != null && StringUtils.isNotBlank(sourceFileName)
                                && picUrlFormFile.getFileSize() > 0)) {
                    File destFile = new File(repository, sourceFileName);
                    if (destFile.exists() && !sourceFileName.matches("[a-zA-z0-9_\\-\\.]")) {
                        int permNameLastIndex = sourceFileName.lastIndexOf(".");
                        int sourceNameLen = sourceFileName.length();
                        final StringBuilder storeFileBuilder = new StringBuilder(
                                StringUtils.left(sourceFileName, permNameLastIndex > 0 ? permNameLastIndex : sourceNameLen)
                        );
                        if (!sourceFileName.matches("[a-zA-z0-9_\\-\\.]")) {
                            storeFileBuilder.delete(0, storeFileBuilder.length());
                            MessageDigest md = MessageDigest.getInstance("SHA-256");
                            md.update(picUrlFormFile.getFileData());
                            byte[] digest = md.digest();
                            String mdstr = ByteUtils.toHexAscii(digest);
                            storeFileBuilder.append(mdstr);
                        }
                        if (destFile.exists()) {
                            storeFileBuilder.append(new Date().getTime());
                        }
                        if (permNameLastIndex > 0) {
                            storeFileBuilder.append(StringUtils.right(sourceFileName, sourceNameLen - permNameLastIndex));
                        }
                        destFile = new File(repository, storeFileBuilder.toString());
                    }
                    IOUtils.copy(picUrlFormFile.getInputStream(), new FileOutputStream(destFile));
                    picPathBuilder.append(destFile.getName());
                }
            } catch (Exception e) {
                log.error(String.format("上传文件失败!"), e);
            }
            final String picUrl = hasPic ? picPathBuilder.toString() : "";
//            JSONObject resJson = new JSONObject();
//            resJson.put("resultCode", "0000");
//            JSONObject resultJson = new JSONObject();
//            resultJson.put("id"                 , "000000001");
//            resultJson.put("loanRefId"          , loanRefId);
//            resultJson.put("repaymentMethod"    , repaymentMethod);
//            resultJson.put("collectedAmount"    , collectedAmount);
//            resultJson.put("applicant"          , "qinwei001");
//            resultJson.put("status"             , "UnExecute");
//            resultJson.put("confirmComment"     , "");
//            resultJson.put("username"           , "11111111111");
//            resultJson.put("repaymentDate"      , new Date().getTime());
//            resJson.put("result", resultJson);
            try {
                JSONObject resJson = cashBusFacade.repaymentRequset(batchName, loanRefId, repaymentMethod, collectedAmounti, picUrl, comment, repaymentTimel);
                if ("0000".equals(resJson.optString("resultCode"))) {
                    isSuccess = true;
                    JSONObject result = resJson.optJSONObject("result");
                    result.put("picUrl", picUrl);
                    long repaymentDate = result.optLong("repaymentDate");
                    try {
                        result.put("repaymentDate", new SimpleDateFormat("y年M月d日H时m分s秒").format(new Date(repaymentDate)));
                    } catch (Exception e) {
                        log.warn(e);
                    }
                    request.setAttribute("result", result);
                    request.setAttribute("picUrl", picUrl);
                } else {
                    isSuccess = false;
                    errorMessage = resJson.optString("resultDesc");
                }
            } catch (Exception e) {
                isSuccess = false;
                errorMessage = e.getMessage();
                errorMessage = errorMessage.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            }
        }
        request.setAttribute("isSuccess", isSuccess);
        request.setAttribute("errorMessage", errorMessage);
        request.setAttribute("batchName", batchName);
        return mapping.findForward("incomeBillRequestResult");
    }

    /**
     * 查看入账状态(list)
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public  ActionForward queryLoanInBillStat(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        final String auditStatus = StringUtils.defaultIfBlank(request.getParameter("auditStatus"), "Executed");
        try {
            batchName = new String(batchName.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.warn(e);
        }
        try {
            JSONObject resJson = cashBusFacade.repaymentRequestList(batchName, loanRefId, auditStatus, 0, 0, 10, 0);
            if ("0000".equals(resJson.optString("resultCode"))) {
                isSuccess = true;
                JSONObject resultJson = resJson.optJSONObject("result");
                int totalCount = resultJson.optInt("totalCount");
                request.setAttribute("totalCount", totalCount);
                JSONArray resultList = resultJson.optJSONArray("resultList");
                Iterator iterator = resultList.iterator();
                while (iterator.hasNext()) {
                    JSONObject result = (JSONObject) iterator.next();
                    long repaymentDate = result.optLong("repaymentDate");
                    try {
                        result.put("repaymentDate", getZhCnDateFormat().format(new Date(repaymentDate)));
                    } catch (Exception e) {
                        log.warn(e);
                    }
                }
                request.setAttribute("results", resultList);
            } else {
                isSuccess = false;
                errorMessage = resJson.optString("resultDesc");
            }
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = e.getMessage();
            errorMessage = errorMessage.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
        }
        request.setAttribute("isSuccess", isSuccess);
        request.setAttribute("errorMessage", errorMessage);
        request.setAttribute("batchName", batchName);
        return mapping.findForward("queryIncomeBillResult");
    }

    /**
     * 查询还款明细
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward queryLoanReimDetailt(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        try {
            batchName = new String(batchName.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.warn(e);
        }
        try {
            JSONObject resJson = cashBusFacade.repaymentDetail(batchName, loanRefId);
            if ("0000".equals(resJson.optString("resultCode"))) {
                isSuccess = true;
                JSONArray results = resJson.optJSONArray("result");
                Iterator iterator = results.iterator();
                while (iterator.hasNext()) {
                    JSONObject result = (JSONObject) iterator.next();
                    long repaymentDate = result.optLong("repaymentDate");
                    try {
                        result.put("repaymentDate", getZhCnDateFormat().format(new Date(repaymentDate)));
                    } catch (Exception e) {
                        log.warn(e);
                    }
                }
                request.setAttribute("results", results);

            } else {
                isSuccess = false;
                errorMessage = resJson.optString("resultDesc");
            }
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = e.getMessage();
            errorMessage = errorMessage.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
        }
        request.setAttribute("isSuccess", isSuccess);
        request.setAttribute("errorMessage", errorMessage);
        request.setAttribute("batchName", batchName);
        return mapping.findForward("queryLoanReimDetail");
    }

    /**
     * 载入敏感信息请求页面
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward loadReqSensitiveInfo(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String errorMessage = "";
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        try {
            batchName = new String(batchName.getBytes("ISO-8859-1"), "UTF-8");
            request.setAttribute("batchRefId", batchName);
            request.setAttribute("loanRefId", loanRefId);
        } catch (UnsupportedEncodingException e) {
            log.warn(e);
        }
        return mapping.findForward("reqSensitiveInfo");
    }

    /**
     * 请求敏感信息
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward reqSensitiveInfo(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        final String applicationCategory = StringUtils.defaultIfBlank(request.getParameter("applicationCategory"), "idcard");
        request.setAttribute("batchRefId", batchName);
        request.setAttribute("loanRefId", loanRefId);
        try {
            JSONObject resJson = cashBusFacade.sensitiveInfoRequest(batchName, loanRefId, applicationCategory, new Date().getTime());
            if ("0000".equals(resJson.optString("resultCode"))) {
                JSONObject result = resJson.optJSONObject("result");
                long applicationDate = result.optLong("applicationDate");
                long deadline = result.optLong("deadline");
                try {
                    DateFormat zhCnDateFormat = getZhCnDateFormat();
                    result.put("applicationDate", zhCnDateFormat.format(new Date(applicationDate)));
                    result.put("deadline", zhCnDateFormat.format(new Date(deadline)));
                } catch (Exception e) {
                    log.warn(e);
                }
                request.setAttribute("result", result);
            } else {
                isSuccess = false;
                errorMessage = resJson.optString("resultDesc");
            }
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = e.getMessage().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
        }
        request.setAttribute("isSuccess", isSuccess);
        request.setAttribute("errorMessage", errorMessage);
        return mapping.findForward("reqSensitiveInfoResult");
    }

    /**
     * 敏感信息审核状态
     *
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward reqSensitiveInfoStat(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        final String auditStatus = StringUtils.defaultIfBlank(request.getParameter("auditStatus"), "Approved");
        final String startTimeStr = StringUtils.defaultIfBlank(request.getParameter("startTime"), "0");
        final String endTimeStr = StringUtils.defaultIfBlank(request.getParameter("endTime"), String.valueOf(new Date().getTime()));
        final String offsetStr = StringUtils.defaultIfBlank(request.getParameter("offset"), "0");
        final String limitStr = StringUtils.defaultIfBlank(request.getParameter("limit"), "10");
        int offset = 0;
        int limit = 10;
        long startTime = 0;
        long endTime = new Date().getTime();

        try {
            batchName = new String(batchName.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e);
        }

        request.setAttribute("batchRefId", batchName);
        request.setAttribute("loanRefId", loanRefId);
        try {
            offset = Integer.valueOf(offsetStr);
        } catch (NumberFormatException e) {
            isSuccess = false;
            errorMessage = "页偏移无效";
        }
        try {
            limit = Integer.valueOf(limitStr);
        } catch (NumberFormatException e) {
            errorMessage = "页容量无效";
            isSuccess = false;
        }

        try {
            startTime = Long.valueOf(startTimeStr);
        } catch (NumberFormatException e) {
            errorMessage = "开始时间无效";
            isSuccess = false;
        }
        try {
            endTime = Long.valueOf(endTimeStr);
        } catch (NumberFormatException e) {
            errorMessage = "截止时间无效";
            isSuccess = false;
        }
        request.setAttribute("offset", offset);
        request.setAttribute("limit", limit);
        request.setAttribute("auditStatus", auditStatus);

        try {
            JSONObject resJson = cashBusFacade.sensitiveInfoRequestStat(batchName, loanRefId, auditStatus, startTime, endTime, offset, limit);
            if ("0000".equals(resJson.optString("resultCode"))) {
                JSONObject result = resJson.optJSONObject("result");
                int totalCount = result.optInt("totalCount");
                JSONArray resultList = result.optJSONArray("resultList");
                Iterator<JSONObject> iterator = resultList.iterator();
                request.setAttribute("totalCount", totalCount);
                request.setAttribute("message", result.optString("message"));
                while (iterator.hasNext()) {
                    JSONObject item = iterator.next();
                    try {
                        long deadline = item.optLong("deadline");
                        item.put("deadline", getZhCnDateFormat().format(new Date(deadline)));
                    } catch (Exception e) {
                        log.warn(e);
                    }
                }
                request.setAttribute("results", resultList);
            } else {
                isSuccess = false;
                errorMessage = resJson.optString("resultDesc");
            }
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = e.getMessage().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
        }
        request.setAttribute("isSuccess", isSuccess);
        request.setAttribute("errorMessage", errorMessage);
        return mapping.findForward("reqSensitiveInfoStatResult");
    }

    /**
     * 获取敏感信息
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward sensitiveInfo(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        try {
            batchName = new String(batchName.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e);
        }
        try {
            JSONObject resJson = cashBusFacade.sensitiveInfo(batchName, loanRefId);
            if ("0000".equals(resJson.optString("resultCode"))) {
                JSONObject result = resJson.optJSONObject("result");
                String idcardUrl = result.optString("idcardUrl");
                String contractUrl = result.optString("contractUrl");
                result.put("idcardUrl", URLEncoder.encode(idcardUrl, "UTF-8"));
                result.put("contractUrl", URLEncoder.encode(contractUrl, "UTF-8"));
                request.setAttribute("result", result);
            } else {
                isSuccess = false;
                errorMessage = resJson.optString("resultDesc");
            }
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = e.getMessage().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
        }
        request.setAttribute("isSuccess", isSuccess);
        request.setAttribute("errorMessage", errorMessage);
        return mapping.findForward("sensitiveInfo");
    }

    /**
     * 获取现金巴士资源
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward cashBusResource(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String src = StringUtils.defaultIfBlank(request.getParameter("src"), "");
        try {
            src = new String(src.getBytes("ISO-8859-1"), "UTF-8");
        } catch (Exception e) {
            log.error(e);
        }

        try {
            IOUtils.copy(cashBusFacade.getResource(src), response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 载入催记页面
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward loadRecord(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String batchRefId       = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId  = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        try {
            batchRefId = new String(batchRefId.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e);
        }
        request.setAttribute("batchRefId", batchRefId);
        request.setAttribute("loanRefId" , loanRefId);
        return mapping.findForward("loadRecord");
    }

    /**
     * 催记
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward record(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";
        final String batchRefId       = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String loanRefId        = StringUtils.defaultIfBlank(request.getParameter("loanRefId"), "");
        final String content          = StringUtils.defaultIfBlank(request.getParameter("content"), "");
        request.setAttribute("batchRefId", batchRefId);
        request.setAttribute("loanRefId" , loanRefId);
        if (isSuccess = (!StringUtils.isBlank(content)) ) {
            errorMessage = "催记内容不能为空!";
        }
        if (isSuccess) {
            try {
                JSONObject record = cashBusFacade.record(batchRefId, loanRefId, content);
                if (isSuccess = ("0000".equals(record.optString("resultCode"))) ) {
                    errorMessage = "";
                }else{
                    errorMessage = record.optString("resultDesc");
                }
            } catch (Exception e) {
                isSuccess       = false;
                errorMessage    = e.getMessage().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            }
        }
        request.setAttribute("batchRefId", batchRefId);
        request.setAttribute("loanRefId" , loanRefId);
        request.setAttribute("isSuccess", isSuccess);
        request.setAttribute("errorMessage", errorMessage);
        return mapping.findForward("recordResult");
    }

    /**
     * 载入日还款记录请求页面
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward loadDayRepayment(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String batchRefId       = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        try {
            batchRefId              = new String(batchRefId.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e);
        }
        request.setAttribute("batchRefId", batchRefId);
        return mapping.findForward("dayRepayment");
    }

    /**
     * 获取日还款记录
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward dayRepayment(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";

        final DateFormat commonTimeFormat = getCommonTimeFormat();
        final DateFormat commonDateFormat = getCommonDateFormat();
        final DateFormat commonDayFormat = getCommonDayFormat();
        final Date now = new Date();

        String batchRefId           = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String startTimeStr   = StringUtils.defaultIfBlank(request.getParameter("startTime"), "00:00:00");
        final String dayStr         = StringUtils.defaultIfBlank(request.getParameter("repaymentDay"), getCommonDayFormat().format(new Date()));
        final String endTimeStr     = StringUtils.defaultIfBlank(request.getParameter("endTime"), commonTimeFormat.format(now));

        long startTime  = 0l;
        long endTime    = now.getTime();
        try {
            startTime   = commonDateFormat.parse(new StringBuilder(dayStr).append(" 00:00:00").toString()).getTime();
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = "开始时间不正确";
            log.error(errorMessage, e);
        }
        try {
            endTime     = commonDateFormat.parse(new StringBuilder(dayStr).append(" 23:59:59").toString()).getTime() + 999 ; // 日期的 23:59:59.999秒
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = "截止时间不正确";
            log.error(errorMessage, e);
        }
        if (isSuccess) {
            try {
                JSONObject resJson = cashBusFacade.dayRepayment(batchRefId, startTime, endTime);
                if ( ( isSuccess = "0000".equals(resJson.optString("resultCode")) ) ) {
                    final JSONArray results = resJson.optJSONArray("result");
                    final Iterator<JSONObject> iterator = results.iterator();
                    while (iterator.hasNext()) {
                        JSONObject item = iterator.next();
                        long repaymentDate = item.optLong("repaymentDate");
                        item.put("repaymentDate", getZhCnDateFormat().format(new Date(repaymentDate)));
                    }
                    request.setAttribute("results" , results);
                }else{
                    errorMessage = resJson.optString("resultDesc");
                }
            } catch (Exception e) {
                isSuccess = false;
                errorMessage = e.getMessage().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            }
        }
        request.setAttribute("batchRefId"   , batchRefId);
        request.setAttribute("startTime"    , startTime);
        request.setAttribute("endTime"      , endTime);
        request.setAttribute("isSuccess"    , isSuccess);
        request.setAttribute("errorMessage" , errorMessage);
        return mapping.findForward("dayRepaymentResult");
    }

    /**
     * 月对账表
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward loadMonthBillVerify(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String batchRefId       = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        try {
            batchRefId          = new String(batchRefId.getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e);
        }
        request.setAttribute("batchRefId", batchRefId);
        Calendar monthStart = Calendar.getInstance();
        monthStart.set(Calendar.DAY_OF_MONTH, 1);
        monthStart.set(Calendar.HOUR_OF_DAY, 0);
        monthStart.set(Calendar.MINUTE, 0);
        monthStart.set(Calendar.SECOND, 1);
        String monthDate = getCommonDateFormat().format(monthStart.getTime());
        request.setAttribute("startTime", monthDate);
        return mapping.findForward("loadMonthBillVerify");
    }

    /**
     * 月度对账表
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward monthBillVerify(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        boolean isSuccess = true;
        String errorMessage = "";

        final DateFormat commonDateFormat = getCommonDateFormat();
        final Date now = new Date();

        String batchRefId           = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String startTimeStr   = StringUtils.defaultIfBlank(request.getParameter("startTime"), "1970/01/01 00:00:00");
        final String endTimeStr     = StringUtils.defaultIfBlank(request.getParameter("endTime"), commonDateFormat.format(now));
        final String offsetStr      = StringUtils.defaultIfBlank(request.getParameter("offset"), "0");
        final String limitStr       = StringUtils.defaultIfBlank(request.getParameter("limit"), "10");

        int offset = 0;
        int limit = 10;
        long startTime  = 0l;
        long endTime    = now.getTime();
        try {
            startTime   = commonDateFormat.parse(startTimeStr).getTime();
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = "开始时间不正确";
            log.error(errorMessage, e);
        }
        try {
            endTime     = commonDateFormat.parse(endTimeStr).getTime();
        } catch (Exception e) {
            isSuccess = false;
            errorMessage = "截止时间不正确";
            log.error(errorMessage, e);
        }
        try {
            offset = Integer.valueOf(offsetStr);
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "页偏移无效");
        }
        try {
            limit = Integer.valueOf(limitStr);
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "页容量无效");
        }
        request.setAttribute("offset", offset);
        request.setAttribute("limit", limit);
        if (isSuccess) {
            try {
                JSONObject resJson = cashBusFacade.monthReconciliation(batchRefId, startTime, endTime, offset, limit);
                if ( ( isSuccess = "0000".equals(resJson.optString("resultCode")) ) ) {
                    final JSONObject result             = resJson.optJSONObject("result");
                    int totalCount                      = result.optInt("totalCount");
                    request.setAttribute("totalCount", totalCount);
                    JSONArray resultList = result.optJSONArray("resultList");
//                    if ((isSuccess = resultList.size() > 0)) {
                        final Iterator<JSONObject> iterator = resultList.iterator();
                        while (iterator.hasNext()) {
                            JSONObject item = iterator.next();
                            long repaymentDate = item.optLong("repaymentDate");
                            item.put("repaymentDate", getZhCnDateFormat().format(new Date(repaymentDate)));
                            long feePaidDate = item.optLong("feePaidDate");
                            item.put("feePaidDate", getZhCnDateFormat().format(new Date(feePaidDate)));
                        }
                        request.setAttribute("resultList" , resultList);
//                    } else {
//                        errorMessage = result.optString("resultDesc");
//                    }
                    request.setAttribute("commission", result.optDouble("message"));
                }else{
                    errorMessage = resJson.optString("resultDesc");
                }
            } catch (Exception e) {
                isSuccess = false;
                errorMessage = e.getMessage().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
            }
        }
        request.setAttribute("batchRefId"   , batchRefId);
        request.setAttribute("startTime"    , startTime);
        request.setAttribute("endTime"      , endTime);
        request.setAttribute("isSuccess"    , isSuccess);
        request.setAttribute("errorMessage" , errorMessage);
        return mapping.findForward("monthBillVerifyResult");
    }

    /**
     * 导出月度对账表
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward exportMonthBill(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String errorMessage         = "";
        final DateFormat commonDateFormat = getCommonDateFormat();
        final Date now              = new Date();

        String batchRefId           = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        final String limitStr       = StringUtils.defaultIfBlank(request.getParameter("limit"), "1000");
        final String startTimeStr   = StringUtils.defaultIfBlank(request.getParameter("startTime"), "0");
        final String endTimeStr     = StringUtils.defaultIfBlank(request.getParameter("endTime"), String.valueOf(now.getTime()));
        int offset                  = 0;     //  从0条开始，每次迭代+scopeLimit，因此取scopeLimit相反数
        int limit                   = 0;
        int scopeLimit              = cashBusFacade.getDefaultPullSize();
        long startTime              = 0l;
        long endTime                = now.getTime();
        try {
            startTime   = Long.valueOf(startTimeStr);
        } catch (Exception e) {
            errorMessage = "开始时间不正确";
            log.error(errorMessage, e);
        }
        try {
            endTime     = Long.valueOf(endTimeStr);
        } catch (Exception e) {
            errorMessage = "截止时间不正确";
            log.error(errorMessage, e);
        }
        try {
            limit = Integer.valueOf(limitStr);
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "页容量无效");
        }
        try {
            batchRefId = new String( batchRefId.getBytes("ISO-8859-1") , "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("不支持的编码", e);
        }
        response.setCharacterEncoding("UTF-8");
        if (StringUtils.isBlank(batchRefId)) {
            response.setContentType("text/html");
            try {
                response.getWriter().write("批次名不能为空!");
            } catch (IOException e) {
            }
            return null;
        }
        try {
            String filePath                     = new StringBuilder(request.getRealPath("/expTempFile")).append("/").append(batchRefId).append(getZhCnDateFormat().format(new Date())).append(".xls").toString();
            final File file                     = new File(filePath);
            final File parentDir                = file.getParentFile();
            final DateFormat zhCnDateFormat     = getZhCnDateFormat();
            if(!parentDir.exists()){
                parentDir.mkdirs();
            }
            final WritableWorkbook workbook     = Workbook.createWorkbook(file);
            final WritableSheet sheet           = workbook.createSheet(batchRefId, 0);

            final String filename               = URLEncoder.encode(file.getName(), "utf-8");
            response.setContentType("application/xls");
            response.setHeader("Content-Disposition", "attachment; filename=" + filename);
            while (limit > offset || limit ==0) {
                if (offset < limit || limit==0) {
//                    scopeLimit = limit - offset;
                    JSONObject resJson = cashBusFacade.monthReconciliation(batchRefId, startTime, endTime, offset, scopeLimit);
                    if ( (  "0000".equals(resJson.optString("resultCode")) ) ) {
                        final JSONObject result = resJson.optJSONObject("result");
                        int totalCount          = result.optInt("totalCount",-1);
                        limit = totalCount;
                        if (offset == 0) { //   写表格文件头
                            try {
                                StringBuilder firstHeadBuilder = new StringBuilder(batchRefId).append("月度对账表");
                                firstHeadBuilder.append(limit).append("条,共").append(totalCount).append("条.");
                                firstHeadBuilder.append("佣金共:").append(result.optString("message")).append("元.");
                                String firsthead = firstHeadBuilder.toString();
                                //  第一个单元格
                                sheet.addCell(new Label(0,      0, firsthead));
                                //  首行标题
                                sheet.addCell(new Label(0,      1, "批次名称"));
                                sheet.addCell(new Label(1,      1, "借款refId"));
                                sheet.addCell(new Label(2,      1, "委外商"));
                                sheet.addCell(new Label(3,      1, "还款金额"));
                                sheet.addCell(new Label(4,      1, "还款方式"));
                                sheet.addCell(new Label(5,      1, "还款时间"));
                                sheet.addCell(new Label(6,      1, "佣金比例%"));
                                sheet.addCell(new Label(7,      1, "佣金(单位分)"));
                                sheet.addCell(new Label(8,      1, "结算佣金时间"));
                                sheet.addCell(new Label(9,      1, "结算佣金人员"));
                                sheet.addCell(new Label(10,     1, "还款流水Id"));
                                sheet.addCell(new Label(11,     1, "还款流水Id"));
                            } catch (WriteException e) {
                                log.error("写表头失败！", e);
                                errorMessage = "导出失败!";
                            }
                        }
                        JSONArray resultList = result.optJSONArray("resultList");
                        //  写表格内容
                        for(int index = 0 ; index < resultList.size();index++) {
                            JSONObject item = resultList.optJSONObject(index);
                            try {
                                sheet.addCell(new Label(0   ,   offset+index+2  , item.optString("batchName")));
                                sheet.addCell(new Label(1   ,   offset+index+2  , item.optString("loanRefId")));
                                sheet.addCell(new Label(2   ,   offset+index+2  , item.optString("agencyName")));
                                sheet.addCell(new Label(3   ,   offset+index+2  , item.optString("collectedAmount")));
                                sheet.addCell(new Label(5   ,   offset+index+2  , zhCnDateFormat.format(new Date(item.optLong("repaymentDate")))));
                                sheet.addCell(new Label(4   ,   offset+index+2  , item.optString("repaymentMethod")));
                                sheet.addCell(new Label(6   ,   offset+index+2  , item.optString("feeRate")));
                                sheet.addCell(new Label(7   ,   offset+index+2  , item.optString("feePaidAmount")));
                                sheet.addCell(new Label(8   ,   offset+index+2  , zhCnDateFormat.format(new Date(item.optLong("feePaidDate")))));
                                sheet.addCell(new Label(9   ,   offset+index+2  , item.optString("feePaymentOperator")));
                                sheet.addCell(new Label(10  ,   offset+index+2  , item.optString("txId")));
                                sheet.addCell(new Label(11  ,   offset+index+2  , item.optString("taskId")));
                            } catch (WriteException e) {
                                log.error("写表内容失败!", e);
                                errorMessage = "导出失败!";
                            }
                        }
                        if(limit==0 && offset==0) {
                            log.error(String.format("出现错误！   并且未获取到值   总记录数   %s    从%s开始获取", limit, offset));
                            limit = -1;
                            break;
                        }
                    }
                }else{
                    log.warn(String.format("出现错误!! %s", errorMessage));
                    if(limit==0||offset==0) {
                        log.error(String.format("出现错误！   并且未获取到值   总记录数   %s    从%s开始获取", limit, offset));
                        limit = -1;
                        break;
                    }
                }
                offset += scopeLimit;
            }
            try {
                workbook.write();
                workbook.close();
            } catch (Exception e) {
                log.error("无法关闭文件!", e);
            }
            IOUtils.copy(FileUtils.openInputStream(file), response.getOutputStream());
            return null;
        } catch (IOException e) {
            log.error("导出失败,", e);
            errorMessage = "导出失败!";
        }
        try {
            response.getWriter().write(errorMessage);
        } catch (IOException e1) {
        }
        return null;
    }

    /**
     * 载入导出案件页面
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward loadExportCase(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        String batchRefId   = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        String createdStr   = StringUtils.defaultIfBlank(request.getParameter("created"), String.valueOf(new Date().getTime()));
        String withdrawStr  = StringUtils.defaultIfBlank(request.getParameter("withdrawTime"), String.valueOf(new Date().getTime()));
        try {
            batchRefId = new String( batchRefId.getBytes("ISO-8859-1") , "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("不支持的编码", e);
        }
        request.setAttribute("batchRefId"   , batchRefId);
        request.setAttribute("created"      , createdStr);
        request.setAttribute("withdraw"     , withdrawStr);
        return mapping.findForward("exportCase");
    }

    /**
     * 导出批次和案件
     * @param mapping
     * @param form
     * @param request
     * @param response
     * @return
     */
    public ActionForward exportCase(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        log.debug("导出现金巴士案件");
        boolean isSuccess   = true;
        String errorMessage = "";
        String batchRefId   = StringUtils.defaultIfBlank(request.getParameter("batchRefId"), "");
        String createdStr   = StringUtils.defaultIfBlank(request.getParameter("created"), String.valueOf(new Date().getTime()));
        String withdrawStr  = StringUtils.defaultIfBlank(request.getParameter("withdrawTime"), "");
        String autoCreatBatS= StringUtils.defaultIfBlank(request.getParameter("autoCreateBat"), "false");
        boolean autoCreateBat= false;
//        try {
//            batchRefId = new String( batchRefId.getBytes("ISO-8859-1") , "UTF-8");
//        } catch (UnsupportedEncodingException e) {
//            log.error("不支持的编码", e);
//        }
        final CaseBat caseBat = new CaseBat();
        try {
            autoCreateBat = Boolean.valueOf(autoCreatBatS);
        } catch (Exception e) {
        }
        Date createTime     = new Date();
        Date withdrawTime   = new Date(0);
        try {
            createTime.setTime(Long.valueOf(createdStr));
        } catch (NumberFormatException e) {
            log.error("请求格式错误" + createdStr, e);
        }
        try {
            withdrawTime.setTime(Long.valueOf(withdrawStr));
            caseBat.setCbatBackdateP(withdrawTime);
        } catch (NumberFormatException e) {
            log.error("请求格式错误" + withdrawStr, e);
        }
        LimUser limUser = (LimUser)request.getSession().getAttribute("limUser");
        if (autoCreateBat && !caseBatBiz.checkBatCode(batchRefId)) {  //  批次不存在，创建
            log.debug("创建批次");
            caseBat.setCbatCode(batchRefId);
            caseBat.setCbatArea(new TypeList(115l));    //  默认北京
            caseBat.setCaseTypeList(new TypeList(42l)); //  默认长账龄
            caseBat.setBankTypeList(new TypeList(130l));//  默认委托方为现金巴士

            caseBat.setCbatNum(Integer.valueOf(0));
            caseBat.setCbatState(Integer.valueOf(0));
            caseBat.setCbatInsDate(GetDate.getCurTime());
            caseBat.setCbatInsUser(limUser.getUserSeName());
            caseBat.setCbatDate(createTime);
            String caseBatlog = new StringBuilder(getZhCnDateFormat().format(new Date()))
                    .append("&nbsp;&nbsp;&nbsp;").append(limUser.getUserSeName())
                    .append("：现金巴士接入自动创建案件批次[").append(caseBat.getCbatCode())
                    .append( "]<br/>").toString();
            caseBat.setCbatLog(caseBatlog);
            this.caseBatBiz.saveBat(caseBat);
            log.debug("批次创建完成");
        }
        int limit       = 0;
        int offset      = 0;
        int scopeLimit  = cashBusFacade.getDefaultPullSize();
        try {
            String filePath                 = new StringBuilder(request.getRealPath("/expTempFile")).append("/exportLoan").append(StringFormat.filterFileName(batchRefId.replaceAll(" ",""))).append(getZhCnDateFormat().format(new Date())).append(".xls").toString();
            final File file                 = new File(filePath);
            final File parentDir            = file.getParentFile();
            if(!parentDir.exists()){
                parentDir.mkdirs();
            }
            log.debug("保存到  " + filePath);
            final WritableWorkbook workbook = Workbook.createWorkbook(file);
            final WritableSheet sheet       = workbook.createSheet(batchRefId, 0);

//            limit = -1;
            while (limit > offset || limit == 0) {
                log.debug(String.format("需要获取  从%s获取 %s条  ", offset, scopeLimit));
                if (offset < limit || limit == 0 ) {
                    try {
                        JSONObject resJson = cashBusFacade.getBatch(batchRefId, offset, scopeLimit);
                        if ("0000".equals(resJson.optString("resultCode"))) {
                            JSONObject result = resJson.optJSONObject("result");
                            JSONArray resultList = result.optJSONArray("resultList");
                            limit = result.optInt("totalCount",-1);
                            log.debug(String.format("limit of totalCount  %s   ", limit));
                            if (offset == 0) {
                                //  首行标题
                                try {
                                    sheet.addCell(new Label(0   ,     0, "用户姓名"));
                                    sheet.addCell(new Label(1   ,     0, "借款refId"));
                                    sheet.addCell(new Label(2   ,     0, "用户身份证号"));
                                    sheet.addCell(new Label(3   ,     0, "借款天数"));
                                    sheet.addCell(new Label(4   ,     0, "当前应还金额"));
                                    sheet.addCell(new Label(5   ,     0, "银行名称"));
                                    sheet.addCell(new Label(6   ,     0, "婚否"));
                                    sheet.addCell(new Label(7   ,     0, "年龄"));
                                    sheet.addCell(new Label(8   ,     0, "用户电话"));
                                    sheet.addCell(new Label(9   ,     0, "借款金额"));
                                    sheet.addCell(new Label(10  ,     0, "原始还款时间"));
                                    sheet.addCell(new Label(11  ,     0, "逾期天数"));
                                    sheet.addCell(new Label(12  ,     0, "银行卡号"));
                                    sheet.addCell(new Label(13  ,     0, "联系人1关系"));
                                    sheet.addCell(new Label(14  ,     0, "联系人1电话"));
                                    sheet.addCell(new Label(15  ,     0, "联系人2关系"));
                                    sheet.addCell(new Label(16  ,     0, "联系人2电话"));
                                    sheet.addCell(new Label(17  ,     0, "工作省份"));
                                    sheet.addCell(new Label(18  ,     0, "工作城市"));
                                    sheet.addCell(new Label(19  ,     0, "工作电话"));
                                    sheet.addCell(new Label(20  ,     0, "工作单位"));
                                    sheet.addCell(new Label(21  ,     0, "工作地址"));
                                    sheet.addCell(new Label(22  ,     0, "常用联系人1"));
                                    sheet.addCell(new Label(23  ,     0, "常用联系人2"));
                                    sheet.addCell(new Label(24  ,     0, "常用联系人3"));
                                    sheet.addCell(new Label(25  ,     0, "常用联系人4"));
                                    sheet.addCell(new Label(26  ,     0, "常用联系人5"));
                                    sheet.addCell(new Label(27  ,     0, "常用联系人6"));
                                    sheet.addCell(new Label(28  ,     0, "常用联系人7"));
                                    sheet.addCell(new Label(29  ,     0, "常用联系人8"));
                                    sheet.addCell(new Label(30  ,     0, "常用联系人9"));
                                    sheet.addCell(new Label(31  ,     0, "常用联系人10"));
                                    sheet.addCell(new Label(32  ,     0, "常用联系人11"));
                                    sheet.addCell(new Label(33  ,     0, "常用联系人12"));
                                    sheet.addCell(new Label(34  ,     0, "常用联系人13"));
                                    sheet.addCell(new Label(35  ,     0, "常用联系人14"));
                                    sheet.addCell(new Label(36  ,     0, "常用联系人15"));
                                    sheet.addCell(new Label(37  ,     0, "常用联系人16"));
                                    sheet.addCell(new Label(38  ,     0, "常用联系人17"));
                                    sheet.addCell(new Label(39  ,     0, "常用联系人18"));
                                    sheet.addCell(new Label(40  ,     0, "常用联系人19"));
                                    sheet.addCell(new Label(41  ,     0, "常用联系人20"));
                                    sheet.addCell(new Label(42  ,     0, "常用联系人21"));
                                    sheet.addCell(new Label(43  ,     0, "常用联系人22"));
                                    sheet.addCell(new Label(44  ,     0, "常用联系人23"));
                                    sheet.addCell(new Label(45  ,     0, "常用联系人24"));
                                    sheet.addCell(new Label(46  ,     0, "常用联系人25"));
                                    sheet.addCell(new Label(47  ,     0, "常用联系人26"));
                                } catch (WriteException e) {
                                    log.warn("写入Excel失败!", e);
                                }
                            }
                            for(int index = 0 ; index<resultList.size();index++) {
                                final int row = 1 + index + offset;
//                                log.debug(String.format("添加第 %s  行数据", row));
                                final JSONObject item = resultList.optJSONObject(index);
                                try {
                                    sheet.addCell(new Label(0   ,     row , item.optString("name")));
                                    sheet.addCell(new Label(1   ,     row , item.optString("loanRefId")));
                                    sheet.addCell(new Label(2   ,     row , item.optString("userCardNo")));
                                    sheet.addCell(new Label(3   ,     row , item.optString("days")));
                                    sheet.addCell(new Label(4   ,     row , item.optString("originalAmountDue")));
                                    sheet.addCell(new Label(5   ,     row , item.optString("bankName")));
                                    sheet.addCell(new Label(6   ,     row , item.optString("maritalStatus")));
                                    sheet.addCell(new Label(7   ,     row , item.optString("age")));
                                    sheet.addCell(new Label(8   ,     row , item.optString("userPhone")));
                                    sheet.addCell(new Number(9  ,     row , item.optDouble("amount")));
                                    sheet.addCell(new Label(10  ,     row , getCommonDateFormat_().format(new Date(item.optLong("repaymentDate")))));
                                    sheet.addCell(new Label(11  ,     row , item.optString("overdueDays")));
                                    sheet.addCell(new Label(12  ,     row , item.optString("bankCardNo")));
                                    sheet.addCell(new Label(13  ,     row , item.optString("relativeType")));
                                    sheet.addCell(new Label(14  ,     row , item.optString("relativePhone")));
                                    sheet.addCell(new Label(15  ,     row , item.optString("socialType")));
                                    sheet.addCell(new Label(16  ,     row , item.optString("socialPhone")));
                                    sheet.addCell(new Label(17  ,     row , item.optString("workProvince")));
                                    sheet.addCell(new Label(18  ,     row , item.optString("workCity")));
                                    sheet.addCell(new Label(19  ,     row , item.optString("workPhone")));
                                    sheet.addCell(new Label(20  ,     row , item.optString("workUnit")));
                                    sheet.addCell(new Label(21  ,     row , item.optString("workAddress")));
                                    String frequentContactStr = item.optString("frequentContacts");
                                    String[] frequentContacts = frequentContactStr.split(",|;|\\r\\n|\\r|\\n|\\| |\\.");
                                    for(int indexFc = 0 ; indexFc < frequentContacts.length;indexFc++) {
                                        sheet.addCell(new Label(21+indexFc  , row , frequentContacts[indexFc]));
                                    }
                                } catch (WriteException e) {
                                    log.warn("写入Excel失败!", e);
                                }
                            }
                            if(limit==0 && offset==0) {
                                log.error(String.format("出现错误！   并且未获取到值   总记录数   %s    从%s开始获取", limit, offset));
                                limit = -1;
                                break;
                            }
                        }else{
                            isSuccess = false;
                            errorMessage += resJson.optString("resultDesc");
                            log.warn(String.format("出现错误!! %s", errorMessage));
                            if(limit==0||offset==0) {
                                log.error(String.format("出现错误！   并且未获取到值   总记录数   %s    从%s开始获取", limit, offset));
                                limit = -1;
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("写Excel失败！", e);
                        isSuccess = false;
                    }
                }
                offset += scopeLimit;
                log.debug(String.format("offest %s   limit  %s 是否继续下一轮测试获取数据   %s ", offset,limit,(limit > offset || limit == 0)));
            }
            try {
                workbook.write();
            }catch (Exception ex){
                log.error("导出失败!", ex);
            }finally {
                try {
                    workbook.close();
                } catch (Exception ex) {
                    log.error("不能关闭工作薄!", ex);

                }

            }
            if(limit!=0) {
                final String filename = URLEncoder.encode(file.getName(), "utf-8");
                response.setContentType("application/xls");
                response.setHeader("Content-Disposition", "attachment; filename=" + filename);
                IOUtils.copy(FileUtils.openInputStream(file), response.getOutputStream());
                return null;
            }
            errorMessage="请<a href=\"/expTempFile/exportLoan/"+file.getName()+"\">下载</a>后检查数据。";
        } catch (Exception e) {
            log.error("导出现金巴士案件出错",e);
            isSuccess = false;
            errorMessage = "导出现金巴士案件出错!"+e.getMessage();
        }

        request.setAttribute("isSuccess"    , isSuccess);
        request.setAttribute("opTitle"      , "导出现金巴士案件");
        request.setAttribute("errorMessage" , errorMessage);
        request.setAttribute("errorMsg"     , errorMessage);
        return mapping.findForward("error");
    }


    public ActionForward refreshLoanCase(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setCharacterEncoding("utf-8");
        } catch (UnsupportedEncodingException e) {
        }
        String batchName = StringUtils.defaultIfBlank(request.getParameter("batchname"), "");
        try{
            batchName = new String(batchName.getBytes("ISO-8859-1"), "UTF-8");
            request.setAttribute("batchNameNoEncode", batchName);
            request.setAttribute("batchName", URLEncoder.encode(batchName, cashBusFacade.getHttpsUtils().getDefaultCharset()));
            cashBusFacade.loadLoanCaseToData(batchName);
        } catch (Exception e) {
            log.error("拉取数据失败!  ", e);
        }
        request.setAttribute("loadData", "true");
        return getbatch(mapping, form, request, response);
    }

    /**
     * 获取中文日期时间格式化工具
     * @return
     */
    protected DateFormat getZhCnDateFormat(){
        return new SimpleDateFormat("yyyy年M月d日H时m分s秒");
    }

    /**
     * 通用日期传输格式
     * @return
     */
    protected DateFormat getCommonDateFormat(){return new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");}
    protected DateFormat getCommonDateFormat_(){return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");}

    /**
     *
     * @return
     */
    protected DateFormat getCommonTimeFormat(){return new SimpleDateFormat("HH:mm:ss");}
    protected DateFormat getCommonDayFormat(){return new SimpleDateFormat("yyyy/MM/dd");}
}