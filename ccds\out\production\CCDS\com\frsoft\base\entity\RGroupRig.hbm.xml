<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<class name="com.frsoft.base.entity.RGroupRig" table="r_group_rig" schema="dbo">
		<id name="rgrId" type="java.lang.Long">
			<column name="rgr_id" />
			<generator class="identity"/>
		</id>
		<property name="rgrGrpId" type="java.lang.Long">
			<column name="rgr_grp_id" />
		</property>
		<many-to-one name="rgrRight" class="com.frsoft.base.entity.LimRight">
			<column name="rgr_rig_code" />
		</many-to-one> 
	</class>
</hibernate-mapping>