package com.psit.struts.DAO.impl;

import com.psit.struts.DAO.CashBusWorkOrderDAO;
import com.psit.struts.entity.CashBusWorkOrder;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Query;
import org.hibernate.classic.Session;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import java.util.List;

public class CashBusWorkOderDAOImpl extends HibernateDaoSupport implements CashBusWorkOrderDAO {
    protected static final Log log = LogFactory.getLog(AccountDAOImpl.class);
    @Override
    public int getCount(String loanRefId, String status) {
        Session session = (Session)super.getSession();
        String sql = "";
        if (loanRefId != null && !loanRefId.equals("") ) {
            if (sql.length() > 0) {
                sql = sql + " and loanRefId  like '%" + loanRefId + "%'";
            } else {
                sql = " where loanRefId  like '%" + loanRefId + "%'";
            }
        }
        if ((status!=null && !status.equals(""))) {
            if (sql.length() > 0) {
                sql = sql + " and status  like '%" + status + "%'";
            } else {
                sql = " where status  like '%" + status + "%'";
            }
        }

        String queryString = "select count(id) from CashBusWorkOrder " + sql;
        Query query = session.createQuery(queryString);
        int count = Integer.parseInt(query.uniqueResult().toString());
        return count;
    }

    @Override
    public List findAll() {
        return this.getHibernateTemplate().find("from CashBusWorkOrder");
    }

    @Override
    public void save(CashBusWorkOrder cashBusWorkOrder) {
        log.debug("saving CashBusWorkOrder instance");

        try {
            this.getHibernateTemplate().save(cashBusWorkOrder);
            log.debug("save successful");
        } catch (RuntimeException var3) {
            log.error("save failed", var3);
            throw var3;
        }
    }

    @Override
    public CashBusWorkOrder findById(Long id) {
        log.debug("getting Account instance with id: " + id);

        try {
            CashBusWorkOrder instance = (CashBusWorkOrder)this.getHibernateTemplate().get(CashBusWorkOrder.class.getName(), id);
            return instance;
        } catch (RuntimeException var3) {
            log.error("get failed", var3);
            throw var3;
        }
    }

    @Override
    public void update(CashBusWorkOrder cashBusWorkOrder) {
        super.getHibernateTemplate().update(cashBusWorkOrder);
    }

    @Override
    public void delete(CashBusWorkOrder cashBusWorkOrder) {
        log.debug("deleting CashBusWorkOrder instance");

        try {
            this.getHibernateTemplate().delete(cashBusWorkOrder);
            log.debug("delete successful");
        } catch (RuntimeException var3) {
            log.error("delete failed", var3);
            throw var3;
        }
    }

    @Override
    public List<CashBusWorkOrder> findByLoanRefId(String loanRefId) {
        return this.getHibernateTemplate().find("from CashBusWorkOrder cbwo where cbow.loanRefId=?", loanRefId);
    }

}
