<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.VisRecAss" table="vis_rec_ass" schema="dbo" >
        <id name="vraId" type="java.lang.Long">
            <column name="vra_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="visRecord" class="com.frsoft.ccds.entity.VisRecord" fetch="select" not-null="false">
            <column name="vra_vr_id"/>
        </many-to-one>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select" not-null="false">
            <column name="vra_user_code"/>
        </many-to-one>
       
    </class>
</hibernate-mapping>
