<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.CtiServer" table="cti_server" schema="dbo" >
        <id name="ctisId" type="java.lang.Long">
            <column name="ctis_id" />
            <generator class="identity" />
        </id>
        <property name="ctisIp" type="java.lang.String">
            <column name="ctis_ip" length="100" />
        </property>
         <property name="ctisName" type="java.lang.String">
            <column name="ctis_name" length="100" />
        </property>
        <property name="ctisCreMan" type="java.lang.String">
            <column name="ctis_cre_man" length="50" />
        </property>
        <property name="ctisCreTime" type="java.util.Date">
            <column name="ctis_cre_time" length="23" />
        </property>
        <property name="ctisUpdMan" type="java.lang.String">
            <column name="ctis_upd_man" length="50" />
        </property>
        <property name="ctisUpdTime" type="java.util.Date">
            <column name="ctis_upd_time" length="23" />
        </property>
    </class>
</hibernate-mapping>
