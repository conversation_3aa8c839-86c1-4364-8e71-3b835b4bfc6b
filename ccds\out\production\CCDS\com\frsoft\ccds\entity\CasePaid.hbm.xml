<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.CasePaid" table="case_paid" schema="dbo" >
        <id name="paId" type="java.lang.Long">
            <column name="pa_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="bankCase" class="com.frsoft.ccds.entity.BankCase" fetch="select" not-null="false">
            <column name="pa_cas_id"/>
        </many-to-one>
        <property name="paState" type="java.lang.Integer">
            <column name="pa_state"/>
        </property>
        
        <property name="paPtpD" type="java.util.Date">
            <column name="pa_ptp_d" length="23" />
        </property>
        <property name="paPtpNum" type="java.lang.Double">
            <column name="pa_ptp_num" precision="18" />
        </property>
        <property name="paCpTime" type="java.util.Date">
            <column name="pa_cp_time" length="23" />
        </property>
        <property name="paCpNum" type="java.lang.Double">
            <column name="pa_cp_num" precision="18" />
        </property>
        <property name="paComtUser" type="java.lang.String">
            <column name="pa_comt_user" length="25" />
        </property>
        <property name="paComtTime" type="java.util.Date">
            <column name="pa_comt_time" length="23" />
        </property>
        <property name="paPaidTime" type="java.util.Date">
            <column name="pa_paid_time" length="23" />
        </property>
        <property name="paPaidNum" type="java.lang.Double">
            <column name="pa_paid_num" precision="18" />
        </property>
        <property name="paMPaid" type="java.lang.Double">
            <column name="pa_m_paid" precision="18" />
        </property>
        <property name="paCpmPaid" type="java.lang.Double">
            <column name="pa_cpm_paid" precision="18" />
        </property>
        <property name="paBackPaid" type="java.lang.Double">
            <column name="pa_back_paid" precision="18" />
        </property>
        <property name="paPbackPaid" type="java.lang.Double">
            <column name="pa_pback_paid" precision="18" />
        </property>
        <property name="paSurUser" type="java.lang.String">
            <column name="pa_sur_user" length="25" />
        </property>
        <property name="paSurTime" type="java.util.Date">
            <column name="pa_sur_time" length="23" />
        </property>
        <property name="paSurRemark" type="java.lang.String">
            <column name="pa_sur_remark" length="1073741823" />
        </property>
        <property name="paWriter" type="java.lang.String">
            <column name="pa_writer" length="25" />
        </property>
        <property name="paWriTime" type="java.util.Date">
            <column name="pa_wri_time" length="23" />
        </property>
        <property name="paAltUser" type="java.lang.String">
            <column name="pa_alt_user" length="25" />
        </property>
        <property name="paAltTime" type="java.util.Date">
            <column name="pa_alt_time" length="23" />
        </property>
        <property name="paDelUser" type="java.lang.String">
            <column name="pa_del_user" length="25" />
        </property>
        <property name="paDelTime" type="java.util.Date">
            <column name="pa_del_time" length="23" />
        </property>
        <property name="paPayMan" type="java.lang.String">
            <column name="pa_pay_man" length="50" />
        </property>
        <property name="paPayMethod" type="java.lang.String">
            <column name="pa_pay_method" length="100" />
        </property>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="pa_se_no" />
        </many-to-one>
        <property name="paCmPaid" type="java.lang.Double">
            <column name="pa_cm_paid" precision="18" />
        </property>
        <property name="paLastDebtM" type="java.lang.Double">
            <column name="pa_last_debt_m" precision="18" />
        </property>
        <property name="paLeftAmt" type="java.lang.Double">
            <column name="pa_left_amt" precision="18" />
        </property>
    </class>
</hibernate-mapping>
