<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.ccds.entity.Comment" table="comment" schema="dbo" >
        <id name="cotId" type="java.lang.Long">
            <column name="cot_id" />
            <generator class="identity" />
        </id>
        <property name="cotContent" type="java.lang.String">
            <column name="cot_content" length="**********" />
        </property>
        <many-to-one name="bankCase" class="com.frsoft.ccds.entity.BankCase" fetch="select" not-null="false">
            <column name="cot_cas_id" />
        </many-to-one>
        <property name="cotUser" type="java.lang.String">
            <column name="cot_user" length="25" />
        </property>
        <property name="cotTime" type="java.util.Date">
            <column name="cot_time" length="23" />
        </property>
        <property name="cotState" type="java.lang.Integer">
            <column name="cot_state" />
        </property>
    </class>
</hibernate-mapping>
