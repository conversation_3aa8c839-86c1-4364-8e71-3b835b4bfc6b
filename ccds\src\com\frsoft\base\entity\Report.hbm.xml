<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.Report" table="report" schema="dbo">
        <id name="repCode" type="java.lang.Long">
            <column name="rep_code"/>
            <generator class="identity" />
        </id>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="rep_se_no"/>
        </many-to-one>
        <property name="repTitle" type="java.lang.String">
            <column name="rep_title" length="100" />
        </property>
        <property name="repSendTitle" type="java.lang.String">
            <column name="rep_send_title" length="100" />
        </property>
        <property name="repContent" type="java.lang.String">
            <column name="rep_content" length="1073741823" />
        </property>
        <property name="repIsSend" type="java.lang.String">
            <column name="rep_issend" length="1" />
        </property>
        <property name="repIsappro" type="java.lang.String">
            <column name="rep_isappro" length="1" />
        </property>
        <property name="repIsdel" type="java.lang.String">
            <column name="rep_isdel" length="1" />
        </property>
         <property name="repInsUser" type="java.lang.String">
            <column name="rep_ins_user" length="50" />
        </property>
        <property name="repDate" type="java.util.Date">
            <column name="rep_date" length="23" />
        </property>
        <property name="repRecName" type="java.lang.String">
            <column name="rep_rec_name" length="1073741823" />
        </property>
        <many-to-one name="repType" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
        	<column name="rep_type"/>
        </many-to-one>
        <set name="attachments" inverse="true"  cascade="all" where="att_type='rep'">
            <key>
                <column name="att_fk_id"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
        <set name="RRepLims" inverse="true"  cascade="all">
            <key>
                <column name="rrl_rep_code" length="50" />
            </key>
            <one-to-many class="com.frsoft.base.entity.RRepLim" />
        </set>
    </class>
</hibernate-mapping>
