<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>添加录音服务器</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/> 
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript">
		function check(){
			var errStr = "";
			if(isEmpty("ctisIp")){
				errStr+="- 未填写服务器IP！\n";
			}
			if(isEmpty("ctisName")){
				errStr+="- 未填写服务器名称！\n";
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				waitSubmit("doSave");
				waitSubmit("doCancel");
				return $("saveForm").submit();
			}
		}
		
  	</script>
  </head>
  <body>
  	<div class="inputDiv">
        <form id="saveForm" action="ctiServerAction.do" method="post">
            <input type="hidden" name="op" value="saveCtis" />
            <input type="hidden" name="ctisId" value="${ctis.ctisId}" />
            <table class="dashTab inputForm" cellpadding="0" cellspacing="0">
                <tbody>                  
                    <tr>
                        <th class="required">服务器IP：<span class='red'>*</span></th>
                        <td colspan=3 class="longTd">http://
                        	<input type="text" class="inputSize2 inputBoxAlign" name="ctisIp" value='<c:out value="${ctisIp}"/>' onBlur="autoShort(this,50)">&nbsp;:
                        	<input type="text" class="inputSize2 inputBoxAlign" style="width:60px" name="ctisPort" value='<c:out value="${ctisPort}"/>' onBlur="checkIsInt(this)">&nbsp;/
                        	<input type="text" class="inputSize2 inputBoxAlign" name="ctisPath" value='<c:out value="${ctisPath}"/>' onBlur="autoShort(this,30)">
                       	</td>
                    </tr>                                                                        
                    <tr class="noBorderBot">
                    	<th class="required">服务器名称：<span class='red'>*</span></th>
                        <td colspan=3><input type="text" class="inputSize2L" name="ctisName"  value='<c:out value="${ctis.ctisName}"/>' onBlur="autoShort(this,100)"></td>
                    </tr>
                    <tr class="submitTr">
                        <td colspan="4">
                        <input id="cbatSave" class="butSize1" type="button" value="保存" onClick="check()" />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                        </td>
                    </tr>                   
                </tbody>
            </table>
        </form>
    </div>
  </body>
</html>
