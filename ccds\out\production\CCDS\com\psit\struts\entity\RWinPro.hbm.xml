<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RWinPro" table="r_win_pro" schema="dbo" >
        <id name="rwiId" type="java.lang.Long">
            <column name="rwi_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsWarIn" class="com.psit.struts.entity.WmsWarIn" fetch="select" not-null="false">
            <column name="rwi_win_id"/>
        </many-to-one>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rwi_pro_id"/>
        </many-to-one>
        <property name="rwiWinNum" type="java.lang.Double">
            <column name="rwi_win_num" />
        </property>
        <property name="rwiRemark" type="java.lang.String">
            <column name="rwi_remark" length="1073741823" />
        </property>
    </class>
</hibernate-mapping>
