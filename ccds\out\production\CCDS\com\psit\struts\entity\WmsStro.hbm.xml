<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.WmsStro" table="wms_stro" schema="dbo"  >
        <id name="wmsCode" type="java.lang.String">
            <column name="wms_code" length="50" />
            <generator class="assigned" />
        </id>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select" not-null="false">
            <column name="wms_user_code" length="50" />
        </many-to-one>
        <many-to-one name="wmsStroType" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="wms_type_id" />
        </many-to-one>
        <property name="wmsName" type="java.lang.String">
            <column name="wms_name" length="300" />
        </property>
        <property name="wmsLoc" type="java.lang.String">
            <column name="wms_loc" length="1073741823" />
        </property>
        <property name="wmsCreDate" type="java.util.Date">
            <column name="wms_cre_date" length="23" />
        </property>
        <property name="wmsRemark" type="java.lang.String">
            <column name="wms_remark" length="1073741823" />
        </property>
        <property name="wmsIsenabled" type="java.lang.String">
            <column name="wms_isenabled" length="1" />
        </property>
        <set name="RStroPros" inverse="true" cascade="all">
            <key>
                <column name="rsp_stro_code" length="50" />
            </key>
            <one-to-many class="com.psit.struts.entity.RStroPro" />
        </set>
        <set name="wmsWarOuts" inverse="true" cascade="all">
            <key>
                <column name="wwo_stro_code" length="50" />
            </key>
            <one-to-many class="com.psit.struts.entity.WmsWarOut" />
        </set>
    </class>
</hibernate-mapping>
