package com.psit.struts.DAO;

import com.psit.struts.entity.CashBusLoanCase;
import com.psit.struts.entity.CashBusWorkOrder;

import java.util.List;

public interface CashBusLoanCaseDAO {
    long getCount(String loanRefId, String batchName);

    List findAll();

    void save(CashBusLoanCase cashBusLoanCase);

    CashBusLoanCase findById(Long id);

    void update(CashBusLoanCase cashBusLoanCase);

    void delete(CashBusLoanCase cashBusLoanCase);

    List<CashBusLoanCase> findByLoanRefId(String batchName , String loanRefId);
    List<CashBusLoanCase> findByLoanRefId(String batchName , String loanRefId, final  int offset , final  int limit);
    long findByLoanRefIdCount(String batchName , String loanRefId);

    List<CashBusLoanCase> findByBatchName(String batchName );
    List<CashBusLoanCase> findByBatchName(String batchName, final  int offset , final  int limit);
    long findByBatchNameCount(String batchName );

    /**
     * 搜索
     * @param batchName 批次名  强验证
     * @param phone     手机号  弱验证
     * @param name      姓名    弱验证
     * @return
     */
    List<CashBusLoanCase> search(String batchName,String phone,String name , final  int offset , final  int limit);
    long searchCount(String batchName,String phone,String name);

    /**
     * 搜索
     * @param batchName 批次名  强验证
     * @param key       关键字  弱验证
     * @return
     */
    List<CashBusLoanCase> search(String batchName, String key , final  int offset , final  int limit);
    long searchCount(String batchName, String key);
}
