<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.LoanCus" table="loan_cus" schema="dbo" >
        <id name="lcId" type="java.lang.Long">
            <column name="lc_id" />
            <generator class="identity" />
        </id>
        <property name="lcName" type="java.lang.String">
            <column name="lc_name" length="50" />
        </property>
        <property name="lcCardNum" type="java.lang.String">
            <column name="lc_card_num" length="50" />
        </property>
        <property name="lcCardType" type="java.lang.String">
            <column name="lc_card_type" length="20" />
        </property>
        <property name="lcBank" type="java.lang.String">
            <column name="lc_bank" length="100" />
        </property>
        <property name="lcDueM" type="java.lang.Double">
            <column name="lc_due_m" precision="18" />
        </property>
        <property name="lcPrincipal" type="java.lang.Double">
            <column name="lc_principal" precision="18" />
        </property>
        <property name="lcTimeLim" type="java.lang.String">
            <column name="lc_time_lim" length="50" />
        </property>
        <property name="lcQuality" type="java.lang.String">
            <column name="lc_quality" length="20" />
        </property>
        <property name="lcDueNum" type="java.lang.String">
            <column name="lc_due_num" length="50" />
        </property>
        <property name="lcOverdue" type="java.lang.String">
            <column name="lc_overdue" length="50" />
        </property>
        <property name="lcDueDate" type="java.util.Date">
            <column name="lc_due_date" length="23" />
        </property>
        <property name="lcEndDate" type="java.util.Date">
            <column name="lc_end_date" length="23" />
        </property>
        <property name="lcCompany" type="java.lang.String">
            <column name="lc_company" length="100" />
        </property>
        <property name="lcComAddr" type="java.lang.String">
            <column name="lc_com_addr" length="300" />
        </property>
        <property name="lcComPho" type="java.lang.String">
            <column name="lc_com_pho" length="50" />
        </property>
        <property name="lcHomeAddr" type="java.lang.String">
            <column name="lc_home_addr" length="300" />
        </property>
        <property name="lcHomePho" type="java.lang.String">
            <column name="lc_home_pho" length="50" />
        </property>
        <property name="lcManager" type="java.lang.String">
            <column name="lc_manager" length="50" />
        </property>
        <property name="lcNum" type="java.lang.String">
            <column name="lc_num" length="50" />
        </property>
        <property name="lcRemark" type="java.lang.String">
            <column name="lc_remark" length="200" />
        </property>
        <property name="lcRiskRs" type="java.lang.String">
            <column name="lc_risk_rs" length="500" />
        </property>
        <property name="lcRiskAdv" type="java.lang.String">
            <column name="lc_risk_adv" length="500" />
        </property>
        <property name="lcRiskPer" type="java.lang.String">
            <column name="lc_risk_per" length="500" />
        </property>
        <property name="lcRiskCom" type="java.lang.String">
            <column name="lc_risk_com" length="500" />
        </property>
        <property name="lcRiskBiz" type="java.lang.String">
            <column name="lc_risk_biz" length="500" />
        </property>
        <property name="lcRiskQue" type="java.lang.String">
            <column name="lc_risk_que" length="50" />
        </property>
        <property name="lcOpState" type="java.lang.String">
            <column name="lc_op_state" length="200" />
        </property>
        <property name="lcInsUser" type="java.lang.String">
            <column name="lc_ins_user" length="25" />
        </property>
        <property name="lcInsTime" type="java.util.Date">
            <column name="lc_ins_time" length="23" />
        </property>
        <property name="lcAltUser" type="java.lang.String">
            <column name="lc_alt_user" length="25" />
        </property>
        <property name="lcAltTime" type="java.util.Date">
            <column name="lc_alt_time" length="23" />
        </property>
        <many-to-one name="lcLoanSeason" class="com.frsoft.loan.entity.LoanSeason" fetch="select">
        	<column name="lc_lse_id" />
        </many-to-one>
        <set name="attachments" inverse="true"  cascade="all" where="att_type='loanCus'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
	    </set>
    </class>
</hibernate-mapping>
