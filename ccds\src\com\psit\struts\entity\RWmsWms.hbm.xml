<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RWmsWms" table="r_wms_wms" schema="dbo" >
        <id name="rwwId" type="java.lang.Long">
            <column name="rww_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rww_pro_id" />
        </many-to-one>
        <many-to-one name="wmsChange" class="com.psit.struts.entity.WmsChange" fetch="select" not-null="false">
            <column name="wch_id" />
        </many-to-one>
        <property name="rwwNum" type="java.lang.Double">
            <column name="rww_num" />
        </property>
        <property name="rwwRemark" type="java.lang.String">
            <column name="rww_remark"  />
        </property>
    </class>
</hibernate-mapping>
