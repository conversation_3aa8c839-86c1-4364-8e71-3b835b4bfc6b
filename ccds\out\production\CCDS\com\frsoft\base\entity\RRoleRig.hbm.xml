<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.RRoleRig" table="r_role_rig" schema="dbo" >
        <id name="rrrId" type="java.lang.Long">
            <column name="rrr_id" />
            <generator class="assigned" />
        </id>
        <many-to-one name="limRight" class="com.frsoft.base.entity.LimRight" fetch="select" not-null="false">
            <column name="rrr_rig_code" length="50" />
        </many-to-one>
        <many-to-one name="limRole" class="com.frsoft.base.entity.LimRole" fetch="select" not-null="false">
            <column name="rrr_rol_code" length="50" />
        </many-to-one>
    </class>
</hibernate-mapping>
