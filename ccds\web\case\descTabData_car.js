/*车贷详情*/
function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','C_DT'],
		['ID_NO',['C_CODE',1,'合同编号'],'STATE','BACK_DT'],
		['AMT','LAST_M','PAID','M_CAT'],
		['AGE','DAYS','PTP','CP'],
		['OVERDUE_M', 'COUNT', 'OVERDUE_NUM', 'OVERDUE_ONCE'],
		['P_COUNT','PRI','LEFT_PRI','LOAN_T'],
		['LOAN_RATE','MONTH_P',['BIZ',1,'经销商'],['PROD',1,'车型']],
		['MOB','PHO','W_PHO','AREA'],
		['HOM','COM','W_ADDR','REG'],
		['TIPS_DT','DEAD_L','LAST_CL','PR_COUNT'],
		['CL_AREA','EMP_NAME','TREMARK','ASS_TM'],
		[['ASS_HIS',7]]
	);
}

function getLayout2(){
	return  new Array(
		['H_POST','W_POST','REG_POST','EMAIL'],
		[['M_ADDR',5],'M_POST'],
		['FILE_NO','RMB','GB','MY'],
		['POS','PART','ACC',['F_BANK',1,'银行']],
		['CAL_PRICE','CAL_MAKE','PA_VIN','ENGINE_NO'],
		['CAL_LICE',['STOP_DT',1,'保单到期日'],'LOAN_DT','LOAN_END_DT'],
		['BIR','BACK_AMT',['START_DT',1,'申请日'],['BILL_DT',1,'还款日']],
		['OV_DT','OV_INT','OV_P','P_L'],
		['APP_NO','CL_T','SC_PC_NO','SC_NO'],
		[['C1_NAME',1,'配偶姓名'],['C1_ID_NO',1,'配偶证件号'],['C1_HM_PHO',1,'配偶家庭电话'],['C1_W_PHO',1,'配偶单位电话']],
		[['C1_MOB',1,'配偶手机'],['C1_COM',1,'配偶单位'],['C1_ADR',3,'配偶地址']],
		[['C6_NAME',1,'担保人姓名'],['C6_ID_NO',1,'担保人证件号'],['C6_HM_PHO',1,'担保人家庭电话'],['C6_W_PHO',1,'担保人单位电话']],
		[['C6_MOB',1,'担保人手机'],['C6_COM',1,'担保人单位'],['C6_ADR',3,'担保人地址']],
		['C2_NAME','C2_ID_NO','C2_HM_PHO','C2_W_PHO'],
		['C2_MOB','C2_COM',['C2_ADR',3]],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		['C4_NAME','C4_ID_NO','C4_HM_PHO','C4_W_PHO'],
		['C4_MOB','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		[['O_REC',3],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}