<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>

<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>新建联系电话</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
	<script type="text/javascript" src="js/case.js"></script>
	<script type="text/javascript" >
    	function check(){
			var errStr = "";
			if(isEmpty("name")){
				errStr+="- 未填写姓名！\n";
			 }
			 else if(checkLength("name",50)){
				errStr+="- 姓名不能超过50个字！\n";
			 }
			if(isEmpty("phone")){
				errStr+="- 未填写电话！\n";
			 }
			 if(isEmpty("phoType")){
				errStr+="- 未选择分类！\n"; 
			 }
			 if(isEmpty("state")){
			 	errStr+="- 未选择状态！ \n"
			 }
			 if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			 else{
				waitSubmit("save","保存中...");
				waitSubmit("doCancel");
				return $("create").submit();
			 }
		}
		
		window.onload=function(){
			loadPhoneType("phoType");
		}
  </script>
  </head>
 
  <body>

  <div class="inputDiv">
  	<form id="create" action="phoneListAction.do" method="post">
  		<input type="hidden" name="op" value="savePhone" />
  		  <input type="hidden" name="casId"  value="${casId}"/>
        <table class="dashTab inputForm" cellpadding="0" cellspacing="0">
            <tbody>
                <tr>
                    <th class="required">姓名：<span class="red">*</span></th>
                    <td><input class="inputSize2" type="text" id="name" name="name" onBlur="autoShort(this,50)"/></td>
                    <th>关系：</th>
                    <td><input class="inputSize2" type="text" id="rel" name="rel" onBlur="autoShort(this,50)"></td>
                </tr>
                <tr>
                    <th class="required">电话：<span class="red">*</span></th>
                    <td colspan="3"><input class="inputSize2L" type="text" id="phone" name="phone" onBlur="autoShort(this,100)"/></td>
                </tr>
                <tr>
                  	<th class="required">分类：<span class="red">*</span></th>
                    <td>
                         <select id="phoType" name="category" class="inputSize2"></select>	
                    </td>
                    <th class="required">状态：<span class="red">*</span></th>
                    <td>
                         <select id="state" name="state" class="inputSize2">
                          <option value="">请选择</option>
                          <option value="1">有效</option>
                          <option value="2">无效</option>
                          <option value="0">未知</option>
                         </select>	
                    </td>
                 </tr>   
                <tr class="noBorderBot">
                    <th>备注：</th>
                    <td colspan="3"><textarea class="inputSize2L" rows="8" id="remark" name="remark" onBlur="autoShort(this,4000)"></textarea></td>
                </tr>		
                <tr class="submitTr">
                    <td colspan="4">
                    <input id="save" class="butSize1" type="button" value="保存" onClick="check()" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                    </td>
                </tr>
            </tbody>
        </table>
	</form>
    </div>
  </body>
</html>
