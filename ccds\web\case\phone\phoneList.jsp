 <%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>电话列表</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
	<style type="text/css">
    .newPhone a:link,.newPhone a:visited{
		color:#FF9900;
		font-weight:bold;
	}
	.newPhone a:hover,.newPhone a:active{
		color:#FF9900;
		font-weight:bold;
	}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="js/cti.js"></script>
    <script type="text/javascript" src="js/md5.js"></script>
    <c:choose>
	    <c:when test="${HAS_BF_MOD}">
	    	<script type="text/javascript" src="js/bfcti-connect.js"></script>
	    </c:when>
	    <c:otherwise>
	    	<script type="text/javascript" src="js/cti-connect${CUS_VER_ID}.js"></script>
	    </c:otherwise>
    </c:choose>
    <script type="text/javascript">
     if('${msg}'!=null&&'${msg}'!=""){
	    alert('${msg}');
		parent.history.go(0);
	 }
    
	function toSendMsg(phoneNum,name,rel,conType){
		if(phoneNum!=""){
			parent.casePopDiv('SMS',[phoneNum,name,rel,conType,"${casId}"]);
		}
		else{
			alert("接收号码为空！");
			return;
		}
	}
		
	function syncPhone(){
		if(confirm("是否同步共债案件下的电话？\n（如果共债案件的电话在当前案件中存在则同步\n其状态，不存在则将该电话添加到当前案件中）")){
			if("${casId}"!=""){
				var url = "phoneListAction.do";
				var pars = "op=syncPhoneState&caseId=${casId}&ran=" + Math.random();/*ran为随机数*/
				new Ajax.Request(url, {
					method: 'post',
					parameters: pars,
					onSuccess: function(transport) {
						 var rs = transport.responseText;
						 if(rs=="1"){
							alert("同步成功！");
							loadList();
						 }
						 else{
							alert("无需同步！");
						 }
					},
					onFailure: function(response){
						if (response.status == 404)
							alert("您访问的地址不存在！");
						else
							alert("Error: status code is " + response.status);
					}
				});
			}
		}
	}
	
     function batchUpdate(state,obj){
		var priKey = $N("priKey");
		//var state = $('updState').value;
		var priKeys='';
		for(var i=0;i<priKey.length;i++){ 
			if(priKey[i].checked==true){ 
				priKeys +=priKey[i].value+',';
			}
	    } 
	  /* if(isEmpty("updState")){
	   		alert("未选择状态!");
			return false;
	   }*/
	   if(checkBoxIsEmpty("priKey")){
	   		var url = "phoneListAction.do";
			var pars = "op=batchUpdate&casId=${casId}&updState="+state+"&ids="+priKeys;
			new Ajax.Request(url, {
				method: 'post',
				parameters: pars,
				onSuccess: function(transport) {
					 var rs = transport.responseText;
					 if(rs=="1"){
						alert("更新成功！");
						loadList();
					 }
					 else{
						alert("更新失败！");
					 }
					 obj.blur();
				},
				onFailure: function(response){
					if (response.status == 404)
						alert("您访问的地址不存在！");
					else
						alert("Error: status code is " + response.status);
				}
			});
		}
		/*else{
			$("updState").value="";
		}*/	
     }
     /*
    function check(count,arg){
       	var errStr = "";
       	var sts = $('staSel'+count);
		if(isEmpty(sts)){
			errStr+="- 未选择电话状态！\n";
		 }
		if(errStr!=""){
			errStr+="\n请返回选择...";
			alert(errStr);
			return false;
		 }else{	   
			var state = sts.value;
			var url = "phoneListAction.do";
			var pars = "op=updateState&phlId="+arg+"&state="+state+"&count="+count+"&operational=${operational}&view=${view}&ran=" + Math.random();
			new Ajax.Request(url, {
				method: 'post',
				parameters: pars,
				onSuccess: function(transport) {
					 var str = transport.responseText;
					 var str1 = str.split(',');
					 var state = str1[0];
					 var count = str1[1];
					 switch(state){
						  case '0':
								$('staSpa'+count).hide();
								$('staImg'+count).hide();
								$('staTxt'+count).show();
								$('staTxt'+count).innerHTML='未知';
								break; 
						  case '1':
								$('staSpa'+count).hide();
								$('staImg'+count).hide();
								$('staTxt'+count).show();
								$('staTxt'+count).innerHTML='有效';
								break; 
						  case '2':
								$('staSpa'+count).hide();
								$('staImg'+count).hide();
								$('staTxt'+count).show();
								$('staTxt'+count).innerHTML='无效';
								break; 
					}
				},
				onFailure: function(response){
					if (response.status == 404)
						alert("您访问的地址不存在！");
					else
						alert("Error: status code is " + response.status);
				}
			});
		 }
    }
	
    function showImg(count){
    	var selEl = $('staImg'+count);
   		if(selEl.style.display=='none'){
      		selEl.show();
       }
    }
    function hideImg(count){
     	var selEl = $('staImg'+count);
   		if(selEl.style.display==''){
      		selEl.hide();
      	}
   	}
   	function change(count){
        var selEl = $('staSpa'+count);
        var selImg = $('staImg'+count);
        selEl.show();
        selImg.hide();
   	}
	*/
   	function creTest(){
		var phoneCat = parent.$('contactCat');
		if(parent.showPRInputDiv("","","","")){
			for(var i=0;i<phoneCat.options.length;i++){
				if(phoneCat.options[i].innerHTML=='测试电话'){
					clearInput(parent,phoneCat);
					phoneCat.value = phoneCat.options[i].value;
					//phoneCat.disabled=true;
					break;
				}
			}
		}
   	}
	/*保存备注
	function showRemark(id){
		$("re"+id).hide();
		$("inp"+id).show();
	}
	
	function saveRemark(id){
		waitSubmit("but"+id);
		var url = "phoneListAction.do";
		var pars = "op=updateRemark&phlId="+id+"&remark="+encodeURIComponent(($("txa"+id).value).substring(0,4000))+"&ran=" + Math.random();
		new Ajax.Request(url, {
			method: 'post',
			parameters: pars,
			onSuccess: function(transport) {
				$("inp"+id).hide();
				$("re"+id).innerHTML = $("txa"+id).value;
				$("re"+id).show();
				restoreSubmit("but"+id);
			},
			onFailure: function(response){
				if (response.status == 404)
					alert("您访问的地址不存在！");
				else
					alert("Error: status code is " + response.status);
			}
		});
	}
	*/
	function initPage(){
		//可操作案件
		if('${operational}'!='0'){
			$("newPhone").show();
			$("batNewPR").show();
			if($("toolsBarTop")!=null){
				$("toolsBarTop").show();
			}
			//displayLimAllow("hu028","newTestPho");
		}
	}
	//列表筛选按钮链接
	function filterList(state){
		$("state").value=state;
		setCurItemStyle(state,["1","0","2",""]);
		loadList();
	}
	function dataMapper(obj){
		var datas,className,dblFunc,dataId;
		dataId = obj.phlId;
        var state = "";
		var phoneColor = "";
        switch(obj.phlState){
               case 0: 
                        state = "未知";break;
               case 1:
                        state = "有效";break;
               case 2:
                        state = "无效";break;
        }
		if(obj.phlIsnew==1){
			phoneColor = "newPhone";
		}
        var phoneNum ="";
		if('${operational}'=='0' ){
            phoneNum = "<span class='"+phoneColor+"'>"+obj.phlNum+"&nbsp;</span>";
        }
        else{
            phoneNum = "<span class='"+phoneColor+"'>"
			+("${HAS_CTI}"=="1"||typeof(HAS_BF_MOD_FUNC)=="function"?"<image class='hand' alt='拨号' src='images/content/dial.gif' onclick=\"toDial('"+encodeString(obj.phlNum)+"')\"/>&nbsp;":"")
			+("${HAS_SMS}"=="1"&&"${SMS_NEED_AUD}"!="1"&&("${CUS_VER_ID}"!="134")?"<image class='hand' alt='发短信' src='images/content/email_go.png' onclick=\"toSendMsg('"+encodeString(obj.phlNum)+"','"+encodeString(obj.phlName)+"','"+encodeString(obj.phlRel)+"','"+encodeString(obj.phlCat)+"')\"/>&nbsp;":"")
			+"<a href=\"javascript:void(0)\" onClick=\"parent.showPRInputDiv('"+encodeString(obj.phlNum)+"','"+encodeString(obj.phlName)+"','"+obj.phlCat+"','"+encodeString(obj.phlRel)+"');return false;\">"+obj.phlNum+"</a>&nbsp;</span>";
        }
        var re = "";
        if(obj.phlRemark != undefined){
        	re= obj.phlRemark;
        }
        var remark ="<div style='width:100%; cursor:pointer;' onClick=\"parent.casePopDiv(30,['"+obj.phlId+"','"+re+"']);\">"+re+"</div>";
       	//var remark = "<div id='re"+obj.phlId+"' style='width:90%;cursor:pointer;' ondblclick=\"showRemark('"+obj.phlId+"')\">"+re+"&nbsp;</div><span id='inp"+obj.phlId+"' style='display:none'><textarea id='txa"+obj.phlId+"' class='inputBoxAlign' style='width:80%' rows=1>"+re+"</textarea><button id='but"+obj.phlId+"' class='butSize3 inputBoxAlign' onclick=\"saveRemark('"+obj.phlId+"')\">保存</button></span>";
		var funcCol = "<img onClick=\"parent.casePopDiv(6,['${casId}','"+encodeString(obj.phlNum)+"'])\" class=\"hand\" src=\"images/content/detail.gif\" alt=\"历史电催记录\"/>&nbsp;&nbsp;<img onClick=\"parent.casePopDiv(33,'"+obj.phlId+"')\" class=\"hand\" src=\"images/content/edit.gif\" alt=\"编辑\"/>&nbsp;&nbsp;<img onClick=\"parent.caseDelDiv(1,'"+obj.phlId+"','1')\" class='hand' src='images/content/del.gif' alt='删除'/>";//<img onClick=\"parent.casePopDiv('PLQ','"+encodeString(obj.phlNum)+"')\" class=\"hand\" src=\"images/content/query.gif\" alt=\"归属地查询（需连接网络）\"/>&nbsp;&nbsp;
		if(typeof(HAS_BF_MOD_FUNC)=="function"){
			funcCol = "<img onClick=\"parent.casePopDiv('GET_REC_BY_PHONE','"+encodeString(obj.phlNum)+"')\" class=\"hand\" src=\"images/content/view_rel.gif\" alt=\"录音记录\"/>&nbsp;&nbsp;"+funcCol;
		}
		datas = [state, phoneNum, obj.phlName, obj.phlRel, obj.phlCat, remark, funcCol ];
		if('${operational}'=='0'){
			datas.pop();
		}
		return [datas,className,dblFunc,dataId];
	}
	function loadList(sortCol,isDe,pageSize,curP){
		var url = "phoneListAction.do";
		var pars = [];
		pars.op = "listRePhone";
		pars.casId="${casId}";
		pars.state = $('state').value;
		var loadFunc = "loadList";
		var cols=[
			{name:"状态"},
			{name:"电话",align:"left"},
			{name:"姓名"},
			{name:"关系"},
			{name:"类别"},
			{name:"备注",align:"left",isSort:false},
			{name:"操作",isSort:false}
		];
		if('${operational}'=='0'){
			cols.pop();
		}
		gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
		gridEl.loadData(dataMapper);
		//${"phlState"}.value="";
	}
   	var gridEl = new MGrid("phoneListTab","dataList");
    if('${operational}'!='0'){
        gridEl.config.hasCheckBox = true;
    }
	createProgressBar();
   	window.onload=function(){
		initPage();
		loadList();
   	}
    </script>
</head> 
  
  <body>
  	<div class="divWithScroll2 innerIfm">
     	<table class="normal ifmTopTab" cellpadding="0" cellspacing="0">
            <tr>
            	<!--<th id="toolsBarTop" style="font-weight:normal; vertical-align:top; padding:4px 0 0 0; width:80px; display:none;">
                    <select  id="updState" class="inputBoxAlign" onChange="batchUpdate()">
                        <option value="">标记为...</option>
                        <option value="1">有效</option>
                        <option value="2">无效</option>
                        <option value="0">未知</option>
                    </select>
                </th>-->
                <th style="font-weight:normal">
                <div id="topChoose" class="listTopBox">
                    <!--<a href="javascript:void(0)" onClick="filterList('1')">&nbsp;有效电话&nbsp;</a>&nbsp;
                    <a href="javascript:void(0)" onClick="filterList('0')">&nbsp;未知电话&nbsp;</a>&nbsp;
                    <a href="javascript:void(0)" onClick="filterList('2')">&nbsp;无效电话&nbsp;</a>&nbsp;-->
                    <a href="javascript:void(0)" onClick="batchUpdate('1',this)">&nbsp;标记为有效&nbsp;</a>&nbsp;
                    <a href="javascript:void(0)" onClick="batchUpdate('0',this)">&nbsp;标记为未知&nbsp;</a>&nbsp;
                    <a href="javascript:void(0)" onClick="batchUpdate('2',this)">&nbsp;标记为无效&nbsp;</a>&nbsp;
                    <a href="javascript:void(0)" onClick="filterList('')">&nbsp;显示全部电话&nbsp;</a>&nbsp;
                </div></th>
                <td style="width:330px; vertical-align:top;">
                <a id="newPhone" style="display:none" href="javascript:void(0)" onClick="parent.casePopDiv(4,'${casId}');return false;" class="newBlueButton">新建联系电话</a>&nbsp;
                <a id="batNewPR" style="display:none" href="javascript:void(0)" onClick="parent.batNewHur();return false;" class="newBlueButton">批量电催</a>&nbsp;
                <a id="sycPhoneBtn" href="javascript:void(0)" onClick="syncPhone();return false;" class="newBlueButton">同步共债电话</a>
                </td>
			</tr>
        </table>
        <div id="dataList" class="dataList"></div>
        <input type='hidden' name='casId' id='casId' value='${casId}'/>
        <input type="hidden" id="state" value="df"/>
        </div>
        
  </body>
</html>