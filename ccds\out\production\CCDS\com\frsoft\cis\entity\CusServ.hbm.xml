<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.CusServ" table="cus_serv" schema="dbo" >
        <id name="serCode" type="java.lang.Long">
            <column name="ser_code"/>
            <generator class="identity"/>
        </id>
        <many-to-one name="cusCorCus" class="com.frsoft.cis.entity.CusCorCus" fetch="select"  not-null="false">
            <column name="ser_cor_code" length="50" />
        </many-to-one>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select"  not-null="false">
            <column name="ser_user_code" length="50" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="ser_se_no" />
        </many-to-one>
        <property name="serTitle" type="java.lang.String">
            <column name="ser_title" length="300" />
        </property>
        <property name="serCusLink" type="java.lang.String">
            <column name="ser_cus_link" length="50" />
        </property>
        <property name="serMethod" type="java.lang.String">
            <column name="ser_method" length="100" />
        </property>
        <property name="serContent" type="java.lang.String">
            <column name="ser_content" length="1073741823" />
        </property>
        <property name="serExeDate" type="java.util.Date">
            <column name="ser_exe_date" length="23" />
        </property>
        <property name="serCosTime" type="java.lang.String">
            <column name="ser_cos_time" length="50" />
        </property>
        <property name="serState" type="java.lang.String">
            <column name="ser_state" length="10" />
        </property>
        <property name="serFeedback" type="java.lang.String">
            <column name="ser_feedback" length="1073741823" />
        </property>
        <property name="serRemark" type="java.lang.String">
            <column name="ser_remark" length="1073741823" />
        </property>
        <property name="serInsDate" type="java.util.Date">
            <column name="ser_ins_date" length="23" />
        </property>
        <property name="serInpUser" type="java.lang.String">
            <column name="ser_inp_user" length="50" />
        </property>
        <property name="serUpdUser" type="java.lang.String">
            <column name="ser_upd_user" length="50" />
        </property>
       <property name="serUpdDate" type="java.util.Date">
            <column name="ser_upd_date" length="23" />
        </property>
        <property name="serIsDel" type="java.lang.String">
            <column name="ser_isdel" length="1" />
        </property>
        <set name="attachments" inverse="true" order-by="att_date desc"  cascade="all" where="att_type='cusServ'">
            <key>
                <column name="att_fk_id"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
    </class>
</hibernate-mapping>
