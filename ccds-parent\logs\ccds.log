2025-06-24 13:33:16 [main] INFO  com.frsoft.ccds.CCDSApplication - Starting CCDSApplication using Java 1.8.0_441 on itxinfei with PID 42540 (D:\cuishou\ccds-parent\ccds-main\target\classes started by itxinfei in D:\cuishou\ccds-parent)
2025-06-24 13:33:16 [main] DEBUG com.frsoft.ccds.CCDSApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-24 13:33:16 [main] INFO  com.frsoft.ccds.CCDSApplication - The following 1 profile is active: "dev"
2025-06-24 13:33:16 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.frsoft.ccds.CCDSApplication]; nested exception is org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'globalExceptionHandler' for bean class [com.frsoft.ccds.framework.web.exception.GlobalExceptionHandler] conflicts with existing, non-compatible bean definition of same name and class [com.frsoft.ccds.config.GlobalExceptionHandler]
2025-06-24 13:33:16 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.frsoft.ccds.CCDSApplication]; nested exception is org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'globalExceptionHandler' for bean class [com.frsoft.ccds.framework.web.exception.GlobalExceptionHandler] conflicts with existing, non-compatible bean definition of same name and class [com.frsoft.ccds.config.GlobalExceptionHandler]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:188)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:331)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:756)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:572)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.frsoft.ccds.CCDSApplication.main(CCDSApplication.java:17)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'globalExceptionHandler' for bean class [com.frsoft.ccds.framework.web.exception.GlobalExceptionHandler] conflicts with existing, non-compatible bean definition of same name and class [com.frsoft.ccds.config.GlobalExceptionHandler]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:349)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:287)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:295)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:249)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:206)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:174)
	... 13 common frames omitted
