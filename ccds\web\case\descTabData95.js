/*标准版详情*/
function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE_WITH_REMARK','C_DT'],
		['ID_NO','C_CODE','STATE','BACK_DT'],
		['AMT','RMB','GB','MY'],
		['PAID_LEFT','AGE','DAYS','M_CAT'],
		[['LAST_M',3],'AREA','CL_COUNT'],
		['PRI','LEFT_PRI','LOAN_DT','OVERDUE_NUM'],
		['LOAN_RATE','MONTH_P','OVERDUE_M','OVD_PRINC'],
		['OV_INT','PENALTY','DAMAGE','OV_P'],
		['MOB','PHO','W_PHO','HOM'],
		['COM','W_ADDR','REG','<PERSON>_<PERSON>DR'],
		['PTP','CP','COUNT','MIN_P'],
		[['TIPS_DT',3],'DEAD_L','PR_COUNT'],
		['CL_AREA','EMP_NAME','LAST_CL','LAST_VIS'],
		['REM1','REM2','REM3','REM4'],
		['REM5','REM6',['RECYCLE_DATE',3]],
		[['TREMARK',7]]
	);
}
function getLayout2(){
	return  new Array(
		['ASS_TM',['ASS_HIS',5]],
		['H_POST','W_POST','M_POST','REG_POST'],
		['ACC','F_BANK','HOST','ALT_HOLD'],
		['NOOUT','BILL_DT','CYCLE','P_CRLINE'],
		['START_DT','STOP_DT','LAST_P_DT','LAST_P'],
		['LAST_C_DT','LAST_R_DT','OV_DT','OVERDUE_ONCE'],
		['G_AMT','OVER_LIMIT','BACK_AMT','P_L'],
		['MNG_COST','C_L','LOAN_T','DELAY_LV'],
		['ACC_NAME','P_COUNT','CL_T','LOAN_END_DT'],
		['BIR','BIR_AGE','QQ','EMAIL'],
		['POS','PART','PROD','BIZ'],
		['APP_NO','FILE_NO','SC_PC_NO','SC_NO'],
		['C1_NAME','C1_ID_NO','C1_HM_PHO','C1_W_PHO'],
		['C1_MOB','C1_COM',['C1_ADR',3]],
		['C2_NAME','C2_ID_NO','C2_HM_PHO','C2_W_PHO'],
		['C2_MOB','C2_COM',['C2_ADR',3]],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		['C4_NAME','C4_ID_NO','C4_HM_PHO','C4_W_PHO'],
		['C4_MOB','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		['C6_NAME','C6_ID_NO','C6_HM_PHO','C6_W_PHO'],
		['C6_MOB','C6_COM',['C6_ADR',3]],
		[['O_REC',7]],
		[['ATT',7]]
	);
}


function batCardInfMapper(obj){
	var datas,className,dblFunc,dataId;
	var dblFunc = "descPop('caseAction.do?op=caseDesc&caseId="+obj.casId+"&view="+$('view').value+"')";
	var casCode = "<a href=\"javascript:void(0)\" onclick=\""+dblFunc+";return false;\">"+obj.casCode+"</a>";
	addSumOfBat(obj.casM,obj.casPtpM,obj.casCpM,obj.casPaidM, obj.casState);
	className = getCaseColor(obj);
	datas = [casCode, obj.casCaCd,obj.casAccNum,obj.casM,obj.casMCat,obj.casPtpM,obj.casCpM,obj.casPaidM,obj.casExcLim,obj.casPrTime,obj.casPrCount?obj.casPrCount:0];
	return [datas,className,dblFunc,dataId];
}
function loadBatCardList(sortCol,isDe,pageSize,curP){
	var url = "caseAction.do";
	var pars = [];
	pars.op = "listBatOthCard";
	pars.caseId=$("caseId").value;
	pars.ccId=$("ccId").value;
    pars.cbatId=$("cbatId").value;
    pars.idno=$("idno").value;
	var loadFunc = "loadBatCardList";
	var cols=[
		{name:"个案序列号",width:'10%'},
		{name:"卡号",width:'10%'},
		{name:"账号",width:'10%'},
		{name:"委案金额",renderer:"money",align:"right",width:'12%'},
		{name:"币种",width:'6%'},
		{name:"PTP",renderer:"money",align:"right",width:'12%'},
		{name:"CP",renderer:"money",align:"right",width:'12%'},
		{name:"已还款",renderer:"money",align:"right",width:'12%'},
		{name:"逾期账龄",width:'6%'},
		{name:"上次通电",renderer:"time",width:'6%'},
		{name:"通电次数",width:'4%'}
	];
	cardGridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
	cardGridEl.loadData(batCardInfMapper);
}
var cardGridEl = new MGrid("batCardListTab","cardInfList");
cardGridEl.config.sortable=false;
cardGridEl.config.isResize=false;
cardGridEl.config.isShort=false;