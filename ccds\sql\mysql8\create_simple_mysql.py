#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建简化的MySQL数据库结构
只包含表结构，移除存储过程和复杂语法
"""

import re

def create_simple_mysql():
    """创建简化的MySQL数据库结构"""
    
    sql_content = """-- MySQL 8.0 版本的CCDS数据库结构
-- 简化版本，只包含表结构

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ccds`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 系统用户表
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `user_code` varchar(50) NOT NULL COMMENT '用户编码',
  `login_name` varchar(50) NOT NULL COMMENT '登录名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `emp_name` varchar(100) DEFAULT NULL COMMENT '员工姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `enabled` char(1) DEFAULT '1' COMMENT '状态(0停用 1正常)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`user_code`),
  UNIQUE KEY `uk_login_name` (`login_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- 系统角色表
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_key` varchar(50) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) DEFAULT '1' COMMENT '角色状态(0停用 1正常)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统角色表';

-- 系统权限表
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission` (
  `permission_id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_name` varchar(50) NOT NULL COMMENT '权限名称',
  `permission_key` varchar(100) NOT NULL COMMENT '权限标识',
  `permission_type` char(1) DEFAULT 'M' COMMENT '权限类型(M目录 C菜单 F按钮)',
  `parent_id` bigint DEFAULT 0 COMMENT '父权限ID',
  `order_num` int DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) DEFAULT NULL COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `is_frame` int DEFAULT 1 COMMENT '是否为外链(0是 1否)',
  `is_cache` int DEFAULT 0 COMMENT '是否缓存(0缓存 1不缓存)',
  `menu_type` char(1) DEFAULT NULL COMMENT '菜单类型(M目录 C菜单 F按钮)',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态(0显示 1隐藏)',
  `status` char(1) DEFAULT '0' COMMENT '权限状态(0正常 1停用)',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统权限表';

-- 用户角色关联表
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `user_code` varchar(50) NOT NULL COMMENT '用户编码',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_code`, `role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`role_id`, `permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 系统组织机构表
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `parent_id` bigint DEFAULT 0 COMMENT '父部门ID',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) DEFAULT '' COMMENT '部门名称',
  `order_num` int DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `status` char(1) DEFAULT '0' COMMENT '部门状态(0正常 1停用)',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0代表存在 2代表删除)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 员工表
DROP TABLE IF EXISTS `sal_emp`;
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT COMMENT '员工编号',
  `se_code` varchar(50) DEFAULT NULL COMMENT '员工代码',
  `se_name` varchar(100) DEFAULT NULL COMMENT '员工姓名',
  `se_ide_code` varchar(50) DEFAULT NULL COMMENT '身份证号',
  `se_sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `se_phone` varchar(50) DEFAULT NULL COMMENT '手机号',
  `se_email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `se_address` varchar(500) DEFAULT NULL COMMENT '地址',
  `se_user_code` varchar(50) DEFAULT NULL COMMENT '关联用户编码',
  `se_isenabled` char(1) DEFAULT '1' COMMENT '是否启用',
  `se_inser_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `se_inser_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `se_alt_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `se_alt_user` varchar(50) DEFAULT NULL COMMENT '修改人',
  `se_remark` text COMMENT '备注',
  PRIMARY KEY (`se_no`),
  KEY `idx_se_name` (`se_name`),
  KEY `idx_se_user_code` (`se_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 客户表
DROP TABLE IF EXISTS `cus_cor_cus`;
CREATE TABLE `cus_cor_cus` (
  `cor_code` bigint NOT NULL AUTO_INCREMENT COMMENT '客户编码',
  `cor_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `cor_phone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `cor_address` text COMMENT '地址',
  `cor_user_code` varchar(50) DEFAULT NULL COMMENT '负责人编码',
  `cor_creat_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `cor_upd_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cor_ins_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `cor_upd_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `cor_isdelete` char(1) DEFAULT '0' COMMENT '是否删除',
  `cor_remark` text COMMENT '备注',
  PRIMARY KEY (`cor_code`),
  KEY `idx_cor_user_code` (`cor_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 催收案件表
DROP TABLE IF EXISTS `bank_case`;
CREATE TABLE `bank_case` (
  `cas_id` bigint NOT NULL AUTO_INCREMENT COMMENT '案件ID',
  `cas_code` varchar(100) DEFAULT NULL COMMENT '案件编号',
  `cas_name` varchar(50) DEFAULT NULL COMMENT '债务人姓名',
  `cas_phone` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `cas_m` decimal(18,2) DEFAULT NULL COMMENT '欠款金额',
  `cas_paid_m` decimal(18,2) DEFAULT 0.00 COMMENT '已还金额',
  `cas_state` int DEFAULT 1 COMMENT '案件状态',
  `cas_se_no` bigint DEFAULT NULL COMMENT '负责员工编号',
  `cas_ins_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `cas_ins_user` varchar(25) DEFAULT NULL COMMENT '创建人',
  `cas_alt_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cas_alt_user` varchar(25) DEFAULT NULL COMMENT '更新人',
  `cas_remark` text COMMENT '备注',
  PRIMARY KEY (`cas_id`),
  KEY `idx_cas_se_no` (`cas_se_no`),
  KEY `idx_cas_state` (`cas_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='催收案件表';

-- 催收记录表
DROP TABLE IF EXISTS `pho_red`;
CREATE TABLE `pho_red` (
  `pr_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `pr_cas_id` bigint DEFAULT NULL COMMENT '案件ID',
  `pr_contact` varchar(200) DEFAULT NULL COMMENT '联系人',
  `pr_content` text COMMENT '催收内容',
  `pr_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '催收时间',
  `pr_se_no` bigint DEFAULT NULL COMMENT '催收员工编号',
  `pr_con_type` varchar(50) DEFAULT NULL COMMENT '联系方式',
  PRIMARY KEY (`pr_id`),
  KEY `idx_pr_cas_id` (`pr_cas_id`),
  KEY `idx_pr_se_no` (`pr_se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='催收记录表';

-- 还款记录表
DROP TABLE IF EXISTS `case_paid`;
CREATE TABLE `case_paid` (
  `pa_id` bigint NOT NULL AUTO_INCREMENT COMMENT '还款ID',
  `pa_cas_id` bigint DEFAULT NULL COMMENT '案件ID',
  `pa_paid_num` decimal(18,2) DEFAULT NULL COMMENT '还款金额',
  `pa_paid_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '还款时间',
  `pa_se_no` bigint DEFAULT NULL COMMENT '经办员工编号',
  `pa_remark` text COMMENT '备注',
  PRIMARY KEY (`pa_id`),
  KEY `idx_pa_cas_id` (`pa_cas_id`),
  KEY `idx_pa_se_no` (`pa_se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='还款记录表';

-- 插入初始数据

-- 插入系统角色
INSERT INTO `sys_role` (`role_id`, `role_name`, `role_key`, `role_sort`, `status`, `create_by`, `remark`) VALUES
(1, '超级管理员', 'admin', 1, '1', 'admin', '超级管理员'),
(2, '催收主管', 'manager', 2, '1', 'admin', '催收主管'),
(3, '催收员', 'collector', 3, '1', 'admin', '催收员'),
(4, '普通用户', 'user', 4, '1', 'admin', '普通用户');

-- 插入系统权限
INSERT INTO `sys_permission` (`permission_id`, `permission_name`, `permission_key`, `permission_type`, `parent_id`, `order_num`, `path`, `perms`, `icon`, `create_by`, `remark`) VALUES
(1, '系统管理', 'system', 'M', 0, 1, '/system', NULL, 'system', 'admin', '系统管理目录'),
(2, '用户管理', 'system:user', 'C', 1, 1, '/system/user', 'system:user:list', 'user', 'admin', '用户管理菜单'),
(3, '角色管理', 'system:role', 'C', 1, 2, '/system/role', 'system:role:list', 'peoples', 'admin', '角色管理菜单'),
(4, '权限管理', 'system:permission', 'C', 1, 3, '/system/permission', 'system:permission:list', 'tree-table', 'admin', '权限管理菜单'),
(5, '催收管理', 'collection', 'M', 0, 2, '/collection', NULL, 'money', 'admin', '催收管理目录'),
(6, '案件管理', 'collection:case', 'C', 5, 1, '/collection/case', 'collection:case:list', 'documentation', 'admin', '案件管理菜单'),
(7, '催收记录', 'collection:record', 'C', 5, 2, '/collection/record', 'collection:record:list', 'edit', 'admin', '催收记录菜单'),
(8, '还款管理', 'collection:payment', 'C', 5, 3, '/collection/payment', 'collection:payment:list', 'money', 'admin', '还款管理菜单'),
(9, '客户管理', 'customer', 'M', 0, 3, '/customer', NULL, 'peoples', 'admin', '客户管理目录'),
(10, '客户信息', 'customer:info', 'C', 9, 1, '/customer/info', 'customer:info:list', 'user', 'admin', '客户信息菜单'),
(11, '用户新增', 'system:user:add', 'F', 2, 1, '', 'system:user:add', '#', 'admin', '用户新增按钮'),
(12, '用户修改', 'system:user:edit', 'F', 2, 2, '', 'system:user:edit', '#', 'admin', '用户修改按钮'),
(13, '用户删除', 'system:user:remove', 'F', 2, 3, '', 'system:user:remove', '#', 'admin', '用户删除按钮'),
(14, '用户重置密码', 'system:user:resetPwd', 'F', 2, 4, '', 'system:user:resetPwd', '#', 'admin', '用户重置密码按钮'),
(15, '案件新增', 'collection:case:add', 'F', 6, 1, '', 'collection:case:add', '#', 'admin', '案件新增按钮'),
(16, '案件修改', 'collection:case:edit', 'F', 6, 2, '', 'collection:case:edit', '#', 'admin', '案件修改按钮'),
(17, '案件删除', 'collection:case:remove', 'F', 6, 3, '', 'collection:case:remove', '#', 'admin', '案件删除按钮');

-- 插入角色权限关联
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) VALUES
-- 超级管理员拥有所有权限
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10), (1, 11), (1, 12), (1, 13), (1, 14), (1, 15), (1, 16), (1, 17),
-- 催收主管拥有催收相关权限
(2, 5), (2, 6), (2, 7), (2, 8), (2, 9), (2, 10), (2, 15), (2, 16),
-- 催收员拥有基本催收权限
(3, 5), (3, 6), (3, 7), (3, 8), (3, 9), (3, 10),
-- 普通用户只有查看权限
(4, 5), (4, 6), (4, 7), (4, 8), (4, 9), (4, 10);

-- 插入部门信息
INSERT INTO `sys_dept` (`dept_id`, `parent_id`, `ancestors`, `dept_name`, `order_num`, `leader`, `phone`, `email`, `status`, `create_by`) VALUES
(1, 0, '0', '枫软科技', 1, '张总', '13800138000', '<EMAIL>', '0', 'admin'),
(2, 1, '0,1', '催收部', 1, '李主管', '13800138001', '<EMAIL>', '0', 'admin'),
(3, 1, '0,1', '技术部', 2, '王经理', '13800138002', '<EMAIL>', '0', 'admin'),
(4, 1, '0,1', '财务部', 3, '赵会计', '13800138003', '<EMAIL>', '0', 'admin');

-- 插入系统用户
INSERT INTO `sys_user` (`user_code`, `login_name`, `password`, `emp_name`, `email`, `phone`, `enabled`, `create_by`, `remark`) VALUES
('admin', 'admin', '$2a$10$7JB720yubVSOfvVMe6/YqOsrtwt07CFElVPJSMVNYEnuqJDKHFOH6', '系统管理员', '<EMAIL>', '13800138000', '1', 'admin', '系统管理员'),
('manager', 'manager', '$2a$10$7JB720yubVSOfvVMe6/YqOsrtwt07CFElVPJSMVNYEnuqJDKHFOH6', '催收主管', '<EMAIL>', '13800138001', '1', 'admin', '催收主管'),
('collector', 'collector', '$2a$10$7JB720yubVSOfvVMe6/YqOsrtwt07CFElVPJSMVNYEnuqJDKHFOH6', '催收员', '<EMAIL>', '13800138002', '1', 'admin', '催收员');

-- 插入用户角色关联
INSERT INTO `sys_user_role` (`user_code`, `role_id`) VALUES
('admin', 1),
('manager', 2),
('collector', 3);

-- 插入员工信息
INSERT INTO `sal_emp` (`se_no`, `se_code`, `se_name`, `se_ide_code`, `se_sex`, `se_phone`, `se_email`, `se_user_code`, `se_isenabled`, `se_inser_user`, `se_remark`) VALUES
(1, 'EMP001', '系统管理员', '110101199001011234', '男', '13800138000', '<EMAIL>', 'admin', '1', 'admin', '系统管理员'),
(2, 'EMP002', '催收主管', '110101199002021234', '女', '13800138001', '<EMAIL>', 'manager', '1', 'admin', '催收主管'),
(3, 'EMP003', '催收员', '110101199003031234', '男', '13800138002', '<EMAIL>', 'collector', '1', 'admin', '催收员');

-- 插入示例客户
INSERT INTO `cus_cor_cus` (`cor_code`, `cor_name`, `cor_phone`, `cor_address`, `cor_user_code`, `cor_ins_user`, `cor_remark`) VALUES
(1, '张三', '***********', '北京市朝阳区', 'collector', 'admin', '示例客户1'),
(2, '李四', '***********', '上海市浦东新区', 'collector', 'admin', '示例客户2'),
(3, '王五', '***********', '广州市天河区', 'collector', 'admin', '示例客户3');

-- 插入示例催收案件
INSERT INTO `bank_case` (`cas_id`, `cas_code`, `cas_name`, `cas_phone`, `cas_m`, `cas_paid_m`, `cas_state`, `cas_se_no`, `cas_ins_user`, `cas_remark`) VALUES
(1, 'CASE001', '张三', '***********', 50000.00, 10000.00, 1, 3, 'admin', '信用卡逾期案件'),
(2, 'CASE002', '李四', '***********', 80000.00, 20000.00, 1, 3, 'admin', '贷款逾期案件'),
(3, 'CASE003', '王五', '***********', 30000.00, 5000.00, 1, 3, 'admin', '消费贷逾期案件');

-- 插入示例催收记录
INSERT INTO `pho_red` (`pr_id`, `pr_cas_id`, `pr_contact`, `pr_content`, `pr_se_no`, `pr_con_type`) VALUES
(1, 1, '张三', '电话联系，承诺本月底还款10000元', 3, '电话'),
(2, 2, '李四', '短信提醒，已读不回', 3, '短信'),
(3, 3, '王五', '上门拜访，协商分期还款方案', 3, '上门');

-- 插入示例还款记录
INSERT INTO `case_paid` (`pa_id`, `pa_cas_id`, `pa_paid_num`, `pa_se_no`, `pa_remark`) VALUES
(1, 1, 10000.00, 3, '首次还款'),
(2, 2, 20000.00, 3, '部分还款'),
(3, 3, 5000.00, 3, '最低还款');

-- 输出统计信息
SELECT '数据库初始化完成' as message;
SELECT COUNT(*) as user_count FROM sys_user;
SELECT COUNT(*) as emp_count FROM sal_emp;
SELECT COUNT(*) as role_count FROM sys_role;
SELECT COUNT(*) as right_count FROM sys_permission;
SELECT COUNT(*) as org_count FROM sys_dept;

SET FOREIGN_KEY_CHECKS = 1;
"""
    
    return sql_content

def main():
    """主函数"""
    sql_content = create_simple_mysql()
    
    output_file = '../ccds/sql/mysql8/mysql8_CCDS.sql'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    print(f"简化的MySQL数据库结构已生成: {output_file}")

if __name__ == "__main__":
    main()
