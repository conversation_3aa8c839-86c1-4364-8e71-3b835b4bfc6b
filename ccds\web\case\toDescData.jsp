<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>案件详细资料</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
		#bankData th{
			background:#ccc;
			border:solid #fff 1px;
			border-top:0;
			border-right:0;
		}
		#bankData td{
		}
    </style>
     <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
    <script language="javascript" type="text/javascript">
	createIfmLoad('ifmLoad');//进度条
   	window.onload=function(){
		loadEnd('ifmLoad');
   	}
  	</script>
  </head>
  
  <body>
  	<div class="divWithScroll2 innerIfm">
    <logic:notEmpty name="bankCase">
    <table id="bankData" class="dashTab descForm2" style="width:98%" cellpadding="0" cellspacing="0">
        <tbody>
            <!--<tr>
                <th>账号：</th>
                <td>${bankCase.casAccNum}&nbsp;</td>
                <th>卡类：</th>
                <td>${bankCase.casCardCat}&nbsp;</td>
                <th>逾期账龄：</th>
                <td>${bankCase.casExcLim}&nbsp;</td>
            </tr>
            <tr>
                <th>保证金：</th>
                <td>${bankCase.casGuaM}&nbsp;</td>
                <th>币种：</th>
                <td>${bankCase.casMCat}&nbsp;</td>
            </tr>
            <tr>
                <th>原催收记录：</th>
                <td colspan="5">${bankCase.casPreRec}&nbsp;</td>
            </tr>
            -->
            
            <tr>
                <th>家庭号码：</th>
                <td>${bankCase.casHomPho}&nbsp;</td>
                <th>单位号码：</th>
                <td>${bankCase.casWorkPho}&nbsp;</td>
                <th>手机：</th>
                <td>${bankCase.casMobPho}&nbsp;</td>
            </tr>
            <tr>
                <th>邮编：</th>
                <td>${bankCase.casPostCode}&nbsp;</td>
                <th>单位地址：</th>
                <td colspan="3">${bankCase.casWorkAdd}&nbsp;</td>
            </tr>
            <tr>
                <th>家庭地址：</th>
                <td>${bankCase.casHomAdd}&nbsp;</td>
                <th>对账单地址：</th>
                <td>${bankCase.casMailAdd}&nbsp;</td>
                <th>户籍地址：</th>
                <td>${bankCase.casRegAdd}&nbsp;</td>
            </tr>
            <tr>
                <th>联系人1姓名：</th>
                <td>${bankCase.casName1}&nbsp;</td>
                <th>联系人1关系：</th>
                <td>${bankCase.casRe1}&nbsp;</td>
                <th>联系人1证件号：</th>
                <td>${bankCase.casNum1}&nbsp;</td>
            </tr>
            <tr>
                <th>联系人1电话：</th>
                <td>${bankCase.casConPho1}&nbsp;</td>
                <th>联系人1手机：</th>
                <td>${bankCase.casConMob1}&nbsp;</td>
                <th>联系人1单位：</th>
                <td>${bankCase.casConCom1}&nbsp;</td>
            </tr>
            <tr>
                <th>联系人1地址：</th>
                <td colspan="5">${bankCase.casConAdd1}&nbsp;</td>
            </tr>
            <tr>
                <th>联系人2姓名：</th>
                <td>${bankCase.casName2}&nbsp;</td>
                <th>联系人2关系：</th>
                <td>${bankCase.casRe2}&nbsp;</td>
                <th>联系人2证件号：</th>
                <td>${bankCase.casNum2}&nbsp;</td>
            </tr>
            <tr>
                <th>联系人2电话：</th>
                <td>${bankCase.casConPho2}&nbsp;</td>
                <th>联系人2手机：</th>
                <td>${bankCase.casConMob2}&nbsp;</td>
                <th>联系人2单位：</th>
                <td>${bankCase.casConCom2}&nbsp;</td>
            </tr>
            <tr>
                <th>联系人2地址：</th>
                <td colspan="5">${bankCase.casConAdd2}&nbsp;</td>
            </tr>
        </tbody>
    </table>
    </logic:notEmpty>
      </div>
   
  </body>
</html>
