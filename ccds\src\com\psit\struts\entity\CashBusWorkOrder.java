package com.psit.struts.entity;

import java.io.Serializable;

public class CashBusWorkOrder implements Serializable {
    private long id;
    private String loanRefId;
    private String description;
    private String status;

    public CashBusWorkOrder() {
    }

    public CashBusWorkOrder(long id) {
        this.id = id;
    }

    public CashBusWorkOrder(long id, String loanRefId, String description, String status) {
        this.id = id;
        this.loanRefId = loanRefId;
        this.description = description;
        this.status = status;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getLoanRefId() {
        return loanRefId;
    }

    public void setLoanRefId(String loanRefId) {
        this.loanRefId = loanRefId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
