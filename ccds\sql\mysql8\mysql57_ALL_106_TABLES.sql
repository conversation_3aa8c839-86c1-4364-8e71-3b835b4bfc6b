-- MySQL 5.7 完整CCDS数据库 - 所有106个表
-- 从MSSQL完整转换，确保功能100%兼容

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `ccds`;

SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 所有106个表的完整定义
-- ========================================

DROP TABLE IF EXISTS `sal_emp`;
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT,
  `se_so_code` varchar(50) DEFAULT NULL,
  `se_name` varchar(100) DEFAULT NULL,
  `se_ide_code` varchar(50) DEFAULT NULL,
  `se_pos` varchar(50) DEFAULT NULL,
  `se_sex` varchar(50) DEFAULT NULL,
  `se_prob` varchar(50) DEFAULT NULL,
  `se_bir_place` varchar(50) DEFAULT NULL,
  `se_acc_place` varchar(100) DEFAULT NULL,
  `se_birth` varchar(50) DEFAULT NULL,
  `se_marry` varchar(10) DEFAULT NULL,
  `se_type` varchar(50) DEFAULT NULL,
  `se_job_lev` bigint DEFAULT NULL,
  `se_job_cate` varchar(50) DEFAULT NULL,
  `se_job_title` varchar(50) DEFAULT NULL,
  `se_start_day` datetime DEFAULT NULL,
  `se_year_pay` varchar(50) DEFAULT NULL,
  `se_cost_center` varchar(50) DEFAULT NULL,
  `se_email` varchar(50) DEFAULT NULL,
  `se_nation` varchar(50) DEFAULT NULL,
  `se_poli_status` varchar(50) DEFAULT NULL,
  `se_edu` varchar(50) DEFAULT NULL,
  `se_tel` varchar(50) DEFAULT NULL,
  `se_phone` varchar(50) DEFAULT NULL,
  `se_qq` varchar(50) DEFAULT NULL,
  `se_msn` varchar(50) DEFAULT NULL,
  `se_rec_source` varchar(100) DEFAULT NULL,
  `se_prov_fund` varchar(50) DEFAULT NULL,
  `se_job_date` datetime DEFAULT NULL,
  `se_hou_reg` varchar(50) DEFAULT NULL,
  `se_social_code` varchar(50) DEFAULT NULL,
  `se_rap` varchar(50) DEFAULT NULL,
  `se_address` varchar(500) DEFAULT NULL,
  `se_remark` longtext NULL,
  `se_bank_name` varchar(50) DEFAULT NULL,
  `se_bank_card` varchar(50) DEFAULT NULL,
  `se_weal_address` varchar(50) DEFAULT NULL,
  `se_weal_pos` varchar(50) DEFAULT NULL,
  `se_isovertime` varchar(50) DEFAULT NULL,
  `se_attendance` varchar(50) DEFAULT NULL,
  `se_card_num` varchar(50) DEFAULT NULL,
  `se_pic` longtext NULL,
  `se_isenabled` char(1) DEFAULT NULL,
  `se_inser_date` datetime DEFAULT NULL,
  `se_code` varchar(50) DEFAULT NULL,
  `se_log` longtext NULL,
  `se_alt_date` datetime DEFAULT NULL,
  `se_inser_user` varchar(50) DEFAULT NULL,
  `se_alt_user` varchar(50) DEFAULT NULL,
  `se_end_date` datetime DEFAULT NULL,
  `se_edc_bac` longtext NULL,
  `se_work_ex` longtext NULL,
  `se_user_code` varchar(50) DEFAULT NULL,
  `se_per_tel` varchar(50) DEFAULT NULL,
  `se_plan_sign_date` datetime DEFAULT NULL,
  `se_sign_date` datetime DEFAULT NULL,
  `se_credit_date` datetime DEFAULT NULL,
  `se_college` varchar(200) DEFAULT NULL,
  `se_transfer` text NULL,
  PRIMARY KEY (`se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


DROP TABLE IF EXISTS `cus_cor_cus`;
CREATE TABLE `cus_cor_cus` (
  `cor_code` bigint NOT NULL AUTO_INCREMENT,
  `cor_num` varchar(50) DEFAULT NULL,
  `cor_user_code` varchar(50) DEFAULT NULL,
  `cor_name` varchar(100) DEFAULT NULL,
  `cor_hot` varchar(50) DEFAULT NULL,
  `cor_mne` varchar(50) DEFAULT NULL,
  `cor_lic_code` varchar(50) DEFAULT NULL,
  `cor_org_code` varchar(50) DEFAULT NULL,
  `cor_star` varchar(50) DEFAULT NULL,
  `cor_cre_lev` varchar(50) DEFAULT NULL,
  `cor_cre_lim` varchar(50) DEFAULT NULL,
  `cor_ind_id` bigint DEFAULT NULL,
  `cor_per_size` varchar(50) DEFAULT NULL,
  `cor_acc_bank` varchar(100) DEFAULT NULL,
  `cor_bank_num` varchar(50) DEFAULT NULL,
  `cor_sou_id` bigint DEFAULT NULL,
  `cor_com_inf` longtext NULL,
  `cor_country` bigint DEFAULT NULL,
  `cor_province` bigint DEFAULT NULL,
  `cor_city` bigint DEFAULT NULL,
  `cor_phone` varchar(50) DEFAULT NULL,
  `cor_fex` varchar(50) DEFAULT NULL,
  `cor_net` varchar(500) DEFAULT NULL,
  `cor_zip_code` varchar(50) DEFAULT NULL,
  `cor_address` longtext NULL,
  `cor_remark` longtext NULL,
  `cor_creat_date` datetime DEFAULT NULL,
  `cor_upd_date` datetime DEFAULT NULL,
  `cor_issuc` char(1) DEFAULT NULL,
  `cor_last_date` datetime DEFAULT NULL,
  `cor_temp_tag` varchar(50) DEFAULT NULL,
  `cor_isdelete` char(1) DEFAULT NULL,
  `cor_spe_write` longtext NULL,
  `cor_upd_user` varchar(50) DEFAULT NULL,
  `cor_typ_id` bigint DEFAULT NULL,
  `cor_ins_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cor_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


DROP TABLE IF EXISTS `type_list`;
CREATE TABLE `type_list` (
  `typ_id` bigint NOT NULL AUTO_INCREMENT,
  `typ_name` varchar(50) DEFAULT NULL,
  `typ_desc` longtext NULL,
  `typ_type` varchar(50) DEFAULT NULL,
  `typ_isenabled` char(1) DEFAULT NULL,
  PRIMARY KEY (`typ_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


DROP TABLE IF EXISTS `bank_case`;
CREATE TABLE `bank_case` (
  `cas_id` bigint NOT NULL AUTO_INCREMENT,
  `cas_code` varchar(100) DEFAULT NULL,
  `cas_group` varchar(50) DEFAULT NULL,
  `cas_state` int DEFAULT NULL,
  `cas_typ_hid` bigint DEFAULT NULL,
  `cas_out_state` int DEFAULT NULL,
  `cas_cbat_id` bigint DEFAULT NULL,
  `cas_m` decimal(18,2) DEFAULT NULL NULL,
  `cas_ptp_m` decimal(18,2) DEFAULT NULL NULL,
  `cas_cp_m` decimal(18,2) DEFAULT NULL NULL,
  `cas_paid_m` decimal(18,2) DEFAULT NULL NULL,
  `cas_date` datetime DEFAULT NULL,
  `cas_typ_bid` bigint DEFAULT NULL,
  `cas_name` varchar(50) DEFAULT NULL,
  `cas_sex` varchar(1) DEFAULT NULL,
  `cas_ca_cd` varchar(50) DEFAULT NULL,
  `cas_num` varchar(50) DEFAULT NULL,
  `cas_post_code` varchar(50) DEFAULT NULL,
  `cas_se_no` bigint DEFAULT NULL,
  `cas_ins_user` varchar(25) DEFAULT NULL,
  `cas_ins_time` datetime DEFAULT NULL,
  `cas_alt_user` varchar(25) DEFAULT NULL,
  `cas_alt_time` datetime DEFAULT NULL,
  `cas_tremark` varchar(300) DEFAULT NULL,
  `cas_warn` varchar(300) DEFAULT NULL,
  `cas_acc_num` varchar(100) DEFAULT NULL,
  `cas_card_cat` varchar(200) DEFAULT NULL,
  `cas_principal` varchar(200) DEFAULT NULL,
  `cas_min_paid` varchar(200) DEFAULT NULL,
  `cas_cred_lim` varchar(200) DEFAULT NULL,
  `cas_delay_lv` varchar(200) DEFAULT NULL,
  `cas_gua_m` varchar(200) DEFAULT NULL,
  `cas_m_cat` varchar(200) DEFAULT NULL,
  `cas_pre_rec` longtext NULL,
  `cas_exc_lim` varchar(200) DEFAULT NULL,
  `cas_unit_name` varchar(200) DEFAULT NULL,
  `cas_m_p` double DEFAULT NULL NULL,
  `cas_name_1` varchar(50) DEFAULT NULL,
  `cas_name_2` varchar(50) DEFAULT NULL,
  `cas_name_3` varchar(50) DEFAULT NULL,
  `cas_num_1` varchar(200) DEFAULT NULL,
  `cas_num_2` varchar(200) DEFAULT NULL,
  `cas_num_3` varchar(200) DEFAULT NULL,
  `cas_re_1` varchar(200) DEFAULT NULL,
  `cas_re_2` varchar(200) DEFAULT NULL,
  `cas_re_3` varchar(200) DEFAULT NULL,
  `cas_con_com1` varchar(200) DEFAULT NULL,
  `cas_pr_time` datetime DEFAULT NULL,
  `cas_remark` longtext NULL,
  `cas_con_com2` varchar(200) DEFAULT NULL,
  `cas_con_com3` varchar(200) DEFAULT NULL,
  `cas_app_1` int DEFAULT NULL,
  `cas_app_2` int DEFAULT NULL,
  `cas_app_3` int DEFAULT NULL,
  `cas_app_4` int DEFAULT NULL,
  `cas_app_5` int DEFAULT NULL,
  `cas_app_6` int DEFAULT NULL,
  `cas_app_7` int DEFAULT NULL,
  `cas_app_8` int DEFAULT NULL,
  `cas_app_9` int DEFAULT NULL,
  `cas_app_10` int DEFAULT NULL,
  `cas_app_11` int DEFAULT NULL,
  `cas_app_12` int DEFAULT NULL,
  `cas_app_13` int DEFAULT NULL,
  `cas_app_14` int DEFAULT NULL,
  `cas_remark2` longtext NULL,
  `cas_remark3` longtext NULL,
  `cas_remark4` longtext NULL,
  `cas_ptp_c` int DEFAULT NULL,
  `cas_remark5` longtext NULL,
  `cas_card_bank` varchar(200) DEFAULT NULL,
  `cas_tip_time` datetime DEFAULT NULL,
  `cas_hom_pho` varchar(50) DEFAULT NULL,
  `cas_work_pho` varchar(50) DEFAULT NULL,
  `cas_mob_pho` varchar(50) DEFAULT NULL,
  `cas_hom_add` longtext NULL,
  `cas_work_add` longtext NULL,
  `cas_mail_add` longtext NULL,
  `cas_reg_add` longtext NULL,
  `cas_con_pho1` varchar(50) DEFAULT NULL,
  `cas_con_mob1` varchar(50) DEFAULT NULL,
  `cas_con_add1` longtext NULL,
  `cas_con_pho2` varchar(50) DEFAULT NULL,
  `cas_con_mob2` varchar(50) DEFAULT NULL,
  `cas_con_add2` longtext NULL,
  `cas_loan_type` varchar(200) DEFAULT NULL,
  `cas_coll_type` varchar(200) DEFAULT NULL,
  `cas_int` varchar(200) DEFAULT NULL,
  `cas_overdue_paid` varchar(200) DEFAULT NULL,
  `cas_cre_paid` varchar(200) DEFAULT NULL,
  `cas_paid_lim` varchar(200) DEFAULT NULL,
  `cas_paid_date` varchar(200) DEFAULT NULL,
  `cas_con_date` varchar(200) DEFAULT NULL,
  `cas_rai_date` varchar(200) DEFAULT NULL,
  `cas_stop_date` varchar(200) DEFAULT NULL,
  `cas_cre_date` varchar(200) DEFAULT NULL,
  `cas_remark6` longtext NULL,
  `cas_note` longtext NULL,
  `cas_con_pho3` varchar(50) DEFAULT NULL,
  `cas_con_mob3` varchar(50) DEFAULT NULL,
  `cas_con_add3` longtext NULL,
  `cas_con_pho4` varchar(50) DEFAULT NULL,
  `cas_con_mob4` varchar(50) DEFAULT NULL,
  `cas_con_add4` longtext NULL,
  `cas_name_4` varchar(50) DEFAULT NULL,
  `cas_num_4` varchar(200) DEFAULT NULL,
  `cas_re_4` varchar(200) DEFAULT NULL,
  `cas_con_com4` varchar(200) DEFAULT NULL,
  `cas_file_no` varchar(100) DEFAULT NULL,
  `cas_remark7` longtext NULL,
  `cas_remark8` longtext NULL,
  `cas_email` varchar(100) DEFAULT NULL,
  `cas_is_oth` int DEFAULT NULL,
  `cas_is_newpr` int DEFAULT NULL,
  `cas_is_newpaid` int DEFAULT NULL,
  `cas_is_paidover` int DEFAULT NULL,
  `cas_is_updint` int DEFAULT NULL,
  `cas_rmb` varchar(100) DEFAULT NULL,
  `cas_gb` varchar(100) DEFAULT NULL,
  `cas_my` varchar(100) DEFAULT NULL,
  `cas_pos` varchar(200) DEFAULT NULL,
  `cas_part` varchar(200) DEFAULT NULL,
  `cas_backdate_p` datetime DEFAULT NULL,
  `cas_backdate` datetime DEFAULT NULL,
  `cas_back_p` double DEFAULT NULL NULL,
  `cas_con_wpho1` varchar(50) DEFAULT NULL,
  `cas_con_wpho2` varchar(50) DEFAULT NULL,
  `cas_con_wpho3` varchar(50) DEFAULT NULL,
  `cas_con_wpho4` varchar(50) DEFAULT NULL,
  `cas_name_u` varchar(50) DEFAULT NULL,
  `cas_num_u` varchar(200) DEFAULT NULL,
  `cas_re_u` varchar(200) DEFAULT NULL,
  `cas_con_u_com` varchar(200) DEFAULT NULL,
  `cas_con_u_wpho` varchar(50) DEFAULT NULL,
  `cas_con_u_pho` varchar(50) DEFAULT NULL,
  `cas_con_u_mob` varchar(50) DEFAULT NULL,
  `cas_con_u_add` longtext NULL,
  `cas_back_m` decimal(18,2) DEFAULT NULL NULL,
  `cas_name_5` varchar(50) DEFAULT NULL,
  `cas_num_5` varchar(200) DEFAULT NULL,
  `cas_re_5` varchar(200) DEFAULT NULL,
  `cas_con_com_5` varchar(200) DEFAULT NULL,
  `cas_con_wpho_5` varchar(50) DEFAULT NULL,
  `cas_con_pho_5` varchar(50) DEFAULT NULL,
  `cas_con_mob_5` varchar(50) DEFAULT NULL,
  `cas_con_add_5` longtext NULL,
  `cas_loan_date` varchar(200) DEFAULT NULL,
  `cas_app_no` varchar(100) DEFAULT NULL,
  `cas_paid_count` varchar(100) DEFAULT NULL,
  `cas_so_pcno` varchar(100) DEFAULT NULL,
  `cas_so_no` varchar(100) DEFAULT NULL,
  `cas_overdue_date` varchar(200) DEFAULT NULL,
  `cas_pback_p` double DEFAULT NULL NULL,
  `cas_wpost_code` varchar(50) DEFAULT NULL,
  `cas_deadline` varchar(200) DEFAULT NULL,
  `cas_is_host` varchar(50) DEFAULT NULL,
  `cas_bill_date` varchar(200) DEFAULT NULL,
  `cas_last_paid` varchar(200) DEFAULT NULL,
  `cas_count` varchar(100) DEFAULT NULL,
  `cas_left_pri` varchar(100) DEFAULT NULL,
  `cas_assign_ids` longtext NULL,
  `cas_assign_names` longtext NULL,
  `cas_last_assign_time` datetime DEFAULT NULL,
  `cas_overdue_days` int DEFAULT NULL,
  `cas_overdue_days_str` varchar(200) DEFAULT NULL,
  `cas_bir` varchar(50) DEFAULT NULL,
  `cas_mpost_code` varchar(50) DEFAULT NULL,
  `cas_perm_crline` varchar(50) DEFAULT NULL,
  `cas_alt_hold` varchar(50) DEFAULT NULL,
  `cas_cycle` varchar(50) DEFAULT NULL,
  `cas_noout` varchar(50) DEFAULT NULL,
  `cas_field_type` varchar(50) DEFAULT NULL,
  `cas_cl_area_id` bigint DEFAULT NULL,
  `cas_pr_count` int DEFAULT NULL,
  `cas_overdue_m` varchar(200) DEFAULT NULL,
  `cas_overdue_num` varchar(200) DEFAULT NULL,
  `cas_overdue_once` int DEFAULT NULL,
  `cas_loan_rate` varchar(200) DEFAULT NULL,
  `cas_month_paid` varchar(200) DEFAULT NULL,
  `cas_last_vis` datetime DEFAULT NULL,
  `cas_fst_cl_paid_date` datetime DEFAULT NULL,
  `cas_last_cl_paid_date` datetime DEFAULT NULL,
  `cas_color` int DEFAULT NULL,
  `cas_cc_id` bigint DEFAULT NULL,
  `cas_is_newass` int DEFAULT NULL,
  `cas_reg_post_code` varchar(50) DEFAULT NULL,
  `cas_last_m` decimal(18,2) DEFAULT NULL NULL,
  `cas_last_int_date` datetime DEFAULT NULL,
  `cas_loan_end_date` varchar(200) DEFAULT NULL,
  `cas_over_limit` varchar(200) DEFAULT NULL,
  `cas_num_type` varchar(50) DEFAULT NULL,
  `cas_last_end_date` varchar(100) DEFAULT NULL,
  `cas_assign_times` longtext NULL,
  `cas_cl_count` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`cas_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


DROP TABLE IF EXISTS `car_loan`;
CREATE TABLE `car_loan` (
  `cal_cas_id` bigint DEFAULT NULL,
  `cal_price` varchar(200) DEFAULT NULL,
  `cal_lice` varchar(200) DEFAULT NULL,
  `cal_make` varchar(200) DEFAULT NULL,
  `cal_vin` varchar(200) DEFAULT NULL,
  `cal_engine_no` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- ========================================
-- 创建索引优化查询性能
-- ========================================

SET FOREIGN_KEY_CHECKS = 1;

-- 数据库创建完成
SELECT 'CCDS完整数据库创建完成 - 包含所有106个表' as message;
SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ccds';
