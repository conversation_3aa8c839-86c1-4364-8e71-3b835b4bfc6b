<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.SalSupplier" table="sal_supplier" schema="dbo" >
        <id name="ssuId" type="java.lang.Long">
            <column name="ssu_id" />
            <generator class="identity" />
        </id>
        <property name="ssuCode" type="java.lang.String">
            <column name="ssu_code" length="300" />
        </property>
        <property name="ssuName" type="java.lang.String">
            <column name="ssu_name" length="100" />
        </property>
        <property name="ssuPhone" type="java.lang.String">
            <column name="ssu_phone" length="50" />
        </property>
        <property name="ssuFex" type="java.lang.String">
            <column name="ssu_fex" length="50" />
        </property>
        <property name="ssuEmail" type="java.lang.String">
            <column name="ssu_email" length="50" />
        </property>
        <property name="ssuNet" type="java.lang.String">
            <column name="ssu_net" length="200" />
        </property>
        <property name="ssuAdd" type="java.lang.String">
            <column name="ssu_add" length="**********" />
        </property>
        <property name="ssuPrd" type="java.lang.String">
            <column name="ssu_prd" length="**********" />
        </property>
        <many-to-one name="country" class="com.frsoft.base.entity.CusArea" fetch="select" not-null="false">
            <column name="ssu_county" />
        </many-to-one>
        <many-to-one name="province" class="com.frsoft.base.entity.CusProvince" fetch="select" not-null="false">
            <column name="ssu_pro" />
        </many-to-one>
        <many-to-one name="city" class="com.frsoft.base.entity.CusCity" fetch="select" not-null="false">
            <column name="ssu_city" />
        </many-to-one>
        <many-to-one name="typeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="ssu_type_id" />
        </many-to-one>
        <property name="ssuZipCode" type="java.lang.String">
            <column name="ssu_zip_code" length="50" />
        </property>
        <property name="ssuBank" type="java.lang.String">
            <column name="ssu_bank" length="50" />
        </property>
        <property name="ssuBankName" type="java.lang.String">
            <column name="ssu_bank_name" length="50" />
        </property>
        <property name="ssuBankCode" type="java.lang.String">
            <column name="ssu_bank_code" length="50" />
        </property>
        <property name="ssuIsdel" type="java.lang.String">
            <column name="ssu_isdel" length="1" />
        </property>
        <property name="ssuRemark" type="java.lang.String">
            <column name="ssu_remark" length="**********" />
        </property>
        <property name="ssuInpUser" type="java.lang.String">
            <column name="ssu_inp_user" length="50" />
        </property>
        <property name="ssuCreDate" type="java.util.Date">
            <column name="ssu_cre_date" length="23" />
        </property>
        <property name="ssuAltDate" type="java.util.Date">
            <column name="ssu_alt_date" length="23" />
        </property>
        <property name="ssuAltUser" type="java.lang.String">
            <column name="ssu_alt_user" length="50" />
        </property>
        <set name="supContacts" inverse="true" order-by="scn_cre_date desc" cascade="all"> 
        	<key>
        		<column name="scn_ssu_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.SupContact" />
        </set>
        <set name="salPurOrds" inverse="true" order-by="spo_cre_date desc" cascade="all">
        	<key>
        		<column name="spo_sup_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.SalPurOrd" />
        </set>
        <set name="inquirys" inverse="true" order-by="inq_ins_date desc" cascade="all">
        	<key>
        		<column name="inq_ssu_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.Inquiry" />
        </set>
    </class>
</hibernate-mapping>
