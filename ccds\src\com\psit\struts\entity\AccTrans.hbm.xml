<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.AccTrans" table="acc_trans" schema="dbo" >
        <id name="atrId" type="java.lang.Long">
            <column name="atr_id" />
            <generator class="identity" />
        </id>
        <property name="atrCode" type="java.lang.String">
            <column name="atr_code" length="50" />
        </property>
         <property name="atrContent" type="java.lang.String">
            <column name="atr_content" length="100" />
        </property>
        <property name="atrDate" type="java.util.Date">
            <column name="atr_date" length="23" />
        </property>
        <property name="atrMon" type="java.lang.Double">
            <column name="atr_mon" precision="18" />
        </property>
    
        <many-to-one name="inAccount" class="com.psit.struts.entity.Account" fetch="select" not-null="false">
        	<column name="atr_in_aco" />
        </many-to-one>
        <many-to-one name="outAccount" class="com.psit.struts.entity.Account" fetch="select" not-null="false">
        	<column name="atr_out_aco" />
        </many-to-one>
        <property name="atrRemark" type="java.lang.String">
            <column name="atr_remark" length="**********" />
        </property>
        <property name="atrIsdel" type="java.lang.String">
            <column name="atr_isdel" length="1" />
        </property>
        <property name="atrInpUser" type="java.lang.String">
            <column name="atr_inp_user" length="50" />
        </property>
        <property name="atrUndoUser" type="java.lang.String">
            <column name="atr_undo_user" length="50" />
        </property>
        <property name="atrCreDate" type="java.util.Date">
            <column name="atr_cre_date" length="23" />
        </property>
         <property name="atrUndoDate" type="java.util.Date">
            <column name="atr_undo_date" length="23" />
        </property>
    </class>
</hibernate-mapping>
