#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理转换后的MySQL SQL文件
移除MSSQL残留语法，修复格式问题
"""

import re
import sys

def clean_mysql_sql(sql_content):
    """清理MySQL SQL文件"""
    
    # 1. 移除所有IF NOT EXISTS语句
    sql_content = re.sub(r'IF\s+NOT\s+EXISTS[^;]*;', '', sql_content, flags=re.IGNORECASE | re.DOTALL)
    
    # 2. 移除BEGIN语句
    sql_content = re.sub(r'\bBEGIN\b', '', sql_content, flags=re.IGNORECASE)
    
    # 3. 修复重复的ENGINE声明
    sql_content = re.sub(r'ENGINE=InnoDB[^;]*;\s*\)\s*ENGINE=InnoDB[^;]*;', 
                        r') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;', 
                        sql_content, flags=re.IGNORECASE)
    
    # 4. 修复PRIMARY KEY后面的ENGINE声明
    sql_content = re.sub(r'PRIMARY\s+KEY\s*\(([^)]+)\)\s*ENGINE=InnoDB[^;]*;',
                        r'PRIMARY KEY (\1)', sql_content, flags=re.IGNORECASE)
    
    # 5. 修复CREATE INDEX语句
    sql_content = re.sub(r'CREATE\s+NONCLUSTERED\s+INDEX', 'CREATE INDEX', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'\)\s*WITH\s*\([^)]+\)\s*ENGINE=InnoDB[^;]*;', ');', sql_content, flags=re.IGNORECASE)
    
    # 6. 修复错误的数据类型
    sql_content = re.sub(r'`varchar`\(max\)', 'text', sql_content, flags=re.IGNORECASE)
    
    # 7. 清理多余的空行
    sql_content = re.sub(r'\n\s*\n\s*\n', '\n\n', sql_content)
    
    # 8. 移除空的ENGINE声明行
    sql_content = re.sub(r'^\s*ENGINE=InnoDB[^;]*;\s*$', '', sql_content, flags=re.MULTILINE)
    
    # 9. 确保每个CREATE TABLE语句都有正确的结尾
    lines = sql_content.split('\n')
    cleaned_lines = []
    in_create_table = False
    
    for line in lines:
        line = line.strip()
        if not line:
            cleaned_lines.append('')
            continue
            
        if line.upper().startswith('DROP TABLE') or line.upper().startswith('CREATE TABLE'):
            in_create_table = True
            cleaned_lines.append(line)
        elif in_create_table and line.startswith(')'):
            # 确保CREATE TABLE有正确的结尾
            if not line.endswith(';'):
                line = ') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;'
            else:
                line = ') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;'
            cleaned_lines.append(line)
            in_create_table = False
        elif line.upper().startswith('CREATE INDEX'):
            # 修复索引语句
            line = re.sub(r'\s+ASC\s*$', '', line)
            if not line.endswith(';'):
                line += ';'
            cleaned_lines.append(line)
        else:
            cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("使用方法: python clean_mysql_sql.py <输入文件> <输出文件>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        print(f"正在读取文件: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print("正在清理SQL语法...")
        cleaned_content = clean_mysql_sql(sql_content)
        
        print(f"正在写入文件: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print("清理完成!")
        print(f"输出文件: {output_file}")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
