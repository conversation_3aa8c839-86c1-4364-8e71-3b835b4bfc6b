<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.WmsWarOut" table="wms_war_out" schema="dbo" >
        <id name="wwoId" type="java.lang.Long">
            <column name="wwo_id"/>
            <generator class="identity" />
        </id>
        <many-to-one name="wmsStro" class="com.psit.struts.entity.WmsStro" fetch="select" not-null="false">
            <column name="wwo_stro_code" length="50" />
        </many-to-one>
        <many-to-one name="salOrdCon" class="com.frsoft.cis.entity.SalOrdCon" fetch="select" not-null="false">
            <column name="wwo_ord_code" length="50" />
        </many-to-one>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select" not-null="false">
            <column name="wwo_user_code" length="50" />
        </many-to-one>
        <property name="wwoCode" type="java.lang.String">
            <column name="wwo_code" length="50" />
        </property>
        <property name="wwoTitle" type="java.lang.String">
            <column name="wwo_title" length="1073741823" />
        </property>
        <property name="wwoInpDate" type="java.util.Date">
            <column name="wwo_inp_date" length="23" />
        </property>
        <property name="wwoOutDate" type="java.util.Date">
            <column name="wwo_out_date" length="23" />
        </property>
        <property name="wwoState" type="java.lang.String">
            <column name="wwo_state" length="1" />
        </property>
        <property name="wwoRemark" type="java.lang.String">
            <column name="wwo_remark" length="1073741823" />
        </property>
        <property name="wwoIsdel" type="java.lang.String">
            <column name="wwo_isdel" length="1" />
        </property>
        <property name="wwoAltDate" type="java.util.Date">
            <column name="wwo_alt_date" length="23" />
        </property>
        <property name="wwoInpName" type="java.lang.String">
            <column name="wwo_inp_name" length="50" />
        </property>
        <property name="wwoAltName" type="java.lang.String">
            <column name="wwo_alt_name" length="50" />
        </property>
        <property name="wwoResName" type="java.lang.String">
            <column name="wwo_res_name" length="50" />
        </property>
        <property name="wwoUserName" type="java.lang.String">
            <column name="wwo_user_name" length="50" />
        </property>
        <property name="wwoAppDate" type="java.util.Date">
            <column name="wwo_app_date" length="23" />
        </property>
        <property name="wwoAppMan" type="java.lang.String">
            <column name="wwo_app_man" length="50" />
        </property>
        <property name="wwoAppDesc" type="java.lang.String">
            <column name="wwo_app_desc" length="1073741823" />
        </property>
        <property name="wwoAppIsok" type="java.lang.String">
            <column name="wwo_app_isok" length="1" />
        </property>
        <property name="wwoCanDate" type="java.util.Date">
            <column name="wwo_can_date" length="23" />
        </property>
        <property name="wwoCanMan" type="java.lang.String">
            <column name="wwo_can_man" length="50" />
        </property>
        <set name="RWoutPros" inverse="true"  cascade="delete" order-by="rwo_id">
            <key>
                <column name="rwo_wout_id"/>
            </key>
            <one-to-many class="com.psit.struts.entity.RWoutPro" />
        </set>
    </class>
</hibernate-mapping>
