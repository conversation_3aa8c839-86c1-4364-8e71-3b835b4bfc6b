<%@ page language="java" pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>案人数据列表</title>
    
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="js/caseAss.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
	<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
  	<script language="javascript" type="text/javascript">
		/*function check(){
			if(isEmpty("idNumber")){
				alert("未录入证件号！");
				return false;
			}
			else{
				loadList();
			}
		}*/
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			dataId = obj.exiId;
			datas = [obj.exiIdNumber, obj.exiName, obj.exiType, convertToBR(obj.exiContent), obj.exiRemark, obj.exiCreTime, obj.exiCreMan ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "extraInfAction.do";
			var pars = $("searchForm").serialize(true);
			pars.op="listExtraInf";
			var loadFunc = "loadList";
			var cols=[
				{name:"证件号"},
				{name:"姓名"},
				{name:"信息类型"},
				{name:"信息内容",isSort:false,align:"left"},
				{name:"备注",isSort:false},
				{name:"导入时间",renderer:"time"},
				{name:"导入人"}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("extraInfListTab","dataList");
    	gridEl.config.hasCheckBox = true;
		createProgressBar();
		window.onload=function(){
			loadList();
			//增加清空按钮
			createCancelButton(loadList,'searchForm',-50,5,'searButton','after');
		}
  	</script>
  </head>
  
  <body>
    <div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>数据管理 > 案人数据库 <span id="changeFuncBt" onMouseOver="popFuncMenu(['case',5],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['case',5],true)" onMouseOut="popFuncMenu(['case',5],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
           	</div>
  	   		<table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                             <div id="tabType1" class="tabTypeWhite" onClick="self.location.href='extraInfAction.do?op=toListExtraInf'">案人数据库</div>
                        </div>
                     </th>
                     <td><a href="javascript:void(0)" onClick="assPopDiv(8);return false;" class="newBlueButton">导入案人数据</a></td>
                     <td><a href="javascript:void(0)" onClick="assPopDiv(9);return false;" class="newBlueButton">新建案人数据</a></td>
                </tr>
            </table>
            <script type="text/javascript">loadTabTypeWidth();</script>
            <div id="listContent">
                <div class="listSearch">
                	<form class="listSearchForm" id="searchForm" onSubmit="loadList();return false;" >
                    <table cellpadding="0" cellspacing="0">
                        <tr>
                        	<th>证件号：</th>
                            <td>
                            	<input class="inputSize2 inputBoxAlign" type="text" id="idNumber" name="idNumber" onBlur="autoShort(this,25)"/>
                            	<input type="checkbox" class="inputBoxAlign" id="isMatchFst" name="isMatchFst" value="1" /><label for="isMatchFst" style="font-size:11px">匹配开头</label>&nbsp;&nbsp;&nbsp;
                            </td>    
                         	<th>姓名：</th>
                            <td><input class="inputSize2" type="text" name="exiName" onBlur="autoShort(this,50)"/></td>    
                         	<th>信息类型：</th>
                            <td><input class="inputSize2" type="text" name="type" onBlur="autoShort(this,50)"/></td>
                         	<th>信息内容：</th>
                            <td><input class="inputSize2" style="width:150px" type="text" name="content" onBlur="autoShort(this,500)"/></td>
                            <th>导入日期：</th>
                            <td><input name="creDateStart" id="staTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('endTim');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'endTim\')}'})"/>&nbsp;到&nbsp;<input name="creDateEnd" id="endTim" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'staTim\')}'})"/></td>
                            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize1 inputBoxAlign" value="查询"/></td>
                         </tr>
                     </table>
                    </form>
                </div>
                <div id="toolsBarTop" class="bottomBar">
                    <span class="grayBack" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="cancelBatchAss(4,'案人数据')">删除案人数据</span>
                </div>
                <div id="dataList" class="dataList"></div>
            </div>
  		</div> 
	</div>
  </body>
  
</html>
