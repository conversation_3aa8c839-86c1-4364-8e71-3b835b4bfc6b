<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.Attachment" table="attachment" schema="dbo">
        <id name="attId" type="java.lang.Long">
            <column name="att_id" />
            <generator class="native" />
        </id>
        <discriminator column="att_type" type="java.lang.String"/>
        <property name="attName" type="java.lang.String">
            <column name="att_name" length="1073741823" />
        </property>
        <property name="attSize" type="java.lang.Long">
            <column name="att_size" />
        </property>
        <property name="attPath" type="java.lang.String">
            <column name="att_path" />
        </property>
        <property name="attIsJunk" type="java.lang.String">
            <column name="att_isJunk" length="1" />
        </property>
        <property name="attDate" type="java.util.Date">
            <column name="att_date" length="23" />
        </property>
        <property name="attFileType" type="java.lang.Integer">
            <column name="att_file_type" />
        </property>
		
		<subclass name="com.frsoft.cis.entity.AttOrd" discriminator-value="ord">
			<many-to-one name="salOrdCon" column="att_fk_id" class="com.frsoft.cis.entity.SalOrdCon" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.cis.entity.AttAllCus" discriminator-value="allCus">
        	<many-to-one name="cusCorCus" column="att_fk_id" class="com.frsoft.cis.entity.CusCorCus" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.base.entity.AttRep" discriminator-value="rep">
        	<many-to-one name="report" column="att_fk_id" class="com.frsoft.base.entity.Report" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.cis.entity.AttServ" discriminator-value="cusServ">
        	<many-to-one name="cusServ" column="att_fk_id" class="com.frsoft.cis.entity.CusServ" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.base.entity.AttMes" discriminator-value="mes">
        	<many-to-one name="message" column="att_fk_id" class="com.frsoft.base.entity.Message" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.cis.entity.AttPra" discriminator-value="cusPra">
        	<many-to-one name="salPra" column="att_fk_id" class="com.frsoft.cis.entity.SalPra" not-null="false" />
        </subclass>
         <subclass name="com.psit.struts.entity.AttQuo" discriminator-value="quo">
        	<many-to-one name="quote" column="att_fk_id" class="com.psit.struts.entity.Quote" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.base.entity.AttTa" discriminator-value="ta">
        	<many-to-one name="taLim" column="att_fk_id" class="com.frsoft.base.entity.TaLim" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.base.entity.AttTask" discriminator-value="task">
        	<many-to-one name="salTask" column="att_fk_id" class="com.frsoft.base.entity.SalTask" not-null="false" />
        </subclass>
       	<subclass name="com.psit.struts.entity.AttPTask" discriminator-value="ptask">
        	<many-to-one name="proTask" column="att_fk_id" class="com.psit.struts.entity.ProTask" not-null="false" />
        </subclass>
        <subclass name="com.psit.struts.entity.AttPRTa" discriminator-value="prta">
        	<many-to-one name="prta" column="att_fk_id" class="com.psit.struts.entity.ProTaskLim" not-null="false" />
        </subclass>
        <subclass name="com.psit.struts.entity.AttPro" discriminator-value="proj">
        	<many-to-one name="pro" column="att_fk_id" class="com.psit.struts.entity.Project" not-null="false" />
        </subclass>
        <subclass name="com.psit.struts.entity.AttPsta" discriminator-value="psta">
        	<many-to-one name="psta" column="att_fk_id" class="com.psit.struts.entity.ProStage" not-null="false" />
        </subclass>
        <subclass name="com.psit.struts.entity.AttSpo" discriminator-value="spo">
        	<many-to-one name="salPurOrd" column="att_fk_id" class="com.psit.struts.entity.SalPurOrd" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.base.entity.AttNews" discriminator-value="news">
        	<many-to-one name="news" column="att_fk_id" class="com.frsoft.base.entity.News" not-null="false" />
        </subclass>
		<subclass name="com.frsoft.base.entity.AttEmp" discriminator-value="emp">
	        <many-to-one name="salEmp" column="att_fk_id" class="com.frsoft.base.entity.SalEmp" not-null="false" />
		</subclass>
		<subclass name="com.frsoft.ccds.entity.AttBankCase" discriminator-value="bankCase">
        	<many-to-one name="bankCase" column="att_fk_id" class="com.frsoft.ccds.entity.BankCase" not-null="false" />
        </subclass>
        <subclass name="com.frsoft.loan.entity.AttLoanCus" discriminator-value="loanCus">
        	<many-to-one name="loanCus" column="att_fk_id" class="com.frsoft.loan.entity.LoanCus"  not-null="false" />
		</subclass>
        <subclass name="com.frsoft.ccds.entity.AttVis" discriminator-value="vis">
        	<many-to-one name="visRecord" column="att_fk_id" class="com.frsoft.ccds.entity.VisRecord"  not-null="false" />
		</subclass>
    </class>
</hibernate-mapping>
