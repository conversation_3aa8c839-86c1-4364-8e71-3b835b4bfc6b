# 生产环境配置
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **********************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      # 生产环境连接池配置
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 生产环境关闭SQL监控
      filters: wall
      connection-properties: druid.stat.mergeSql=false;druid.stat.slowSqlMillis=10000
      # 生产环境关闭监控页面
      web-stat-filter:
        enabled: false
      stat-view-servlet:
        enabled: false

# 生产环境日志配置
logging:
  level:
    com.frsoft.ccds: info
    org.springframework: warn
    org.mybatis: warn
    root: warn
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/ccds-prod.log
    max-size: 100MB
    max-history: 30

# 生产环境系统配置
ccds:
  profile: /opt/ccds/upload
  addressEnabled: true
  captchaType: math
