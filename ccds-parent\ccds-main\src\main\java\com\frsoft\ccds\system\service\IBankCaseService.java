package com.frsoft.ccds.system.service;

import com.frsoft.ccds.system.domain.BankCase;

import java.util.List;

/**
 * 银行案件Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IBankCaseService {
    
    /**
     * 查询银行案件
     * 
     * @param casId 银行案件主键
     * @return 银行案件
     */
    BankCase selectBankCaseByCasId(Long casId);

    /**
     * 查询银行案件列表
     * 
     * @param bankCase 银行案件
     * @return 银行案件集合
     */
    List<BankCase> selectBankCaseList(BankCase bankCase);

    /**
     * 新增银行案件
     * 
     * @param bankCase 银行案件
     * @return 结果
     */
    int insertBankCase(BankCase bankCase);

    /**
     * 修改银行案件
     * 
     * @param bankCase 银行案件
     * @return 结果
     */
    int updateBankCase(BankCase bankCase);

    /**
     * 批量删除银行案件
     * 
     * @param casIds 需要删除的银行案件主键集合
     * @return 结果
     */
    int deleteBankCaseByCasIds(Long[] casIds);

    /**
     * 删除银行案件信息
     * 
     * @param casId 银行案件主键
     * @return 结果
     */
    int deleteBankCaseByCasId(Long casId);
    
    /**
     * 根据员工编号查询案件列表
     * 
     * @param seNo 员工编号
     * @return 案件列表
     */
    List<BankCase> selectBankCaseListBySeNo(Long seNo);
    
    /**
     * 根据案件状态查询案件列表
     * 
     * @param casState 案件状态
     * @return 案件列表
     */
    List<BankCase> selectBankCaseListByState(Integer casState);
    
    /**
     * 根据客户姓名模糊查询案件列表
     * 
     * @param casName 客户姓名
     * @return 案件列表
     */
    List<BankCase> selectBankCaseListByName(String casName);
    
    /**
     * 根据客户电话查询案件列表
     * 
     * @param casPhone 客户电话
     * @return 案件列表
     */
    List<BankCase> selectBankCaseListByPhone(String casPhone);
    
    /**
     * 统计案件总数
     * 
     * @param bankCase 查询条件
     * @return 案件总数
     */
    int countBankCase(BankCase bankCase);
    
    /**
     * 分配案件给员工
     * 
     * @param casIds 案件ID数组
     * @param seNo 员工编号
     * @param assignUser 分配用户
     * @return 结果
     */
    int assignBankCaseToEmployee(Long[] casIds, Long seNo, String assignUser);
}
