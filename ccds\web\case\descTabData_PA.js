function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','AREA'],
		['ID_NO',['C_CODE',1,'合同号'],'OV_DT','AGE'],
		['AMT','PAID','BACK_P','C_DT'],
		['LAST_M','EMP_NAME','ASS_TM',['FILE_NO',1,'保单号']],
		['LAST_CL','PR_COUNT','CL_AREA','DAYS'],
		['COUNT','P_COUNT', 'PRI','LEFT_PRI'],
		['LOAN_DT','M_CAT','PA_CLA_M','PA_DAY_P'],
		[['OV_P',1,'违约金'],'PA_PREM','MNG_COST','OV_INT'],
		['PENALTY','PA_F_COST','DEAD_L','TREMARK'],
		['COM','W_PHO','W_ADDR','W_POST'],
		['PA_COM2','PA_W_PHO2','PA_COM2_ADDR','PART'],
		['POS','PHO','HOM','REG'],
		['MOB','PA_PHO2','PA_ADDR2',['H_POST',1,'家庭/户籍邮编']],
		[['C4_NAME',1,'配偶姓名'],'C1_NAME','C2_NAME','C3_NAME'],
		[['C4_HM_PHO',1,'配偶家庭电话'],'C1_HM_PHO','C2_HM_PHO','C3_HM_PHO'],
		[['C4_W_PHO',1,'配偶单位电话'],'C1_W_PHO','C2_W_PHO','C3_W_PHO'],
		[['C4_MOB',1,'配偶手机'],'C1_MOB','C2_MOB','C3_MOB'],
		['PA_C4_MOB2','PA_C1_MOB2','PA_C2_MOB2','PA_C3_MOB2'],
		['PA_H_FAX1','PA_H_FAX2','PA_FAX1','PA_FAX2'],
		['STATE','TIPS_DT','PTP','CP']
	);
}
function getLayout2(){
	return  new Array(
		['ASS_HIS','PA_PMAN','PA_CLA_DT','PA_EFF_DT'],
		['PA_IS_WD',['F_BANK',1,'还款银行'],['ACC',1,'还款账号'],'ACC_NAME'],
		['PA_H_T1','PA_H_OWN1','PA_H_T2','PA_H_OWN2'],
		['PA_COM_T','PA_COM_DT','PA_TAX_NO','PA_COM_NO'],
		['SC_NO','CL_T','G_AMT','LOAN_T'],
		['C1_ID_NO','C1_COM',['C1_ADR',3]],
		['C2_ID_NO','C2_COM',['C2_ADR',3]],
		['C3_ID_NO','C3_COM',['C3_ADR',3]],
		[['C4_ID_NO',1,'配偶证件'],['C4_COM',1,'配偶单位'],['C4_ADR',3,'配偶公司地址']],
		[['C5_NAME',1,'联系人4姓名'],['C5_ID_NO',1,'联系人4证件号'],['C5_HM_PHO',1,'联系人4家庭电话'],['C5_MOB',1,'联系人4手机']],
		[['C5_W_PHO',1,'联系人4手机2'],['C5_COM',1,'联系人4单位'],['C5_ADR',3,'联系人4地址']],
		[['C6_NAME',1,'联系人5姓名'],['C6_ID_NO',1,'联系人5证件号'],['C6_HM_PHO',1,'联系人5家庭电话'],['C6_MOB',1,'联系人5手机']],
		[['C6_W_PHO',1,'联系人5手机2'],['C6_COM',1,'联系人5单位'],['C6_ADR',3,'联系人5地址']],
		[['O_REC',3],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}