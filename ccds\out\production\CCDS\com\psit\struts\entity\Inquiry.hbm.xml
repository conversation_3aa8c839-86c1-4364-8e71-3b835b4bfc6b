<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.Inquiry" table="inquiry" schema="dbo" >
        <id name="inqId" type="java.lang.Long">
            <column name="inq_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salSupplier" class="com.psit.struts.entity.SalSupplier" fetch="select" not-null="false">
            <column name="inq_ssu_id" />
        </many-to-one>
        <many-to-one name="project" class="com.psit.struts.entity.Project" fetch="select" not-null="false">
            <column name="inq_pro_id" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="inq_se_no" />
        </many-to-one>
        <property name="inqTitle" type="java.lang.String">
            <column name="inq_title" length="100" />
        </property>
        <property name="inqPrice" type="java.lang.Double">
            <column name="inq_price" precision="18" />
        </property>
        <property name="inqDate" type="java.util.Date">
            <column name="inq_date" length="23" />
        </property>
        <property name="inqInpUser" type="java.lang.String">
            <column name="inq_inp_user" length="50" />
        </property>
        <property name="inqUpdUser" type="java.lang.String">
            <column name="inq_upd_user" length="50" />
        </property>
        <property name="inqInsDate" type="java.util.Date">
            <column name="inq_ins_date" length="23" />
        </property>
        <property name="inqUpdDate" type="java.util.Date">
            <column name="inq_upd_date" length="23" />
        </property>
        <property name="inqRemark" type="java.lang.String">
            <column name="inq_remark" length="1073741823" />
        </property>
        <property name="inqIsdel" type="java.lang.String">
            <column name="inq_isdel" length="1" />
        </property>
        <set name="rinqPros" inverse="true" order-by="rqp_id desc" cascade="delete">
        	<key>
        		<column name="rqp_inq_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.RInqPro"/>
        </set>
    </class>
</hibernate-mapping>
