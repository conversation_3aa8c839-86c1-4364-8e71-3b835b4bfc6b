<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.base.entity.UserLog" table="user_log" schema="dbo">
        <id name="ulgId" type="java.lang.Long">
            <column name="ulg_id" />
            <generator class="identity" />
        </id>
        <property name="ulgType" type="java.lang.String">
            <column name="ulg_type" length="50" />
        </property>
        <property name="ulgOper" type="java.lang.String">
            <column name="ulg_oper" length="50" />
        </property>
        <property name="ulgOpTime" type="java.util.Date">
            <column name="ulg_op_time" length="23" />
        </property>
        <property name="ulgOpContent" type="java.lang.String">
            <column name="ulg_op_content" />
        </property>
        <property name="ulgActedUser" type="java.lang.String">
            <column name="ulg_acted_user" length="50" />
        </property>
        <many-to-one name="ulgUser" class="com.frsoft.base.entity.LimUser">
            <column name="ulg_user" />
        </many-to-one>
    </class>
</hibernate-mapping>
