<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.ProTask" table="pro_task" schema="dbo" >
        <id name="prtaId" type="java.lang.Long">
            <column name="prta_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="project" class="com.psit.struts.entity.Project" fetch="select" not-null="false">
            <column name="prta_pro_id" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="prta_se_no"/>
        </many-to-one>
        <property name="prtaStaName" type="java.lang.String">
            <column name="prta_sta_name" length="300" />
        </property>
        <property name="prtaName" type="java.lang.String">
            <column name="prta_name" length="50" />
        </property>
        <property name="prtaTitle" type="java.lang.String">
            <column name="prta_title" length="300" />
        </property>
        <property name="prtaRelDate" type="java.util.Date">
            <column name="prta_rel_date" length="23" />
        </property>
        <property name="prtaChangeDate" type="java.util.Date">
            <column name="prta_change_date" length="23" />
        </property>
        <property name="prtaFinDate" type="java.util.Date">
            <column name="prta_fin_date" length="23" />
        </property>
        <property name="prtaFctDate" type="java.util.Date">
            <column name="prta_fct_date" length="23" />
        </property>
        <property name="prtaLev" type="java.lang.String">
            <column name="prta_lev" length="50" />
        </property>
        <property name="prtaState" type="java.lang.String">
            <column name="prta_state" length="1" />
        </property>
        <property name="prtaUpdUser" type="java.lang.String">
            <column name="prta_upd_user" length="50" />
        </property>
        <property name="prtaTag" type="java.lang.String">
            <column name="prta_tag" length="1073741823" />
        </property>
        <property name="prtaDesc" type="java.lang.String">
            <column name="prta_desc" length="1073741823" />
        </property>
        <property name="prtaLog" type="java.lang.String">
            <column name="prta_log" length="1073741823" />
        </property>
        <property name="prtaRemark" type="java.lang.String">
            <column name="prta_remark" length="1073741823" />
        </property>
        <property name="prtaIsdel" type="java.lang.String">
            <column name="prta_isdel" length="1" />
        </property>
        <set name="proTaskLims" inverse="true"  order-by="ptl_id desc" cascade="all">
            <key>
                <column name="ptl_prta_id" />
            </key>
            <one-to-many class="com.psit.struts.entity.ProTaskLim" />
        </set>
        <set name="attachments" inverse="true"  cascade="all" where="att_type='ptask'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
    </class>
</hibernate-mapping>
