<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>通道通话量统计</title>
    
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/bfmod.css"/>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
	<script type="text/javascript" src="js/formCheck.js"></script>
	<script type="text/javascript" src="js/bfmod.js"></script>
  	<script language="javascript" type="text/javascript">
	  	function check(){
			var errStr = "";
			if(isEmpty("statDateStart")&&isEmpty("statDateStart")){
				errStr+="未选择录音日期！";
			}
			if(errStr!=""){
				alert(errStr);
				return false;
			}
			else{
				loadList();
			}
	   	}    
  	
  		function initPage(){
  			var menuItems = $("statTopMenuBar").childNodes;
  			menuItems[0].className = "statTopMenuCur";
  			for(var i=0; i<menuItems.length; i++){
  				if(menuItems[i].className != "statTopMenuCur"){
  	  				menuItems[i].className="statTopMenu";
  	  				menuItems[i].onmouseover=function(){
  	  					this.className='statTopMenuOver';
  	  				}
  	  				menuItems[i].onmouseout=function(){
  	  					this.className='statTopMenu';
  	  				}
  				}
  			}
  			
  		}
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			datas = [obj.channelNum,obj.localPhone,
			         obj.callIn1,obj.callOut1,formatSecond(obj.durIn1),formatSecond(obj.durOut1),
			         obj.callIn2,obj.callOut2,formatSecond(obj.durIn2),formatSecond(obj.durOut2),
			         obj.callIn3,obj.callOut3,formatSecond(obj.durIn3),formatSecond(obj.durOut3),
			         obj.callIn4,obj.callOut4,formatSecond(obj.durIn4),formatSecond(obj.durOut4),
			         obj.callInTotal,obj.callOutTotal,formatSecond(obj.durInTotal),formatSecond(obj.durOutTotal),
			         obj.totalCall,formatSecond(obj.totalDur) ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(){
			var url = "soundRecordAction.do";
			var pars =  $("searchForm").serialize(true);
			pars.op="statRecByChannel";
			var loadFunc = "loadList";
			var cols=[];
			gridEl.init(url,pars,cols,loadFunc);
			gridEl.loadData(dataMapper,columnCreator,errInfoCallBack);
		}
		function columnCreator(jsonData,colArr){
			var dateCol = [];
			colArr.push({html:"<tr><th rowspan='2' style='height:50px; width:2%;'>通道</th>"});
			dateCol.push("<th rowspan='2' style='height:50px; width:4%;'>本机号码</th>");
			dateCol.push("<th colspan='4' style='width:16%;'>8:00前</th>");
			dateCol.push("<th colspan='4' style='width:16%;'>8:00-12:00</th>");
			dateCol.push("<th colspan='4' style='width:16%;'>12:00-18:00</th>");
			dateCol.push("<th colspan='4' style='width:16%;'>18:00以后</th>");
			colArr.push({html:dateCol.join("")});
			
			colArr.push({html:"<th rowspan='2' style='width:4%;'>呼入总量</th>"});
			colArr.push({html:"<th rowspan='2' style='width:4%;'>呼出总量</th>"});
			colArr.push({html:"<th rowspan='2' style='width:6%;'>呼入总时长</th>"});
			colArr.push({html:"<th rowspan='2' style='width:6%;'>呼出总时长</th>"});
			colArr.push({html:"<th rowspan='2' style='width:4%;'>总通话量</th>"});
			colArr.push({html:"<th rowspan='2' style='width:6%;'>总通话时长</th></tr><tr>"});
			for(var j=0; j<8; j++){
				if(j%2==0){
					colArr.push({html:"<th style='width:3%;'>呼入量</th>"});
					colArr.push({html:"<th style='width:3%;'>呼出量</th>"});
				}
				else{
					colArr.push({html:"<th style='width:5%;'>呼入时长</th>"});
					colArr.push({html:"<th style='width:5%;'>呼出时长</th>"});
				}
			}
		}
		
    	var gridEl = new MGrid("statRecByChannelTab","dataList");
		gridEl.config.hasPage = false;
		gridEl.config.sortable = false;
		gridEl.config.isShort = false;
		gridEl.config.isResize = false;
		gridEl.config.listType = "simpleStat";
		gridEl.config.className = "noBr";
		createProgressBar();
		window.onload=function(){
			initPage();
			closeProgressBar();
		} 
  	</script>
  </head>
  
  <body>
    <div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>录音管理 > 通话统计 <span id="changeFuncBt" onMouseOver="popFuncMenu(['rec',1],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['rec',1],true)" onMouseOut="popFuncMenu(['rec',1],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
           	</div>
  	   		<table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                             <div id="tabType1" class="tabTypeWhite" onClick="self.location.href='soundRecordAction.do?op=toStatRecByChannel'">通话统计</div>
                        </div>
                     </th>
                </tr>
            </table>
            <script type="text/javascript">loadTabTypeWidth();</script>
            <div id="listContent">
            	<div id="statTopMenuBar"><span onclick="self.location.href='soundRecordAction.do?op=toStatRecByChannel'">通道通电量统计</span><span onclick="self.location.href='soundRecordAction.do?op=toStatRecByEmp'">催收员通电量时长统计</span></div>
            	<c:if test="${empty ctisList}">
			   		<div class="grayBack" style="padding:10px; font-size:14px">未添加录音服务器，请在<a href="ctiServerAction.do?op=toListCtis">服务器设置</a>中添加</div>
			    </c:if>
			    <c:if test="${!empty ctisList}">
			  	<div class="listSearch">
			  		<form class="listSearchForm" id="searchForm" onSubmit="check();return false;" >
                    <table cellpadding="0" cellspacing="0">
                    	<tr>
                    		<th>&nbsp;<b>选择录音服务器：</b></th>
                    		<td><select id="serverId" name="serverId" class="inputSize2"><c:forEach items="${ctisList}" var="ctis"><option value="${ctis.ctisId}">${ctis.ctisName}</option></c:forEach></select>
                    		<c:forEach items="${ctisList}" var="ctish">
                    		<input type="hidden" id="ctiServer${ctish.ctisId}" value="${ctish.ctisIp}"/>
                    		</c:forEach>
                    		</td>
                    	</tr>
                    	<tr>	
                    		<th>录音日期：</th>
                            <td><input name="statDateStart" id="statDateStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({onpicked:function(){$('statDateEnd').focus();},maxDate:'#F{$dp.$D(\'statDateEnd\')}'})"/>&nbsp;到&nbsp;<input name="statDateEnd" id="statDateEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'statDateStart\')}'})"/>
                       		&nbsp;&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize1 inputBoxAlign" value="查询"/></td>
                         </tr>
                     </table>
                    </form>
			   	</div>
			  	<div id="errMsgLayer" class="redWarn" style="display:none; margin:0;"></div>
                <div id="dataList" class="dataList"></div>	
                </c:if>
            </div>
  		</div> 
	</div>
  </body>
  
</html>
