<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.LoanCom" table="loan_com" schema="dbo" >
        <id name="lcmId" type="java.lang.Long">
            <column name="lcm_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="lcmLoanSeason" class="com.frsoft.loan.entity.LoanSeason" fetch="select">
            <column name="lcm_lse_id" />
        </many-to-one>
        <property name="lcmCompany" type="java.lang.String">
            <column name="lcm_company" length="100" />
        </property>
        <property name="lcmContent" type="java.lang.String">
            <column name="lcm_content" length="1073741823" />
        </property>
        <property name="lcmInsUser" type="java.lang.String">
            <column name="lcm_ins_user" length="25" />
        </property>
        <property name="lcmInsTime" type="java.util.Date">
            <column name="lcm_ins_time" length="23" />
        </property>
        <property name="lcmAltUser" type="java.lang.String">
            <column name="lcm_alt_user" length="25" />
        </property>
        <property name="lcmAltTime" type="java.util.Date">
            <column name="lcm_alt_time" length="23" />
        </property>
    </class>
</hibernate-mapping>
