<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic" prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>    
    <title>批量申请协助</title>
    <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	 <script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript">
		function check(){
			var errStr = "";
		 	if(isEmpty("assType")){
				errStr+="- 未选择协催类型！\n";
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else{
				waitSubmit("dosend");
				waitSubmit("doCancel");
				return $("create").submit();			  
			}
		}
		
		window.onload=function(){
			var typNames = new Array('<bean:message bundle="CUSTOM_STR" key="ass.type3" />',
    		'<bean:message bundle="CUSTOM_STR" key="ass.type4" />','<bean:message bundle="CUSTOM_STR" key="ass.type5" />',
    		'<bean:message bundle="CUSTOM_STR" key="ass.type6" />','<bean:message bundle="CUSTOM_STR" key="ass.type7" />',
    		'<bean:message bundle="CUSTOM_STR" key="ass.type8" />','<bean:message bundle="CUSTOM_STR" key="ass.type9" />',
    		'<bean:message bundle="CUSTOM_STR" key="ass.type10" />','<bean:message bundle="CUSTOM_STR" key="ass.type11" />',
    		'<bean:message bundle="CUSTOM_STR" key="ass.type12" />','<bean:message bundle="CUSTOM_STR" key="ass.type13" />',
    		'<bean:message bundle="CUSTOM_STR" key="ass.type14" />');
			$("assType").add(new Option("",""));
			for(var i=0;i<typNames.length;i++){
				$("assType").add(new Option(typNames[i],i));
			}
		}
  </script> 
</head>
  <body>
  <div class="inputDiv">
  	<form action="assitanceAction.do" method="post" name="create">
  	<input type="hidden" name="op" value="batchSaveAss">
  	<input type="hidden" name="casIds" value="${casIds}">
		<table class="dashTab inputForm" cellpadding="0" cellspacing="0">
			<tbody>
                <tr>
                  <th class="required">协催类型：<span class="red">*</span></th>
                  <td>
                  	<select id="assType" name="assType" class="inputSize2"></select>
                  </td>
                </tr>
            	<tr class="noBorderBot">
                	<th>申请内容：</th>
                    <td>
                   <textarea class="inputSize2L" rows="10" name="content" id="content" onBlur="autoShort(this,2000)"></textarea>
                    </td>

               </tr>
                <tr class="submitTr">
                    <td colspan="2">
                    <input type="button" class="butSize1" id="dosend" value="提交" onClick="check()">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"></td>
                </tr>	
            </tbody>
					
	  </table>
	</form>
  </div>
  </body>
</html>
