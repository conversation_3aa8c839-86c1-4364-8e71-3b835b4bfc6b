package com.frsoft.ccds.common.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 安全工具类测试
 * 
 * <AUTHOR>
 */
public class SecurityUtilsTest {

    @Test
    public void testMd5() {
        // 测试MD5加密
        String input = "123456";
        String expected = "e10adc3949ba59abbe56e057f20f883e"; // 123456的MD5值
        String actual = SecurityUtils.md5(input);
        assertEquals(expected, actual);
        
        // 测试空字符串
        String emptyMd5 = SecurityUtils.md5("");
        assertEquals("d41d8cd98f00b204e9800998ecf8427e", emptyMd5);
    }

    @Test
    public void testSha256() {
        // 测试SHA256加密
        String input = "123456";
        String expected = "8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92"; // 123456的SHA256值
        String actual = SecurityUtils.sha256(input);
        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateSalt() {
        // 测试生成盐值
        String salt1 = SecurityUtils.generateSalt();
        String salt2 = SecurityUtils.generateSalt();
        
        // 盐值不应该为空
        assertNotNull(salt1);
        assertNotNull(salt2);
        assertFalse(salt1.isEmpty());
        assertFalse(salt2.isEmpty());
        
        // 两次生成的盐值应该不同
        assertNotEquals(salt1, salt2);
    }

    @Test
    public void testEncryptPassword() {
        // 测试加盐密码加密
        String password = "123456";
        String salt = "testsalt";
        String encrypted = SecurityUtils.encryptPassword(password, salt);
        
        assertNotNull(encrypted);
        assertFalse(encrypted.isEmpty());
        
        // 相同密码和盐值应该产生相同的加密结果
        String encrypted2 = SecurityUtils.encryptPassword(password, salt);
        assertEquals(encrypted, encrypted2);
        
        // 不同盐值应该产生不同的加密结果
        String encrypted3 = SecurityUtils.encryptPassword(password, "differentsalt");
        assertNotEquals(encrypted, encrypted3);
    }

    @Test
    public void testVerifyPassword() {
        // 测试密码验证
        String password = "123456";
        String salt = "testsalt";
        String encrypted = SecurityUtils.encryptPassword(password, salt);
        
        // 正确密码应该验证通过
        assertTrue(SecurityUtils.verifyPassword(password, salt, encrypted));
        
        // 错误密码应该验证失败
        assertFalse(SecurityUtils.verifyPassword("wrongpassword", salt, encrypted));
        
        // 错误盐值应该验证失败
        assertFalse(SecurityUtils.verifyPassword(password, "wrongsalt", encrypted));
    }

    @Test
    public void testIsAdmin() {
        // 测试管理员判断
        assertTrue(SecurityUtils.isAdmin("admin"));
        assertFalse(SecurityUtils.isAdmin("user"));
        assertFalse(SecurityUtils.isAdmin(""));
        assertFalse(SecurityUtils.isAdmin(null));
    }

    @Test
    public void testMaskMobile() {
        // 测试手机号脱敏
        assertEquals("138****8000", SecurityUtils.maskMobile("13800138000"));
        assertEquals("159****5678", SecurityUtils.maskMobile("15912345678"));
        
        // 测试异常情况
        assertEquals("", SecurityUtils.maskMobile(""));
        assertEquals(null, SecurityUtils.maskMobile(null));
        assertEquals("123", SecurityUtils.maskMobile("123")); // 长度不够11位
    }

    @Test
    public void testMaskIdCard() {
        // 测试身份证号脱敏
        assertEquals("3201**********1234", SecurityUtils.maskIdCard("320101199001011234"));
        assertEquals("3201**********123X", SecurityUtils.maskIdCard("32010119900101123X"));
        
        // 测试异常情况
        assertEquals("", SecurityUtils.maskIdCard(""));
        assertEquals(null, SecurityUtils.maskIdCard(null));
        assertEquals("123", SecurityUtils.maskIdCard("123")); // 长度不够8位
    }

    @Test
    public void testMaskBankCard() {
        // 测试银行卡号脱敏
        assertEquals("6222 **** **** 0123", SecurityUtils.maskBankCard("6222021234567890123"));
        assertEquals("1234 **** **** 5678", SecurityUtils.maskBankCard("****************"));
        
        // 测试异常情况
        assertEquals("", SecurityUtils.maskBankCard(""));
        assertEquals(null, SecurityUtils.maskBankCard(null));
        assertEquals("123", SecurityUtils.maskBankCard("123")); // 长度不够8位
    }

    @Test
    public void testMaskName() {
        // 测试姓名脱敏
        assertEquals("张", SecurityUtils.maskName("张")); // 单字符
        assertEquals("张*", SecurityUtils.maskName("张三")); // 两字符
        assertEquals("张*三", SecurityUtils.maskName("张三丰")); // 三字符
        assertEquals("张**丰", SecurityUtils.maskName("张三四丰")); // 四字符
        assertEquals("张***丰", SecurityUtils.maskName("张三四五丰")); // 五字符
        
        // 测试异常情况
        assertEquals("", SecurityUtils.maskName(""));
        assertEquals(null, SecurityUtils.maskName(null));
    }
}
