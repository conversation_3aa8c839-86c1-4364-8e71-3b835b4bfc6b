<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.CashBusLoanCase" table="cashBusLoanCase" schema="dbo">
        <id name="id" type="java.lang.Long">
            <column name="id"/>
            <generator class="identity"/>
        </id>
        <property name="loanRefId" type="java.lang.String">
            <column name="loanRefId" length="50"/>
        </property>
        <property name="batchName" type="java.lang.String">
            <column name="batchName" />
        </property>
        <property name="userCardNo" type="java.lang.String">
            <column name="userCardNo"/>
        </property>
        <property name="days" type="integer">
            <column name="days" length="20"/>
        </property>
        <property name="originalAmountDue" type="integer">
            <column name="originalAmountDue"/>
        </property>
        <property name="bankName" type="java.lang.String">
            <column name="bankName"/>
        </property>
        <property name="relativeType" type="java.lang.String">
            <column name="relativeType"/>
        </property>
        <property name="relativePhone" type="java.lang.String">
            <column name="relativePhone"/>
        </property>
        <property name="socialType" type="java.lang.String">
            <column name="socialType"/>
        </property>
        <property name="socialPhone" type="java.lang.String">
            <column name="socialPhone"/>
        </property>
        <property name="maritalStatus" type="java.lang.String">
            <column name="maritalStatus"/>
        </property>
        <property name="workCity" type="java.lang.String">
            <column name="workCity"/>
        </property>
        <property name="workPhone" type="java.lang.String">
            <column name="workPhone"/>
        </property>
        <property name="age" type="integer">
            <column name="age"/>
        </property>
        <property name="userPhone" type="java.lang.String">
            <column name="userPhone"/>
        </property>
        <property name="amount" type="integer">
            <column name="amount"/>
        </property>
        <property name="bankCardNo" type="java.lang.String">
            <column name="bankCardNo"/>
        </property>
        <property name="srcJson" type="java.lang.String">
            <column name="srcJson"/>
        </property>
        <property name="name" type="java.lang.String">
            <column name="name" />
        </property>
    </class>
</hibernate-mapping>
