#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSSQL to MySQL SQL转换脚本
处理常见的语法差异和数据类型转换
"""

import re
import os
import sys
import chardet

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def read_sql_file(file_path):
    """读取SQL文件，自动检测编码"""
    encoding = detect_encoding(file_path)
    print(f"检测到文件编码: {encoding}")
    
    # 尝试多种编码
    encodings = [encoding, 'utf-8', 'gbk', 'gb2312', 'utf-16', 'utf-16le', 'utf-16be']
    
    for enc in encodings:
        if enc:
            try:
                with open(file_path, 'r', encoding=enc) as f:
                    content = f.read()
                    print(f"成功使用编码 {enc} 读取文件")
                    return content
            except (UnicodeDecodeError, UnicodeError):
                continue
    
    raise Exception("无法读取文件，尝试了多种编码都失败")

def convert_mssql_to_mysql(sql_content):
    """将MSSQL语法转换为MySQL语法"""

    # 1. 移除MSSQL特有的语句
    sql_content = re.sub(r'SET\s+ANSI_NULLS\s+(ON|OFF)', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'SET\s+QUOTED_IDENTIFIER\s+(ON|OFF)', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'\bGO\b', '', sql_content, flags=re.IGNORECASE)

    # 2. 移除MSSQL的条件创建语法和注释
    sql_content = re.sub(r'/\*\*\*\*\*\*[^*]*\*\*\*\*\*\*/', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'IF\s+NOT\s+EXISTS\s*\([^)]+\)\s*BEGIN', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'\bEND\b(?=\s*(?:GO|$|\n))', '', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'IF\s+NOT\s+EXISTS\s*\([^)]+\)\s*CREATE', 'CREATE', sql_content, flags=re.IGNORECASE)
    
    # 3. 转换数据类型
    # IDENTITY -> AUTO_INCREMENT
    sql_content = re.sub(r'\[(\w+)\]\s+\[bigint\]\s+IDENTITY\(1,1\)\s+NOT\s+NULL', 
                        r'`\1` bigint NOT NULL AUTO_INCREMENT', sql_content, flags=re.IGNORECASE)
    sql_content = re.sub(r'\[(\w+)\]\s+\[int\]\s+IDENTITY\(1,1\)\s+NOT\s+NULL', 
                        r'`\1` int NOT NULL AUTO_INCREMENT', sql_content, flags=re.IGNORECASE)
    
    # 数据类型转换
    type_mappings = {
        r'\[bigint\]': 'bigint',
        r'\[int\]': 'int',
        r'\[varchar\]\((\d+)\)': r'varchar(\1)',
        r'\[nvarchar\]\((\d+)\)': r'varchar(\1)',
        r'\[nvarchar\]\(max\)': 'text',
        r'\[char\]\((\d+)\)': r'char(\1)',
        r'\[nchar\]\((\d+)\)': r'char(\1)',
        r'\[text\]': 'text',
        r'\[ntext\]': 'text',
        r'\[datetime\]': 'datetime',
        r'\[date\]': 'date',
        r'\[time\]': 'time',
        r'\[decimal\]\((\d+),\s*(\d+)\)': r'decimal(\1,\2)',
        r'\[numeric\]\((\d+),\s*(\d+)\)': r'decimal(\1,\2)',
        r'\[money\]': 'decimal(19,4)',
        r'\[smallmoney\]': 'decimal(10,4)',
        r'\[float\]': 'double',
        r'\[real\]': 'float',
        r'\[bit\]': 'tinyint(1)',
        r'\[image\]': 'longblob',
        r'\[varbinary\]\(max\)': 'longblob',
        r'\[varbinary\]\((\d+)\)': r'varbinary(\1)',
        r'\[binary\]\((\d+)\)': r'binary(\1)',
        r'\[uniqueidentifier\]': 'varchar(36)',
        r'\[timestamp\]': 'timestamp'
    }
    
    for mssql_type, mysql_type in type_mappings.items():
        sql_content = re.sub(mssql_type, mysql_type, sql_content, flags=re.IGNORECASE)
    
    # 4. 移除COLLATE子句
    sql_content = re.sub(r'\s+COLLATE\s+\w+', '', sql_content, flags=re.IGNORECASE)
    
    # 5. 转换字段名的方括号为反引号
    sql_content = re.sub(r'\[(\w+)\]', r'`\1`', sql_content)
    
    # 6. 转换表名的方括号为反引号，移除schema前缀
    sql_content = re.sub(r'\[dbo\]\.\[(\w+)\]', r'`\1`', sql_content)
    sql_content = re.sub(r'dbo\.(\w+)', r'\1', sql_content)
    
    # 7. 转换主键约束
    sql_content = re.sub(r'CONSTRAINT\s+`([^`]+)`\s+PRIMARY\s+KEY\s+CLUSTERED\s*\(\s*`(\w+)`\s+ASC\s*\)\s*WITH\s*\([^)]+\)',
                        r'PRIMARY KEY (`\2`)', sql_content, flags=re.IGNORECASE)

    # 8. 转换索引
    sql_content = re.sub(r'CREATE\s+NONCLUSTERED\s+INDEX\s+`([^`]+)`\s+ON\s+`(\w+)`\s*\(\s*`(\w+)`\s+ASC\s*\)\s*WITH\s*\([^)]+\)',
                        r'CREATE INDEX `\1` ON `\2` (`\3`)', sql_content, flags=re.IGNORECASE)

    # 9. 处理IF NOT EXISTS检查索引
    sql_content = re.sub(r'IF\s+NOT\s+EXISTS\s*\([^)]+\)\s*CREATE\s+INDEX', 'CREATE INDEX IF NOT EXISTS', sql_content, flags=re.IGNORECASE)

    # 10. 移除dbo schema前缀
    sql_content = re.sub(r'`dbo`\.`(\w+)`', r'`\1`', sql_content)

    # 11. 处理ENGINE和字符集
    sql_content = re.sub(r'\)\s*$', r') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;', sql_content, flags=re.MULTILINE)
    
    # 12. 添加MySQL特有的设置
    mysql_header = """-- MySQL 8.0 版本的CCDS数据库结构
-- 从MSSQL转换而来

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ccds`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

"""

    mysql_footer = """
SET FOREIGN_KEY_CHECKS = 1;
"""

    # 13. 清理多余的空行和空格
    sql_content = re.sub(r'\n\s*\n\s*\n', '\n\n', sql_content)
    sql_content = re.sub(r'^\s+', '', sql_content, flags=re.MULTILINE)

    # 14. 转换CREATE TABLE语法
    sql_content = re.sub(r'CREATE\s+TABLE\s+`(\w+)`\s*\(',
                        r'DROP TABLE IF EXISTS `\1`;\nCREATE TABLE `\1` (',
                        sql_content, flags=re.IGNORECASE)

    # 15. 移除use ccds语句（因为我们在header中已经有了）
    sql_content = re.sub(r'use\s+ccds\s*;', '', sql_content, flags=re.IGNORECASE)
    
    return mysql_header + sql_content + mysql_footer

def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("使用方法: python mssql_to_mysql_converter.py <输入文件> <输出文件>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        sys.exit(1)
    
    try:
        print(f"正在读取文件: {input_file}")
        sql_content = read_sql_file(input_file)
        
        print("正在转换SQL语法...")
        mysql_content = convert_mssql_to_mysql(sql_content)
        
        print(f"正在写入文件: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(mysql_content)
        
        print("转换完成!")
        print(f"输出文件: {output_file}")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
