<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RShipPro" table="r_ship_pro" schema="dbo" >
        <id name="rshpId" type="java.lang.Long">
            <column name="rshp_id" />
            <generator class="assigned" />
        </id>
        <many-to-one name="wmsShipment" class="com.psit.struts.entity.WmsShipment" fetch="select" not-null="false">
            <column name="rshp_ship_code" length="50" />
        </many-to-one>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rshp_pro_id"/>
        </many-to-one>
    </class>
</hibernate-mapping>
