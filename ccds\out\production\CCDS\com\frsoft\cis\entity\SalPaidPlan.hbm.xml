<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.SalPaidPlan" table="sal_paid_plan" schema="dbo" >
        <id name="spdId" type="java.lang.Long">
            <column name="spd_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="cusCorCus" class="com.frsoft.cis.entity.CusCorCus" fetch="select" not-null="false">
        	<column name="spd_cor_code"/>
        </many-to-one>
        <many-to-one name="salOrdCon" class="com.frsoft.cis.entity.SalOrdCon" fetch="select" not-null="false">
            <column name="spd_ord_code"/>
        </many-to-one>
        <many-to-one name="spdResp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
        	<column name="spd_se_no"/>
        </many-to-one>
        <property name="spdPrmDate" type="java.util.Date">
            <column name="spd_prm_date" length="23" />
        </property>
        <property name="spdPayMon" type="java.lang.Double">
            <column name="spd_pay_mon" precision="18" />
        </property>
        <property name="spdIsp" type="java.lang.String">
            <column name="spd_isp" length="1" />
        </property>
        <property name="spdUserCode" type="java.lang.String">
            <column name="spd_user_code" length="50" />
        </property>
        <property name="spdAltUser" type="java.lang.String">
            <column name="spd_alt_user" length="50" />
        </property>
        <property name="spdCreDate" type="java.util.Date">
            <column name="spd_cre_date" length="23" />
        </property>
        <property name="spdAltDate" type="java.util.Date">
            <column name="spd_alt_date" length="23" />
        </property>
        <property name="spdIsdel" type="java.lang.String">
            <column name="spd_isdel" length="1" />
        </property>
        <property name="spdContent" type="java.lang.String">
            <column name="spd_content" length="200" />
        </property>
    </class>
</hibernate-mapping>
