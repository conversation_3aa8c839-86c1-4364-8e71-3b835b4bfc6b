package com.frsoft.ccds.common.utils;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 缓存工具类
 * 简单的内存缓存实现，支持过期时间
 * 
 * <AUTHOR>
 */
@Component
public class CacheUtils {
    
    /** 缓存存储 */
    private static final ConcurrentHashMap<String, CacheItem> CACHE_MAP = new ConcurrentHashMap<>();
    
    /** 定时清理器 */
    private static final ScheduledExecutorService SCHEDULER = Executors.newScheduledThreadPool(1);
    
    static {
        // 每分钟清理一次过期缓存
        SCHEDULER.scheduleAtFixedRate(CacheUtils::clearExpired, 1, 1, TimeUnit.MINUTES);
    }
    
    /**
     * 缓存项
     */
    private static class CacheItem {
        private final Object value;
        private final long expireTime;
        
        public CacheItem(Object value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }
        
        public Object getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return expireTime > 0 && System.currentTimeMillis() > expireTime;
        }
    }
    
    /**
     * 存储缓存（永不过期）
     * 
     * @param key 缓存键
     * @param value 缓存值
     */
    public static void put(String key, Object value) {
        put(key, value, 0);
    }
    
    /**
     * 存储缓存（指定过期时间）
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param expireSeconds 过期时间（秒），0表示永不过期
     */
    public static void put(String key, Object value, long expireSeconds) {
        if (StringUtils.isEmpty(key)) {
            return;
        }
        
        long expireTime = expireSeconds > 0 ? System.currentTimeMillis() + expireSeconds * 1000 : 0;
        CACHE_MAP.put(key, new CacheItem(value, expireTime));
    }
    
    /**
     * 获取缓存
     * 
     * @param key 缓存键
     * @return 缓存值
     */
    public static Object get(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        
        CacheItem item = CACHE_MAP.get(key);
        if (item == null) {
            return null;
        }
        
        if (item.isExpired()) {
            CACHE_MAP.remove(key);
            return null;
        }
        
        return item.getValue();
    }
    
    /**
     * 获取缓存（指定类型）
     * 
     * @param key 缓存键
     * @param clazz 值类型
     * @param <T> 泛型类型
     * @return 缓存值
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key, Class<T> clazz) {
        Object value = get(key);
        if (value == null) {
            return null;
        }
        
        if (clazz.isInstance(value)) {
            return (T) value;
        }
        
        return null;
    }
    
    /**
     * 删除缓存
     * 
     * @param key 缓存键
     */
    public static void remove(String key) {
        if (StringUtils.isNotEmpty(key)) {
            CACHE_MAP.remove(key);
        }
    }
    
    /**
     * 删除多个缓存
     * 
     * @param keys 缓存键数组
     */
    public static void remove(String... keys) {
        if (keys != null) {
            for (String key : keys) {
                remove(key);
            }
        }
    }
    
    /**
     * 清空所有缓存
     */
    public static void clear() {
        CACHE_MAP.clear();
    }
    
    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    public static boolean exists(String key) {
        return get(key) != null;
    }
    
    /**
     * 获取缓存大小
     * 
     * @return 缓存项数量
     */
    public static int size() {
        return CACHE_MAP.size();
    }
    
    /**
     * 清理过期缓存
     */
    private static void clearExpired() {
        CACHE_MAP.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }
    
    /**
     * 获取或设置缓存
     * 如果缓存不存在，则执行supplier并缓存结果
     * 
     * @param key 缓存键
     * @param supplier 值提供者
     * @param expireSeconds 过期时间（秒）
     * @param <T> 泛型类型
     * @return 缓存值
     */
    @SuppressWarnings("unchecked")
    public static <T> T getOrSet(String key, java.util.function.Supplier<T> supplier, long expireSeconds) {
        T value = (T) get(key);
        if (value == null && supplier != null) {
            value = supplier.get();
            if (value != null) {
                put(key, value, expireSeconds);
            }
        }
        return value;
    }
    
    /**
     * 获取或设置缓存（永不过期）
     * 
     * @param key 缓存键
     * @param supplier 值提供者
     * @param <T> 泛型类型
     * @return 缓存值
     */
    public static <T> T getOrSet(String key, java.util.function.Supplier<T> supplier) {
        return getOrSet(key, supplier, 0);
    }
    
    /**
     * 刷新缓存
     * 删除旧缓存并设置新值
     * 
     * @param key 缓存键
     * @param value 新值
     * @param expireSeconds 过期时间（秒）
     */
    public static void refresh(String key, Object value, long expireSeconds) {
        remove(key);
        put(key, value, expireSeconds);
    }
    
    /**
     * 刷新缓存（永不过期）
     * 
     * @param key 缓存键
     * @param value 新值
     */
    public static void refresh(String key, Object value) {
        refresh(key, value, 0);
    }
}
