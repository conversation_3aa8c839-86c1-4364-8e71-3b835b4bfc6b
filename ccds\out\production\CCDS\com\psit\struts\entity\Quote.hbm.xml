<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.Quote" table="quote" schema="dbo" >
        <id name="quoId" type="java.lang.Long">
            <column name="quo_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salOpp" class="com.frsoft.cis.entity.SalOpp" fetch="select" not-null="false">
            <column name="quo_opp_id" />
        </many-to-one>
        <many-to-one name="project" class="com.psit.struts.entity.Project" fetch="select" not-null="false">
            <column name="quo_pro_id" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="quo_se_no" />
        </many-to-one>
        <property name="quoTitle" type="java.lang.String">
            <column name="quo_title" length="100" />
        </property>
        <property name="quoPrice" type="java.lang.Double">
            <column name="quo_price" precision="18" />
        </property>
        <property name="quoInpUser" type="java.lang.String">
            <column name="quo_inp_user" length="50" />
        </property>
        <property name="quoUpdUser" type="java.lang.String">
            <column name="quo_upd_user" length="50" />
        </property>
        <property name="quoIsDel" type="java.lang.String">
            <column name="quo_isdel" length="1" />
        </property>
        <property name="quoUpdDate" type="java.util.Date">
            <column name="quo_upd_date" length="23" />
        </property>
        <property name="quoDesc" type="java.lang.String">
            <column name="quo_desc" length="1073741823" />
        </property>
        <property name="quoRemark" type="java.lang.String">
            <column name="quo_remark" length="1073741823" />
        </property>
        <property name="quoDate" type="java.util.Date">
            <column name="quo_date" length="23" />
        </property>
        <property name="quoInsDate" type="java.util.Date">
            <column name="quo_ins_date" length="23" />
        </property>
        <set name="attachments" inverse="true"  cascade="all"  where="att_type='quo'">
            <key>
                <column name="att_fk_id"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
        <set name="rquoPros" inverse="true"  cascade="all" >
            <key>
                <column name="rup_quo_id"/>
            </key>
            <one-to-many class="com.psit.struts.entity.RQuoPro" />
        </set>
    </class>
</hibernate-mapping>
