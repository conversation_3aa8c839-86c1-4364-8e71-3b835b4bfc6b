package com.frsoft.ccds.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志工具类
 * 
 * <AUTHOR>
 */
public class LogUtils {
    
    /** 系统日志 */
    private static final Logger SYS_LOG = LoggerFactory.getLogger("sys");
    
    /** 业务日志 */
    private static final Logger BIZ_LOG = LoggerFactory.getLogger("biz");
    
    /** 性能日志 */
    private static final Logger PERF_LOG = LoggerFactory.getLogger("perf");
    
    /** 错误日志 */
    private static final Logger ERROR_LOG = LoggerFactory.getLogger("error");
    
    /**
     * 记录系统日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void sys(String message, Object... args) {
        SYS_LOG.info(message, args);
    }
    
    /**
     * 记录系统调试日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void sysDebug(String message, Object... args) {
        SYS_LOG.debug(message, args);
    }
    
    /**
     * 记录系统警告日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void sysWarn(String message, Object... args) {
        SYS_LOG.warn(message, args);
    }
    
    /**
     * 记录系统错误日志
     * 
     * @param message 日志消息
     * @param throwable 异常
     */
    public static void sysError(String message, Throwable throwable) {
        SYS_LOG.error(message, throwable);
    }
    
    /**
     * 记录业务日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void biz(String message, Object... args) {
        BIZ_LOG.info(message, args);
    }
    
    /**
     * 记录业务调试日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void bizDebug(String message, Object... args) {
        BIZ_LOG.debug(message, args);
    }
    
    /**
     * 记录业务警告日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void bizWarn(String message, Object... args) {
        BIZ_LOG.warn(message, args);
    }
    
    /**
     * 记录业务错误日志
     * 
     * @param message 日志消息
     * @param throwable 异常
     */
    public static void bizError(String message, Throwable throwable) {
        BIZ_LOG.error(message, throwable);
    }
    
    /**
     * 记录性能日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void perf(String message, Object... args) {
        PERF_LOG.info(message, args);
    }
    
    /**
     * 记录性能调试日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void perfDebug(String message, Object... args) {
        PERF_LOG.debug(message, args);
    }
    
    /**
     * 记录错误日志
     * 
     * @param message 日志消息
     * @param throwable 异常
     */
    public static void error(String message, Throwable throwable) {
        ERROR_LOG.error(message, throwable);
    }
    
    /**
     * 记录错误日志
     * 
     * @param message 日志消息
     * @param args 参数
     */
    public static void error(String message, Object... args) {
        ERROR_LOG.error(message, args);
    }
    
    /**
     * 记录用户操作日志
     * 
     * @param userCode 用户编码
     * @param operation 操作
     * @param target 操作目标
     * @param result 操作结果
     */
    public static void userOperation(String userCode, String operation, String target, String result) {
        biz("用户操作 - 用户:{}, 操作:{}, 目标:{}, 结果:{}", userCode, operation, target, result);
    }
    
    /**
     * 记录登录日志
     * 
     * @param userCode 用户编码
     * @param ip IP地址
     * @param success 是否成功
     * @param message 消息
     */
    public static void login(String userCode, String ip, boolean success, String message) {
        if (success) {
            sys("用户登录成功 - 用户:{}, IP:{}, 消息:{}", userCode, ip, message);
        } else {
            sysWarn("用户登录失败 - 用户:{}, IP:{}, 消息:{}", userCode, ip, message);
        }
    }
    
    /**
     * 记录登出日志
     * 
     * @param userCode 用户编码
     * @param ip IP地址
     */
    public static void logout(String userCode, String ip) {
        sys("用户登出 - 用户:{}, IP:{}", userCode, ip);
    }
    
    /**
     * 记录数据库操作日志
     * 
     * @param operation 操作类型
     * @param table 表名
     * @param id 记录ID
     * @param userCode 操作用户
     */
    public static void dbOperation(String operation, String table, Object id, String userCode) {
        biz("数据库操作 - 操作:{}, 表:{}, ID:{}, 用户:{}", operation, table, id, userCode);
    }
    
    /**
     * 记录API调用日志
     * 
     * @param method HTTP方法
     * @param url 请求URL
     * @param userCode 用户编码
     * @param ip IP地址
     * @param duration 耗时（毫秒）
     */
    public static void apiCall(String method, String url, String userCode, String ip, long duration) {
        perf("API调用 - 方法:{}, URL:{}, 用户:{}, IP:{}, 耗时:{}ms", method, url, userCode, ip, duration);
    }
    
    /**
     * 记录SQL执行日志
     * 
     * @param sql SQL语句
     * @param duration 耗时（毫秒）
     */
    public static void sqlExecution(String sql, long duration) {
        if (duration > 1000) { // 超过1秒的慢SQL
            perfDebug("慢SQL - 耗时:{}ms, SQL:{}", duration, sql);
        } else {
            perfDebug("SQL执行 - 耗时:{}ms, SQL:{}", duration, sql);
        }
    }
    
    /**
     * 记录缓存操作日志
     * 
     * @param operation 操作类型
     * @param key 缓存键
     * @param hit 是否命中
     */
    public static void cacheOperation(String operation, String key, boolean hit) {
        perfDebug("缓存操作 - 操作:{}, 键:{}, 命中:{}", operation, key, hit);
    }
}
