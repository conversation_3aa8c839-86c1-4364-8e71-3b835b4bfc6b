<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>

<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>新增PTP记录</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>

	<script type="text/javascript" >
    	function check(){
			var errStr = "";
			if(isEmpty("ptpNum")){
				errStr+="- 未填写PTP金额！\n";
			 }
			if(isEmpty("endTim")){
				errStr+="- 未选择PTP时间！\n";
			 }
			 if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			 else{
				waitSubmit("save","保存中...");
				waitSubmit("doCancel");
				return $("create").submit();
			 }
		}

  </script></head>
  
  <body>

  <div class="inputDiv">
  	<form id="create" action="caseAction.do" method="post">
  		<input type="hidden" name="op" value="saveNewPTP" />
  		  <input type="hidden" name="casId"  value="${casId}"/>
        <table class="dashTab inputForm single" cellpadding="0" cellspacing="0">
            <tbody>
                <tr>
                    <th class="required">PTP金额：<span class="red">*</span></th>
                    <td><input class="inputSize2" type="text" name="ptpNum" onBlur="checkPrice(this)"/></td>
                </tr>
                <tr class="noBorderBot">
                    <th class="required">PTP时间：<span class="red">*</span></th>
                    <td><input name="endTim" id="endTim" value="${endTime}" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:85px;" readonly="readonly" ondblClick="clearInput(this)" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/></td>
                </tr>	
                <tr class="submitTr">
                    <td colspan="2">
                    <input id="save" class="butSize1" type="button" value="保存" onClick="check()" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                    </td>
                </tr>
            </tbody>
        </table>
	</form>
    </div>
  </body>
</html>
