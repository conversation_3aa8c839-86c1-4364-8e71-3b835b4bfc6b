<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.SalPaidPast" table="sal_paid_past" schema="dbo" >
        <id name="spsId" type="java.lang.Long">
            <column name="sps_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salOrdCon" class="com.frsoft.cis.entity.SalOrdCon" fetch="select" not-null="false">
            <column name="sps_ord_code" length="50" />
        </many-to-one>
        <many-to-one name="salPaidType" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="sps_type_id" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
        	<column name="sps_se_no" length="50"/>
        </many-to-one>
        <many-to-one name="account" class="com.psit.struts.entity.Account" fetch="select" not-null="false">
        	<column name="sps_aco_id"/>
        </many-to-one>
        <many-to-one name="typeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
        	<column name="sps_acc_type_id"/>
        </many-to-one>
        <many-to-one name="cusCorCus" class="com.frsoft.cis.entity.CusCorCus" fetch="select" not-null="false">
        	<column name="sps_cus_id"></column>
        </many-to-one>
        <property name="spsContent" type="java.lang.String">
        	<column name="sps_content" length="100"/>
        </property>
        <property name="spsFctDate" type="java.util.Date">
            <column name="sps_fct_date" length="23" />
        </property>
        <property name="spsCount" type="java.lang.Integer">
            <column name="sps_count" />
        </property>
        <property name="spsPayType" type="java.lang.String">
            <column name="sps_pay_type" length="50" />
        </property>
        <property name="spsPayMon" type="java.lang.Double">
            <column name="sps_pay_mon" precision="18" />
        </property>
        <property name="spsIsinv" type="java.lang.String">
            <column name="sps_isinv" length="1" />
        </property>
        <property name="spsRemark" type="java.lang.String">
            <column name="sps_remark" length="1073741823" />
        </property>
        <property name="spsUserCode" type="java.lang.String">
            <column name="sps_user_code" length="50" />
        </property>
        <property name="spsAltUser" type="java.lang.String">
            <column name="sps_alt_user" length="50" />
        </property>
        <property name="spsCreDate" type="java.util.Date">
            <column name="sps_cre_date" length="23" />
        </property>
        <property name="spsAltDate" type="java.util.Date">
            <column name="sps_alt_date" length="23" />
        </property>
        <property name="spsIsdel" type="java.lang.String">
            <column name="sps_isdel" length="1" />
        </property>
        <property name="spsCode" type="java.lang.String">
            <column name="sps_code" length="300" />
        </property>
        <property name="spsOutName" type="java.lang.String">
            <column name="sps_out_name" length="100" />
        </property>
        <property name="spsUndoDate" type="java.util.Date">
        	<column name="sps_undo_date" length="23"/>
        </property>
        <property name="spsUndoUser" type="java.lang.String">
        	<column name="sps_undo_user" length="50"/>
        </property>
    </class>
</hibernate-mapping>
