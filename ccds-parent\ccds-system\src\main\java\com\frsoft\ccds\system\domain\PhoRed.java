package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 催收记录实体类
 * 对应数据库表: pho_red
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class PhoRed {
    
    /** 记录ID */
    private Long prId;
    
    /** 类型ID */
    private Long prTypId;
    
    /** 联系人 */
    private String prContact;
    
    /** 案件ID */
    private Long prCasId;
    
    /** 催收内容 */
    private String prContent;
    
    /** 催收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date prTime;
    
    /** 员工编号 */
    private Long prSeNo;
    
    /** 联系方式 */
    private String prConType;
    
    /** 催收结果 */
    private String prResult;
    
    /** 下次联系时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date prNextTime;
    
    /** 备注 */
    private String prRemark;
    
    /** 是否删除 */
    private String prIsdel;
    
    // 关联对象
    /** 案件信息 */
    private BankCase bankCase;
    
    /** 员工信息 */
    private SalEmp salEmp;
    
    /** 类型信息 */
    private TypeList typeList;
}
