package com.psit.struts.DAO.impl;

import org.apache.log4j.Logger;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import java.sql.SQLException;
import java.util.List;

public class TemplateReward<T> extends HibernateDaoSupport {

    protected Logger log = Logger.getLogger("transMsgLog");;

    public List<T> find(final String sql, final Object[] params, final int offset, final int limit) {
        return (List<T>)this.getHibernateTemplate().executeFind(new HibernateCallback() {
            @Override
            public Object doInHibernate(org.hibernate.Session session) throws HibernateException, SQLException {
                log.debug(String.format("执行SQL   %s", sql));
                Query query = session.createQuery(sql);
                for (int index = 0; index < params.length; ++index) {
                    query.setParameter(index, params[index]);
                }
                query.setMaxResults(limit);
                query.setFirstResult(offset);
                return query.list();
            }
        });
    }

    public long countFind(final String fromSql, final Object[] params) {
        String sql = String.format("select count(id) %s", fromSql);
        log.debug(String.format("执行SQL   %s", sql));
        Query query = getSession().createQuery(sql);
        for (int index = 0; index < params.length; ++index) {
            query.setParameter(index, params[index]);
        }
        return (Long)query.uniqueResult();
    }

}
