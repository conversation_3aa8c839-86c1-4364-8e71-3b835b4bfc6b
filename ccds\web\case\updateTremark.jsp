<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>    
    <title>更新催收小结</title>
    <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	 <script type="text/javascript" src="js/common.js"></script>
	 <script type="text/javascript">
	   /* funtion load(){
			var str = parent.document.getElementById('tremardSpan').innerHTML;
			$('remark').innerHTML=str;
		}
		 
		window.onload=function(){
			  load();
		}
	 */
	 
	    function check(){
			var errStr = "";
		 	if(isEmpty("tremark")&&checkLength("tremark",300)){
				errStr+="- 催收小结不能超过300个字！\n";
		 	}
         	if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
		 	}
		   	else{
				waitSubmit("save","保存中...");
				waitSubmit("doCancel");
				return $("remarkForm").submit();
		 	}
   		}
	 </script>
</head>
  <body>
  <div class="inputDiv">
  	<form action="caseAction.do" method="post" id="remarkForm">
  		<input type="hidden" name="op" value="saveTremark">
  	 	<input type="hidden" name="casId"  value="${casId}"/>
		<table class="dashTab inputForm" cellpadding="0" cellspacing="0">
			<tbody>
                <tr class="noBorderBot">
                    <th>催收小结：</th>
                    <td colspan="3"><textarea class="inputSize2L" rows="10" id="tremark" name="tremark" onBlur="autoShort(this,300)">${tremark}</textarea></td>
                </tr>
                <tr class="submitTr">
                    <td colspan="4">
                    <input type="button" class="butSize1" id="save" value="保存" onClick="check()">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"></td>
                </tr>	
            </tbody>					
	  </table>
	</form>
  </div>
  </body>
</html>
