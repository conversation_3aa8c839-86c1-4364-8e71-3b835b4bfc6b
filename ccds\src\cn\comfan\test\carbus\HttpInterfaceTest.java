package cn.comfan.test.carbus;

import com.frsoft.ccds.util.CashBusFacade;
import com.frsoft.ccds.util.HttpsUtils;
import net.sf.json.JSONObject;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.FileSystemXmlApplicationContext;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by comfa on 2017/7/18.
 */
public class HttpInterfaceTest {
    public static void main(String[] args){
        ApplicationContext cx=new FileSystemXmlApplicationContext("E:\\DevlopFoler\\JavaProject\\CCDS\\web\\WEB-INF\\acxtest.xml");
        CashBusFacade cashBusFacade = (CashBusFacade) cx.getBean("cashBusFacade");
        JSONObject allBatchs = cashBusFacade.getAllBatchs();
        System.out.println(allBatchs);
    }
}
