<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.AccLine" table="acc_line" schema="dbo" >
        <id name="aclId" type="java.lang.Long">
            <column name="acl_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="account" class="com.psit.struts.entity.Account" fetch="select" not-null="false">
        	<column name="acl_aco_id" />
        </many-to-one>
        <property name="aclType" type="java.lang.String">
            <column name="acl_type" length="1" />
        </property>
        <property name="aclNoteId" type="java.lang.Long">
            <column name="acl_note_Id" />
        </property>
        <property name="aclMon" type="java.lang.Double">
            <column name="acl_mon" precision="18" />
        </property>
        <property name="aclCurMon" type="java.lang.Double">
            <column name="acl_cur_mon" precision="18" />
        </property>
        <property name="aclCreDate" type="java.util.Date">
            <column name="acl_cre_date" length="23" />
        </property>
        <property name="aclIsInv" type="java.lang.String">
            <column name="acl_isInv" length="1" />
        </property>
        <property name="aclUser" type="java.lang.String">
        	<column name="acl_user" length="50"/>
        </property>
        <property name="aclContent" type="java.lang.String">
        	<column name="acl_content" length="100"/>
        </property>
        <property name="aclOther" type="java.lang.String">
        	<column name="acl_other" length="100"/>
        </property>
        
    </class>
</hibernate-mapping>
