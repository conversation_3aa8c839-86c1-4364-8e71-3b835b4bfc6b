<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.LoanSeason" table="loan_season" schema="dbo" >
        <id name="lseId" type="java.lang.Long">
            <column name="lse_id" />
            <generator class="identity" />
        </id>
        <property name="lseName" type="java.lang.String">
            <column name="lse_name" length="50" />
        </property>
        <set name="loanCuses" inverse="true">
            <key>
                <column name="lc_cse_id" />
            </key>
            <one-to-many class="com.frsoft.loan.entity.LoanCus" />
        </set>
    </class>
</hibernate-mapping>
