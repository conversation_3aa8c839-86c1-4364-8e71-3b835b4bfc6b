<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.loan.entity.LoanPer" table="loan_per" schema="dbo" >
        <id name="lpId" type="java.lang.Long">
            <column name="lp_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="lpLoanSeason" class="com.frsoft.loan.entity.LoanSeason" fetch="select">
            <column name="lp_lse_id" />
        </many-to-one>
        <property name="lpName" type="java.lang.String">
            <column name="lp_name" length="50" />
        </property>
        <property name="lpCardNum" type="java.lang.String">
            <column name="lp_card_num" length="50" />
        </property>
        <property name="lpContent" type="java.lang.String">
            <column name="lp_content" length="1073741823" />
        </property>
        <property name="lpInsUser" type="java.lang.String">
            <column name="lp_ins_user" length="25" />
        </property>
        <property name="lpInsTime" type="java.util.Date">
            <column name="lp_ins_time" length="23" />
        </property>
        <property name="lpAltUser" type="java.lang.String">
            <column name="lp_alt_user" length="25" />
        </property>
        <property name="lpAltTime" type="java.util.Date">
            <column name="lp_alt_time" length="23" />
        </property>
    </class>
</hibernate-mapping>
