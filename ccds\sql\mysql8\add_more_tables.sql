-- 添加更多重要表到CCDS数据库
-- 继续添加剩余的重要业务表

USE `ccds`;

-- 47. 销售发货表
DROP TABLE IF EXISTS `sal_ship`;
CREATE TABLE `sal_ship` (
  `ssh_id` bigint NOT NULL AUTO_INCREMENT,
  `ssh_ord_code` bigint DEFAULT NULL,
  `ssh_code` varchar(300) DEFAULT NULL,
  `ssh_date` datetime DEFAULT NULL,
  `ssh_state` varchar(50) DEFAULT NULL,
  `ssh_remark` longtext,
  `ssh_user_code` varchar(50) DEFAULT NULL,
  `ssh_se_no` bigint DEFAULT NULL,
  `ssh_cre_date` datetime DEFAULT NULL,
  `ssh_alt_date` datetime DEFAULT NULL,
  `ssh_alt_user` varchar(50) DEFAULT NULL,
  `ssh_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`ssh_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 48. 采购订单表
DROP TABLE IF EXISTS `spo_ord_con`;
CREATE TABLE `spo_ord_con` (
  `spo_id` bigint NOT NULL AUTO_INCREMENT,
  `spo_code` varchar(300) DEFAULT NULL,
  `spo_title` varchar(300) DEFAULT NULL,
  `spo_type_id` bigint DEFAULT NULL,
  `spo_ssu_id` bigint DEFAULT NULL,
  `spo_sum_mon` decimal(18,2) DEFAULT NULL,
  `spo_paid_mon` decimal(18,2) DEFAULT NULL,
  `spo_mon_type` varchar(50) DEFAULT NULL,
  `spo_state` varchar(10) DEFAULT NULL,
  `spo_ship_state` varchar(10) DEFAULT NULL,
  `spo_own_code` varchar(50) DEFAULT NULL,
  `spo_deadline` datetime DEFAULT NULL,
  `spo_end_date` datetime DEFAULT NULL,
  `spo_ord_date` datetime DEFAULT NULL,
  `spo_inp_date` datetime DEFAULT NULL,
  `spo_isfail` char(1) DEFAULT NULL,
  `spo_remark` longtext,
  `spo_change_date` datetime DEFAULT NULL,
  `spo_paid_method` varchar(20) DEFAULT NULL,
  `spo_inp_code` varchar(50) DEFAULT NULL,
  `spo_ssu_con` varchar(100) DEFAULT NULL,
  `spo_se_no` bigint DEFAULT NULL,
  `spo_con_date` datetime DEFAULT NULL,
  `spo_change_user` varchar(50) DEFAULT NULL,
  `spo_app_date` datetime DEFAULT NULL,
  `spo_app_man` varchar(50) DEFAULT NULL,
  `spo_app_desc` longtext,
  `spo_app_isok` char(1) DEFAULT NULL,
  `spo_content` longtext,
  PRIMARY KEY (`spo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 49. 采购付款计划表
DROP TABLE IF EXISTS `spo_paid_plan`;
CREATE TABLE `spo_paid_plan` (
  `spp_id` bigint NOT NULL AUTO_INCREMENT,
  `spp_spo_id` bigint DEFAULT NULL,
  `spp_prm_date` datetime DEFAULT NULL,
  `spp_count` int DEFAULT NULL,
  `spp_pay_mon` decimal(18,2) DEFAULT NULL,
  `spp_mon_type` varchar(50) DEFAULT NULL,
  `spp_user_code` varchar(50) DEFAULT NULL,
  `spp_isp` char(1) DEFAULT NULL,
  `spp_resp` varchar(50) DEFAULT NULL,
  `spp_cre_date` datetime DEFAULT NULL,
  `spp_alt_date` datetime DEFAULT NULL,
  `spp_alt_user` varchar(50) DEFAULT NULL,
  `spp_isdel` char(1) DEFAULT NULL,
  `spp_content` varchar(100) DEFAULT NULL,
  `spp_ssu_id` bigint DEFAULT NULL,
  PRIMARY KEY (`spp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 50. 采购发货表
DROP TABLE IF EXISTS `spo_ship`;
CREATE TABLE `spo_ship` (
  `sps_id` bigint NOT NULL AUTO_INCREMENT,
  `sps_spo_id` bigint DEFAULT NULL,
  `sps_code` varchar(300) DEFAULT NULL,
  `sps_date` datetime DEFAULT NULL,
  `sps_state` varchar(50) DEFAULT NULL,
  `sps_remark` longtext,
  `sps_user_code` varchar(50) DEFAULT NULL,
  `sps_se_no` bigint DEFAULT NULL,
  `sps_cre_date` datetime DEFAULT NULL,
  `sps_alt_date` datetime DEFAULT NULL,
  `sps_alt_user` varchar(50) DEFAULT NULL,
  `sps_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`sps_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 51. 仓库管理表
DROP TABLE IF EXISTS `wms_manage`;
CREATE TABLE `wms_manage` (
  `wma_id` bigint NOT NULL AUTO_INCREMENT,
  `wma_type` varchar(50) DEFAULT NULL,
  `wma_code` varchar(300) DEFAULT NULL,
  `wma_date` datetime DEFAULT NULL,
  `wma_state` varchar(50) DEFAULT NULL,
  `wma_remark` longtext,
  `wma_user_code` varchar(50) DEFAULT NULL,
  `wma_se_no` bigint DEFAULT NULL,
  `wma_cre_date` datetime DEFAULT NULL,
  `wma_alt_date` datetime DEFAULT NULL,
  `wma_alt_user` varchar(50) DEFAULT NULL,
  `wma_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`wma_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 52. 产品单位表
DROP TABLE IF EXISTS `wms_unit`;
CREATE TABLE `wms_unit` (
  `wun_id` bigint NOT NULL AUTO_INCREMENT,
  `wun_name` varchar(50) DEFAULT NULL,
  `wun_desc` longtext,
  `wun_isenabled` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`wun_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 53. 产品类型表
DROP TABLE IF EXISTS `wms_pro_type`;
CREATE TABLE `wms_pro_type` (
  `wpt_id` bigint NOT NULL AUTO_INCREMENT,
  `wpt_name` varchar(50) DEFAULT NULL,
  `wpt_desc` longtext,
  `wpt_isenabled` varchar(10) DEFAULT NULL,
  `wpt_up_id` bigint DEFAULT NULL,
  PRIMARY KEY (`wpt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 54. 客户来源表
DROP TABLE IF EXISTS `cus_source`;
CREATE TABLE `cus_source` (
  `sou_id` bigint NOT NULL AUTO_INCREMENT,
  `sou_name` varchar(100) DEFAULT NULL,
  `sou_desc` longtext,
  `sou_isenabled` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`sou_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 55. 客户行业表
DROP TABLE IF EXISTS `cus_industry`;
CREATE TABLE `cus_industry` (
  `ind_id` bigint NOT NULL AUTO_INCREMENT,
  `ind_name` varchar(100) DEFAULT NULL,
  `ind_desc` longtext,
  `ind_isenabled` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`ind_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 56. 客户联系人表
DROP TABLE IF EXISTS `cus_contact`;
CREATE TABLE `cus_contact` (
  `con_id` bigint NOT NULL AUTO_INCREMENT,
  `con_cor_code` bigint DEFAULT NULL,
  `con_name` varchar(100) DEFAULT NULL,
  `con_sex` varchar(10) DEFAULT NULL,
  `con_pos` varchar(50) DEFAULT NULL,
  `con_phone` varchar(50) DEFAULT NULL,
  `con_mobile` varchar(50) DEFAULT NULL,
  `con_email` varchar(100) DEFAULT NULL,
  `con_qq` varchar(50) DEFAULT NULL,
  `con_msn` varchar(50) DEFAULT NULL,
  `con_address` varchar(500) DEFAULT NULL,
  `con_remark` longtext,
  `con_ins_date` datetime DEFAULT NULL,
  `con_upd_date` datetime DEFAULT NULL,
  `con_inp_user` varchar(50) DEFAULT NULL,
  `con_upd_user` varchar(50) DEFAULT NULL,
  `con_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`con_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 57. 销售发票表
DROP TABLE IF EXISTS `sal_invoice`;
CREATE TABLE `sal_invoice` (
  `sin_id` bigint NOT NULL AUTO_INCREMENT,
  `sin_ord_code` bigint DEFAULT NULL,
  `sin_con` longtext,
  `sin_type` bigint DEFAULT NULL,
  `sin_mon` decimal(18,2) DEFAULT NULL,
  `sin_date` datetime DEFAULT NULL,
  `sin_remark` longtext,
  `sin_code` varchar(100) DEFAULT NULL,
  `sin_isPaid` char(1) DEFAULT NULL,
  `sin_isPlaned` varchar(50) DEFAULT NULL,
  `sin_user_code` varchar(50) DEFAULT NULL,
  `sin_resp` varchar(50) DEFAULT NULL,
  `sin_mon_type` varchar(50) DEFAULT NULL,
  `sin_alt_user` varchar(50) DEFAULT NULL,
  `sin_cre_date` datetime DEFAULT NULL,
  `sin_alt_date` datetime DEFAULT NULL,
  `sin_isdel` char(1) DEFAULT NULL,
  `sin_spo_id` bigint DEFAULT NULL,
  `sin_isrecieve` char(1) DEFAULT NULL,
  PRIMARY KEY (`sin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 58. 日程安排表
DROP TABLE IF EXISTS `schedule`;
CREATE TABLE `schedule` (
  `sch_id` bigint NOT NULL AUTO_INCREMENT,
  `sch_type` bigint DEFAULT NULL,
  `sch_title` varchar(100) DEFAULT NULL,
  `sch_start_date` datetime DEFAULT NULL,
  `sch_se_no` bigint DEFAULT NULL,
  `sch_start_time` varchar(50) DEFAULT NULL,
  `sch_date` datetime DEFAULT NULL,
  `sch_state` varchar(50) DEFAULT NULL,
  `sch_end_time` varchar(50) DEFAULT NULL,
  `sch_ins_user` varchar(50) DEFAULT NULL,
  `sch_upd_user` varchar(50) DEFAULT NULL,
  `sch_upd_date` datetime DEFAULT NULL,
  PRIMARY KEY (`sch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 59. 销售任务表
DROP TABLE IF EXISTS `sal_task`;
CREATE TABLE `sal_task` (
  `st_id` bigint NOT NULL AUTO_INCREMENT,
  `st_title` varchar(200) DEFAULT NULL,
  `st_se_no` bigint DEFAULT NULL,
  `st_name` varchar(50) DEFAULT NULL,
  `st_rel_date` datetime DEFAULT NULL,
  `st_fin_date` datetime DEFAULT NULL,
  `st_lev` varchar(50) DEFAULT NULL,
  `st_cyc` varchar(50) DEFAULT NULL,
  `st_type_id` bigint DEFAULT NULL,
  `st_stu` char(1) DEFAULT NULL,
  `st_mon` decimal(18,2) DEFAULT NULL,
  `st_tag` longtext,
  `st_remark` longtext,
  `st_change_date` datetime DEFAULT NULL,
  `st_log` longtext,
  `st_isdel` char(1) DEFAULT NULL,
  `st_fct_date` datetime DEFAULT NULL,
  `st_upd_user` varchar(50) DEFAULT NULL,
  `st_start_date` datetime DEFAULT NULL,
  PRIMARY KEY (`st_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 60. 任务权限表
DROP TABLE IF EXISTS `ta_lim`;
CREATE TABLE `ta_lim` (
  `ta_lim_id` bigint NOT NULL AUTO_INCREMENT,
  `ta_se_no` bigint DEFAULT NULL,
  `ta_isdel` char(1) DEFAULT NULL,
  `ta_task_id` bigint DEFAULT NULL,
  `ta_fin_date` datetime DEFAULT NULL,
  `ta_isfin` char(1) DEFAULT NULL,
  `ta_desc` longtext,
  `ta_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ta_lim_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

SELECT 'CCDS数据库继续扩展完成 - 添加了更多业务表' as message;
