<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.RQuoPro" table="r_quo_pro" schema="dbo" >
    	 <id name="rupId" type="java.lang.Long">
            <column name="rup_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="wmsProduct" class="com.frsoft.cis.entity.WmsProduct" fetch="select" not-null="false">
            <column name="rup_wpr_id"/>
        </many-to-one>
        <many-to-one name="quote" class="com.psit.struts.entity.Quote" fetch="select" not-null="false">
            <column name="rup_quo_id"/>
        </many-to-one>
        <property name="rupNum" type="java.lang.Double">
            <column name="rup_num" precision="18" />
        </property>
       
        <property name="rupPrice" type="java.lang.Double">
            <column name="rup_price" length="18" />
        </property>
        <property name="rupAllPrice" type="java.lang.Double">
            <column name="rup_all_price" length="18" />
        </property>
         <property name="rupRemark" type="java.lang.String">
            <column name="rup_remark" length="1073741823" />
        </property>
    </class>
</hibernate-mapping>
