<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.WmsProduct" table="wms_product" schema="dbo" >
        <id name="wprId" type="java.lang.Long">
            <column name="wpr_id"/>
            <generator class="identity" />
        </id>
        <many-to-one name="wmsProType" class="com.psit.struts.entity.WmsProType" fetch="select" not-null="false">
            <column name="wpr_type_id" />
        </many-to-one>
        <many-to-one name="typeList" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="wpr_unit" />
        </many-to-one>
         <property name="wprCode" type="java.lang.String">
            <column name="wpr_code" length="100" />
        </property>
        <property name="wprName" type="java.lang.String">
            <column name="wpr_name" length="100" />
        </property>
        <property name="wprModel" type="java.lang.String">
            <column name="wpr_model" length="100" />
        </property>
        <property name="wprProvider" type="java.lang.String">
            <column name="wpr_provider" length="100" />
        </property>
        <property name="wprUpLim" type="java.lang.Integer">
            <column name="wpr_up_lim" />
        </property>
        <property name="wprLowLim" type="java.lang.Integer">
            <column name="wpr_low_lim" />
        </property>
        <property name="wprCostPrc" type="java.lang.Double">
            <column name="wpr_cost_prc" precision="18" />
        </property>
        <property name="wprSalePrc" type="java.lang.Double">
            <column name="wpr_sale_prc" precision="18" />
        </property>
        <property name="wprPic" type="java.lang.String">
            <column name="wpr_pic" />
        </property>
        <property name="wprCreDate" type="java.util.Date">
            <column name="wpr_cre_date" length="23" />
        </property>
        <property name="wprEditDate" type="java.util.Date">
            <column name="wpr_edit_date" length="23" />
        </property>
        <property name="wprDesc" type="java.lang.String">
            <column name="wpr_desc" length="1073741823" />
        </property>
        <property name="wprRemark" type="java.lang.String">
            <column name="wpr_remark" length="1073741823" />
        </property>
        <property name="wprStates" type="java.lang.String">
            <column name="wpr_states" length="1" />
        </property>
        <property name="wprRange" type="java.lang.String">
            <column name="wpr_range" length="1073741823" />
        </property>
        <property name="wprTechnology" type="java.lang.String">
            <column name="wpr_technology" length="1073741823" />
        </property>
        <property name="wprProblem" type="java.lang.String">
            <column name="wpr_problem" length="1073741823" />
        </property>
        <property name="wprIsdel" type="java.lang.String">
            <column name="wpr_isdel" length="1" />
        </property>
        <property name="wprCuserCode" type="java.lang.String">
            <column name="wpr_cuser_code" length="50" />
        </property>
        <property name="wprEuserCode" type="java.lang.String">
            <column name="wpr_euser_code" length="50" />
        </property>
        <property name="wprIscount" type="java.lang.String">
            <column name="wpr_iscount" length="1" />
        </property>
        <set name="ROrdPros" inverse="true"  cascade="all">
            <key>
                <column name="rop_pro_id"/>
            </key>
            <one-to-many class="com.frsoft.cis.entity.ROrdPro" />
        </set>
    </class>
</hibernate-mapping>
