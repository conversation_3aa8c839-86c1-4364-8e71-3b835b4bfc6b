<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.SalTask" table="sal_task" schema="dbo" >
        <id name="stId" type="java.lang.Long">
            <column name="st_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="st_se_no"/>
        </many-to-one>
        <many-to-one name="salTaskType" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="st_type_id" />
        </many-to-one>
        <property name="stName" type="java.lang.String">
            <column name="st_name" length="50" />
        </property>
        <property name="stTitle" type="java.lang.String">
            <column name="st_title" length="200" />
        </property>
        <property name="stRelDate" type="java.util.Date">
            <column name="st_rel_date" length="23" />
        </property>
        <property name="stStartDate" type="java.util.Date">
            <column name="st_start_date" length="23" />
        </property>
        <property name="stFinDate" type="java.util.Date">
            <column name="st_fin_date" length="23" />
        </property>
        <property name="stFctDate" type="java.util.Date">
            <column name="st_fct_date" length="23" />
        </property>
        <property name="stLev" type="java.lang.String">
            <column name="st_lev" length="50" />
        </property>
        <property name="stUpdUser" type="java.lang.String">
            <column name="st_upd_user" length="50" />
        </property>
        <property name="stStu" type="java.lang.String">
            <column name="st_stu" length="1" />
        </property>
        <property name="stTag" type="java.lang.String">
            <column name="st_tag" length="1073741823" />
        </property>
        <property name="stRemark" type="java.lang.String">
            <column name="st_remark" length="1073741823" />
        </property>
        <property name="stChangeDate" type="java.util.Date">
            <column name="st_change_date" length="23" />
        </property>
        <property name="stIsdel" type="java.lang.String">
            <column name="st_isdel" length="1" />
        </property>
        <property name="stLog" type="java.lang.String">
        	<column name="st_log" length="1073741823" />
        </property>
        <set name="taLims" inverse="true"  order-by="ta_lim_id" cascade="all">
            <key>
                <column name="ta_task_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.TaLim" />
        </set>
        <set name="attachments" inverse="true"  order-by="att_date desc" cascade="all" where="att_type='task'">
            <key>
                <column name="att_fk_id"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
    </class>
</hibernate-mapping>
