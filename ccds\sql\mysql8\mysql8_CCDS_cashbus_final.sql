-- MySQL 8.0 版本的CCDS数据库结构
-- 从MSSQL自动转换生成
CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ccds`;
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
insert into CCDS.dbo.lim_function(fun_code,fun_desc,fun_type) values ('c006','现金巴士接入','case');
--insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus013' , '获取批次'),('cashbus014' , '案件查看'),('cashbus015' , '入账请求'),('cashbus016' , '入账状态'),('cashbus017' , '还款明细'),('cashbus018' , '敏感信息'),('cashbus019' , '资源获取'),('cashbus020' , '催记'),('cashbus021' , '日还款记录'),('cashbus022' , '月对账表'),('cashbus023' , '导出借款信息');
--insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca023','c006','cashbus013'),('ca024','c006','cashbus014'),('ca025','c006','cashbus015'),('ca027','c006','cashbus016'),('ca028','c006','cashbus017'),('ca029','c006','cashbus018'),('ca030','c006','cashbus019'),('ca031','c006','cashbus020'),('ca032','c006','cashbus021'),('ca033','c006','cashbus022'),('ca034','c006','cashbus023');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus013' , '获取批次');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus014' , '案件查看');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus015' , '入账请求');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus016' , '入账状态');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus017' , '还款明细');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus018' , '敏感信息');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus019' , '资源获取');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus020' , '催记');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus021' , '日还款记录');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus022' , '月对账表');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus023' , '导出借款信息');
insert into CCDS.dbo.lim_operate(ope_code,ope_desc) values ('cashbus024' , '工单');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca023','c006','cashbus013');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca024','c006','cashbus014');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca025','c006','cashbus015');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca027','c006','cashbus016');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca028','c006','cashbus017');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca029','c006','cashbus018');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca030','c006','cashbus019');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca031','c006','cashbus020');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca032','c006','cashbus021');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca033','c006','cashbus022');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca034','c006','cashbus023');
insert into CCDS.dbo.lim_right(rig_code,rig_fun_code,rig_ope_code) values ('ca035','c006','cashbus024');
USE `CCDS`
;
DROP TABLE IF EXISTS `cashbus_workOrder`;
CREATE TABLE `cashbus_workOrder`(
`id` bigint NOT NULL AUTO_INCREMENT,
`loanRefId` varchar(50) NOT NULL,
`description` text NULL,
`status` varchar(20) NULL
,
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
TEXTIMAGE_ON `PRIMARY`
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'借款ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_workOrder', @level2type=N'COLUMN',@level2name=N'loanRefId'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工单描述' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_workOrder', @level2type=N'COLUMN',@level2name=N'description'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashbus_workOrder', @level2type=N'COLUMN',@level2name=N'status'
/**  现金巴金借款案件表  **/
DROP TABLE IF EXISTS `cashBusLoanCase`;
CREATE TABLE `cashBusLoanCase`(
`id` bigint NOT NULL AUTO_INCREMENT,
`batchName` varchar(255) NULL,
`loanRefId` varchar(255) NULL,
`name` varchar(255) NULL,
`userCardNo` varchar(255) NULL,
`days` int NULL,
`originalAmountDue` int NULL,
`bankName` varchar(255) NULL,
`relativeType` varchar(255) NULL,
`socialType` varchar(255) NULL,
`maritalStatus` varchar(255) NULL,
`workCity` varchar(255) NULL,
`workPhone` varchar(255) NULL,
`age` int NULL,
`userPhone` varchar(255) NULL,
`amount` int NULL,
`bankCardNo` varchar(255) NULL,
`relativePhone` varchar(255) NULL,
`socialPhone` varchar(255) NULL,
`srcJson` text NULL
,
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
TEXTIMAGE_ON `PRIMARY`
;
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'id'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'批次名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'batchName'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'借款ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'loanRefId'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N' 用户姓名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'name'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'证件号码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'userCardNo'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'借款天数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'days'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'当前应还款' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'originalAmountDue'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N' 关系1' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'relativeType'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关系2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'socialType'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N' 婚否' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'maritalStatus'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作城市' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'workCity'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作电话' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'workPhone'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'年龄' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'age'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户电话' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'userPhone'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'借款金额' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'amount'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'银行卡号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'bankCardNo'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关系1电话' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'relativePhone'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N' 关系2电话' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'socialPhone'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原始JSON' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase', @level2type=N'COLUMN',@level2name=N'srcJson'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'现金巴士借款案件' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'cashBusLoanCase'
SET FOREIGN_KEY_CHECKS = 1;