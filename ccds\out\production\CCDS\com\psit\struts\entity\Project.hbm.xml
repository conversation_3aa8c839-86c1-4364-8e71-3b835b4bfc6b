<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.Project" table="project" schema="dbo" >
        <id name="proId" type="java.lang.Long">
            <column name="pro_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="proType" class="com.frsoft.base.entity.TypeList" fetch="select" not-null="false">
            <column name="pro_typ_id" />
        </many-to-one>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select" not-null="false">
            <column name="pro_user_code" length="50" />
        </many-to-one>
        <many-to-one name="cusCorCus" class="com.frsoft.cis.entity.CusCorCus" fetch="select"  not-null="false">
            <column name="pro_cor_code" length="50" />
        </many-to-one>
        <property name="proTitle" type="java.lang.String">
            <column name="pro_title" length="300" />
        </property>
        <property name="proState" type="java.lang.String">
            <column name="pro_state" length="50" />
        </property>
         <property name="proPeriod" type="java.lang.String">
            <column name="pro_period" length="50" />
        </property>
       
        <property name="proCreDate" type="java.util.Date">
            <column name="pro_cre_date" length="23" />
        </property>
        <property name="proFinDate" type="java.util.Date">
            <column name="pro_fin_date" length="23" />
        </property>
        <property name="proDesc" type="java.lang.String">
            <column name="pro_desc" length="1073741823" />
        </property>
        <property name="proRemark" type="java.lang.String">
            <column name="pro_remark" length="1073741823" />
        </property>
        <property name="proInpUser" type="java.lang.String">
            <column name="pro_inp_user" length="50" />
        </property>
        <property name="proUpdUser" type="java.lang.String">
            <column name="pro_upd_user" length="50" />
        </property>
        <property name="proInsDate" type="java.util.Date">
            <column name="pro_ins_date" length="23" />
        </property>
        <property name="proModDate" type="java.util.Date">
            <column name="pro_mod_date" length="23" />
        </property>
        <property name="proIsdel" type="java.lang.String">
            <column name="pro_isdel" length="1" />
        </property>
        <property name="proPro" type="java.lang.String">
        	<column name="pro_pro" length="300"/>
        </property>
        <property name="proProLog" type="java.lang.String">
        	<column name="pro_pro_log" length="1073741823"/>
        </property>
        <set name="proTasks" inverse="true" cascade="all">
            <key>
                <column name="prta_pro_id" />
            </key>
            <one-to-many class="com.psit.struts.entity.ProTask" />
        </set>
        <set name="proActors" inverse="true"  cascade="all" order-by="act_id asc">
            <key>
                <column name="act_pro_id" />
            </key>
            <one-to-many class="com.psit.struts.entity.ProActor" />
        </set>
        <set name="proTaskLims" inverse="true" cascade="all">
            <key>
                <column name="ptl_prta_id" />
            </key>
            <one-to-many class="com.psit.struts.entity.ProTaskLim" />
        </set>
        <set name="proStages" inverse="true"  cascade="delete" order-by="sta_id asc">
            <key>
                <column name="sta_pro_id" />
            </key>
            <one-to-many class="com.psit.struts.entity.ProStage" />
        </set>
    
        <set name="salOrdCons" inverse="true"  cascade="all" order-by="sod_code">
            <key>
                <column name="sod_pro_id" />
            </key>
            <one-to-many class="com.frsoft.cis.entity.SalOrdCon" />
        </set>
        <set name="attachments" inverse="true"  cascade="all" where="att_type='proj'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
        <set name="salPurOrds" inverse="true" cascade="all" order-by="spo_cre_date desc">
        	<key>
        		<column name="spo_proj_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.SalPurOrd"/>
        </set>
        <set name="inquirys" inverse="true" cascade="all" order-by="inq_ins_date desc">
        	<key>
        		<column name="inq_pro_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.Inquiry"/>
       	</set>
        <set name="quotes" inverse="true" cascade="all" order-by="quo_ins_date desc">
        	<key>
        		<column name="quo_pro_id"/>
        	</key>
        	<one-to-many class="com.psit.struts.entity.Quote"/>
       	</set>
    </class>
</hibernate-mapping>
