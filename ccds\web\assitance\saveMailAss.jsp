<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>申请信函</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>

	<script type="text/javascript" >
    	function check(){
			var errStr = "";
			/*if(isEmpty("mailCate")){
				errStr+="- 未选择信函类型！\n"; 
			 }*/
			 if("${CUS_VER_ID}"=="2"){
				 if(isEmpty("assTxt")||!(/^\d{6}$/.test($("assTxt").value))){
					errStr+="- 申请内容只能填写6位邮编！\n";
				 }
			 }
			
			 
			 if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			 else{
				waitSubmit("save","保存中...");
				waitSubmit("doCancel");
				return $("create").submit();
			 }
		}
		function initForm(){
			var mailCount ;
			if('${address.adrMailCount}' != ""){
				mailCount = ${address.adrMailCount}+1;
			}
			else{
				mailCount = 1;
			}
			$("mailCount").value=mailCount;
			$("mailCountTd").innerHTML = mailCount;
		}
		window.onload=function(){
			initForm();
		}
  </script></head>
  
  <body>

  <div class="inputDiv">
  	<form id="create" action="assitanceAction.do" method="post">
  		<input type="hidden" name="op" value="saveMailAss" />
  		<input type="hidden" name="adrId"  value="${address.adrId}"/>
  		<input type="hidden" name="mailCount" id="mailCount" />
        <table class="dashTab inputForm" cellpadding="0" cellspacing="0">
            <tbody>
                <tr>
                    <th>地址：</th>
                    <td class="longTd" colspan="3"><input type="text" class="inputSize2L" name="adrStr" onBlur="autoShort(this,200)" value="${address.adrAdd}"/></td>
                </tr>
                <tr>
                  	<th>信函模板：</th>
                    <td><c:if test="${!empty templateList}">
                        <select name="tmpName" class="inputSize2 inputBoxAlign">
                        	<option value=""></option>
                            <c:forEach items="${templateList}" var="t">
                            <option value="${t.tmpName}">${t.tmpName}</option>
                            </c:forEach>
                        </select>
                        </c:if>
                        <c:if test="${empty templateList}">
                            <select class="inputSize2 inputBoxAlign" disabled="disabled">
                                <option>未添加模板</option>
                            </select>
                        </c:if>
                         <!--<select id="mailCate" name="mailCate" class="inputSize2">
                          <option value="">请选择</option>
                          <option value="通知函">通知函</option>
                          <option value="提示函">提示函</option>
                          <option value="法律告知书">法律告知书</option>
                          <option value="律师函">律师函</option>
                          <option value="法院传票">法院传票</option>
                         </select>	-->
                    </td>
                    <th>期次</th>
                    <td id="mailCountTd"></td>
                 </tr>
                 <tr>
                    <th>联系人：</th>
                    <td colspan="3"><input type="text" class="inputSize2" name="contUser" onBlur="autoShort(this,20)" value="${address.bankCase.salEmp.seName}"/></td>
                 </tr>
                 <tr class="noBorderBot">   
                    <th>申请内容：</th>
                    <td colspan="3"><textarea class="inputSize2L" rows="10" id="assTxt" name="assTxt" onBlur="autoShort(this,4000)"></textarea></td>
                </tr>	
                <tr class="submitTr">
                    <td colspan="4">
                    <input id="save" class="butSize1" type="button" value="保存" onClick="check()" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input id="doCancel" class="butSize1" type="button" value="取消" onClick="cancel()" />
                    </td>
                </tr>
            </tbody>
        </table>
	</form>
    </div>
  </body>
</html>
