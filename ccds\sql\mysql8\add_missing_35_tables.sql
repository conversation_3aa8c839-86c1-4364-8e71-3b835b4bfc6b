-- 添加缺少的35个表，确保达到完整的106个表
-- 这些是原始MSSQL中存在但MySQL中缺少的表

USE `ccds`;

-- 1. spo_paid_past (供应商付款历史表)
DROP TABLE IF EXISTS `spo_paid_past`;
CREATE TABLE `spo_paid_past` (
  `spa_id` bigint NOT NULL AUTO_INCREMENT,
  `spa_code` varchar(300) DEFAULT NULL,
  `spa_spo_id` bigint DEFAULT NULL,
  `spa_aco_id` bigint DEFAULT NULL,
  `spa_ssu_id` bigint DEFAULT NULL,
  `spa_fct_date` datetime DEFAULT NULL,
  `spa_type_id` bigint DEFAULT NULL,
  `spa_pay_type` varchar(50) DEFAULT NULL,
  `spa_pay_mon` decimal(18,2) DEFAULT NULL,
  `spa_in_name` varchar(100) DEFAULT NULL,
  `spa_inp_user` varchar(50) DEFAULT NULL,
  `spa_se_no` bigint DEFAULT NULL,
  `spa_isinv` char(1) DEFAULT NULL,
  `spa_remark` longtext,
  `spa_cre_date` datetime DEFAULT NULL,
  `spa_isdel` char(1) DEFAULT NULL,
  `spa_content` varchar(100) DEFAULT NULL,
  `spa_acc_type_id` bigint DEFAULT NULL,
  `spa_alt_date` datetime DEFAULT NULL,
  `spa_alt_user` varchar(50) DEFAULT NULL,
  `spa_undo_date` datetime DEFAULT NULL,
  `spa_undo_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`spa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 2. sal_paid_plan (销售付款计划表)
DROP TABLE IF EXISTS `sal_paid_plan`;
CREATE TABLE `sal_paid_plan` (
  `spd_id` bigint NOT NULL AUTO_INCREMENT,
  `spd_ord_code` bigint DEFAULT NULL,
  `spd_prm_date` datetime DEFAULT NULL,
  `spd_count` int DEFAULT NULL,
  `spd_pay_mon` decimal(18,2) DEFAULT NULL,
  `spd_mon_type` varchar(50) DEFAULT NULL,
  `spd_user_code` varchar(50) DEFAULT NULL,
  `spd_isp` char(1) DEFAULT NULL,
  `spd_resp` varchar(50) DEFAULT NULL,
  `spd_cre_date` datetime DEFAULT NULL,
  `spd_alt_date` datetime DEFAULT NULL,
  `spd_alt_user` varchar(50) DEFAULT NULL,
  `spd_isdel` char(1) DEFAULT NULL,
  `spd_content` varchar(100) DEFAULT NULL,
  `spd_cor_code` bigint DEFAULT NULL,
  PRIMARY KEY (`spd_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 3. inquiry (询价表)
DROP TABLE IF EXISTS `inquiry`;
CREATE TABLE `inquiry` (
  `inq_id` bigint NOT NULL AUTO_INCREMENT,
  `inq_ssu_id` bigint DEFAULT NULL,
  `inq_pro_id` bigint DEFAULT NULL,
  `inq_title` varchar(100) DEFAULT NULL,
  `inq_price` decimal(18,2) DEFAULT NULL,
  `inq_se_no` bigint DEFAULT NULL,
  `inq_date` datetime DEFAULT NULL,
  `inq_inp_user` varchar(50) DEFAULT NULL,
  `inq_upd_user` varchar(50) DEFAULT NULL,
  `inq_ins_date` datetime DEFAULT NULL,
  `inq_upd_date` datetime DEFAULT NULL,
  `inq_remark` longtext,
  `inq_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`inq_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 4. quote (报价表)
DROP TABLE IF EXISTS `quote`;
CREATE TABLE `quote` (
  `quo_id` bigint NOT NULL AUTO_INCREMENT,
  `quo_opp_id` bigint DEFAULT NULL,
  `quo_title` varchar(100) DEFAULT NULL,
  `quo_price` decimal(18,2) DEFAULT NULL,
  `quo_se_no` bigint DEFAULT NULL,
  `quo_remark` longtext,
  `quo_date` datetime DEFAULT NULL,
  `quo_desc` longtext,
  `quo_ins_date` datetime DEFAULT NULL,
  `quo_upd_date` datetime DEFAULT NULL,
  `quo_inp_user` varchar(50) DEFAULT NULL,
  `quo_upd_user` varchar(50) DEFAULT NULL,
  `quo_isdel` char(1) DEFAULT NULL,
  `quo_pro_id` bigint DEFAULT NULL,
  PRIMARY KEY (`quo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 5. sal_opp (销售机会表)
DROP TABLE IF EXISTS `sal_opp`;
CREATE TABLE `sal_opp` (
  `opp_id` bigint NOT NULL AUTO_INCREMENT,
  `opp_cor_code` bigint DEFAULT NULL,
  `opp_title` varchar(300) DEFAULT NULL,
  `opp_lev` varchar(50) DEFAULT NULL,
  `opp_exe_date` datetime DEFAULT NULL,
  `opp_des` longtext,
  `opp_remark` longtext,
  `opp_ins_date` datetime DEFAULT NULL,
  `opp_isexe` varchar(10) DEFAULT NULL,
  `opp_state` varchar(10) DEFAULT NULL,
  `opp_upd_date` datetime DEFAULT NULL,
  `opp_inp_user` varchar(50) DEFAULT NULL,
  `opp_upd_user` varchar(50) DEFAULT NULL,
  `opp_isdel` char(1) DEFAULT NULL,
  `opp_sign_date` datetime DEFAULT NULL,
  `opp_money` decimal(18,2) DEFAULT NULL,
  `opp_stage` bigint DEFAULT NULL,
  `opp_possible` varchar(50) DEFAULT NULL,
  `opp_sta_remark` varchar(100) DEFAULT NULL,
  `opp_sta_update` datetime DEFAULT NULL,
  `opp_sta_log` longtext,
  `opp_find_date` datetime DEFAULT NULL,
  `opp_user_code` varchar(50) DEFAULT NULL,
  `opp_se_no` bigint DEFAULT NULL,
  PRIMARY KEY (`opp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 6. sal_pra (销售活动表)
DROP TABLE IF EXISTS `sal_pra`;
CREATE TABLE `sal_pra` (
  `pra_id` bigint NOT NULL AUTO_INCREMENT,
  `pra_cor_code` bigint DEFAULT NULL,
  `pra_title` varchar(300) DEFAULT NULL,
  `pra_content` longtext,
  `pra_ins_date` datetime DEFAULT NULL,
  `pra_type` varchar(100) DEFAULT NULL,
  `pra_state` varchar(100) DEFAULT NULL,
  `pra_isPrice` varchar(10) DEFAULT NULL,
  `pra_exe_date` datetime DEFAULT NULL,
  `pra_cost_time` varchar(20) DEFAULT NULL,
  `pra_cus_link` varchar(50) DEFAULT NULL,
  `pra_se_no` bigint DEFAULT NULL,
  `pra_back` longtext,
  `pra_remark` longtext,
  `pra_upd_date` datetime DEFAULT NULL,
  `pra_opp_id` bigint DEFAULT NULL,
  `pra_inp_user` varchar(50) DEFAULT NULL,
  `pra_upd_user` varchar(50) DEFAULT NULL,
  `pra_isdel` char(1) DEFAULT NULL,
  `pra_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`pra_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 7. pro_task (项目任务表)
DROP TABLE IF EXISTS `pro_task`;
CREATE TABLE `pro_task` (
  `prta_id` bigint NOT NULL AUTO_INCREMENT,
  `prta_se_no` bigint DEFAULT NULL,
  `prta_pro_id` bigint DEFAULT NULL,
  `prta_sta_name` varchar(300) DEFAULT NULL,
  `prta_name` varchar(50) DEFAULT NULL,
  `prta_title` varchar(300) DEFAULT NULL,
  `prta_rel_date` datetime DEFAULT NULL,
  `prta_change_date` datetime DEFAULT NULL,
  `prta_fin_date` datetime DEFAULT NULL,
  `prta_lev` varchar(50) DEFAULT NULL,
  `prta_state` char(1) DEFAULT NULL,
  `prta_cyc` varchar(50) DEFAULT NULL,
  `prta_tag` longtext,
  `prta_desc` longtext,
  `prta_log` longtext,
  `prta_remark` longtext,
  `prta_isdel` char(1) DEFAULT NULL,
  `prta_fct_date` datetime DEFAULT NULL,
  `prta_upd_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`prta_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 8. cus_serv (客户服务表)
DROP TABLE IF EXISTS `cus_serv`;
CREATE TABLE `cus_serv` (
  `ser_code` bigint NOT NULL AUTO_INCREMENT,
  `ser_cor_code` bigint DEFAULT NULL,
  `ser_title` varchar(300) DEFAULT NULL,
  `ser_cus_link` varchar(50) DEFAULT NULL,
  `ser_method` varchar(100) DEFAULT NULL,
  `ser_content` longtext,
  `ser_exe_date` datetime DEFAULT NULL,
  `ser_cos_time` varchar(50) DEFAULT NULL,
  `ser_state` varchar(10) DEFAULT NULL,
  `ser_se_no` bigint DEFAULT NULL,
  `ser_feedback` longtext,
  `ser_remark` longtext,
  `ser_ins_date` datetime DEFAULT NULL,
  `ser_upd_date` datetime DEFAULT NULL,
  `ser_inp_user` varchar(50) DEFAULT NULL,
  `ser_upd_user` varchar(50) DEFAULT NULL,
  `ser_isdel` char(1) DEFAULT NULL,
  `ser_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ser_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 9. sal_ord_con (销售订单表)
DROP TABLE IF EXISTS `sal_ord_con`;
CREATE TABLE `sal_ord_con` (
  `sod_code` bigint NOT NULL AUTO_INCREMENT,
  `sod_num` varchar(300) DEFAULT NULL,
  `sod_til` varchar(300) DEFAULT NULL,
  `sod_type_id` bigint DEFAULT NULL,
  `sod_cus_code` bigint DEFAULT NULL,
  `sod_pro_id` bigint DEFAULT NULL,
  `sod_sum_mon` decimal(18,2) DEFAULT NULL,
  `sod_paid_mon` decimal(18,2) DEFAULT NULL,
  `sod_mon_type` varchar(50) DEFAULT NULL,
  `sod_state` varchar(10) DEFAULT NULL,
  `sod_ship_state` varchar(10) DEFAULT NULL,
  `sod_own_code` varchar(50) DEFAULT NULL,
  `sod_deadline` datetime DEFAULT NULL,
  `sod_end_date` datetime DEFAULT NULL,
  `sod_ord_date` datetime DEFAULT NULL,
  `sod_inp_date` datetime DEFAULT NULL,
  `sod_isfail` char(1) DEFAULT NULL,
  `sod_remark` longtext,
  `sod_change_date` datetime DEFAULT NULL,
  `sod_paid_method` varchar(20) DEFAULT NULL,
  `sod_inp_code` varchar(50) DEFAULT NULL,
  `sod_cus_con` varchar(100) DEFAULT NULL,
  `sod_se_no` bigint DEFAULT NULL,
  `sod_con_date` datetime DEFAULT NULL,
  `sod_change_user` varchar(50) DEFAULT NULL,
  `sod_app_date` datetime DEFAULT NULL,
  `sod_app_man` varchar(50) DEFAULT NULL,
  `sod_app_desc` longtext,
  `sod_app_isok` char(1) DEFAULT NULL,
  `sod_content` longtext,
  PRIMARY KEY (`sod_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 10. wms_shipment (仓库发货表)
DROP TABLE IF EXISTS `wms_shipment`;
CREATE TABLE `wms_shipment` (
  `wsh_code` varchar(50) NOT NULL,
  `wsh_wout_code` varchar(50) DEFAULT NULL,
  `wsh_ord_code` bigint DEFAULT NULL,
  `wsh_state` char(1) DEFAULT NULL,
  `wsh_out_date` datetime DEFAULT NULL,
  `wsh_inp_date` datetime DEFAULT NULL,
  `wsh_user_code` varchar(50) DEFAULT NULL,
  `wsh_rec_man` varchar(50) DEFAULT NULL,
  `wsh_type` varchar(50) DEFAULT NULL,
  `wsh_cost` decimal(18,2) DEFAULT NULL,
  `wsh_remark` longtext,
  PRIMARY KEY (`wsh_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

SELECT 'CCDS数据库 - 第一批缺失表添加完成' as message;
