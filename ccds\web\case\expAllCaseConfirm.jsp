<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>导出数据确认</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
    	body{
			background:#fff;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
		function initForm(){
			var formArgs = parent.getFormArgs();
			formArgs.op = "expCRByCase";
			createHideInput("expForm",formArgs);
		}
    	function confirmForm(){
			//waitSubmit("toConfirm");
			//waitSubmit("toCancel");
			//$("expForm").submit();
			var formArgs = parent.getFormArgs();
			if(formArgs.type!="default"){
				toExportData($("expForm").action,$("expForm").serialize(true));
			}
			else{
				waitSubmit("toConfirm");
				waitSubmit("toCancel");
				$("expForm").submit();
			}
		}
		window.onload=function(){
			initForm();
		}
  </script>
  </head>
  
  <body> 
  	<div style="padding:20px 0 0 0;" >
        <form id="expForm" action="caseAction.do" method="post" style="margin:0px">
            <div id="submitTd">
            <button id="toConfirm" class ="butSize1" onClick="confirmForm()">确定导出</button>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <button id="toCancel" class ="butSize1" onClick="cancel()">取消</button>
            </div>
        </form>
    </div> 
	</body>
</html>
