<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.WmsProType" table="wms_pro_type" schema="dbo" >
        <id name="wptId" type="java.lang.Long">
            <column name="wpt_id" />
            <generator class="identity" />
        </id>
        <property name="wptName" type="java.lang.String">
            <column name="wpt_name" length="50" />
        </property>
        <property name="wptDesc" type="java.lang.String">
            <column name="wpt_desc" length="1073741823" />
        </property>
        <property name="wptIsenabled" type="java.lang.String">
            <column name="wpt_isenabled" length="10" />
        </property>
        <many-to-one name="wmsProType" class="com.psit.struts.entity.WmsProType" fetch="select" not-null="false">
            <column name="wpt_up_id"/>
        </many-to-one>
        <set name="wmsProducts" inverse="true"  order-by="wpr_Id" cascade="all" where="wpr_isdel='0'">
            <key>
                <column name="wpr_type_id" />
            </key>
            <one-to-many class="com.frsoft.cis.entity.WmsProduct" />
        </set>
        <set name="wmsProTypes" inverse="true" >
            <key>
                <column name="wpt_up_id" />
            </key>
            <one-to-many class="com.psit.struts.entity.WmsProType" />
        </set>
    </class>
</hibernate-mapping>
