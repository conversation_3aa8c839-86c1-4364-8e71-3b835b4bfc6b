<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
  <!-- 无效页面 -->
     <base href="<%=basePath%>"></base>  
     <title>导出催收记录</title>
     <link rel="shortcut icon" href="favicon.ico"/>    
 	 <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
     <meta http-equiv="pragma" content="no-cache">
	 <meta http-equiv="cache-control" content="no-cache">
	 <meta http-equiv="expires" content="0">
	 <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <link rel="stylesheet" type="text/css" href="css/style.css"/>
     <script type="text/javascript" src="js/prototype.js"></script>
     <script type="text/javascript" src="js/common.js"></script>
     <script type="text/javascript" src="js/formCheck.js"></script>
	 <script type="text/javascript">
		function outCheck(){
			waitSubmit("out");
			waitSubmit("doCancel");
            return $("outForm").submit();
		}
　		function close(){
			setTimeout("closeDiv('')",3000);
		}
		window.onload=function(){
			loadPopWinHeight("150");
			if("${cbatId}"=="selected"){
				$("cbatId").value=getBacthIds();
			}
		}
	 </script>
</head>
  <body>
  <div class="inputDiv">
  	<form action="caseAction.do" method="post" id="outForm">
  		<input type="hidden" name="op" value="outCRecJC" />
        <!-- 批次 -->
        <input type="hidden" id="cbatId" name="cbatId" value="${cbatId}" />
        <!-- 案件查询 -->
        <input type="hidden" name="bankName" value="${bankName}" />
        <input type="hidden" name="bankIds" value="${bankIds}" />
        <input type="hidden" name="prEmpName" value="${prEmpName}" />
        <input type="hidden" name="startTime" value="${startTime}" />
        <input type="hidden" name="endTime" value="${endTime}" />
        <!-- 选中案件 -->
        <input type="hidden" name="caseIds" value="${caseIds}" />
        <!-- 单案件 -->
        <input type="hidden" name="casId" value="${casId}" />
        <div style="padding:10px; font-weight:bold">
        	导出案件操作记录：
            <input id="wordExOld" type="radio" name="type" value="0" checked /><label for="wordExOld">导出成Word&nbsp;&nbsp;</label>
            <input id="xlsExOld" type="radio" name="type" value="2" /><label for="xlsExOld">导出成Excel&nbsp;&nbsp;</label>
        </div>
        <div style=" background:#CCC;padding:10px; color:#333"> 
			导出催收记录：
			<input id="wordEx" type="radio" name="type" value="table" /><label for="wordEx">导出成Word&nbsp;&nbsp;&nbsp;&nbsp;</label>
            <input id="xlsEx" type="radio" name="type" value="row" /><label for="xlsEx">导出成Excel&nbsp;&nbsp;&nbsp;&nbsp;</label>
		</div>
		<div class="submitLayer" style="padding:5px;">
            <input type="button" class="butSize1" id="out" value="导出" onClick="outCheck()">
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type="button" class="butSize1" id="doCancel"  value="取消" onClick="cancel()"/>
		</div>
	</form>
  </div>
  </body>
</html>
