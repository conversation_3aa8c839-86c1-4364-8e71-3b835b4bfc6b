package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 系统用户实体类
 * 对应数据库表: lim_user
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class SysUser {

    /** 用户编码 */
    private String userCode;

    /** 登录名 */
    private String userLoginName;

    /** 密码 */
    private String userPwd;

    /** 上级用户编码 */
    private String userUpCode;

    /** 用户级别 */
    private String userLev;

    /** 销售组织编码 */
    private String userSoCode;

    /** 员工ID */
    private Long userSeId;

    /** 员工姓名 */
    private String userSeName;

    /** 描述 */
    private String userDesc;

    /** 是否启用 */
    private String userIsenabled;

    /** 工号 */
    private String userNum;

    /** 角色ID */
    private Long userRoleId;

    /** 是否登录 */
    private String userIslogin;

    /** 登录IP */
    private String userIp;

    /** 登录失败次数 */
    private Integer userFail;

    /** 密码更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date userPwdUpdDate;

    /** CTI登录名 */
    private String userCtiLogin;

    /** CTI密码 */
    private String userCtiPwd;
    
    /** CTI服务器 */
    private String userCtiServer;

    /** CTI电话 */
    private String userCtiPhone;

    /** 短信最大发送数 */
    private Integer userSmsMaxNum;

    // 关联对象
    /** 员工信息 */
    private SalEmp salEmp;

    /** 角色信息 */
    private LimRole limRole;

    /** 组信息 */
    private LimGroup limGroup;
}