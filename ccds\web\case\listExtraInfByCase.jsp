 <%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>案人数据列表</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
    	function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			dataId = obj.exiId;
			datas = [obj.exiName, obj.exiType, convertToBR(obj.exiContent), obj.exiRemark, obj.exiCreTime ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "extraInfAction.do";
			var pars = [];
			pars.op = "listExtraInfByCase";
			pars.caseId="${caseId}";
			
			var loadFunc = "loadList";
			var cols=[
				{name:"姓名",width:"10%"},
				{name:"信息类型",width:"10%"},
				{name:"信息内容",isSort:false,width:"38%",align:"left"},
				{name:"备注",isSort:false,width:"28%"},
				{name:"导入时间",renderer:"time",width:"14%"}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("extraInfByCaseListTab","dataList");
    	gridEl.config.sortable=false;
		gridEl.config.isResize=true;
		gridEl.config.isShort=false;
		createProgressBar();
   		window.onload=function(){
			if("${operational}"!="0"){
				$("listTop").show();
			}
			loadList();
		}
    </script>
</head> 
  
  <body>
  	<div class="divWithScroll2 innerIfm">
    	<table id="listTop" style="display:none" class="normal ifmTopTab" cellpadding="0" cellspacing="0">
  			<tr>
            	<th>&nbsp;</th>
                <td style="width:260px; vertical-align:top;"><a href="javascript:void(0)" onClick="parent.casePopDiv(313,'${caseId}');return false;" class="newBlueButton">新建案人数据</a></td>
  			</tr>
  		</table>
  		<div id="dataList" class="dataList"></div>
    </div>
  </body>
</html>