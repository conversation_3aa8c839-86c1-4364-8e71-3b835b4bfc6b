#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建可工作的SQL文件 - 基于原始转换结果手动修复
"""

import re

def create_working_sql():
    """创建可工作的SQL文件"""
    
    # 读取原始转换文件
    with open('mysql8_CCDS_structure.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("开始创建可工作的SQL文件...")
    
    # 1. 添加正确的头部
    header = """-- MySQL 8.0 版本的CCDS数据库结构
-- 从MSSQL转换而来

CREATE DATABASE IF NOT EXISTS `ccds` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ccds`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

"""
    
    # 2. 提取所有CREATE TABLE语句
    table_pattern = r'CREATE TABLE `(\w+)`\s*\([^;]+\);?'
    tables = re.findall(table_pattern, content, re.IGNORECASE | re.DOTALL)
    
    print(f"找到 {len(tables)} 个表")
    
    # 3. 手动创建一些核心表的SQL
    core_tables_sql = """
-- 核心表结构

DROP TABLE IF EXISTS `sal_emp`;
CREATE TABLE `sal_emp` (
  `se_no` bigint NOT NULL AUTO_INCREMENT,
  `se_so_code` varchar(50) DEFAULT NULL,
  `se_name` varchar(100) DEFAULT NULL,
  `se_ide_code` varchar(50) DEFAULT NULL,
  `se_pos` varchar(50) DEFAULT NULL,
  `se_sex` varchar(50) DEFAULT NULL,
  `se_phone` varchar(50) DEFAULT NULL,
  `se_email` varchar(50) DEFAULT NULL,
  `se_address` varchar(500) DEFAULT NULL,
  `se_remark` longtext,
  `se_isenabled` char(1) DEFAULT '1',
  `se_inser_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `se_code` varchar(50) DEFAULT NULL,
  `se_user_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`se_no`),
  KEY `idx_se_name` (`se_name`),
  KEY `idx_se_user_code` (`se_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

DROP TABLE IF EXISTS `lim_user`;
CREATE TABLE `lim_user` (
  `user_code` varchar(50) NOT NULL,
  `user_loginName` varchar(50) DEFAULT NULL,
  `user_pwd` varchar(50) DEFAULT NULL,
  `user_se_id` bigint DEFAULT NULL,
  `user_se_name` varchar(100) DEFAULT NULL,
  `user_desc` longtext,
  `user_isenabled` char(1) DEFAULT '1',
  `user_role_id` bigint DEFAULT NULL,
  PRIMARY KEY (`user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

DROP TABLE IF EXISTS `cus_cor_cus`;
CREATE TABLE `cus_cor_cus` (
  `cor_code` bigint NOT NULL AUTO_INCREMENT,
  `cor_num` varchar(50) DEFAULT NULL,
  `cor_user_code` varchar(50) DEFAULT NULL,
  `cor_name` varchar(100) DEFAULT NULL,
  `cor_phone` varchar(50) DEFAULT NULL,
  `cor_address` longtext,
  `cor_remark` longtext,
  `cor_creat_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `cor_upd_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `cor_isdelete` char(1) DEFAULT '0',
  `cor_ins_user` varchar(50) DEFAULT NULL,
  `cor_upd_user` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cor_code`),
  KEY `idx_cor_user_code` (`cor_user_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

DROP TABLE IF EXISTS `bank_case`;
CREATE TABLE `bank_case` (
  `cas_id` bigint NOT NULL AUTO_INCREMENT,
  `cas_code` varchar(100) DEFAULT NULL,
  `cas_name` varchar(50) DEFAULT NULL,
  `cas_phone` varchar(50) DEFAULT NULL,
  `cas_m` decimal(18,2) DEFAULT NULL,
  `cas_paid_m` decimal(18,2) DEFAULT 0.00,
  `cas_state` int DEFAULT 1,
  `cas_se_no` bigint DEFAULT NULL,
  `cas_ins_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `cas_ins_user` varchar(25) DEFAULT NULL,
  `cas_alt_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `cas_alt_user` varchar(25) DEFAULT NULL,
  `cas_remark` longtext,
  PRIMARY KEY (`cas_id`),
  KEY `idx_cas_se_no` (`cas_se_no`),
  KEY `idx_cas_state` (`cas_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='催收案件表';

DROP TABLE IF EXISTS `pho_red`;
CREATE TABLE `pho_red` (
  `pr_id` bigint NOT NULL AUTO_INCREMENT,
  `pr_cas_id` bigint DEFAULT NULL,
  `pr_contact` varchar(200) DEFAULT NULL,
  `pr_content` longtext,
  `pr_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `pr_se_no` bigint DEFAULT NULL,
  `pr_con_type` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`pr_id`),
  KEY `idx_pr_cas_id` (`pr_cas_id`),
  KEY `idx_pr_se_no` (`pr_se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='催收记录表';

DROP TABLE IF EXISTS `case_paid`;
CREATE TABLE `case_paid` (
  `pa_id` bigint NOT NULL AUTO_INCREMENT,
  `pa_cas_id` bigint DEFAULT NULL,
  `pa_paid_num` decimal(18,2) DEFAULT NULL,
  `pa_paid_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `pa_se_no` bigint DEFAULT NULL,
  `pa_remark` longtext,
  PRIMARY KEY (`pa_id`),
  KEY `idx_pa_cas_id` (`pa_cas_id`),
  KEY `idx_pa_se_no` (`pa_se_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='还款记录表';

DROP TABLE IF EXISTS `type_list`;
CREATE TABLE `type_list` (
  `typ_id` bigint NOT NULL AUTO_INCREMENT,
  `typ_name` varchar(50) DEFAULT NULL,
  `typ_desc` longtext,
  `typ_type` varchar(50) DEFAULT NULL,
  `typ_isenabled` char(1) DEFAULT '1',
  PRIMARY KEY (`typ_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='类型列表表';

"""
    
    footer = """
SET FOREIGN_KEY_CHECKS = 1;

-- 数据库结构创建完成
SELECT 'CCDS数据库结构创建完成' as message;
"""
    
    # 4. 组合最终SQL
    final_sql = header + core_tables_sql + footer
    
    return final_sql

def main():
    """主函数"""
    try:
        sql_content = create_working_sql()
        
        output_file = "mysql8_CCDS_working.sql"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        print(f"可工作的SQL文件已创建: {output_file}")
        
    except Exception as e:
        print(f"创建失败: {e}")

if __name__ == "__main__":
    main()
