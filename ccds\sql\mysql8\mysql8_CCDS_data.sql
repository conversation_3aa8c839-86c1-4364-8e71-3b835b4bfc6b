-- MySQL 8.0 版本的CCDS数据库初始数据
USE `ccds`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 插入默认组织数据
INSERT INTO `sal_org` (`so_code`, `so_name`, `so_parent_code`, `so_type`, `so_level`, `so_order`, `so_desc`, `so_isenabled`, `so_create_date`, `so_create_user`) VALUES
('ROOT', '总公司', NULL, '公司', 1, 1, '根组织', '1', NOW(), 'system'),
('DEPT001', '技术部', 'ROOT', '部门', 2, 1, '技术开发部门', '1', NOW(), 'system'),
('DEPT002', '业务部', 'ROOT', '部门', 2, 2, '业务管理部门', '1', NOW(), 'system'),
('DEPT003', '催收部', 'ROOT', '部门', 2, 3, '催收业务部门', '1', NOW(), 'system');

-- 插入默认角色数据
INSERT INTO `lim_role` (`rol_id`, `rol_name`, `rol_desc`, `rol_isenabled`, `rol_create_date`, `rol_create_user`) VALUES
(1, '系统管理员', '系统管理员角色，拥有所有权限', '1', NOW(), 'system'),
(2, '业务管理员', '业务管理员角色，管理业务相关功能', '1', NOW(), 'system'),
(3, '催收员', '催收员角色，负责催收业务', '1', NOW(), 'system'),
(4, '普通用户', '普通用户角色，基础查看权限', '1', NOW(), 'system');

-- 插入默认权限数据
INSERT INTO `lim_right` (`rig_code`, `rig_name`, `rig_desc`, `rig_type`, `rig_url`, `rig_parent_code`, `rig_order`, `rig_isenabled`) VALUES
('SYSTEM', '系统管理', '系统管理模块', 'MENU', '/system', NULL, 1, '1'),
('SYSTEM_USER', '用户管理', '用户管理功能', 'MENU', '/system/user', 'SYSTEM', 1, '1'),
('SYSTEM_USER_LIST', '用户列表', '查看用户列表', 'FUNCTION', '/system/user/list', 'SYSTEM_USER', 1, '1'),
('SYSTEM_USER_ADD', '新增用户', '新增用户功能', 'FUNCTION', '/system/user/add', 'SYSTEM_USER', 2, '1'),
('SYSTEM_USER_EDIT', '编辑用户', '编辑用户功能', 'FUNCTION', '/system/user/edit', 'SYSTEM_USER', 3, '1'),
('SYSTEM_USER_DELETE', '删除用户', '删除用户功能', 'FUNCTION', '/system/user/delete', 'SYSTEM_USER', 4, '1'),
('SYSTEM_ROLE', '角色管理', '角色管理功能', 'MENU', '/system/role', 'SYSTEM', 2, '1'),
('SYSTEM_ROLE_LIST', '角色列表', '查看角色列表', 'FUNCTION', '/system/role/list', 'SYSTEM_ROLE', 1, '1'),
('SYSTEM_ROLE_ADD', '新增角色', '新增角色功能', 'FUNCTION', '/system/role/add', 'SYSTEM_ROLE', 2, '1'),
('SYSTEM_ROLE_EDIT', '编辑角色', '编辑角色功能', 'FUNCTION', '/system/role/edit', 'SYSTEM_ROLE', 3, '1'),
('SYSTEM_ROLE_DELETE', '删除角色', '删除角色功能', 'FUNCTION', '/system/role/delete', 'SYSTEM_ROLE', 4, '1'),
('BUSINESS', '业务管理', '业务管理模块', 'MENU', '/business', NULL, 2, '1'),
('COLLECTION', '催收管理', '催收管理模块', 'MENU', '/collection', NULL, 3, '1'),
('COLLECTION_CASE', '案件管理', '催收案件管理', 'MENU', '/collection/case', 'COLLECTION', 1, '1'),
('COLLECTION_CASE_LIST', '案件列表', '查看案件列表', 'FUNCTION', '/collection/case/list', 'COLLECTION_CASE', 1, '1'),
('COLLECTION_CASE_DETAIL', '案件详情', '查看案件详情', 'FUNCTION', '/collection/case/detail', 'COLLECTION_CASE', 2, '1'),
('COLLECTION_CASE_ASSIGN', '案件分配', '分配催收案件', 'FUNCTION', '/collection/case/assign', 'COLLECTION_CASE', 3, '1');

-- 插入默认员工数据
INSERT INTO `sal_emp` (`se_no`, `se_so_code`, `se_name`, `se_ide_code`, `se_pos`, `se_sex`, `se_email`, `se_phone`, `se_isenabled`, `se_inser_date`, `se_code`, `se_inser_user`, `se_user_code`) VALUES
(1, 'ROOT', '系统管理员', '110101199001010001', '系统管理员', '男', '<EMAIL>', '***********', '1', NOW(), 'EMP001', 'system', 'admin'),
(2, 'DEPT003', '张三', '110101199002020002', '催收专员', '男', '<EMAIL>', '13800138001', '1', NOW(), 'EMP002', 'admin', 'zhangsan'),
(3, 'DEPT003', '李四', '110101199003030003', '催收专员', '女', '<EMAIL>', '13800138002', '1', NOW(), 'EMP003', 'admin', 'lisi');

-- 插入默认用户数据
INSERT INTO `lim_user` (`user_code`, `user_loginName`, `user_pwd`, `user_up_code`, `user_lev`, `user_so_code`, `user_se_id`, `user_se_name`, `user_desc`, `user_isenabled`, `user_role_id`, `user_islogin`, `user_fail`) VALUES
('admin', 'admin', '21232f297a57a5a743894a0e4a801fc3', NULL, '1', 'ROOT', 1, '系统管理员', '系统管理员账户', '1', 1, '0', 0),
('zhangsan', 'zhangsan', '5d41402abc4b2a76b9719d911017c592', 'admin', '2', 'DEPT003', 2, '张三', '催收专员账户', '1', 3, '0', 0),
('lisi', 'lisi', '5d41402abc4b2a76b9719d911017c592', 'admin', '2', 'DEPT003', 3, '李四', '催收专员账户', '1', 3, '0', 0);

-- 插入用户权限关系数据（给admin用户分配所有权限）
INSERT INTO `r_user_rig` (`rur_user_code`, `rur_rig_code`, `rur_create_date`, `rur_create_user`) 
SELECT 'admin', `rig_code`, NOW(), 'system' FROM `lim_right` WHERE `rig_isenabled` = '1';

-- 插入催收员基础权限
INSERT INTO `r_user_rig` (`rur_user_code`, `rur_rig_code`, `rur_create_date`, `rur_create_user`) VALUES
('zhangsan', 'COLLECTION', NOW(), 'admin'),
('zhangsan', 'COLLECTION_CASE', NOW(), 'admin'),
('zhangsan', 'COLLECTION_CASE_LIST', NOW(), 'admin'),
('zhangsan', 'COLLECTION_CASE_DETAIL', NOW(), 'admin'),
('lisi', 'COLLECTION', NOW(), 'admin'),
('lisi', 'COLLECTION_CASE', NOW(), 'admin'),
('lisi', 'COLLECTION_CASE_LIST', NOW(), 'admin'),
('lisi', 'COLLECTION_CASE_DETAIL', NOW(), 'admin');

SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建结果
SELECT '数据库初始化完成' AS message;
SELECT COUNT(*) AS user_count FROM lim_user;
SELECT COUNT(*) AS emp_count FROM sal_emp;
SELECT COUNT(*) AS role_count FROM lim_role;
SELECT COUNT(*) AS right_count FROM lim_right;
SELECT COUNT(*) AS org_count FROM sal_org;
