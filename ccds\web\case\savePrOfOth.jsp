<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>添加催收记录</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
    <link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
    	body{
			background:#fff;
		}
    </style>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/case.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
    <script type="text/javascript" src="js/formCheck.js"></script>
    <script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
		function initForm(){
			loadPhoneType("phoneType");
		}
		
		function check(){
			var errStr = "";
			if(isEmpty("phoneRec")){ 
				errStr+="未填写催收内容！\n"; 
			}
			if(errStr!=""){
				errStr+="\n请返回修改...";
				alert(errStr);
				return false;
			}
			else {
				waitSubmit("toSave");
				$('saveForm').submit();
			}
		}
		
		window.onload=function(){
			initForm();
		}
  	</script>
  </head>
  <body> 
  	<div class="inputDiv">
        <form id="saveForm" action="caseAction.do" style="margin:0; padding:5px 0 0 0;" method="post">
  			<input type="hidden" name="op" value="savePrOfOth">
  	 		<input type="hidden" name="casId"  value="${casId}"/>
        	<table cellpadding="0" cellspacing="0" class="dashTab inputForm">
                <tr>
                	<th>催收措施：</th>
                    <td>
                    	<select class="inputSize2" name="phoRed.prCat">
                            <option value="1">外访</option>
                    		<option value="3">辅助措施</option>
                        </select>
                    </td>
                    <th>电话/地址：</th>
                    <td><input class="inputSize2" type="text" name="phoRed.prContact" onBlur="autoShort(this,50)"/></td>
                </tr>
                <tr>
                    <th>姓名/类型：</th>
                    <td><input class="inputSize2 inputBoxAlign" style="width:72px" type="text" name="phoRed.prName" onBlur="autoShort(this,50)"/>&nbsp;<select id="phoneType" name="phoRed.prConType" class="inputSize2 inputBoxAlign" style="width:75px"></select></td>
                    <th>关系：</th>
                    <td><input class="inputSize2 inputBoxAlign" style="width:72px" type="text" id='prRel' name="phoRed.prRel" onBlur="autoShort(this,50)"/>&nbsp;<select id="relType" style="width:75px" class="inputSize2 inputBoxAlign" onChange="setValueFromSel(this,'prRel')" >
                         <option value="">请选择</option>
                         <c:if test="${!empty relType}">
                         <c:forEach var="relType" items="${relType}">
                         <option value="${relType.typName}">${relType.typName}</option>
                         </c:forEach>
                         </c:if>
                     </select></td> 
                </tr>
                <tr class="noBorderBot">
                    <th class="required">催收内容：<span class="red">*</span></th>
                    <td colspan="3"><textarea class="inputSize2L" style="width:405px" rows="5" id="phoneRec" name="phoRed.prContent" onBlur="autoShort(this,1000)"></textarea></td>
               	</tr>
                <tr class="submitTr">
                    <td colspan="4"><button id="toSave" onClick="check()">保存</button></td>
                </tr>
            </table>
        </form>
    </div> 
  </body>
</html>
