-- 数据库修复脚本
-- 解决字段缺失问题

USE `ccds`;

-- 检查并添加缺失的字段
-- 如果sal_emp表的se_position字段不存在，则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'ccds' 
     AND TABLE_NAME = 'sal_emp' 
     AND COLUMN_NAME = 'se_position') = 0,
    'ALTER TABLE sal_emp ADD COLUMN se_position varchar(50) DEFAULT NULL COMMENT ''职位''',
    'SELECT ''se_position字段已存在'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加se_phone字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'ccds' 
     AND TABLE_NAME = 'sal_emp' 
     AND COLUMN_NAME = 'se_phone') = 0,
    'ALTER TABLE sal_emp ADD COLUMN se_phone varchar(20) DEFAULT NULL COMMENT ''电话''',
    'SELECT ''se_phone字段已存在'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加se_email字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'ccds' 
     AND TABLE_NAME = 'sal_emp' 
     AND COLUMN_NAME = 'se_email') = 0,
    'ALTER TABLE sal_emp ADD COLUMN se_email varchar(100) DEFAULT NULL COMMENT ''邮箱''',
    'SELECT ''se_email字段已存在'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加se_entry_date字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'ccds' 
     AND TABLE_NAME = 'sal_emp' 
     AND COLUMN_NAME = 'se_entry_date') = 0,
    'ALTER TABLE sal_emp ADD COLUMN se_entry_date date DEFAULT NULL COMMENT ''入职日期''',
    'SELECT ''se_entry_date字段已存在'' as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示sal_emp表的当前结构
DESCRIBE sal_emp;

-- 重新插入默认员工数据（如果不存在）
INSERT INTO `sal_emp` (`se_name`, `se_user_code`, `se_so_code`, `se_isenabled`, `se_position`) 
VALUES ('系统管理员', 'admin', 'ORG001', '1', '管理员')
ON DUPLICATE KEY UPDATE 
    `se_name` = '系统管理员',
    `se_position` = '管理员';

-- 验证数据插入
SELECT * FROM sal_emp WHERE se_user_code = 'admin';

COMMIT;
