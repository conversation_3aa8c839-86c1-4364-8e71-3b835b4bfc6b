package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 角色实体类
 * 对应数据库表: lim_role
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class LimRole {
    
    /** 角色ID */
    private Long rolId;
    
    /** 角色名称 */
    private String rolName;
    
    /** 角色级别 */
    private Integer rolLev;
    
    /** 角色描述 */
    private String rolDesc;
    
    /** 组ID */
    private Long rolGrpId;
}
