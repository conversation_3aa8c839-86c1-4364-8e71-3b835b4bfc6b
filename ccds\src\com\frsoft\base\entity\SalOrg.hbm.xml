<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.SalOrg" table="sal_org" schema="dbo" >
        <id name="soCode" type="java.lang.String">
            <column name="so_code" length="50" />
            <generator class="assigned" />
        </id>
        <property name="soName" type="java.lang.String">
            <column name="so_name" length="50" />
        </property>
        <many-to-one name="salOrg" class="com.frsoft.base.entity.SalOrg" fetch="select" not-null="false">
            <column name="so_up_code" length="50" />
        </many-to-one>
        <property name="soConArea" type="java.lang.String">
            <column name="so_con_area" length="1073741823" />
        </property>
        <property name="soLoc" type="java.lang.String">
            <column name="so_loc" length="1073741823" />
        </property>
        <property name="soUserCode" type="java.lang.String">
            <column name="so_user_code" length="50" />
        </property>
        <property name="soEmpNum" type="java.lang.String">
            <column name="so_emp_num" length="50" />
        </property>
        <property name="soResp" type="java.lang.String">
            <column name="so_resp" length="1073741823" />
        </property>
        <property name="soOrgCode" type="java.lang.String">
            <column name="so_org_code" length="50" />
        </property>
        <property name="soRemark" type="java.lang.String">
            <column name="so_remark" length="1073741823" />
        </property>
        <property name="soIsenabled" type="java.lang.String">
            <column name="so_isenabled" length="1" />
        </property>
        <property name="soCostCenter" type="java.lang.String">
            <column name="so_cost_center" length="100" />
        </property>
        <property name="soOrgNature" type="java.lang.String">
            <column name="so_org_nature" length="100" />
        </property>
    </class>
</hibernate-mapping>
