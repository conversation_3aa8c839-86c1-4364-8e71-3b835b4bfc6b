package com.frsoft.ccds.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 还款记录实体类
 * 对应数据库表: case_paid
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class CasePaid {
    
    /** 还款ID */
    private Long paId;
    
    /** 状态 */
    private Integer paState;
    
    /** 案件ID */
    private Long paCasId;
    
    /** 承诺还款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paPtpD;
    
    /** 承诺还款金额 */
    private BigDecimal paPtpNum;
    
    /** 实际还款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paPaidDate;
    
    /** 实际还款金额 */
    private BigDecimal paPaidNum;
    
    /** 员工编号 */
    private Long paSeNo;
    
    /** 备注 */
    private String paRemark;
    
    /** 还款类型 */
    private String paType;
    
    /** 还款方式 */
    private String paMethod;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paInsTime;
    
    /** 创建用户 */
    private String paInsUser;
    
    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paAltTime;
    
    /** 修改用户 */
    private String paAltUser;
    
    // 关联对象
    /** 案件信息 */
    private BankCase bankCase;
    
    /** 员工信息 */
    private SalEmp salEmp;
}
