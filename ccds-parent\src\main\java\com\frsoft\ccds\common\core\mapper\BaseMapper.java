package com.frsoft.ccds.common.core.mapper;

import java.util.List;

/**
 * 通用Mapper基类
 * 
 * @param <T> 实体类型
 * <AUTHOR>
 */
public interface BaseMapper<T> {
    
    /**
     * 根据ID查询
     * 
     * @param id 主键ID
     * @return 实体对象
     */
    T selectById(Long id);
    
    /**
     * 查询所有记录
     * 
     * @return 实体列表
     */
    List<T> selectAll();
    
    /**
     * 根据条件查询列表
     * 
     * @param entity 查询条件
     * @return 实体列表
     */
    List<T> selectList(T entity);
    
    /**
     * 根据条件查询单个记录
     * 
     * @param entity 查询条件
     * @return 实体对象
     */
    T selectOne(T entity);
    
    /**
     * 根据条件查询记录数
     * 
     * @param entity 查询条件
     * @return 记录数
     */
    int selectCount(T entity);
    
    /**
     * 插入记录
     * 
     * @param entity 实体对象
     * @return 影响行数
     */
    int insert(T entity);
    
    /**
     * 插入记录（选择性插入）
     * 
     * @param entity 实体对象
     * @return 影响行数
     */
    int insertSelective(T entity);
    
    /**
     * 批量插入
     * 
     * @param list 实体列表
     * @return 影响行数
     */
    int insertBatch(List<T> list);
    
    /**
     * 根据ID更新
     * 
     * @param entity 实体对象
     * @return 影响行数
     */
    int updateById(T entity);
    
    /**
     * 根据ID选择性更新
     * 
     * @param entity 实体对象
     * @return 影响行数
     */
    int updateByIdSelective(T entity);
    
    /**
     * 根据条件更新
     * 
     * @param entity 更新内容
     * @param condition 更新条件
     * @return 影响行数
     */
    int updateByCondition(T entity, T condition);
    
    /**
     * 根据ID删除
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Long id);
    
    /**
     * 根据ID批量删除
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteByIds(Long[] ids);
    
    /**
     * 根据条件删除
     * 
     * @param entity 删除条件
     * @return 影响行数
     */
    int deleteByCondition(T entity);
    
    /**
     * 根据ID逻辑删除
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int logicDeleteById(Long id);
    
    /**
     * 根据ID批量逻辑删除
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int logicDeleteByIds(Long[] ids);
}
