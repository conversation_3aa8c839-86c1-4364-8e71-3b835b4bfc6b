<%@ page language="java" pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!doctype html>
<html>
<head>
<base href="<%=basePath%>"></base>  
<meta charset="utf-8">
<title>自动分案获取案件管理查询条件</title>
<link rel="shortcut icon" href="favicon.ico"/>
<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<script type="text/javascript" src="js/prototype.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">
	window.onload=function(){
		var formArgs = parent.getFormArgs();
		formArgs.op="toSetAutoAssignEmpOfSearch";
		$('saveForm').action="caseAction.do";
		createHideInput("saveForm",formArgs);
		$("saveForm").submit();
	}
	
</script>
</head>

<body>
<form id="saveForm" method="post"></form>
</body>
</html>