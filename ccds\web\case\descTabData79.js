function getLayout1(){
	return  new Array(
		['NAME','BANK','BAT_CODE','C_DT'],
		['ID_NO','C_CODE','STATE','AREA'],
		['AMT','PRI','OV_INT','OV_P'],
		[['RMB',1,'出催金额'],['GB',1,'降一期金额'],['MY',1,'降二期金额'],'MIN_P'],
		['AGE','BILL_DT','LAST_P_DT','BACK_DT'],
		['PAID_LEFT','M_CAT','DAYS','COUNT'],
		['MOB','PHO','W_PHO','HOM'],
		['COM','W_ADDR','REG','M_ADDR'],
		['C1_NAME','C1_HM_PHO','C1_W_PHO','C1_MOB'],
		['PTP','CP','TIPS_DT','PR_COUNT'],
		['CL_AREA','EMP_NAME','LAST_CL','LAST_VIS'],
		[['TREMARK',7]]
	);
}
function getLayout2(){
	return  new Array(
		['FILE_NO','LEFT_PRI','LOAN_RATE','MONTH_P'],
		['ACC','START_DT','LAST_P','CL_T'],
		['LOAN_DT','F_BANK','OVERDUE_M','DEAD_L'],
		['P_COUNT','PROD',['BIZ',3]],
		['OVERDUE_NUM','HOST','ALT_HOLD','NOOUT'],
		['ASS_TM',['ASS_HIS',5]],
		['H_POST','W_POST','M_POST','EMAIL'],
		['STOP_DT','LAST_C_DT','LAST_R_DT','CYCLE'],
		['OV_DT','G_AMT','APP_NO','C_L'],
		['DELAY_LV','BACK_AMT','P_L','P_CRLINE'],
		['LOAN_T','POS','PART','BIR'],
		[['SC_PC_NO',3],['SC_NO',3]],
		['C1_ID_NO','C1_COM',['C1_ADR',3]],
		['C2_NAME','C2_ID_NO','C2_HM_PHO','C2_W_PHO'],
		['C2_MOB','C2_COM',['C2_ADR',3]],
		['C3_NAME','C3_ID_NO','C3_HM_PHO','C3_W_PHO'],
		['C3_MOB','C3_COM',['C3_ADR',3]],
		['C4_NAME','C4_ID_NO','C4_HM_PHO','C4_W_PHO'],
		['C4_MOB','C4_COM',['C4_ADR',3]],
		['C5_NAME','C5_ID_NO','C5_HM_PHO','C5_W_PHO'],
		['C5_MOB','C5_COM',['C5_ADR',3]],
		['C6_NAME','C6_ID_NO','C6_HM_PHO','C6_W_PHO'],
		['C6_MOB','C6_COM',['C6_ADR',3]],
		[['O_REC',3],'REM1','REM2'],
		['REM3','REM4','REM5','REM6'],
		[['ATT',7]]
	);
}