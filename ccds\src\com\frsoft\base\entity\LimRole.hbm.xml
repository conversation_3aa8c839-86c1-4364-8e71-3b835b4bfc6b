<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.LimRole" table="lim_role" schema="dbo">
        <id name="rolId" type="java.lang.Long">
            <column name="rol_id" />
            <generator class="identity" />
        </id>
        <property name="rolLev" type="java.lang.Integer">
            <column name="rol_lev" />
        </property>
        <property name="rolName" type="java.lang.String">
            <column name="rol_name" length="50" />
        </property>
        <property name="roleDesc" type="java.lang.String">
            <column name="rol_desc" length="1073741823" />
        </property>
        <many-to-one name="rolGroup" class="com.frsoft.base.entity.LimGroup">
        	<column name="rol_grp_id" />
        </many-to-one>
        <set name="RRoleRigs" inverse="true">
            <key>
                <column name="rrr_rol_code" />
            </key>
            <one-to-many class="com.frsoft.base.entity.RRoleRig" />
        </set>
        <set name="limUsers" inverse="true"  order-by="user_code desc">
            <key>
                <column name="user_role_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.LimUser" />
        </set>
        <set name="salEmps" inverse="true">
            <key>
                <column name="se_job_lev"/>
            </key>
            <one-to-many class="com.frsoft.base.entity.SalEmp" />
        </set>
    </class>
</hibernate-mapping>
