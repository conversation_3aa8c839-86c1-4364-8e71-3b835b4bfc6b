<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.SupContact" table="sup_contact" schema="dbo" >
        <id name="scnId" type="java.lang.Long">
            <column name="scn_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salSupplier" class="com.psit.struts.entity.SalSupplier" fetch="select" not-null="false">
            <column name="scn_ssu_id" />
        </many-to-one>
        <property name="scnName" type="java.lang.String">
            <column name="scn_name" length="50" />
        </property>
        <property name="scnSex" type="java.lang.String">
            <column name="scn_sex" length="50" />
        </property>
        <property name="scnDep" type="java.lang.String">
            <column name="scn_dep" length="1073741823" />
        </property>
        <property name="scnService" type="java.lang.String">
            <column name="scn_service" length="100" />
        </property>
        <property name="scnPhone" type="java.lang.String">
            <column name="scn_phone" length="50" />
        </property>
        <property name="scnWorkPho" type="java.lang.String">
            <column name="scn_work_pho" length="50" />
        </property>
        <property name="scnHomePho" type="java.lang.String">
            <column name="scn_home_pho" length="50" />
        </property>
        <property name="scnFex" type="java.lang.String">
            <column name="scn_fex" length="50" />
        </property>
        <property name="scnZipCode" type="java.lang.String">
            <column name="scn_zip_code" length="50" />
        </property>
        <property name="scnEmail" type="java.lang.String">
            <column name="scn_email" length="100" />
        </property>
        <property name="scnQq" type="java.lang.String">
            <column name="scn_qq" length="50" />
        </property>
        <property name="scnMsn" type="java.lang.String">
            <column name="scn_msn" length="100" />
        </property>
        <property name="scnAdd" type="java.lang.String">
            <column name="scn_add" length="1073741823" />
        </property>
        <property name="scnOthLink" type="java.lang.String">
            <column name="scn_oth_link" length="1073741823" />
        </property>
        <property name="scnRemark" type="java.lang.String">
            <column name="scn_remark" length="1073741823" />
        </property>
        <property name="scnInpUser" type="java.lang.String">
            <column name="scn_inp_user" length="50" />
        </property>
        <property name="scnUpdUser" type="java.lang.String">
            <column name="scn_upd_user" length="50" />
        </property>
        <property name="scnCreDate" type="java.util.Date">
            <column name="scn_cre_date" length="23" />
        </property>
        <property name="scnModDate" type="java.util.Date">
            <column name="scn_mod_date" length="23" />
        </property>
        <property name="scnIsdel" type="java.lang.String">
            <column name="scn_isdel" length="1" />
        </property>
    </class>
</hibernate-mapping>
