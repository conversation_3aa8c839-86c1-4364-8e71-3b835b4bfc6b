<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.cis.entity.SalPra" table="sal_pra" schema="dbo" >
        <id name="praId" type="java.lang.Long">
            <column name="pra_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="cusCorCus" class="com.frsoft.cis.entity.CusCorCus" fetch="select"  not-null="false">
            <column name="pra_cor_code" length="50" />
        </many-to-one>
        <many-to-one name="salOpp" class="com.frsoft.cis.entity.SalOpp" fetch="select"  not-null="false">
            <column name="pra_opp_id"/>
        </many-to-one>
        <many-to-one name="limUser" class="com.frsoft.base.entity.LimUser" fetch="select"  not-null="false">
            <column name="pra_user_code" length="50" />
        </many-to-one>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="pra_se_no" />
        </many-to-one>
        <property name="praTitle" type="java.lang.String">
            <column name="pra_title" length="300" />
        </property>
        <property name="praContent" type="java.lang.String">
            <column name="pra_content" length="1073741823" />
        </property>
        <property name="praInsDate" type="java.util.Date">
            <column name="pra_ins_date" length="23" />
        </property>
        <property name="praType" type="java.lang.String">
            <column name="pra_type" length="100" />
        </property>
        <property name="praState" type="java.lang.String">
            <column name="pra_state" length="100" />
        </property>
        <property name="praContType" type="java.lang.String">
            <column name="pra_cont_type" length="100" />
        </property>
        <property name="praUpdDate" type="java.util.Date">
            <column name="pra_upd_date" length="23" />
        </property>
        <property name="praExeDate" type="java.util.Date">
            <column name="pra_exe_date" length="23" />
        </property>
        <property name="praCostTime" type="java.lang.String">
            <column name="pra_cost_time" length="20" />
        </property>
        <property name="praCusLink" type="java.lang.String">
            <column name="pra_cus_link" length="50" />
        </property>
        <property name="praInpUser" type="java.lang.String">
            <column name="pra_inp_user" length="50" />
        </property>
        <property name="praUpdUser" type="java.lang.String">
            <column name="pra_upd_user" length="50" />
        </property>
        <property name="praRemark" type="java.lang.String">
            <column name="pra_remark" length="1073741823" />
        </property>
         <property name="praIsDel" type="java.lang.String">
            <column name="pra_isdel" length="1" />
        </property>
        <set name="attachments" inverse="true" order-by="att_date desc"  cascade="all" where="att_type='cusPra'">
            <key>
                <column name="att_fk_id" />
            </key>
            <one-to-many class="com.frsoft.base.entity.Attachment" />
        </set>
    </class>
</hibernate-mapping>
