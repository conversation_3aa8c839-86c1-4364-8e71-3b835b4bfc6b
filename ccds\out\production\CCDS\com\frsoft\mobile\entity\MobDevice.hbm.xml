<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="com.frsoft.mobile.entity.MobDevice" table="mob_device" schema="dbo" >
        <id name="mdvId" type="java.lang.Long">
            <column name="mdv_id" />
            <generator class="identity" />
        </id>
        <property name="mdvMacAddr" type="java.lang.String">
            <column name="mdv_mac_addr" length="50"  not-null="true" />
        </property>
        <property name="mdvHasAccess" type="java.lang.Integer">
            <column name="mdv_has_access" />
        </property>
        <property name="mdvIsGpsEnabled" type="java.lang.Integer">
            <column name="mdv_is_gps_enabled" />
        </property>
        <property name="mdvRemark" type="java.lang.String">
            <column name="mdv_remark" length="500" />
        </property>
        <property name="mdvCreUser" type="java.lang.String">
            <column name="mdv_cre_user" length="25" />
        </property>
        <property name="mdvUpdUser" type="java.lang.String">
            <column name="mdv_upd_user" length="25" />
        </property>
        <property name="mdvCreTime" type="java.util.Date">
            <column name="mdv_cre_time" length="23" />
        </property>
        <property name="mdvUpdTime" type="java.util.Date">
            <column name="mdv_upd_time" length="23" />
        </property>
        <many-to-one name="mdvLimUser" class="com.frsoft.base.entity.LimUser">
            <column name="mdv_user_code" />
        </many-to-one>
    </class>
</hibernate-mapping>
