<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.RNewLim" table="r_new_lim" schema="dbo">
        <id name="rnlId" type="java.lang.Long">
            <column name="rnl_id" />
            <generator class="identity" />
        </id>
        <many-to-one name="salEmp" class="com.frsoft.base.entity.SalEmp" fetch="select" not-null="false">
            <column name="rnl_se_no"/>
        </many-to-one>
        <many-to-one name="news" class="com.frsoft.base.entity.News" fetch="select" not-null="false">
            <column name="rnl_new_code" length="50" />
        </many-to-one>
        <property name="rnlDate" type="java.util.Date">
            <column name="rnl_date" length="23" />
        </property>
    </class>
</hibernate-mapping>
