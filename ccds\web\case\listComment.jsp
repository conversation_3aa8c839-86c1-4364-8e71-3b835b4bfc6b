 <%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://struts.apache.org/tags-logic"  prefix="logic"%>
<%@ taglib uri="http://struts.apache.org/tags-bean" prefix="bean"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";	
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    
    <title>评语列表</title>
    <link rel="shortcut icon" href="favicon.ico"/>
    <meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <script type="text/javascript" src="js/prototype.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript">
    	function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			switch(obj.cotState){
				case 1:
					className="cRowRed";
					break;
				case 2:
					className="cRowBlue";
					break;
			}
			var funcCol = "<img onClick=\"parent.casePopDiv(39,'"+obj.cotId+"')\" class=\"hand\" src=\"images/content/edit.gif\" alt=\"编辑\"/>&nbsp;&nbsp;<img onClick=\"parent.caseDelDiv(5,'"+obj.cotId+"','1')\" class='hand' src='images/content/del.gif' alt='删除'/>";
			datas = [obj.cotContent, obj.cotUser, obj.cotTime, funcCol];
			if('${operational}'=='0'){
				datas.pop();
			}
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "caseAction.do";
			var pars = [];
			pars.op = "listComment";
			pars.casId="${casId}";
			
			var loadFunc = "loadList";
			var cols=[
				{name:"评语内容",width:'72%',align:'left'},
				{name:"提交人",width:'10%'},
				{name:"提交时间",renderer:"time",width:'13%'},
				{name:"操作",width:'5%'}
			];
			if('${operational}'=='0'){
				cols.pop();
			}
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper);
		}
		
    	var gridEl = new MGrid("commentListTab${operational}","dataList");
    	gridEl.config.sortable=false;
		gridEl.config.isResize=false;
		gridEl.config.isShort=false;
		createProgressBar();
   		window.onload=function(){
			loadList();
		}
    </script>
</head> 
  
  <body>
  	<div class="divWithScroll2 innerIfm">
  		<div id="dataList" class="dataList"></div>
    </div>
  </body>
</html>