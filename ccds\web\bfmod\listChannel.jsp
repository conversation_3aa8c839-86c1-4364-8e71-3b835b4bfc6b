<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>通道列表</title>
    
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="js/bfmod.js"></script>
  	<script language="javascript" type="text/javascript">
  		var reloadInterval;
  		function startAutoReload(){
  			reloadInterval = setInterval("loadList()",5*1000);
  			$("autoReloadBtn").onclick=stopAutoReload;
  			$("autoReloadBtn").innerHTML = "关闭自动刷新";
  		}
  		function stopAutoReload(){
  			clearInterval(reloadInterval);
  			$("autoReloadBtn").onclick=startAutoReload;
  			$("autoReloadBtn").innerHTML = "打开自动刷新";
  		}
  	
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			dataId = obj.channelNo;
			datas = [obj.channelNo, obj.tel,obj.ip, obj.statusName,/*  obj.userCode, obj.userName, */ "<img onClick=\"ctiPopDiv(2,['"+dataId+"','"+obj.tel+"','"+obj.ip+"'])\" class=\"hand\" src=\"images/content/edit.gif\" alt=\"编辑\"/>&nbsp;&nbsp;<img onClick=\"ctiPopDiv(21,['"+dataId+"'])\" class=\"hand\" src=\"images/content/cancel.gif\" alt=\"清空\"/>" ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "ctiServerAction.do";
			var pars = [];
			pars.op="listChannel";
			pars.serverId = $("serverId").value;
			var loadFunc = "loadList";
			var cols=[
				{name:"通道号"},
				{name:"电话"},
				{name:"IP"},
				{name:"状态"},
				//{name:"账号ID"},
				//{name:"账号姓名"},
				{name:"操作"}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper,null,errInfoCallBack);
		}
		
    	var gridEl = new MGrid("channelListTab","dataList");
    	gridEl.config.sortable = false;
    	<c:if test="${!empty ctisList}">
		createProgressBar();
		window.onload=function(){
			//loadList();	
			closeProgressBar();
		} 
		</c:if>
  	</script>
  </head>
  
  <body>
    <div id="mainbox">
    	<div id="contentbox">
        	<div id="title">
            	<table>
                	<tr>
                    	<th>录音管理 > 通道设置 <span id="changeFuncBt" onMouseOver="popFuncMenu(['rec',3],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['rec',3],true)" onMouseOut="popFuncMenu(['rec',3],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
           	</div>
  	   		<table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                             <div id="tabType1" class="tabTypeWhite" onClick="self.location.href='ctiServerAction.do?op=toListChannel'">通道设置</div>
                        </div>
                     </th>
                </tr>
            </table>
            <script type="text/javascript">loadTabTypeWidth();</script>
            <div id="listContent">
                <c:if test="${empty ctisList}">
               	<div class="grayBack" style="padding:10px; font-size:14px">未添加录音服务器，请在<a href="ctiServerAction.do?op=toListCtis">服务器设置</a>中添加</div>
                </c:if>
                <c:if test="${! empty ctisList}">
                	<div class="listSearch">
                		选择录音服务器：<select id="serverId" class="inputSize2 inputBoxAlign">
                		<c:forEach items="${ctisList}" var="ctis"><option value="${ctis.ctisId}">${ctis.ctisName}</option></c:forEach></select>&nbsp;&nbsp;
                		<button class="inputBoxAlign butSize3" onclick="loadList()">点击查看通道</button>
                		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<button class="inputBoxAlign butSize3" id="autoReloadBtn" onclick="startAutoReload()">打开自动刷新</button>
                	</div>
                	<div id="errMsgLayer" class="redWarn" style="display:none; margin:0;"></div>
                	<div id="dataList" class="dataList"></div>	
                </c:if>
                
            </div>
  		</div> 
	</div>
  </body>
  
</html>
