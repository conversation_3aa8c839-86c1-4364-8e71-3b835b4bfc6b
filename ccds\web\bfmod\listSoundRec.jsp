<%@ page language="java" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core"  prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>录音记录列表</title>
    
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
	<style type="text/css">
		.inputSize2 { width: 120px; }
		.queryFieldHead { height:30px; font-size:14px; color:#999; }
		.queryFieldHead span { font-size:12px; color:#fca84d; }
	</style>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
	<script type="text/javascript" src="js/formCheck.js"></script>
	<script type="text/javascript" src="js/bfmod.js"></script>
  	<script language="javascript" type="text/javascript">
  		function loadTabType(listType){
			switch(listType){
				case '1'://全部
					$("tabType2").className="tabTypeWhite";
					setOtherStyle(2,2);
					break;
				default:
					$("tabType1").className="tabTypeWhite";
					setOtherStyle(1,2);
			}
			loadTabTypeWidth();
		}
  		
  		function toExportSoundRec(){
  			if($('recStartTime').value==""&&$('recEndTime').value==""){
  				alert("请先选择录音时间");
  			}
  			else{
  				ctiPopDiv(3);
  			}
  		}
  	
  		function getFormArgs(){
			var pars = $("searchForm").serialize(true);
			return pars;
		}
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			var caseId  = "";
			if(obj.caseId!=""){
				caseId = "<a href=\"javascript:void(0)\" onclick=\"descPop('caseAction.do?op=caseDesc&caseId="+obj.caseId+"&view=case');return false;\">"+obj.caseId+"</a>";
			}
			datas = [getCallType(obj.callType), obj.localPhone, obj.callPhone,  obj.recStartTime, obj.recEndTime, formatSecond(obj.duration), caseId, obj.channelNum, getRecFile(obj.soundRecPath,obj.id), obj.userName ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(sortCol,isDe,pageSize,curP){
			var url = "soundRecordAction.do";
			var pars =  getFormArgs();
			pars.op="listSoundRec";
			pars.isAll = "${isAll}";
			var loadFunc = "loadList";
			var cols=[
				{name:"呼叫类型",width:'5%'},
				{name:"本机号码",width:'10%'},
				{name:"对方号码",width:'10%'},
				{name:"录音时间",renderer:'stime',width:'15%'},
				{name:"结束时间",renderer:'stime',width:'15%'},
				{name:"通话时长",width:'10%'},
				{name:"案件ID",width:'10%'},
				{name:"通道号",width:'5%'},
				{name:"录音文件",width:'10%'},
				{name:"员工姓名",width:'10%'}
			];
			gridEl.init(url,pars,cols,loadFunc,sortCol,isDe,pageSize,curP);
			gridEl.loadData(dataMapper,null,errInfoCallBack);
			
			if($("toolsBarTop").style.display=="none"){
				$("toolsBarTop").show();
			}
		}
		
    	var gridEl = new MGrid("soundRecByPageListTab","dataList");
    	gridEl.config.sortable = false;
		createProgressBar();
		window.onload=function(){
			if("${CUR_CTI_SERVER_IP}"!=""){
				$("casNameTxt").innerHTML=getCasNameTxt("${CUS_VER_ID}");
				$("bankTxt").innerHTML=getBankTxt("${CUS_VER_ID}");
				$("casCaCdTxt").innerHTML=getCasCaCdTxt("${CUS_VER_ID}");
				loadCaseState('caseState');
				$("caseState").value="";//默认全部
			}
			closeProgressBar();
		} 
  	</script>
  </head>
  
  <body>
    <div id="mainbox">
    	<div id="contentbox">
    		<div id="mulTextTip" class="floatTipsDiv" style="display:none;">&nbsp;可输入多个查询文本，多个文本之间请换行（单个查询是模糊查询，多个查询是精确查询）&nbsp;</div>
        	<div id="title">
            	<table>
                	<tr>
                    	<th>录音管理 > 录音查询 <span id="changeFuncBt" onMouseOver="popFuncMenu(['rec',0],true)">切换</span><div id="funcMenu" onMouseOver="popFuncMenu(['rec',0],true)" onMouseOut="popFuncMenu(['rec',0],false)" style="display:none"><div id="showFuncBt">切换</div></div></th>
                        <td><a class="refreshButton" href="javascript:void(0)" onClick="self.history.go(0)" title="刷新">&nbsp;</a></td>
                    </tr>
                </table>
           	</div>
  	   		<table class="mainTab" cellpadding="0" cellspacing="0">
                <tr>
                    <th>
                        <div id="tabType">
                             <div id="tabType1" onClick="self.location.href='soundRecordAction.do?op=toListLocalSoundRec'">本地录音查询</div>
                             <div id="tabType2" onClick="self.location.href='soundRecordAction.do?op=toListAllSoundRec'">所有录音查询</div>
                        </div>
                     </th>
                </tr>
            </table>
            <script type="text/javascript">
            	loadTabType('${isAll}');
            	loadTabTypeWidth();
				displayLimAllow("r_recq003","tabType2");	
            </script>
            <div id="listContent">
            	<c:if test="${empty CUR_CTI_SERVER_IP}">
			   		<div class="grayBack" style="padding:10px; font-size:14px">未添加录音服务器，请在<a href="ctiServerAction.do?op=toListCtis">服务器设置</a>中添加</div>
			    </c:if>
			    <c:if test="${!empty CUR_CTI_SERVER_IP}">
			  	<div class="listSearch">
			  		<form class="listSearchForm" id="searchForm" onSubmit="loadList();return false;" >
                    <table cellpadding="0" cellspacing="0">
                    	<c:if test="${!empty ctisList}">
                    	<tr>
                    		<th>&nbsp;<b>选择录音服务器：</b></th>
                    		<td colspan=7><select id="serverId" name="soundRecQuery.serverId" class="inputSize2"><c:forEach items="${ctisList}" var="ctis"><option value="${ctis.ctisId}">${ctis.ctisName}</option></c:forEach></select>
                    		<c:forEach items="${ctisList}" var="ctish">
                    		<input type="hidden" id="ctiServer${ctish.ctisId}" value="${ctish.ctisIp}"/>
                    		</c:forEach>
                    		</td>
                    	</tr>
                    	</c:if>
                    	<c:if test="${empty ctisList}">
                    		<input type="hidden" id="curServerIP" value="${CUR_CTI_SERVER_IP}" /><!-- 生成录音文件链接使用 -->
                    	</c:if> 
                    	<tr> 
                    		<td colspan=8 class="queryFieldHead">录音信息</td>
                    	</tr>
                    	<tr>
                    		<th>通道号：</th>
                            <td><input class="inputSize2" type="text" name="soundRecQuery.channelNum" onBlur="checkIsNum(this)"/></td>
                    		<th>呼叫类型：</th>
                            <td><select class="inputSize2" name="soundRecQuery.callType"><option></option><option value="1">呼入</option><option value="2">呼出</option></select></td>
                            <th>部门：</th>
                       		<td><select id="org" name="soundRecQuery.orgCode" class="inputSize2 inputBoxAlign"  onChange="loadOrgEmp(this)"><option></option><c:if test="${!empty orgList}"><c:forEach items="${orgList}" var="oList"><option value="${oList.soCode}">${oList.soName}</option></c:forEach></c:if></select></td>
                           	<th>催收员：</th>
                            <td><input style="width:60px;" class="inputSize2 inputBoxAlign" type="text" id="empName" name="soundRecQuery.seName" onBlur="autoShort(this,100)"/>&nbsp;<c:if test="${!empty empList}"><select id="empSel" class="inputSize2 inputBoxAlign" style="width:70px" onChange="setEmpNameFromSel(this,'empName')"><option value="">请选择</option><c:forEach items="${empList}" var="eList"><option value="${eList.seName}">${eList.seName}</option></c:forEach></select></c:if><c:if test="${empty empList}"><select class="inputSize2 inputBoxAlign" style="width:70px" disabled="disabled"><option>未添加</option></select></c:if></td>
                    	</tr>
                    	<tr>	
                    		<th>本机号码：</th>
                            <td><input class="inputSize2" type="text" name="soundRecQuery.localPhone" onBlur="autoShort(this,50)"/></td>
                    		<th>对方号码：</th>
                            <td><input class="inputSize2" type="text" name="soundRecQuery.callPhone" onBlur="autoShort(this,50)"/></td>
                    		<th>录音时间：</th>
                            <td colspan=3><input name="soundRecQuery.recStartTime" id="recStartTime" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:140px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',onpicked:function(){$('recEndTime').focus();},maxDate:'#F{$dp.$D(\'recEndTime\')}'})"/>&nbsp;到&nbsp;<input name="soundRecQuery.recEndTime" id="recEndTime" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand; width:140px;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',minDate:'#F{$dp.$D(\'recStartTime\')}'})"/></td>
                        </tr>
                    	<tr>
                    		<td colspan=8 class="queryFieldHead">案件信息<span>&nbsp;&nbsp;&nbsp;&nbsp;如果查询出的案件过多可能会查询失败，请尽量缩小案件查询范围</span></td>
                    	</tr>
                    	<tr>
                           	<th>催收区域：</th>
                            <td>
                               <c:if test="${!empty userAreaList}">
                               <select name='soundRecQuery.clAreaId' class="inputSize2">
                                   <option value="<c:forEach items="${userAreaList}" var="uArea">${uArea.uarArea.typId},</c:forEach>">全部</option>
                                   <c:forEach items="${userAreaList}" var="userArea">
                                   <option value="${userArea.uarArea.typId}">${userArea.uarArea.typName}</option>
                                   </c:forEach>
                               </select>
                               </c:if>
                               <c:if test="${empty userAreaList}">
                                   <c:if test="${!empty areaList}">
                                   <select name='soundRecQuery.clAreaId' class="inputSize2">
                                       <option value="">全部</option>
                                       <c:forEach items="${areaList}" var="area">
                                       <option value="${area.typId}">${area.typName}</option>
                                       </c:forEach>
                                   </select>    
                                   </c:if>
                                   <c:if test="${empty areaList}">
                                   <select name='soundRecQuery.clAreaId' class="inputSize2">
                                       <option value="">未添加</option>
                                   </select>
                                   </c:if>
                               </c:if></td>
                            <th>案件状态：</th>
                            <td><select  id="caseState" name="soundRecQuery.casState" class="inputSize2"><option value="u">未退案</option></select></td>
                           	<th>委案日期：</th>
                            <td colspan=3><input name="soundRecQuery.casDateStart" id="cdStart" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="var pid1=$('cdEnd');WdatePicker({onpicked:function(){pid1.focus();},maxDate:'#F{$dp.$D(\'cdEnd\')}'})"/>&nbsp;到&nbsp;<input name="soundRecQuery.casDateEnd" id="cdEnd" type="text" class="inputSize2 inputBoxAlign Wdate" style="cursor:hand;" readonly ondblClick="clearInput(this)" onFocus="WdatePicker({minDate:'#F{$dp.$D(\'cdStart\')}'})"/></td>
                        </tr>
                        <tr>
                        	<th><span id="bankTxt"></span>：</th>
                            <td colspan="3">
                               <input type="hidden" id="bankId" name="bankId" />
                               <input style="width:150px;" class="inputSize2 inputBoxAlign" type="text" id="bankName" name="soundRecQuery.bankName" onBlur="autoShort(this,100)"/><c:if test="${!empty bankList}">
                               <select class="inputSize2 inputBoxAlign" style="width:120px" onChange="setBankNameAndIdFromSel(this,'bankId','bankName')">
                                   <option value="">请选择</option>
                                   <c:forEach items="${bankList}" var="bList">
                                   <option value="${bList.typId}">${bList.typName}</option>
                                   </c:forEach>
                               </select>
                               </c:if>
                               <c:if test="${empty bankList}">
                                   <select class="inputSize2 inputBoxAlign" disabled="disabled" style="width:120px">
                                       <option>未添加</option>
                                   </select>
                               </c:if>
                            </td>
                            <th>批次号：</th>
                            <td colspan="3"><input class="inputSize2" style=" width:218px" type="text" id="batCodeStr" name="soundRecQuery.batCode" onDblClick="cleanBatCodeInput()"/><input type="text" style="display:none" id="batIds" name="soundRecQuery.batIds"/>&nbsp;<button class="butSize2" onClick="showBatList()" style="width:40px">选择</button></td>
                        	
                        </tr>
                        <tr>
                        	<th>个案序列号&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                           	<td><textarea rows="3" class="inputSize2 inputBoxAlign" id="caseCode" name="soundRecQuery.casCode"></textarea></td>
                       		<th>证件号&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                           	<td><textarea rows="3" class="inputSize2 inputBoxAlign" id="caseNum" name="soundRecQuery.casNum"></textarea></td>
                       		<th>账号&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                           	<td><textarea rows="3" class="inputSize2 inputBoxAlign" id="casAccNum" name="soundRecQuery.casAccNum"></textarea></td>
                       	 	<th><span id="casCaCdTxt"></span>&nbsp;<img class="imgAlign" src="images/content/help.png" onMouseMove="floatTipsLayer('mulTextTip',0,10)" onMouseOut="floatTipsLayer('mulTextTip')"/>：</th>
                           	<td><textarea rows="3" class="inputSize2 inputBoxAlign" id="caseCaCd" name="soundRecQuery.casCaCd"></textarea></td>
						</tr>                        
                        <tr>	
                       		<th>案件类型：</th>
                           	<td><c:if test="${!empty caseTypeList}"><select id="caseTypeId" name="soundRecQuery.caseTypeId" class="inputSize2 inputBoxAlign"><option value="">请选择</option><c:forEach items="${caseTypeList}" var="cTypeList"><option value="${cTypeList.typId}">${cTypeList.typName}</option></c:forEach></select></c:if><c:if test="${empty caseTypeList}"><select id="caseTypeId" name="soundRecQuery.caseTypeId" class="inputSize2 inputBoxAlign" disabled="disabled"><option>未添加</option></select></c:if></td>
                         	<th>催收状态：</th>
                            <td><c:if test="${!empty caseStateList}"><select id="caseStateId" name="soundRecQuery.clStateId" class="inputSize2 inputBoxAlign"><option value="">请选择</option><option value="NULL">新案</option><c:forEach items="${caseStateList}" var="cStateList"><option value="${cStateList.typId}">${cStateList.typName}</option></c:forEach></select></c:if><c:if test="${empty caseStateList}"><select id="caseStateId" name="soundRecQuery.clStateId" class="inputSize2 inputBoxAlign" disabled="disabled"><option>未添加</option></select></c:if>
                            
                         	<th><span id="casNameTxt"></span>：</th>
                           <td><input class="inputSize2 inputBoxAlign" type="text" name="soundRecQuery.casName" onBlur="autoShort(this,50)"/></td>
                        	<td colspan=2>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input id="searButton" type="submit" class="butSize1 inputBoxAlign" value="查询"/></td>
                         </tr>
                     </table>
                    </form>
			   	</div>
               	<div id="toolsBarTop" class="bottomBar" style="display:none;">
                    <span class="grayBack" style="width:120px;" onMouseOver="this.className='orangeBox red'" onMouseOut="this.className='grayBack'" onClick="toExportSoundRec()">导出查询结果录音</span>
                </div>
			  	<div id="errMsgLayer" class="redWarn" style="display:none; margin:0;"></div>
                <div id="dataList" class="dataList"></div>	
                <input id="getDownloadAccess" type="hidden" style="display:none" />
                <script type="text/javascript">displayLimAllow("r_recq001","getDownloadAccess");</script>
                </c:if>
            </div>
  		</div> 
	</div>
  </body>
  
</html>
