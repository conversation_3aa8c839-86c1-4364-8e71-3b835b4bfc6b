#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终清理脚本 - 彻底清理所有MSSQL语法
"""

import re

def final_clean(content):
    """最终清理"""
    
    print("开始最终清理...")
    
    # 1. 移除所有SET语句
    content = re.sub(r'SET ANSI_PADDING (ON|OFF)', '', content)
    content = re.sub(r'SET ANSI_NULLS (ON|OFF)', '', content)
    content = re.sub(r'SET QUOTED_IDENTIFIER (ON|OFF)', '', content)
    
    # 2. 移除ON PRIMARY子句
    content = re.sub(r'\s+ON\s+`PRIMARY`', '', content)
    content = re.sub(r'\s+TEXTIMAGE_ON\s+`PRIMARY`', '', content)
    
    # 3. 修复PRIMARY KEY语法
    content = re.sub(r'PRIMARY KEY \(`(\w+)`\) ON `PRIMARY`', r'PRIMARY KEY (`\1`)', content)
    
    # 4. 修复ENGINE语法错误
    content = re.sub(r'\) ENGINE=InnoDB[^;]*; ON `PRIMARY`[^;]*', ') ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;', content)
    
    # 5. 修复字段定义中的语法错误
    content = re.sub(r'`rol_grp_id` bigint,', '`rol_grp_id` bigint DEFAULT NULL,', content)
    
    # 6. 清理多余的分号和空行
    content = re.sub(r';\s*;\s*', ';\n', content)
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 7. 移除空行前的分号
    content = re.sub(r';\s*\n\s*;', ';\n', content)
    
    return content

def main():
    """主函数"""
    
    with open('mysql57_ALL_106_TABLES_CLEAN.sql', 'r', encoding='utf-8') as f:
        content = f.read()
    
    cleaned_content = final_clean(content)
    
    with open('mysql57_ALL_106_TABLES_FINAL_CLEAN.sql', 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    print("最终清理完成: mysql57_ALL_106_TABLES_FINAL_CLEAN.sql")

if __name__ == "__main__":
    main()
