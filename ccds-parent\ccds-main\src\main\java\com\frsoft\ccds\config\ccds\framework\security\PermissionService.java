package com.frsoft.ccds.framework.security;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * 自定义权限实现
 */
@Service("permissionService")
public class PermissionService {
    /**
     * 验证用户是否具备某权限
     *
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public boolean hasPermission(String permission) {
        if (permission == null || permission.isEmpty()) {
            return false;
        }
        // TODO: 从SecurityContextHolder中获取用户权限信息
        // 临时返回true，方便测试
        return true;
    }

    /**
     * 判断用户是否拥有某个角色
     *
     * @param role 角色字符串
     * @return 用户是否具备某角色
     */
    public boolean hasRole(String role) {
        if (role == null || role.isEmpty()) {
            return false;
        }
        // TODO: 从SecurityContextHolder中获取用户角色信息
        // 临时返回true，方便测试
        return true;
    }

    /**
     * 验证用户是否具有以下任意一个权限
     *
     * @param permissions 权限列表
     * @return 用户是否具有以下任意一个权限
     */
    public boolean hasAnyPermission(String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return false;
        }
        // TODO: 从SecurityContextHolder中获取用户权限信息
        // 临时返回true，方便测试
        return true;
    }

    /**
     * 判断是否包含权限
     *
     * @param authorities 权限列表
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    private boolean hasPermissions(Set<String> authorities, String permission) {
        return !CollectionUtils.isEmpty(authorities) && authorities.contains(permission);
    }
}