<%@ page language="java" pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <base href="<%=basePath%>"></base>
    <title>手动推荐分案</title>
    <link rel="shortcut icon" href="favicon.ico"/>
	<meta http-equiv="x-ua-compatible" content="ie=EmulateIE7" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">
	<link rel="stylesheet" type="text/css" href="css/style.css"/>
    <style type="text/css">
		#assContainer {
			width:930px;
			height:100%;
			border-left:#999999 1px solid;
			border-right:#999999 1px solid;
			padding:10px;
			background:#fff;
		}
		#assCaseTitle {
			font-size:16px;
			font-style:italic;
			font-weight:bold;
			color:#999999;
			text-align:left;
			padding:5px;
		}
		#assCaseList {
			border:#3243a3 1px solid;
			overflow:auto;
			overfolw-y:auto;
			scrollbar-face-color:#A6CFF4;
			scrollbar-highlight-color:#f9fbff;
			scrollbar-shadow-color:#A6CFF4;
			scrollbar-3dlight-color:#7DB4DB;
			scrollbar-arrow-color:#3243A3;
			scrollbar-track-color:#f0f0f1;
			scrollbar-darkshadow-color:#7DB4DB;
			height:400px;
			text-align:left;
		}
		#operaLayer {
			width:100%;
			padding:5px 5px 10px 5px;
			background-color:#eff7f7;
			text-align:left;
		}
		#stateBar {
			color:#666;
			padding:8px;
			text-align:right;
			border:#fba01f 1px solid;
			background-color:#fefbf2;
		}
		.keyDate {
			font-weight:bold;
			font-size:14px;
			color:#fe6804;
			padding:0 2px 2px 2px;
		}
		#choButton {
			margin-left:10px;
			width:200px;
			height:40px;
			font-size:18px;
		}
		#othCaseLayer{
			width:100%;
			background-color:#FFFFCC;
			text-align:left;
		}
		#othCaseTitle{
			font-size:14px;
			font-style:italic;
			font-weight:bold;
			color:#999999;
			padding:5px;
		}
		#othCaseIfm{
			width:100%;
			height:200px;
		}
		
    </style>
	<script type="text/javascript" src="js/prototype.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/case.js"></script>
    <script type="text/javascript" src="js/config.js"></script>
  	<script language="javascript" type="text/javascript">
		function selectToSum(selTrNum,tdSums){
			$("selSumCase").innerHTML = selTrNum;
			$("selSumMon").innerHTML = "￥"+changeTwoDecimal(tdSums[0]);
		}
		//返回json后回调此方法开始查询推荐催收员(共债催收员)
		function startLoadEmp(jsonData){
			var listObj = jsonData.list;
			if(listObj!=undefined && listObj!=""){
				for(var i=0; i<listObj.length; i++){
					var obj = listObj[i];
					setTimeout(function(id,num){ return function(){loadSysEmp(id,num)};}(obj.casId,obj.casNum),Math.floor(i+1)*150);
				}
			}
		}
		function loadSysEmp(casId,casNum){
			var url = "caseAction.do";
			var pars = "op=getSysEmp&casId="+casId+"&casNum="+encodeURIComponent(casNum)+"&ran=" + Math.random();
			new Ajax.Request(url,{
				method:"get",
				parameters: pars,
				onSuccess: function(transport){
					var responseTxt = transport.responseText;
					if(responseTxt.indexOf(",")!=-1){
						var empName = responseTxt.substring((responseTxt.indexOf(",")+1));
						$('sysEmp'+casId).innerHTML = "<span class='orange bold'>"+empName+"</span>";
					}
					else{
						$('sysEmp'+casId).innerHTML = "<span class='gray'>无</span>";
					}
				},
				onFailure: function(transport){
					$('sysEmp'+casId).innerHTML = "<span class='gray'>服务器忙...</span>";
					/*if (transport.status == 404)
						alert("您访问的url地址不存在！");
					else
						alert("服务器繁忙，请稍后再试(Error:" + transport.status+")");*/
				}
			});
		}
		
		function loadOthCaseIfm(casId,casNum,casCode){
			$("othCaseIfm").src="caseAction.do?op=toListOthCase&caseId="+casId;
			$("casCodeSel").innerHTML = casCode;
			$("casNumSel").innerHTML = casNum;
			if($("othCaseLayer").style.display=="none"){
				$("othCaseLayer").show();
			}
		}
		
		function clearAssedCase(){
			var trArray = $("assignCaseTab").getElementsByTagName("tr");
			for(var i=0;i<trArray.length;i++){
				if(trArray[i].clicked!=undefined&&trArray[i].clicked=="true"){
					$("assignCaseTab").deleteRow(trArray[i].rowIndex);
					i-=1;
				}
			}
			$("selSumCase").innerHTML="0";
			$("selSumMon").innerHTML="￥0.00";
		}
		
		function dataMapper(obj){
			var datas,className,dblFunc,dataId;
			dataId = obj.casId;
			var dblFunc="descPop('caseAction.do?op=caseDesc&caseId="+obj.casId+"&view=case')";
			var sysEmp = "<span id='sysEmp"+obj.casId+"'><img src='images/gif/uploading.gif' alt='加载中...'/></span>";
			var casCode="<a href=\"javascript:void(0)\" onclick=\"loadOthCaseIfm('"+obj.casId+"','"+obj.casNum+"','"+obj.casCode+"');return false;\">"+obj.casCode+"</a>";
			/* var areName = obj.casArea1;
			var prvName =  obj.casArea2;
			var cityName = obj.casArea3;
			var location = areName+"&nbsp;"+prvName+"&nbsp;"+cityName; */
			var outState=getVisState(obj.casOutState);
			var state = "";
			switch(obj.casState){
				case 0: state="正常"; break;
				case 1:state="暂停";break;
				case 2:state="关档";break;
				case 3:state="退档";break;
			}
			className = getCaseColor(obj);
			datas = [sysEmp, state, casCode, obj.casDate, obj.casName, obj.casOverdueNum, obj.casOverdueDays, obj.casCaCd, [obj.casM,obj.casM], obj.typeList?obj.typeList.typName:"新案", obj.casPtpM, obj.casCpM, obj.casPaidM, obj.salEmp?obj.salEmp.seName:"" ];
			return [datas,className,dblFunc,dataId];
		}
		function loadList(){
			var url = "caseAction.do";
			var pars = [];
			pars.op = "listAssignCases";
			pars.casIds="${casIds}";
			
			var loadFunc = "loadList";
			var cols=[
				{name:"推荐催收员"},
				{name:"案件状态"},
				{name:"个案序列号",align:"left"},
				{name:"委案日期",renderer:"date"},
				{name:getCasNameTxt("${CUS_VER_ID}")},
				{name:"逾期期数"},
				{name:"逾期天数"},
				{name:getCasCaCdTxt("${CUS_VER_ID}"),align:"left"},
				{name:"委案金额",renderer:"money",align:"right",isSelSum:true},
				{name:"催收状态"},
				{name:"PTP金额",renderer:"money",align:"right"},
				{name:"CP金额",renderer:"money",align:"right"},
				{name:"已还款",renderer:"money",align:"right"},
				{name:"当前催收员"}
			];
			gridEl.init(url,pars,cols,loadFunc);
			gridEl.loadData(dataMapper,"",startLoadEmp);
		}
		
    	var gridEl = new MGrid("assignCaseTab","assCaseList");
		gridEl.config.hasCheckBox = true;
		gridEl.config.hasPage = false;
		gridEl.config.sortable = false;
		gridEl.config.selCallBackFunc = "selectToSum";
		createProgressBar();
		window.onload=function(){
			loadList();
		}
  	</script>
  </head>
  
  <body>
  <div id="assContainer">
  	<div id="assCaseTitle">待分配的案件</div>
    <div id="assCaseList" class="dataList"></div>
    <div id="operaLayer">
    	<span id="stateBar">
        	已选择<span id="selSumCase" class="keyDate">0</span>件个案，总金额为<span id="selSumMon" class="keyDate">￥0.00</span>
        </span>
        <button id="choButton" class="inputBoxAlign" onClick="batchOp(0)">点击选择催收员</button>
    </div>
    <div id="othCaseLayer" style="display:none">
    	<div id="othCaseTitle">案件「<span id="casCodeSel"></span>」（证件号:<span id="casNumSel"></span>）的共债案件</div>
    	<iframe id="othCaseIfm" src="" scrolling="no" frameborder="0"></iframe>
    </div>
  </div>
  </body>
  
</html>
