<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.frsoft.base.entity.DocTemplate" table="doc_template" schema="dbo">
        <id name="tmpId" type="java.lang.Long">
            <column name="tmp_id" />
            <generator class="identity" />
        </id>
        <property name="tmpName" type="java.lang.String">
            <column name="tmp_name" length="50" />
        </property>
        <property name="tmpHtml" type="java.lang.String">
            <column name="tmp_html" />
        </property>
        <property name="tmpType" type="java.lang.String">
            <column name="tmp_type" length="50" />
        </property>
        <property name="tmpMark" type="java.lang.String">
            <column name="tmp_mark" length="100" />
        </property>
    </class>
</hibernate-mapping>
