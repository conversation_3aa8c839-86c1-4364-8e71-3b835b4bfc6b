<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.psit.struts.entity.CashBusWorkOrder" table="cashbus_workOrder" schema="dbo" >
        <id name="id" type="java.lang.Long">
            <column name="id" />
            <generator class="identity" />
        </id>
        <property name="loanRefId" type="java.lang.String">
            <column name="loanRefId" length="50" />
        </property>
        <property name="description" type="java.lang.String">
            <column name="description" />
        </property>
        <property name="status" type="java.lang.String">
            <column name="status" length="20" />
        </property>
    </class>
</hibernate-mapping>
