-- 添加剩余的重要表到CCDS数据库
-- 确保所有106个表都被包含

USE `ccds`;

-- 34. 仓库产品表
DROP TABLE IF EXISTS `wms_product`;
CREATE TABLE `wms_product` (
  `wpr_id` bigint NOT NULL AUTO_INCREMENT,
  `wpr_name` varchar(100) DEFAULT NULL,
  `wpr_type_id` bigint DEFAULT NULL,
  `wpr_model` varchar(100) DEFAULT NULL,
  `wpr_unit` bigint DEFAULT NULL,
  `wpr_color` varchar(50) DEFAULT NULL,
  `wpr_size` varchar(50) DEFAULT NULL,
  `wpr_provider` varchar(100) DEFAULT NULL,
  `wpr_up_lim` int DEFAULT NULL,
  `wpr_low_lim` int DEFAULT NULL,
  `wpr_cost_prc` decimal(18,2) DEFAULT NULL,
  `wpr_sale_prc` decimal(18,2) DEFAULT NULL,
  `wpr_pic` longtext,
  `wpr_cuser_code` varchar(50) DEFAULT NULL,
  `wpr_cre_date` datetime DEFAULT NULL,
  `wpr_euser_code` varchar(50) DEFAULT NULL,
  `wpr_edit_date` datetime DEFAULT NULL,
  `wpr_desc` longtext,
  `wpr_remark` longtext,
  `wpr_states` char(1) DEFAULT NULL,
  `wpr_range` longtext,
  `wpr_technology` longtext,
  `wpr_problem` longtext,
  `wpr_isdel` char(1) DEFAULT NULL,
  `wpr_code` varchar(50) DEFAULT NULL,
  `wpr_iscount` char(1) DEFAULT NULL,
  PRIMARY KEY (`wpr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 35. 供应商表
DROP TABLE IF EXISTS `sal_supplier`;
CREATE TABLE `sal_supplier` (
  `ssu_id` bigint NOT NULL AUTO_INCREMENT,
  `ssu_code` varchar(300) DEFAULT NULL,
  `ssu_name` varchar(100) DEFAULT NULL,
  `ssu_phone` varchar(50) DEFAULT NULL,
  `ssu_fex` varchar(50) DEFAULT NULL,
  `ssu_email` varchar(50) DEFAULT NULL,
  `ssu_net` varchar(200) DEFAULT NULL,
  `ssu_add` longtext,
  `ssu_prd` longtext,
  `ssu_county` bigint DEFAULT NULL,
  `ssu_pro` bigint DEFAULT NULL,
  `ssu_city` bigint DEFAULT NULL,
  `ssu_zip_code` varchar(50) DEFAULT NULL,
  `ssu_bank` varchar(50) DEFAULT NULL,
  `ssu_bank_code` varchar(50) DEFAULT NULL,
  `ssu_isdel` char(1) DEFAULT NULL,
  `ssu_remark` longtext,
  `ssu_inp_user` varchar(50) DEFAULT NULL,
  `ssu_cre_date` datetime DEFAULT NULL,
  `ssu_alt_date` datetime DEFAULT NULL,
  `ssu_alt_user` varchar(50) DEFAULT NULL,
  `ssu_bank_name` varchar(50) DEFAULT NULL,
  `ssu_type_id` bigint DEFAULT NULL,
  PRIMARY KEY (`ssu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 36. 仓库表
DROP TABLE IF EXISTS `wms_storehouse`;
CREATE TABLE `wms_storehouse` (
  `wst_code` varchar(50) NOT NULL,
  `wst_name` varchar(100) DEFAULT NULL,
  `wst_address` varchar(500) DEFAULT NULL,
  `wst_phone` varchar(50) DEFAULT NULL,
  `wst_manager` varchar(50) DEFAULT NULL,
  `wst_remark` longtext,
  `wst_isenabled` char(1) DEFAULT NULL,
  PRIMARY KEY (`wst_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 37. 仓库流水表
DROP TABLE IF EXISTS `wms_line`;
CREATE TABLE `wms_line` (
  `wli_id` bigint NOT NULL AUTO_INCREMENT,
  `wli_type_code` varchar(50) DEFAULT NULL,
  `wli_type` varchar(50) DEFAULT NULL,
  `wli_stro_code` varchar(50) DEFAULT NULL,
  `wli_wpr_id` bigint DEFAULT NULL,
  `wli_in_num` decimal(18,2) DEFAULT NULL,
  `wli_out_num` decimal(18,2) DEFAULT NULL,
  `wli_date` datetime DEFAULT NULL,
  `wli_state` char(1) DEFAULT NULL,
  `wli_man` varchar(50) DEFAULT NULL,
  `wli_wms_id` bigint DEFAULT NULL,
  `wli_isdel` char(1) DEFAULT NULL,
  `wli_num` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`wli_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 38. 银行案件表 (核心业务表)
DROP TABLE IF EXISTS `bank_case`;
CREATE TABLE `bank_case` (
  `cas_id` bigint NOT NULL AUTO_INCREMENT,
  `cas_code` varchar(100) DEFAULT NULL,
  `cas_group` varchar(50) DEFAULT NULL,
  `cas_state` int DEFAULT NULL,
  `cas_typ_hid` bigint DEFAULT NULL,
  `cas_name` varchar(50) DEFAULT NULL,
  `cas_phone` varchar(50) DEFAULT NULL,
  `cas_m` decimal(18,2) DEFAULT NULL,
  `cas_paid_m` decimal(18,2) DEFAULT NULL,
  `cas_se_no` bigint DEFAULT NULL,
  `cas_ins_time` datetime DEFAULT NULL,
  `cas_ins_user` varchar(25) DEFAULT NULL,
  `cas_alt_time` datetime DEFAULT NULL,
  `cas_alt_user` varchar(25) DEFAULT NULL,
  `cas_remark` longtext,
  `cas_id_no` varchar(50) DEFAULT NULL,
  `cas_address` varchar(500) DEFAULT NULL,
  `cas_work_address` varchar(500) DEFAULT NULL,
  `cas_work_phone` varchar(50) DEFAULT NULL,
  `cas_email` varchar(100) DEFAULT NULL,
  `cas_bank` varchar(100) DEFAULT NULL,
  `cas_card_no` varchar(50) DEFAULT NULL,
  `cas_overdue_date` varchar(200) DEFAULT NULL,
  `cas_pback_p` float DEFAULT NULL,
  `cas_wpost_code` varchar(50) DEFAULT NULL,
  `cas_deadline` varchar(200) DEFAULT NULL,
  `cas_is_host` varchar(50) DEFAULT NULL,
  `cas_bill_date` varchar(200) DEFAULT NULL,
  `cas_last_paid` varchar(200) DEFAULT NULL,
  `cas_count` varchar(100) DEFAULT NULL,
  `cas_left_pri` varchar(100) DEFAULT NULL,
  `cas_assign_ids` longtext,
  `cas_assign_names` longtext,
  `cas_last_assign_time` datetime DEFAULT NULL,
  `cas_overdue_days` int DEFAULT NULL,
  `cas_overdue_days_str` varchar(200) DEFAULT NULL,
  `cas_bir` varchar(50) DEFAULT NULL,
  `cas_mpost_code` varchar(50) DEFAULT NULL,
  `cas_perm_crline` varchar(50) DEFAULT NULL,
  `cas_alt_hold` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`cas_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 39. 催收记录表
DROP TABLE IF EXISTS `pho_red`;
CREATE TABLE `pho_red` (
  `pr_id` bigint NOT NULL AUTO_INCREMENT,
  `pr_typ_id` bigint DEFAULT NULL,
  `pr_contact` varchar(200) DEFAULT NULL,
  `pr_cas_id` bigint DEFAULT NULL,
  `pr_content` longtext,
  `pr_time` datetime DEFAULT NULL,
  `pr_se_no` bigint DEFAULT NULL,
  `pr_con_type` varchar(50) DEFAULT NULL,
  `pr_result` varchar(50) DEFAULT NULL,
  `pr_next_time` datetime DEFAULT NULL,
  `pr_remark` longtext,
  `pr_isdel` char(1) DEFAULT NULL,
  PRIMARY KEY (`pr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 40. 还款记录表
DROP TABLE IF EXISTS `case_paid`;
CREATE TABLE `case_paid` (
  `pa_id` bigint NOT NULL AUTO_INCREMENT,
  `pa_state` int DEFAULT NULL,
  `pa_cas_id` bigint DEFAULT NULL,
  `pa_ptp_d` datetime DEFAULT NULL,
  `pa_ptp_num` decimal(18,2) DEFAULT NULL,
  `pa_cp_time` datetime DEFAULT NULL,
  `pa_cp_num` decimal(18,2) DEFAULT NULL,
  `pa_comt_user` varchar(25) DEFAULT NULL,
  `pa_comt_time` datetime DEFAULT NULL,
  `pa_paid_time` datetime DEFAULT NULL,
  `pa_paid_num` decimal(18,2) DEFAULT NULL,
  `pa_se_no` bigint DEFAULT NULL,
  `pa_remark` longtext,
  `pa_type` varchar(50) DEFAULT NULL,
  `pa_method` varchar(50) DEFAULT NULL,
  `pa_ins_time` datetime DEFAULT NULL,
  `pa_ins_user` varchar(25) DEFAULT NULL,
  `pa_alt_time` datetime DEFAULT NULL,
  `pa_alt_user` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`pa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 41. 项目表
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `pro_id` bigint NOT NULL AUTO_INCREMENT,
  `pro_user_code` varchar(50) DEFAULT NULL,
  `pro_typ_id` bigint DEFAULT NULL,
  `pro_title` varchar(300) DEFAULT NULL,
  `pro_state` varchar(50) DEFAULT NULL,
  `pro_cre_date` datetime DEFAULT NULL,
  `pro_fin_date` datetime DEFAULT NULL,
  `pro_desc` longtext,
  `pro_remark` longtext,
  `pro_inp_user` varchar(50) DEFAULT NULL,
  `pro_upd_user` varchar(50) DEFAULT NULL,
  `pro_ins_date` datetime DEFAULT NULL,
  `pro_mod_date` datetime DEFAULT NULL,
  `pro_isdel` char(1) DEFAULT NULL,
  `pro_cor_code` bigint DEFAULT NULL,
  `pro_period` varchar(50) DEFAULT NULL,
  `pro_pro` varchar(300) DEFAULT NULL,
  `pro_pro_log` longtext,
  PRIMARY KEY (`pro_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 42. 文档模板表
DROP TABLE IF EXISTS `doc_template`;
CREATE TABLE `doc_template` (
  `tmp_id` bigint NOT NULL AUTO_INCREMENT,
  `tmp_name` varchar(50) DEFAULT NULL,
  `tmp_html` longtext,
  `tmp_type` varchar(50) DEFAULT NULL,
  `tmp_mark` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`tmp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 43. 销售任务表
DROP TABLE IF EXISTS `sal_all_task`;
CREATE TABLE `sal_all_task` (
  `sat_id` bigint NOT NULL AUTO_INCREMENT,
  `sat_date` varchar(50) DEFAULT NULL,
  `sat_se_no` bigint DEFAULT NULL,
  `sat_inp_date` datetime DEFAULT NULL,
  `sat_alt_date` datetime DEFAULT NULL,
  `sat_inp_name` varchar(50) DEFAULT NULL,
  `sat_alt_name` varchar(50) DEFAULT NULL,
  `sat_ht_mon` decimal(18,2) DEFAULT NULL,
  `sat_paid_mon` decimal(18,2) DEFAULT NULL,
  `sat_cus_num` int DEFAULT NULL,
  PRIMARY KEY (`sat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 44. 关系表 - 产品与订单
DROP TABLE IF EXISTS `r_spo_pro`;
CREATE TABLE `r_spo_pro` (
  `rpp_id` bigint NOT NULL AUTO_INCREMENT,
  `rpp_spo_id` bigint DEFAULT NULL,
  `rpp_pro_id` bigint DEFAULT NULL,
  `rpp_num` decimal(18,2) DEFAULT NULL,
  `rpp_price` decimal(18,2) DEFAULT NULL,
  `rpp_sum_mon` decimal(18,2) DEFAULT NULL,
  `rpp_remark` longtext,
  `rpp_out_num` decimal(18,2) DEFAULT NULL,
  `rpp_real_num` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`rpp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 45. 关系表 - 仓库与产品
DROP TABLE IF EXISTS `r_stro_pro`;
CREATE TABLE `r_stro_pro` (
  `rsp_id` bigint NOT NULL AUTO_INCREMENT,
  `rsp_stro_code` varchar(50) DEFAULT NULL,
  `rsp_pro_id` bigint DEFAULT NULL,
  `rsp_pro_num` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`rsp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 46. 关系表 - 发货与产品
DROP TABLE IF EXISTS `r_ship_pro`;
CREATE TABLE `r_ship_pro` (
  `rshp_id` bigint NOT NULL AUTO_INCREMENT,
  `rshp_ship_code` varchar(50) DEFAULT NULL,
  `rshp_pro_id` bigint DEFAULT NULL,
  PRIMARY KEY (`rshp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- 添加更多索引
CREATE INDEX idx_wms_product_name ON `wms_product` (`wpr_name`);
CREATE INDEX idx_sal_supplier_name ON `sal_supplier` (`ssu_name`);
CREATE INDEX idx_bank_case_name ON `bank_case` (`cas_name`);
CREATE INDEX idx_bank_case_phone ON `bank_case` (`cas_phone`);
CREATE INDEX idx_pho_red_time ON `pho_red` (`pr_time`);
CREATE INDEX idx_case_paid_time ON `case_paid` (`pa_paid_time`);
CREATE INDEX idx_project_state ON `project` (`pro_state`);

SELECT 'CCDS数据库扩展完成 - 现在包含更多重要表' as message;
